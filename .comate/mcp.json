{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "6000"}}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "path/to/git/repo"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop", "/path/to/other/allowed/dir"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "/path/to/custom/memory.json"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}