###
 # @Description:
 # @Version: 1.0
 # @Autor:
 # @Date: 2025-02-26 09:16:20
 # @LastEditors:
 # @LastEditTime: 2025-02-27 09:46:10
###
# 开发环境配置
NODE_ENV = 'production'

# 开发环境API
VUE_APP_BASE_API_PRE = '/portal-api'
VUE_APP_BASE_PORTAL_PRE = '/portal'
# VUE_APP_BASE_HOST = 'http://*************:8003'
VUE_APP_BASE_HOST = 'http://*************:8068'
# 图片域名拼接
# VUE_APP_FILE_URL = 'http://*************:9000'
# http://*************:8068/
VUE_APP_FILE_URL = 'http://**************:8066'

#服务上下文
VUE_APP_BASE_SERVER_PATH = '/portal-web'
#认证地址
#VUE_APP_AUTH_URL = 'http://*************:8232'
# VUE_APP_AUTH_URL = 'http://*************:11003'
# VUE_APP_AUTH_URL = 'http://*************:8083/guum/sso/auth/'
VUE_APP_AUTH_URL = 'http://**************:8065/guum/sso/auth/'

