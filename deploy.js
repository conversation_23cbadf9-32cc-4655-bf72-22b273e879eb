const path = require("path");
// const archiver = require("archiver");
// const fs = require("fs");
let passandword="P@ssword2024";
let hostip="192"+".168.116.4";
const { NodeSSH } = require("node-ssh");
const ssh = new NodeSSH();
const configs = {
  host: hostip,
  username: "root",
  password: passandword,
  port: 22,
  serverPath: "/opt/www/portal-web",
  distPath: path.resolve(__dirname, "dist/"),
};
//上传到服务器
uploadfile();
function uploadfile() {
  console.log("开始上传");
  ssh
    .connect({
      //configs存放的是连接远程机器的信息
      host: configs.host,
      username: configs.username,
      password: configs.password,
      port: configs.port, //ssh连接默认在22端口
    })
    .then(function () {
      console.log("上传中...");
      //上传网站的发布包至configs中配置的远程服务器的指定地址
      ssh
        .putDirectory(configs.distPath, configs.serverPath)
        .then(function () {
          console.log("上传成功");
          process.exit(0);
        })
        .catch((err) => {
          process.exit(0);
        });
    })
    .catch((err) => {
      process.exit(0);
    });
}

// 开始打包
// startZip();
// function startZip() {
//   console.log("打包成zip");
//   const archive = archiver("zip", {
//     zlib: { level: 9 },
//   }).on("error", (err) => {
//     throw err;
//   });
//   const output = fs
//     .createWriteStream(`${configs.distPath}.zip`)
//     .on("close", (err) => {
//       if (err) {
//         console.log("  关闭archiver异常:", err);
//         return;
//       }
//       console.log("  zip打包成功");
//     });
//   archive.pipe(output); //典型的node流用法
//   archive.directory(configs.distPath, "/"); //将srcPach路径对应的内容添加到zip包中/public路径
//   archive.finalize();
// }
// uploadfile2();
// function uploadfile2() {
//   console.log("开始上传");
//   ssh
//     .connect({
//       //configs存放的是连接远程机器的信息
//       host: configs.host,
//       username: configs.username,
//       password: configs.password,
//       port: configs.port, //ssh连接默认在22端口
//     })
//     .then(function () {
//       console.log("ssh连接成功:", configs.host);
//       console.log("上传中...");
//       //上传网站的发布包至configs中配置的远程服务器的指定地址
//       ssh
//         .putFile(`${configs.distPath}.zip`, `${configs.serverPath}.zip`)
//         .then(function () {
//           console.log("上传成功");
//           process.exit(0);
//         })
//         .catch((err) => {
//           console.log("上传出错:", err);
//           process.exit(0);
//         });
//     })
//     .catch((err) => {
//       console.log("ssh连接失败:", err);
//       process.exit(0);
//     });
// }
