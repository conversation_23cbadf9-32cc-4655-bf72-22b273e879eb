{"name": "tiny-ui", "version": "3.5.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode dev", "local": "vue-cli-service serve --mode local", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode prod", "build:docker": "vue-cli-service build --dest=./docker/dist/", "deploy": "vue-cli-service build --mode dev&&node ./deploy.js", "lint": "vue-cli-service lint"}, "dependencies": {"@aleen42/kindeditor": "^2024.1.0-beta.3", "@babel/parser": "7.7.4", "@lapo/asn1js": "^1.2.1", "@riophae/vue-treeselect": "^0.4.0", "@smallwei/avue": "2.8.23", "@sscfaith/avue-form-design": "1.4.8", "animate.css": "^4.1.1", "avue-plugin-ueditor": "^0.2.18", "axios": "^1.7.2", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "clipboard": "2.0.8", "codemirror": "^5.58.1", "core-js": "^3.21.1", "crypto-js": "^4.2.0", "echarts": "^5.4.3", "element-ui": "^2.15.12", "file-saver": "2.0.5", "highlight.js": "11.9.0", "js-md5": "^0.7.3", "js-sha1": "^0.6.0", "jsrsasign": "^11.1.0", "jszip": "^3.7.0", "jszip-utils": "^0.1.0", "nprogress": "^0.2.0", "ofd-xml-parser": "^0.0.2", "script-loader": "^0.7.2", "sm-crypto": "^0.3.2", "swiper": "^6.5.1", "throttle-debounce": "2.1.0", "vue": "2.6.10", "vue-awesome-swiper": "^4.1.1", "vue-axios": "^3.5.2", "vue-grid-layout": "^2.4.0", "vue-pdf": "^4.3.0", "vue-router": "^3.1.3", "vue-seamless-scroll": "^1.1.23", "vue-video-player": "^5.0.2", "video.js": "^7.14.3", "vuedraggable": "2.24.3", "vuex": "^3.2.0", "web-streams-polyfill": "^3.1.0", "x2js": "^3.4.0", "xgplayer": "^3.0.10", "xml-js": "1.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-service": "^3.12.0", "chai": "^4.2.0", "node-ssh": "^13.0.1", "compression-webpack-plugin": "^3.1.0", "sass": "1.32.13", "sass-loader": "10.1.1", "speed-measure-webpack-plugin": "^1.5.0", "vue-template-compiler": "2.6.10"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}