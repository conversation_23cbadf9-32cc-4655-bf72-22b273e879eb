<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: "app",
  data() {
    return {};
  },
  watch: {},
  created() {
    window.addEventListener("beforeunload", () => {
      let path = this.$route.path;
      sessionStorage.setItem("beforeunload-path", JSON.stringify(path));
    });
  },
  // mounted() {
  //   if (process.env.NODE_ENV == "development") {
  //     this.$store.commit(
  //       "SET_ACCESS_TOKEN",
  //       "eb4a4a9dfccb4a69960c0fbb49a4d3f21"
  //     );
  //   }
  // },
  methods: {
    //app.vue

    //在其他页面发送和接收 socket 消息
    // 发送和接收消息
    handleMsg(msg) {
      let that = this;
      console.log("websocket连接信息", that.$webSocketGlobal.ws);
      if (
        that.$webSocketGlobal.ws &&
        that.$webSocketGlobal.ws.readyState === 1
      ) {
        console.log("发送信息", msg);
        that.$webSocketGlobal.ws.send(msg);
      }
      that.$webSocketGlobal.ws.onmessage = function (res) {
        console.log("收到服务器内容", res);
      };
    },
  },
  computed: {},
};
</script>
<style lang="scss">
@import "styles/iconfont/iconfont.css";
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
