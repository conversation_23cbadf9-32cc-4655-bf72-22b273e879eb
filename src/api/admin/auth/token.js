/*
 * @Author: liuzhengshuai <EMAIL>
 * @Date: 2024-09-11 09:16:40
 * @LastEditors: liuzhengshuai <EMAIL>
 * @LastEditTime: 2024-11-08 15:45:10
 * @FilePath: \uc-portal\src\api\admin\auth\token.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/admin/token/page',
    method: 'get',
    params: query
  })
}

export function delObj(token) {
  return request({
    url: '/admin/token/' + token,
    method: 'delete'
  })
}

export function ssoLoginByCode(code) {
  return request({
    url: '/portal/oauth2/sso/login-by-code?code=' + code,
    method: 'post',
  })
}

export function ssoLoginByTicket(data) {
  return request({
    url: '/sso/login-by-ticket',
    method: 'post',
    data
  })
}

// 获取验证图片  以及token
export function reqGet(data) {
  return request({
    url: '/system/captcha/get',
    method: 'post',
    data
  })
}

// 滑动或者点选验证
export function reqCheck(data) {
  return request({
    url: '/system/captcha/check',
    method: 'post',
    data
  })
}
