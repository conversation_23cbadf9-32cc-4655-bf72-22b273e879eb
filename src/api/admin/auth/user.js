import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/admin/user/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/user/save',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/user/' + id,
    method: 'get'
  })
}
export function getObjDetail(id) {
  return request({
    url: '/admin/user/details/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/user/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/user/edit',
    method: 'put',
    data: obj
  })
}

export function isExsit(params) {
  return request({
    url: '/admin/user/check/exist',
    method: 'get',
    params: params
  })
}

export function listUser() {
  return request({
    url: '/admin/user/listUser',
    method: 'get'
  })
}


export function sendLoginSms(params) {
  return request({
    url: '/admin/auth/sendLoginSmsCode',
    method: 'post',
    data: params
  })
}
//第一次修改密码
export function firstUpdatePassword(params) {
  return request({
    url: "/profile/update-password",
    method: "post",
    data: params,
  });
}
//重置用户密码
export function updateResetUserPassword(params) {
  return request({
    url: "/admin/user/reset-password",
    method: "post",
    data: params,
  });
}
