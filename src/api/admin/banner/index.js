import request from '@/router/axios'


export function getBannerList(params) {
  return request({
    method: 'get',
    url: '/admin/cms/roll/page',
    params,
    headers: {
      apiFlag:true
    }
  });
}
export function getNoLoginBannerList(params) {
  return request({
    method: 'get',
    url: '/portal/article/api/roll/page',
    params,
    // headers: {
    //   apiFlag:true
    // }
  });
}

export function updateBanner(data) {
  return request({
    method: 'put',
    url: `/admin/cms/roll/update`,
    data,
  });
}

export function addBanner(data) {
  return request({
    method: 'post',
    url: '/admin/cms/roll/create',
    data,
  });
}

export function deleteBanner(data) {
  return request({
    method: 'post',
    url: `/admin/cms/roll/delete`,
    data
  });
}

export function getNewsList(query) {
  return request({
    url: "/admin/cms/article/page",
    method: "get",
    params: query,
    // headers: {
    //   apiFlag:true
    // }
  });
}


export function disableBanner(data) {
  return request({
    url: '/admin/cms/roll/disable',
    method: 'post',
    data
  })
}

export function enableBanner(data) {
  return request({
    url: '/admin/cms/roll/enable',
    method: 'post',
    data
  })
}
