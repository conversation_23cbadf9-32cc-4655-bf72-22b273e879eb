import request from '@/router/axios'

export function getSummary(data) {
  return request({
    url: '/admin/statistics/summary',
    method: 'post',
    data
  })
}

export function getTop10(data) {
  return request({
    url: '/admin/statistics/top10',
    method: 'post',
    data
  })
}

export function getAuth(data) {
  return request({
    url: '/admin/statistics/auth',
    method: 'post',
    data
  })
}

export function getVisit(data) {
  return request({
    url: '/admin/statistics/visit',
    method: 'post',
    data
  })
}

export function getLoginPage(params) {
  return request({
    url: '/sys-login-log/page',
    method: 'get',
    params
  })
}
