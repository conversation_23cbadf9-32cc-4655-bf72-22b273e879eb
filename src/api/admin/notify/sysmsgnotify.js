import request from '@/router/axios'

export function pageNotify(query) {
  return request({
    url: '/msg/sys-msg-notify/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/msg/sys-msg-notify/create',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/msg/sys-msg-notify/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/msg/sys-msg-notify/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/msg/sys-msg-notify/update',
    method: 'put',
    data: obj
  })
}


export function getUnreadNotifyList() {
  return request({
    url: '/msg/sys-msg-notify/get-unread-page',
    method: 'get',
  })
}
export function getMyUnreadNotifyList(query) {
  return request({
    url: '/msg/sys-msg-notify/my-notify-page',
    method: 'get',
    params: query
  })
}

// 批量标记已读
export function updateNotifyRead(ids) {
  return request({
    url: '/msg/sys-msg-notify/update-read',
    method: 'put',
    data: ids
  })
}

// 标记所有站内信为已读
export function updateAllNotifyRead() {
  return request({
    url: '/msg/sys-msg-notify/update-all-read',
    method: 'put'
  })
}
