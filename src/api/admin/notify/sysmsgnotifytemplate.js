
import request from '@/router/axios'

export function pageTemplate(query) {
  return request({
    url: '/msg/sys-msg-notify-template/page',
    method: 'get',
    params: query
  })
}

export function addTemplate(obj) {
  return request({
    url: '/msg/sys-msg-notify-template/create',
    method: 'post',
    data: obj
  })
}

export function getTemplate(id) {
  return request({
    url: '/msg/sys-msg-notify-template/' + id,
    method: 'get'
  })
}

export function delTemplate(id) {
  return request({
    url: '/msg/sys-msg-notify-template/' + id,
    method: 'delete'
  })
}

export function updateTemplate(obj) {
  return request({
    url: '/msg/sys-msg-notify-template/update',
    method: 'put',
    data: obj
  })
}
//
export function sendNotify(data) {
  return request({
    url: '/msg/sys-msg-notify-template/send-test',
    method: 'post',
    data: data
  })
}
