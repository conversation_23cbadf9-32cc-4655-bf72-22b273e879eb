import request from '@/router/axios'
//查询所有分组
export function fetchAllGroup() {
  return request({
    // url: '/sys-oauth2-client-group/list',
    url: '/app/group/getAppGroup',
    method: 'get'
  })
}
//新增应用分组
export function addGroup(obj) {
  return request({
    url: '/app/group/createGroup',
    method: 'post',
    data: obj
  })
}
//通过id删除应用分组
export function delGroup(id) {
  return request({
    url: '/app/group/delete/group/' + id,
    method: 'post'
  })
}
//修改应用分组
export function putGroup(obj) {
  return request({
    url: '/app/group/updateGroup',
    method: 'post',
    data: obj
  })
}

//查询所有分组以及分组下的应用
export function fetchAppsByGroup(query) {
  return request({
    url: '/app/pageAppByGroup',
    method: 'get',
    params: query
  })
}

//获取未分配组的应用
export function fetchAllAppNotInGroup() {
  return request({
    url: '/app/group/not-in-group',
    method: 'get'
  })
}


//添加应用到分组
export function addAppToGroup(obj) {
  return request({
    url: '/app/group/add-app',
    method: 'post',
    data: obj
  })
}

//修改应用组内排序
export function editAppToGroup(obj) {
  return request({
    url: '/app/group/change-sort',
    method: 'post',
    data: obj
  })
}

//移除应用
export function removeAppInGroup(obj) {
  return request({
    url: '/app/group/remove-app',
    method: 'post',
    data: obj
  })
}
//查询所有分组以及分组下的应用-树形
export function getClientGroupTree() {
  return request({
    url: '/sys-oauth2-client-group/clientGroupTree',
    method: 'get'
  })
}

//查询用户下可用 应用
export function getCanUseAppList(params) {
  return request({
    url: '/open/oauth2-client/userCanDeliverApps',
    method: 'get',
    params: params
  })
}
