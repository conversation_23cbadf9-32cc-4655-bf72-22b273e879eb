import request from '@/router/axios'

export function fetchAllProvince() {
  return request({
    url: '/sysArea/getProvinces',
    method: 'get'
  })
}

export function fetchChildren(query) {
  return request({
    url: '/sysArea/getChildrenById',
    method: 'get',
    params: query
  })
}
export function addObj(obj) {
  return request({
    url: '/sysArea',
    method: 'post',
    data: obj
  })
}
export function getSysareaObj(id) {
  return request({
    url: '/sysArea/' + id,
    method: 'get'
  })
}
export function delObj(id) {
  return request({
    url: '/sysArea/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/sysArea',
    method: 'put',
    data: obj
  })
}

export function searchOrgByKeyWord(query) {
  return request({
    url: '/sysArea/page',
    method: 'get',
    params: query
  })
}