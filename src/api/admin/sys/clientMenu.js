

import request from '@/router/axios'

export function getMenu(id) {
  return request({
    url: '/client/menu/list-menu',
    params: {parentId: id},
    method: 'get'
  })
}

export function fetchMenuTree(params) {
  return request({
    url: '/client/menu/tree',
    method: 'get',
    params: params
  })
}

export function addObj(obj) {
  return request({
    url: '/client/menu/add',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/client/menu/getMenuById/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/client/menu/del/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/client/menu/update',
    method: 'post',
    data: obj
  })
}



// export function clearMenuCache(){
//   return request({
//     url:'/client/menu/cache',
//     method:'delete'
//   })
// }

