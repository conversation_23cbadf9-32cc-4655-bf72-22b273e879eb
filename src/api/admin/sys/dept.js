import request from '@/router/axios'

export function fetchDeptTree(query) {
  return request({
    url: '/admin/dept/user-tree',
    method: 'get',
    params: query
  })
}

export function fetchTree(query) {
  return request({
    url: '/admin/dept/tree',
    method: 'get',
    params: query
  })
}
export function canUseNetDeptTree(query) {
  return request({
    url: '/admin/dept/currentUserCanOperateDeptTree',
    method: 'get',
    params: query
  })
}
export function getSonDeptById(query) {
  return request({
    url: '/admin/dept/page',
    method: 'get',
    params: query
  })
}

export function canUseDeptSonTree(query) {
  return request({
    url: '/admin/dept/querySubDept',
    method: 'get',
    params: query
    // data:query
  })
}
// 根据名称搜索部门
export function searchDeptByName(query) {
  return request({
    url: '/admin/dept/list/searchWithAuthority',
    method: 'get',
    params: query
    // data:query
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/dept/save',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/dept/getById/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/dept/remove/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/dept/edit',
    method: 'put',
    data: obj
  })
}

export function getdetails(obj) {
  return request({
    url: '/admin/dept/details/' + obj,
    method: 'get'
  })
}

// 部门发布统计

export function getArticleStatsByDept(query) {
  return request({
    url: '/admin/cms/article/getArticleStatsByDept',
    method: 'post',
    data: query

  })
}
/**
* 部门统计获取部门发布详情
*/
export function getDeptArticleDetails(query) {
  return request({
    url: '/admin/cms/article/getDeptArticleDetails',
    method: 'post',
    data: query

  })
}

export function listDept() {
  return request({
    url: '/admin/dept/list-dept',
    method: 'get'

  })
}

export function getAllProvinces(query) {
  return request({
    url: '/sysArea/getProvinces',
    method: 'get',
    params: query
  })
}
export function getAllCitys(query) {
  return request({
    url: '/sysArea/getChildrenById',
    method: 'get',
    params: query
  })
}

