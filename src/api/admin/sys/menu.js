

import request from '@/router/axios'

export function getMenu(id) {
  return request({
    url: '/admin/menu/list-menu',
    params: {parentId: id},
    method: 'get'
  })
}
export function getListMenu(id) {
  return request({
    url: '/admin/menu/list-menu',
    params: {parentId: id},
    method: 'get'
  })
}

export function fetchMenuTree(lazy, parentId,needOther) {
  return request({
    url: '/admin/menu/tree',
    method: 'get',
    params: {lazy: lazy, parentId: parentId,needOther:needOther}
  })
}

export function fetchMenuTreeNew(params) {
  return request({
    url: '/admin/menu/tree',
    method: 'get',
    params: params
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/menu/add',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/menu/getMenuById/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/menu/del/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/menu/update',
    method: 'post',
    data: obj
  })
}

export function clearMenuCache(){
  return request({
    url:'/admin/menu/cache',
    method:'delete'
  })
}

export function getAdminMenuTree() {
  return request({
    url: '/api/admin/menu/tree',
    method: 'get',
    
  })
}

export function getDaohangMenuTree() {
  return request({
    url: '/admin/navigation/tree',
    method: 'get',
    
  })
}

export function getMenusById(id) {
  return request({
    url: '/admin/menu/getMenuBySubSysId/'+id,
    method: 'get',
    
  })
}
export function getMenusInfoById(params) {
  return request({
    url: '/admin/menu/queryMenuDetail',
    method: 'get',
    params:params
  })
}




// export function queryRoleMenu(obj) {
//   return request({
//     url: '/admin/role/menu/queryRoleMenu',
//     method: 'post',
//     data: obj
//   })
// }
