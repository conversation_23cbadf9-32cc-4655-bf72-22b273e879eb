

import request from '@/router/axios'

export function getMenu(id) {
  return request({
    url: '/client/menu/list-menu',
    params: {parentId: id},
    method: 'get'
  })
}

export function fetchMenuTree(lazy, parentId) {
  return request({
    url: '/admin/cms/menu/tree',
    method: 'get',
    // params: {}
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/cms/menu/create',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/cms/menu/detail/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/cms/menu/delete' ,
    method: 'post',
    data:{id:id}
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/cms/menu/update',
    method: 'put',
    data: obj
  })
}
export function activePortalMenu(obj) {
  return request({
    url: '/admin/cms/menu/enable',
    method: 'post',
    data: obj
  })
}
export function disablePortalMenu(obj) {
  return request({
    url: '/admin/cms/menu/disable',
    method: 'post',
    data: obj
  })
}
//获取门户的网站menu
export function getNoLoginMenuList() {
  return request({
    url: '/portal/article/api/menu/tree/0',
    method: 'get'
  })
}
// export function clearMenuCache(){
//   return request({
//     url:'/api/client/menu/cache',
//     method:'delete'
//   })
// }

