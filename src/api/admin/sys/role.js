

import request from '@/router/axios'

export function fetchList (query) {
  return request({
    url: '/admin/role/page',
    method: 'get',
    params: query
  })
}

export function deptRoleList () {
  return request({
    url: '/admin/role/list',
    method: 'get'
  })
}

export function getObj (id) {
  return request({
    url: '/admin/role/' + id,
    method: 'get'
  })
}

export function addObj (obj) {
  return request({
    url: '/admin/role/save',
    method: 'post',
    data: obj
  })
}

export function putObj (obj) {
  return request({
    url: '/admin/role/update',
    method: 'post',
    data: obj
  })
}

export function delObj (id) {
  return request({
    url: '/admin/role/del/' + id,
    method: 'delete'
  })
}

export function permissionUpd (data) {
  return request({
    url: '/admin/role/menu/update',
    method: 'post',
    data:data
    // data: {
    //   roleId: roleId,
    //   menuIds: menuIds
    // }
  })
}

export function fetchRoleTree (roleName) {
  return request({
    url: '/admin/menu/tree/' + roleName,
    method: 'get'
  })
}


export function getAllAppList (query) {
  return request({
    url: '/open/oauth2-client/list-client',
    method: 'get',
    params: query
  })
}
// 绑定应用
export function addBindApp (obj) {
  return request({
    url: '/admin/role/bindRoleApp',
    method: 'post',
    data: obj
  })
}
export function getBindAppList (query) {
  return request({
    url: '/admin/role/is_bind',
    method: 'get',
    params: query
  })
}

export function getBindAnquanAppList (query) {
  return request({
    url: '/admin/roleApp/list',
    method: 'get',
    params: query
  })
}
// 绑定app应用
export function addAppMenus (obj) {
  return request({
    url: '/admin/role/client/menu/update',
    method: 'post',
    data: obj
  })
}

export function getBindClientAppList (query) {
  return request({
    url: '/client/menu/tree/'+query.roleId,
    method: 'get',
    
  })
}

export function pageUserWithRole (query) {
  return request({
    url: '/admin/role/pageUserWithRole',
    method: 'get',
    params:query
  })
}

// 绑定用户
export function bindUser (obj) {
  return request({
    url: '/admin/role/bindUserToRole',
    method: 'post',
    data: obj
  })
}
export function unbindUser (obj) {
  return request({
    url: '/admin/role/unbindUser',
    method: 'post',
    data: obj
  })
}

export function pageUserWithoutRole (query) {
  return request({
    url: '/admin/role/pageUserWithoutRole',
    method: 'get',
    params:query
  })
}
// 查询角色可用、可分配
export function queryRoleCanUseMenu (obj) {
  return request({
    url: '/admin/role/menu/queryRoleMenu',
    method: 'post',
    data: obj
  })
}


