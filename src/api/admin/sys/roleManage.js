import request from "@/router/axios";

export function safeRoleList(query) {
  return request({
    url: "/sys-admrol/page",
    method: "get",
    params: query,
  });
}

export function safeRoleTree(query) {
  return request({
    url: "/sys-admrol/tree",
    method: "get",
    params: query,
  });
}

export function safeRoleCreate(data) {
  return request({
    url: "/sys-admrol/create",
    method: "post",
    data,
  });
}
export function safeRoleUpdate(data) {
  return request({
    url: "/sys-admrol/update",
    method: "put",
    data,
  });
}
export function safeRoleDel(id) {
  return request({
    url: `/sys-admrol/${id}`,
    method: "delete",
  });
}
// 将业务角色绑定到安全角色
export function bindRoleToSecureRole(data) {
  return request({
    url: "/sys-admrol/authOperateRoleToSecureRole",
    method: "post",
    data,
  });
}
// 查询安全角色下的用户
export function queryRoleUser(data) {
  return request({
    url: "/sys-admrol/querySecureRoleUsers",
    method: "post",
    data,
  });
}
//查询未关联指定安全角色的用户列表
export function queryNoRoleUser(data) {
  return request({
    url: "/sys-admrol/queryUnRelateSecureRoleUsers",
    method: "post",
    data,
  });
}
export function bindUserToSecureRole(data) {
  return request({
    url: "/sys-admrol/addUserToAdmrol",
    method: "post",
    data,
  });
}
//删除用户
export function delUserToSecureRole(data) {
  return request({
    url: "/sys-admrol/delUserFromAdmrol",
    method: "post",
    data,
  });
}


//查询已绑定对应安全角色的业务角色列表
export function queryBindServiceRoleList(data) {
  return request({
    url: "/sys-admrol/queryBindSecureRoleOperateRoles",
    method: "post",
    data,
  });
}

//查询未绑定对应安全角色的业务角色列表
export function queryBindNoServiceRoleList(data) {
  return request({
    url: "/sys-admrol/queryUnBindSecureRoleOperateRoles",
    method: "post",
    data,
  });
}

//安全角色删除业务角色
export function delServiceRoleInfo(data) {
  return request({
    url: "/sys-admrol/cancelOperateRoleToSecureRole",
    method: "post",
    data,
  });
}

//查询角色可用、可分配
export function queryCanUserAndAsignTree(data) {
  return request({
    url: "/admin/role/menu/queryRoleMenu",
    method: "post",
    data,
  });
}
//更新安全角色可管理的组织机构
export function updateRoleDept(data) {
  return request({
    url: "/sys-admrol/updateSysSecureRoleDept",
    method: "post",
    data,
  });
}


//安全角色查询已勾选组织机构
export function querySecureOrg(data) {
  return request({
    url: "/sys-admrol/querySecureRoleDept?sysSecureRoleId="+data.sysSecureRoleId,
    method: "post",
    // data,
  });
}

