import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/admin/sys_dict/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/sys_dict',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/sys_dict/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/sys_dict/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/sys_dict',
    method: 'put',
    data: obj
  })
}

export function remote(type) {
  return request({
    url: '/admin/sys_dict/type/' + type,
    method: 'get'
  })
}

export function listAll() {
  return request({
    url: '/admin/sys_dict/list-all',
    method: 'get'
  })
}
//字典子项相关api
export function fetchItemList(query) {
  return request({
    url: '/admin/sys_dict/item/page',
    method: 'get',
    params: query
  })
}


export function addItemObj(obj) {
  return request({
    url: '/admin/sys_dict/item',
    method: 'post',
    data: obj
  })
}

export function getItemObj(id) {
  return request({
    url: '/admin/sys_dict/item/' + id,
    method: 'get'
  })
}

export function delItemObj(id) {
  return request({
    url: '/admin/sys_dict/item/' + id,
    method: 'delete'
  })
}

export function putItemObj(obj) {
  return request({
    url: '/admin/sys_dict/item',
    method: 'put',
    data: obj
  })
}


