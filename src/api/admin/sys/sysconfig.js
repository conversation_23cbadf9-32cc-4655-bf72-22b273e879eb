
import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/admin/sysconfig/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/sysconfig',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/admin/sysconfig/' + id,
    method: 'get'
  })
}

export function getObjByKey(key) {
  return request({
    url: '/admin/sysconfig/configValue/' + key,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/admin/sysconfig/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/sysconfig',
    method: 'put',
    data: obj
  })
}

//保存文件存储配置
export function saveStorageConfig(config) {
  return request({
    url: '/storage/config/save',
    method: 'post',
    data: config
  })
}
// 获取文件存储配置文件
export function getStorageConfig() {
  return request({
    url: '/storage/config/get',
    method: 'get'
  })
}
