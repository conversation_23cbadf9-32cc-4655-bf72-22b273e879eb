

import request from '@/router/axios'

export function getFetchList(params) {
  return request({
    url: '/admin/sysconfig/getConfig',
    params: params,
    method: 'get'
  })
}

// export function fetchMenuTree(lazy, parentId) {
//   return request({
//     url: '/portal/admin/cms/menu/tree',
//     method: 'get',
//     // params: {}
//   })
// }

export function addObj(obj) {
  return request({
    url: '/admin/sysconfig/saveSysSetting',
    method: 'post',
    data: obj
  })
}

// export function getObj(id) {
//   return request({
//     url: '/portal/admin/cms/menu/detail/' + id,
//     method: 'get'
//   })
// }

// export function delObj(id) {
//   return request({
//     url: '/api/admin/sysconfig/'+id ,
//     method: 'DELETE',
   
//   })
// }

// export function putObj(obj) {
//   return request({
//     url: '/portal/admin/cms/menu/update',
//     method: 'put',
//     data: obj
//   })
// }
// export function activePortalMenu(obj) {
//   return request({
//     url: '/portal/admin/cms/menu/enable',
//     method: 'post',
//     data: obj
//   })
// }
// export function disablePortalMenu(obj) {
//   return request({
//     url: '/portal/admin/cms/menu/disable',
//     method: 'post',
//     data: obj
//   })
// }
// //获取门户的网站menu
// export function getNoLoginMenuList() {
//   return request({
//     url: '/portal/portal/article/api/menu/tree/0',
//     method: 'get'
//   })
// }
// export function clearMenuCache(){
//   return request({
//     url:'/api/client/menu/cache',
//     method:'delete'
//   })
// }
// 获取区划
export function getArea(params) {
  return request({
    url: '/sysArea/listAreaByLevel',
    method: 'get',
    params
  })
}
// 高级搜索
export function seniorSearch(data) {
  return request({
    url: '/portal/article/api/article/search',
    method: 'post',
    data
  })
}