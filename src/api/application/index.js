import request from '@/router/axios'

export function getAppList(query) {
    return request({
        url: '/app/listUcApp',
        method: 'get',
        params: query
    })
}


export function getPageData(query) {
    return request({
        url: `/app/page`,
        method: "get",
        params: query
    });
}
export function addAppObj(obj) {
  return request({
    url: '/app/create',
    method: 'post',
    data: obj
  })
}

export function deleteAppObj(id) {
  return request({
    url: '/app/delete/' + id,
    method: 'post'
  })
}
export function updateAppObj(obj) {
    return request({
      url: '/app/update',
      method: 'post',
      data: obj
    })
  }

  export function doSubmitObj(params) {
    return request({
      url: '/app/commit',
        method: 'post',
      data:params
    })
}
export function doShenheObj(params) {
    return request({
      url: '/app/approve',
        method: 'post',
      data:params
    })
  }
// 审核拒绝
  export function doShenheRefuseObj(params) {
    return request({
      url: '/app/refuse',
        method: 'post',
      data:params
    })
  }
  // 启用应用
  export function doEnableApp(params) {
    return request({
      url: '/app/enable',
        method: 'post',
      data:params
    })
}
   // 不启用应用
   export function doNotEnableApp(params) {
    return request({
      url: '/app/close',
        method: 'post',
      data:params
    })
  }

  //登录后获取全部应用
//   export function getApp(params) {
//     return request({
//       url: '/portal/home/<USER>/portal-group-app',
//         method: 'get',
//       data:params
//     })
//   }

// export function putObj(obj) {
//   return request({
//     url: '/common/notifytemplate',
//     method: 'put',
//     data: obj
//   })
// }

// export function getDicData(type) {
//   return request({
//     url: `/common/commDict/type/${type}`,
//     method: "get",
//   });
// }