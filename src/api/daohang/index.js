import request from '@/router/axios'

//查询所有栏目
export function addDaohangData(data) {
    return request({
        url: '/admin/navigation/create',
        method: 'post',
       data:data
        
    })
}
// // 获取导航树
export function getDaohangTree() {
    return request({
        url: '/admin/navigation/tree',
        method: 'get',
        
    })
}

// 修改
export function updateDaohangData(data) {
    return request({
        url: '/admin/navigation/update',
        method: 'put',
        data:data
      
    })
}
//
export function delDaohangData(id) {
    return request({
        url: '/admin/navigation/del/'+id,
        method: 'DELETE',
      
    })
}


//
export function getDaohangSystemList(params) {
    return request({
        url: '/admin/navigation/sys/list',
        method: 'get',
        params:params
        
    })
} 

export function getDaohangSystemPage(params) {
    return request({
        url: '/admin/navigation/sys/page',
        method: 'get',
        params:params
        
    })
} 

//添加导航系统
export function addDaohangSystemData(data) {
    return request({
        url: '/admin/navigation/sys/add',
        method: 'post',
       data:data
        
    })
}

export function delBatchDaohangData(data) {
    return request({
        url: '/admin/navigation/sys/del/batch',
        method: 'post',
        data:data
      
    })
}

// // 分页查询内容
// export function categoryPage(params) {
//     return request({
//         url: '/portal/portal/article/api/article/page',
//         method: 'get',
//         params: params,
//         headers:{apiFlag:true,}
//     })
// }

// // 内容增加阅读量
// export function categoryNum(params) {
//     return request({
//         url: '/portal/portal/article/api/article/add/num',
//         method: 'post',
//         data: params,
//         headers:{apiFlag:true,}
//     })
// }