import request from '@/router/axios'

//查询所有栏目
export function categoryAll() {
    return request({
        url: '/portal/article/api/category/all',
        method: 'get',
        // headers:{apiFlag:true,}
        
    })
}
// 根据栏目编码查询子栏目列表
export function categoryCode(code) {
    return request({
        url: '/portal/article/api/category/child/' + code,
        method: 'get',
        // headers:{apiFlag:true,}
    })
}

// 根据id查询内容详情
export function categoryChild(id) {
    return request({
        url: '/portal/article/api/detail/' + id,
        method: 'get',
        // headers:{apiFlag:true,}
    })
}

// 分页查询内容
export function categoryPage(params) {
    return request({
        url: '/portal/article/api/article/page',
        method: 'get',
        params: params,
        // headers:{apiFlag:true,}
    })
}

// 内容增加阅读量
export function categoryNum(params) {
    return request({
        url: '/portal/article/api/article/add/num',
        method: 'post',
        data: params,
        // headers:{apiFlag:true,}
    })
}