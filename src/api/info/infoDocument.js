import request from '@/router/axios'


export function fetchList(query) {
  return request({
    url: '/msg/record/pageRecord',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/common/notifylog',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/common/notifylog/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/common/notifylog/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/common/notifylog',
    method: 'put',
    data: obj
  })
}

export function getDicData(type) {
  return request({
    url: `/common/commDict/type/${type}`,
    method: "get",
  });
}
