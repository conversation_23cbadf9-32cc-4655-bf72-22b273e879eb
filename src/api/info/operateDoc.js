import request from '@/router/axios'

export function fetchList(query) {
    return request({
        url: '/admin/hallsyslog/page',
        method: 'get',
        params: query
    })
}


export function getDicData(type) {
    return request({
        url: `/common/commDict/type/${type}`,
        method: "get",
    });
}
// export function addObj(obj) {
//   return request({
//     url: '/common/notifytemplate',
//     method: 'post',
//     data: obj
//   })
// }

// export function getObj(id) {
//   return request({
//     url: '/common/notifytemplate/' + id,
//     method: 'get'
//   })
// }

// export function delObj(id) {
//   return request({
//     url: '/common/notifytemplate/' + id,
//     method: 'delete'
//   })
// }

// export function putObj(obj) {
//   return request({
//     url: '/common/notifytemplate',
//     method: 'put',
//     data: obj
//   })
// }

// export function getDicData(type) {
//   return request({
//     url: `/common/commDict/type/${type}`,
//     method: "get",
//   });
// }