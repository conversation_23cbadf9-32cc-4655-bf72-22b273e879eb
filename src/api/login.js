import request from '@/router/axios'
import { getStore, setStore } from "@/util/store.js";
import website from "@/const/website.js";


export const loginByUsername = (login) => {

  let basicAuth = 'Basic ' + window.btoa(website.formLoginClient)
  // 保存当前选中的 basic 认证信息
  setStore({
    name: 'basicAuth',
    content: basicAuth
  })
  return request({
    url: '/admin/auth/loginWithCode',// 2024年6月17日 登录接口替换 admin/auth/login
    headers: {
      Authorization: basicAuth
    },
    method: 'post',
    data: login
  })
}

export const loginByMobile = (phone, code) => {
  let basicAuth = 'Basic ' + window.btoa(website.formLoginClient)
  // 保存当前选中的 basic 认证信息
  setStore({
    name: 'basicAuth',
    content: basicAuth
  })
  return request({
    url: '/admin/auth/smsLogin',
    headers: {
      Authorization: basicAuth
    },
    method: 'post',
    data: { phone: phone, code: code }
  })
}


export const getUserInfo = () => {
  return request({
    url: '/profile/info',
    method: 'get'
  })
}

export const updatePassword = (params) => {
  return request({
    url: '/profile/update-password',
    method: 'post',
    data: params
  })
}
export const workMenu = () => {
  return request({
    url: '/profile/getWorkMenu',
    method: 'get',
  })
}
export const configTheme = (params) => {
  return request({
    url: '/profile/configTheme',
    method: 'post',
    data: params
  })
}

export const logout = (data) => {
  return request({
    url: '/admin/auth/logout',
    method: 'delete',
    data: data
  })
}

/**
 * 注册用户
 */
export const registerUser = (userInfo) => {
  return request({
    url: '/admin/register/user',
    method: 'post',
    data: userInfo
  })
}

/**
 * 注册用户
 */
export const loginByTicket = (params) => {
  return request({
    url: '/open/oauth2/ticket/token',
    method: 'post',
    data: params
  })
}
export const liLoginByUsername = (login) => {

  let basicAuth = 'Basic ' + window.btoa(website.formLoginClient)
  // 保存当前选中的 basic 认证信息
  setStore({
    name: 'basicAuth',
    content: basicAuth
  })
  return request({
    url: '/api/admin/auth/login',
    headers: {
      Authorization: basicAuth
    },
    method: 'post',
    data: login
  })
}

// 根据客户端获取票据
export const getTicket = (obj) => {
  return request({
    url: '/open/oauth2/ticket',
    method: 'post',
    data: obj
  })
}

export const getSmsCode = (params) => {
  return request({
    url: '/admin/auth/sendLoginSmsCode',
    method: 'post',
    data: params
  })
}
