/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-08-14 16:24:15
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-08-14 17:13:26
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/business-cms-source/page",
    method: "get",
    params: query,
  });
}

export function addObj(obj) {
  return request({
    url: "/business-cms-source/create",
    method: "post",
    data: obj,
  });
}

export function delObj(sourceId) {
  return request({
    url: "/business-cms-source/" + sourceId,
    method: "delete",
  });
}
