/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-03-13 15:08:31
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-03-13 15:15:22
 */
import request from '@/router/axios'

export function getMessageList(obj) {
  return request({
    url: "/msg/record/queryUserMessage",
    method: "get",
    params: obj,
  });
}

export function getPersonCount() {
  return request({
    url: "/msg/record/getPersonCount",
    method: "get",
  });
}
