/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2022-01-26 17:33:23
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-08-14 16:07:26
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/admin/cms/article/page",
    method: "get",
    params: query,
    // headers: {
    //   apiFlag:true
    // }
  });
}

export function addObj(obj) {
  return request({
    url: "/common/cmsarticle",
    method: "post",
    data: obj,
    // picFlag: true,
  });
}

export function addInfo(obj) {
  return request({
    url: "/admin/cms/article/create",
    method: "post",
    data: obj,
    // picFlag: true,
  });
}

export function getObj(id) {
  return request({
    url: `/business-cms-article/cmsarticle/${id}`,
    method: "get",
  });
}

export function getArticleObj(id) {
  return request({
    url: `/business-cms-article/getDetail/${id}`,
    method: "get",
  });
}

export function delObj(id) {
  return request({
    url: `/business-cms-article/cmsrelease/${id}`,
    method: "delete",
  });
}

export function deleteObj(id) {
  return request({
    url: `/admin/cms/article/delete`,
    method: "post",
    data:{id:id}
  });
}

export function cancleObj(id) {
  return request({
    url: `/common/cmsrelease/enable/${id}`,
    method: "post",
  });
}
export function updateStatus(id) {
  return request({
    url: `/admin/cms/article/disable`,
    method: "post",
    data: { id: id },
    headers: {
      apiFlag:true
    }
  });
}
export function updateStatusQiyong(id) {
  return request({
    url: `/admin/cms/article/enable`,
    method: "post",
    data:{id:id},
    headers: {
      apiFlag:true
    }
  });
}


export function putObj(obj) {
  return request({
    url: "/business-cms-article/update",
    method: "put",
    data: obj,
    picFlag: true,
    // headers: {
    //   apiFlag:true
    // }
  });
}

export function editObj(obj) {
  return request({
    url: "/admin/cms/article/update",
    method: "put",
    data: obj,
    headers: {
      apiFlag:true
    }
  });
}


// 根据区域查询部门
export function getDeptByAreaId(params) {
  return request({
    url: "/admin/dept/listByAreaId",
    method: "get",
    params
    // headers: {
    //   apiFlag:true
    // }
  });
}
// 获取左侧树栏目分类
export function getPublishCategory(params) {
  return request({
    url: "/admin/cms/category/tree",
    method: "get",
    params
    // headers: {
    //   apiFlag:true
    // }
  });
}
// 获取首页分类数据
export function getPublishCategorys(params) {
  return request({
    url: "/portal/article/api/listByAreaCode",
    method: "get",
    params
    // headers: {
    //   apiFlag:true
    // }
  });
}
// 获取专题专栏数据
export function getZtzlList(params) {
  return request({
    url: "/portal/article/api/getArticleType4",
    method: "get",
    params
    // headers: {
    //   apiFlag:true
    // }
  });
}

export function getCmssource(obj) {
  return request({
    url: "/common/cmssource/page",
    method: "get",
    data: obj,
  });
}
export function getArticleDetail(id) {
  return request({

    
    url: `/admin/cms/article/detail/${id}`,
    method: "get",
    headers: {
      apiFlag:true
    }
  });
}
export function submitNewsObj(params) {
  return request({
    url: `/admin/cms/article/submit`,
    method: "post",
    data: params,
    headers: {
      apiFlag:true
    }
  });
}