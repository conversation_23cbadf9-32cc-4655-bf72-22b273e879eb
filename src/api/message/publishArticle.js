import request from "@/router/axios";
export function fetchList(query) {
  return request({
    url: "/person/personmngdeptinfo/pageTree",
    method: "get",
    params: query,
  });
}

export function addObj(obj) {
  return request({
    url: "/person/personmngdeptinfo/savePersonMngDeptInfo",
    method: "post",
    data: obj,
  });
}

export function getObj(id) {
  return request({
    url: "/person/personmngdeptinfo/" + id,
    method: "get",
  });
}

export function delObj(id) {
  return request({
    url: "/person/personmngdeptinfo/delById/" + id,
    method: "delete",
  });
}
// export function delGroupById(id) {
//     return request({
//         url: "/person/personmngdeptgroup/" + id,
//         method: "delete",
//     });
// }

// export function putObj(obj) {
//   return request({
//     url: "/person/personmngdeptinfo/updatePersonMngDeptInfo",
//     method: "put",
//     data: obj,
//   });
// }

export function getDeptOverview() {
  return request({
    url: "/person/personmngdeptinfo/deptOverview",
    method: "get",
  });
}

export function ImportDept(data) {
  return request({
    url: "/person/personmngdeptinfo/importDept",
    method: "post",
    data,
  });
}

export function getDeptListByHallId(hallId) {
  return request({
    url: `/person/personmngdeptinfo/getDeptListByHallId/${hallId}`,
    method: "get",
  });
}

// 获取省份区域信息
export function getOrgTreeData() {
  return request({
    url: `/common/commonprovinceorg/getOrgTree`,
    method: "get",
  });
}

// 获取省份下级区域信息
export function getDeptTreeData(id) {
  return request({
    url: `/person/personmngdeptpro/getProvinceDeptTree?orgCode=${id}`,
    method: "get",
  });
}
// export function addPersonGroup(data) {
//     return request({
//         url: "/person/personmngdeptgroup",
//         method: "post",
//         data,
//     });
// }
export function getPersonGroupTree(data) {
  return request({
    url: "/person/personmngdeptgroup/treeList",
    method: "get",
    data,
  });
}
// export function updatePersonGroup(data) {
//     return request({
//         url: "/person/personmngdeptgroup",
//         method: "put",
//         data,
//     });
// }

// 批量新增分组
// export function batchAddGroup(data) {
//   return request({
//     url: "/person/personmngdeptgroup/batchSave",
//     method: "POST",
//     data,
//   });
// }
// 获取批次的接口
export function getBatchDic() {
  return request({
    url: "/common/commDict/type/inter_batch",
    method: "get",
  });
}
// 获取部门树信息
export function getGroupDeptTreeInfo() {
  return request({
    url: "/person/personmngdeptinfo/getGroupDeptTreeList",
    method: "get",
  });
}
//批量新增文字分类
export function batchAddGroup(data) {
  return request({
    url: "/business-cms-article-category/saveBatch",
    method: "POST",
    data,
  });
}
export function addPersonGroup(data) {
  return request({
    url: "/admin/cms/category/create",
    method: "post",
    data,
  });
}
export function delGroupById(id) {
  return request({
    url: "/admin/cms/category/delete",
    method: "post",
    data:{id:id}
  });
}
export function updatePersonGroup(data) {
  return request({
    url: "/admin/cms/category/update",
    method: "put",
    data,
    headers: {
      apiFlag:true
    }
  });
}
