/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-03-23 15:05:14
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-05-24 15:18:05
 */
import request from '@/router/axios'

export function getMsgCount() {
  return request({
    url: '/msg/record/getPersonCount',
    method: "get",
  });
}

export function addObj(obj) {
  return request({
    url: "/admin/notice/create",
    method: "post",
    data: obj,
  });
}
export function allRead(obj) {
  return request({
    url: "/msg/record/updateRead",
    method: "post",
    data: obj,
  });
}

export function fetchList(obj) {
  return request({
    url: "/msg/record/queryUserMessage",
    method: "get",
    params: obj,
  });
}

export function fetchDaiBanList(obj) {
  return request({
    url: "/msg/record/queryUserMessage",
    method: "get",
    params: obj,
  });
}

export function getObj(id) {
  return request({
    url: `/admin/notice/${id}`,
    method: "get",
  });
}

// export function change(params) {
//   return request({
//     url: "/common/preferencepagenoticeconfig/change",
//     method: "post",
//     data: params,
//   });
// }

export function delObj(id) {
  return request({
    url: `/admin/notice/${id}`,
    method: "delete",
  });
}

export function putObj(obj) {
  return request({
    url: "/admin/notice/update",
    method: "put",
    data: obj,
  });
}
export function updateStatus(obj) {
  return request({
    url: "/admin/notice/change-status",
    method: "put",
    data: obj,
  });
}
