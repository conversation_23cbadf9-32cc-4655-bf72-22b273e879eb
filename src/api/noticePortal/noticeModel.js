/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-03-23 15:05:14
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-03-23 15:18:10
 */
import request from "@/router/axios";

export function addObj(obj) {
  return request({
    url: "/msg/template/addTemplate",
    method: "post",
    data: obj,
  });
}

export function putObj(obj) {
  return request({
    url: "/msg/template/saveTemplate",
    method: "post",
    data: obj,
  });
}

export function fetchList(obj) {
  return request({
    url: "/msg/template/pageTemplate",
    method: "get",
    params: obj,
  });
}

// export function getObj(id) {
//   return request({
//     url: `/admin/notice/${id}`,
//     method: "get",
//   });
// }

// export function change(params) {
//   return request({
//     url: "/common/preferencepagenoticeconfig/change",
//     method: "post",
//     data: params,
//   });
// }

export function delObj(params) {
  return request({
    url: `/msg/template/delTemplate`,
    method: "delete",
    params
  });
}

// export function putObj(obj) {
//   return request({
//     url: "/admin/notice/update",
//     method: "put",
//     data: obj,
//   });
// }
// export function updateStatus(obj) {
//   return request({
//     url: "/admin/notice/change-status",
//     method: "put",
//     data: obj,
//   });
// }
