import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/open/oauth2-client/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/open/oauth2-client/create',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/open/oauth2-client/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/open/oauth2-client/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/open/oauth2-client/update',
    method: 'put',
    data: obj
  })
}
