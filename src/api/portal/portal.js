/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-03-13 15:08:31
 * @LastEditors: liuzhengshuai <EMAIL>
 * @LastEditTime: 2024-11-12 10:41:47
 */
import request from "@/router/axios";

// export function getAppList(obj) {
//   return request({
//     url: "/api/admin/role/portal-app",
//     method: "get",
//     data: obj,
//   });
// }
//门户首页常用app列表
export function getAppList(obj) {
  return request({
    // url: "/portal/home/<USER>",
    url: "/portal/home/<USER>/usual-app",
    method: "get",
    // params: obj,
  });
}
//查询所有分组以及分组下的应用-树形
export function getClientGroupTree(obj) {
  return request({
    // url: "/portal/home/<USER>",
    url: "/portal/home/<USER>/portal-group-app",
    method: "get",
    params: obj,
  });
}
//自定义个人首页
export function addAppToGroup(obj) {
  return request({
    url: "/portal/home/<USER>",
    method: "post",
    data: obj,
  });
}
export function saveLayoutData(obj) {
  return request({
    url: "/profile/configLayout",
    method: "post",
    data: obj,
  });
}


//获取主体数据
export function getZhutiData() {
  return request({
    // url: "/portal/third/risk/market_develop",
    url: "/third/risk/market_develop",
    method: "get",

  });
}
export function getCompanyFengxianData(params) {
  return request({
    url: "/third/risk/queryRiskClassification",
    method: "post",
    data:params
  });
}
export function getShipinShebeiDengData(params) {
  return request({
    ///portal
    url: "/third/mark/statistics",
    method: "get",
    params:params
  });
}
