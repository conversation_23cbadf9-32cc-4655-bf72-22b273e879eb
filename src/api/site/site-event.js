/*
 * @Author: gyc
 * @Date: 2021-10-Th 05:09:59
 * @Last Modified by:   gyc
 * @Last Modified time: 2021-10-Th 05:09:59
 */

import request from "@/router/axios";

export function listSpecialItemInfo() {
  return request({
    url: "/pre/preHomePage/listSpecialItemInfo",
    method: "get"
  });
}

export function mattersOverView() {
  return request({
    url: "/pre/preHomePage/mattersOverView",
    method: "get"
  });
}

export function listInMatterInfo() {
  return request({
    url: "/pre/preHomePage/listInMatterInfo",
    method: "get"
  });
}

export function listZoneMatterInfo() {
  return request({
    url: "/pre/preHomePage/listZoneMatterInfo",
    method: "get"
  });
}
export function getEventType() {
  return request({
    url: "/pre/predashboard/getPreTypeList",
    method: "get"
  });
}