/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2021-10-22 11:26:28
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2021-12-29 19:30:07
 */
import request from "@/router/axios";

export function getSiteOverview() {
  return request({
    url: "/site/site/getSiteOverview",
    method: "get",
  });
}
export function getSiteChartData() {
  return request({
    url: "/site/site/getSiteChartData",
    method: "get",
  });
}
// 获取专区数量
export function getPrefectureWindow() {
  return request({
    url: "/site/site/getPrefectureWindow",
    method: "get",
  });
}
// 特色窗口
export function getSpecialWindow() {
  return request({
    url: "/site/site/getSpecialWindow",
    method: "get",
  });
}
// 窗口状态
export function getWindowStatus() {
  return request({
    url: "/appsData/appswindowstatus/getWindowNumByStatus",
    method: "get",
  });
}
// 窗口使用情况
export function getWindowUsedRecord() {
  return request({
    url: "/site/site/getWindowUsedRecord",
    method: "get",
  });
}

// 获取楼层
export function getFloorData() {
  return request({
    url: "/site/site/getHallFloor",
    method: "get",
  });
}
export function getCicularMap() {
  return request({
    url: "/site/site/getCicularMapOfOffice",
    method: "get",
  });
}