/*
 * @Description:
 * @Author: yuzhenming
 * @Date: 2021-10-19 16:02:35
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-02-17 17:22:18
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/site/sitefloorinfo/page",
    method: "get",
    params: query,
  });
}

export function addObj(obj) {
  return request({
    url: "/site/sitefloorinfo",
    method: "post",
    data: obj,
    picFlag: true,
  });
}
export function getRegionOverview() {
  return request({
    url: "/site/siteregioninfo/regionOverview",
    method: "get",
  });
}

export function getObj(id) {
  return request({
    url: "/site/sitefloorinfo/" + id,
    method: "get",
  });
}

export function delObj(floorId) {
  return request({
    url: "/site/sitefloorinfo/delByFloorId/" + floorId,
    method: "delete",
  });
}

export function putObj(obj) {
  return request({
    url: "/site/sitefloorinfo",
    method: "put",
    data: obj,
    picFlag: true,
  });
}
// 模板下载
export function downLoadFloorTemplete(obj) {
  return request({
    url: "/site/sitefloorinfo/excelExport",
    method: "get",
    params: obj,
    responseType: "blob",
  });
}

// 批量导入
export function importPatchTemplete(params) {
  return request({
    url: "/site/sitefloorinfo/excelImport",
    method: "post",
    params,
    // responseType: "blob"
  });
}
export function getHallId() {
  return request({
    url: "/site/sitehallinfo/hallList",
    method: "get",
  });
}
export function getFloor(hallId) {
  return request({
    url: "/site/sitehallinfo/floorNumList/" + hallId,
    method: "get",
  });
}
