/*
 * @Description:
 * @Author: yqz
 * @Date: 2021-10-16 10:17:08
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-02-17 18:13:05
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/site/sitehallinfo/page",
    method: "get",
    params: query,
  });
}

export function addObj(obj) {
  return request({
    url: "/site/sitehallinfo",
    method: "post",
    data: obj,
    picFlag: true,
  });
}

export function uploadImg(obj) {
  return request({
    url: "/portal-api/storage/upload",
    method: "post",
    data: obj,
  });
}

export function getObj(id) {
  return request({
    url: "/site/sitehallinfo/" + id,
    method: "get",
  });
}

export function gethallOverview() {
  return request({
    url: "/site/sitehallinfo/hallOverview",
    method: "get",
  });
}

export function delObj(hallId) {
  return request({
    url: "/site/sitehallinfo/delByBsId/" + hallId,
    method: "delete",
  });
}

export function putObj(obj) {
  return request({
    url: "/site/sitehallinfo",
    method: "put",
    data: obj,
    picFlag: true,
  });
}
