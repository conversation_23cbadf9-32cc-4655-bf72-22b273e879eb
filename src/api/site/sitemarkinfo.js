/*
 * @Description:
 * @Author: zhang<PERSON>iyang
 * @Date: 2021-10-23 11:44:07
 * @LastEditors: zhangruiyang
 * @LastEditTime: 2021-11-23 18:39:47
 */
import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/site/sitemarkinfo/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/site/sitemarkinfo',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/site/sitemarkinfo/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/site/sitemarkinfo/delByStationId/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/site/sitemarkinfo',
    method: 'put',
    data: obj
  })
}

export function getMarkOverview() {
  return request({
    url: "/site/sitemarkinfo/markOverview",
    method:"get"
  })
}
