import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/site/sitemodelinfo/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/site/sitemodelinfo',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/site/sitemodelinfo/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/site/sitemodelinfo/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/site/sitemodelinfo',
    method: 'put',
    data: obj
  })
}
