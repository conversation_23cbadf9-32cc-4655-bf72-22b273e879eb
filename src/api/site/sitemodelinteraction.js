import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/site/sitemodelinteraction/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/site/sitemodelinteraction',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/site/sitemodelinteraction/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/site/sitemodelinteraction/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/site/sitemodelinteraction',
    method: 'put',
    data: obj
  })
}
