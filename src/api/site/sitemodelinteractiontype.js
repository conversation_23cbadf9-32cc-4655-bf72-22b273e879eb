import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/site/sitemodelinteractiontype/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/site/sitemodelinteractiontype',
    method: 'post',
    data: obj
  })
}

export function getObj(id) {
  return request({
    url: '/site/sitemodelinteractiontype/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return request({
    url: '/site/sitemodelinteractiontype/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/site/sitemodelinteractiontype',
    method: 'put',
    data: obj
  })
}
