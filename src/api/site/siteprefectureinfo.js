/*
 * @Description:
 * @Author: yuzhen<PERSON>
 * @Date: 2021-10-19 16:02:35
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-02-17 18:14:40
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/site/siteprefectureinfo/page",
    method: "get",
    params: query,
  });
}

export function addObj(obj) {
  return request({
    url: "/site/siteprefectureinfo",
    method: "post",
    data: obj,
    picFlag: true,
  });
}

export function getRegionOverview() {
  return request({
    url: "/site/siteregioninfo/regionOverview",
    method: "get",
  });
}

export function getListStaff() {
  return request({
    url: "/person/personmngstaffinfo/getStaffInfo",
    method: "get",
  });
}

export function getTellCell(staffId) {
  return request({
    url: "/person/personmngstaffinfo/listTellCellInfo/" + staffId,
    method: "get",
  });
}

export function getObj(id) {
  return request({
    url: "/site/siteprefectureinfo/" + id,
    method: "get",
  });
}

export function delObj(prefectureId) {
  return request({
    url: "/site/siteprefectureinfo/delByRegionId/" + prefectureId,
    method: "delete",
  });
}

export function putObj(obj) {
  return request({
    url: "/site/siteprefectureinfo",
    method: "put",
    data: obj,
    picFlag: true,
  });
}

export function getPrefectureDept() {
  return request({
    url: "/person/personmngdeptinfo/listDeptNameInfo",
    method: "get",
  });
}

//根据专区id查部门
export function handleGetDeptByCode(id) {
  return request({
    url: "/site/siteprefectureinfo/getDeptListByPrefectureId/" + id,
    method: "get",
  });
}

export function getPrefecture() {
  return request({
    url: "/common/commDict/type/prefecture_inner_code",
    method: "get",
  });
}

export function getPrefectureName() {
  return request({
    url: "/site/siteprefectureinfo/prefectureNames",
    method: "get",
  });
}
