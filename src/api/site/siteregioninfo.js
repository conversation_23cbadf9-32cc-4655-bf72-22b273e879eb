/*
 * @Description:分区api
 * @Author: lc
 * @Date: 2021-10-22 17:54:23
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-02-17 18:13:40
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/site/siteregioninfo/page",
    method: "get",
    params: query,
  });
}

export function addObj(obj) {
  return request({
    url: "/site/siteregioninfo",
    method: "post",
    data: obj,
    picFlag: true,
  });
}
export function getRegionOverview() {
  return request({
    url: "/site/siteregioninfo/regionOverview",
    method: "get",
  });
}

export function getObj(id) {
  return request({
    url: "/site/siteregioninfo/" + id,
    method: "get",
  });
}

// export function delObj(id) {
//   return request({
//     url: "/site/siteregioninfo/" + id,
//     method: "delete"
//   });
// }
export function delObj(regionId) {
  return request({
    url: "/site/siteregioninfo/delByRegionId/" + regionId,
    method: "delete",
  });
}
export function putObj(obj) {
  return request({
    url: "/site/siteregioninfo",
    method: "put",
    data: obj,
    picFlag: true,
  });
}

export function getRegion() {
  return request({
    url: "/common/commDict/type/region_inner_code",
    method: "get",
  });
}
