/*
 * @Description:
 * @Author: zhang<PERSON>iyang
 * @Date: 2021-10-19 16:21:46
 * @LastEditors: zhangruiyang
 * @LastEditTime: 2021-11-23 18:35:44
 */
import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/site/sitestationinfo/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/site/sitestationinfo',
    method: 'post',
    data: obj,
  })
}

export function getObj(id) {
  return request({
    url: '/site/sitestationinfo/' + id,
    method: 'get'
  })
}

export function delObj(stationId) {
  return request({
    url: '/site/sitestationinfo/delByStationId/' + stationId,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/site/sitestationinfo',
    method: 'put',
    data: obj
  })
}

export function getStationOverview() {
  return request({
    url: "/site/sitestationinfo/stationOverview",
    method:"get"
  })
}
