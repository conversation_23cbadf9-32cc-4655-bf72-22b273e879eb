/*
 * @Description:
 * @Author: yqz
 * @Date: 2021-10-16 10:17:08
 * @LastEditors: yqz
 * @LastEditTime: 2021-10-25 14:10:16
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/site/sitesubhallinfo/page",
    method: "get",
    params: query
  });
}

export function addObj(obj) {
  return request({
    url: "/site/sitesubhallinfo",
    method: "post",
    data: obj
  });
}

export function getObj(id) {
  return request({
    url: "/site/sitesubhallinfo/" + id,
    method: "get"
  });
}

export function delObj(id) {
  return request({
    url: "/site/sitesubhallinfo/delByBsId/" + id,
    method: "delete"
  });
}

export function putObj(obj) {
  return request({
    url: "/site/sitesubhallinfo",
    method: "put",
    data: obj
  });
}
