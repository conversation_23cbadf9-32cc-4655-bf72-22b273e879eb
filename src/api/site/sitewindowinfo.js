/*
 * @Description:
 * @Author: yuzhenming
 * @Date: 2021-10-19 16:02:35
 * @LastEditors: zhangruiyang
 * @LastEditTime: 2022-01-26 15:36:42
 */
import request from "@/router/axios";

export function fetchList(query) {
  return request({
    url: "/site/sitewindowinfo/page",
    method: "get",
    params: query
  });
}

export function addObj(obj) {
  return request({
    url: "/site/sitewindowinfo",
    method: "post",
    data: obj
  });
}

export function getObj(id) {
  return request({
    url: "/site/sitewindowinfo/" + id,
    method: "get"
  });
}

export function delObj(windowNo) {
  return request({
    url: "/site/sitewindowinfo/" + windowNo,
    method: "delete"
  });
}

export function putObj(obj) {
  return request({
    url: "/site/sitewindowinfo",
    method: "put",
    data: obj
  });
}

export function getPrefectureData() {
  return request({
    url: "/site/siteprefectureinfo/listPrefecture",
    method: "get"
  });
}

export function getDeptData() {
  return request({
    url: "/person/personmngdeptinfo/listDeptNameInfo",
    method: "get"
  });
}

export function windowOverview() {
  return request({
    url: "/site/sitewindowinfo/windowOverview",
    method:"get"
  })
}

//查询所有区域
export function getAllRegion(query) {
  return request({
    url: "/site/sitestationinfo/getRegionList",
    method: "get",
    params:query,
  })
}

//根据楼层id、区域id、工位类型分页查询工位
export function getSiteStation(query) {
  return request({
    url: "/site/sitestationinfo/page",
    method: "get",
    params:query
  })
}

//关联工位
export function setSitestation(obj) {
  return request({
    url: "/site/sitestationinfo",
    method: "put",
    data:obj
  })
}
