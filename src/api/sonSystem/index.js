

import request from '@/router/axios'

export function fetchList (query) {
  return request({
    url: '/sub-subsys/page',
    method: 'get',
    params: query
  })
}

// export function deptRoleList () {
//   return request({
//     url: '/admin/role/list',
//     method: 'get'
//   })
// }

export function getObj (id) {
  return request({
    url: '/sub-subsys/' + id,
    method: 'get'
  })
}

export function addObj (obj) {
  return request({
    url: '/sub-subsys/create',
    method: 'post',
    data: obj
  })
}

export function putObj (obj) {
  return request({
    url: '/sub-subsys/update',
    method: 'put',
    data: obj
  })
}

export function delObj (id) {
  return request({
    url: '/sub-subsys/' + id,
    method: 'delete'
  })
}

export function getAllSonSystemList () {
  return request({
    url: '/sub-subsys/all',
    method: 'get',
    // params: query
  })
}