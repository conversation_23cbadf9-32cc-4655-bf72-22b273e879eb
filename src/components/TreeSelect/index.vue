<template>
  <el-select
    :title="multiple ? optionData.name : ''"
    ref="select"
    :value="value"
    :placeholder="placeholder"
    size="mini"
    clearable
    :disabled="disabled"
    :filterable="filterable"
    :filter-method="filterMethod"
    style="width: 100%"
    @clear="clear"
    @visible-change="visibleChange"
  >
    <!-- <el-input
      class="search-content"
      v-model="keyWord"
      placeholder="请输入内容"
    ></el-input> -->
    <el-option
      ref="option"
      class="tree-select__option"
      :value="optionData.id"
      :label="optionData.name"
    >
      <el-tree
        ref="tree"
        class="tree-select__tree"
        :class="`tree-select__tree--${multiple ? 'checked' : 'radio'}`"
        :node-key="nodeKey"
        :data="data"
        :props="props"
        :default-expanded-keys="[value]"
        :show-checkbox="multiple"
        :highlight-current="!multiple"
        :expand-on-click-node="multiple"
        :filter-node-method="filterNode"
        :lazy="lazy"
        :load="loadNode"
        @node-click="handleNodeClick"
        @check-change="handleCheckChange"
      ></el-tree>
    </el-option>

    <div class="block" v-if="pageFlag && searchFlag">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="selfSearchPage.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="selfSearchPage.size"
        layout="pager, jumper"
        :total="selfSearchPage.tatal"
        :pager-count="5"
      >
      </el-pagination>
    </div>
  </el-select>
</template>
  
  <script>
export default {
  name: "TreeSelect",
  props: {
    loadNode: Function,
    // v-model绑定
    value: {
      type: [String, Number],
      default: "",
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    pageFlag: {
      type: Boolean,
      default: false,
    },
    // 树形的数据
    data: {
      type: Array,
      default: function () {
        return [];
      },
    },
    // 每个树节点用来作为唯一标识的属性
    nodeKey: {
      type: [String, Number],
      default: "id",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    slefFilter: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // tree的props配置
    props: {
      type: Object,
      default: function () {
        return {
          label: "label",
          children: "children",
        };
      },
    },
    lazy: {
      type: Boolean,
      default: false,
    },
    selfSearchPage: {
      current: 1,
      size: 20,
      total: 0,
    },
    showTitle: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      optionData: {
        id: "",
        name: "",
      },
      filterFlag: false,
      keyWord: "",
      pageObj: {
        currentPage: 1,
        pageSize: 10,
        tatal: 0,
      },
      searchFlag: false,
      keyWord: "",
    };
  },
  watch: {
    value: {
      handler(val) {
        // console.log("val---", val);

        if (!this.isEmpty(this.data)) {
          this.init(val);
        }
      },
      immediate: true,
    },
    data: function (val) {
      // console.log("val---测试7---", val);
      if (!this.isEmpty(val)) {
        this.init(this.value);
      }
    },
    // data: {
    //   deep: true, //
    //   handler: function (val, oldVal) {
    //     console.log("val---测试8---", val);
    //     if (!this.isEmpty(val)) {
    //       this.init(this.value);
    //     }
    //   },
    // },
  },
  created() {},
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.$emit("selfCurrentChange", val, this.keyWord);
    },
    // 是否为空
    isEmpty(val) {
      for (let key in val) {
        return false;
      }
      return true;
    },
    handleNodeClick(data) {
      if (this.multiple) {
        return;
      }

      this.$emit("input", data[this.nodeKey]);
      this.$emit("selfInput", data[this.nodeKey]);
      // this.$emit("selfInputObj", data);
      this.$refs.select.visible = false;
    },
    handleCheckChange() {
      const nodes = this.$refs.tree.getCheckedNodes();
      const value = nodes.map((item) => item[this.nodeKey]).join(",");
      // console.log("测试1==", value);
      this.$emit("input", value);
      this.$emit("selfInput", value);
    },
    reloadVal() {
      setTimeout(() => {
        let nodes2 = this.$refs.tree.getCheckedNodes();
        const value2 = nodes2.map((item) => item[this.nodeKey]).join(",");
        // console.log("测试3==", value2);
        this.$emit("input", value2);
        this.$emit("selfInput", value2);
      }, 100);
    },
    init(val) {
      // 多选
      if (this.multiple) {
        const arr = val.toString().split(",");
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys(arr);
          const nodes = this.$refs.tree.getCheckedNodes();
          // console.log("测试2==", nodes);
          this.optionData.id = val;
          this.optionData.name = nodes
            .map((item) => item[this.props.label])
            .join(",");
          if (val && !nodes.length) {
            this.optionData.name = this.showTitle;
          }
        });
      }
      // 单选
      else {
        val = val === "" ? null : val;
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(val);
          if (val === null) {
            return;
          }
          const node = this.$refs.tree.getNode(val);
          this.optionData.id = val;
          this.optionData.name = node.label;
        });
      }
    },
    visibleChange(e) {
      if (e) {
        const tree = this.$refs.tree;
        this.filterFlag && tree.filter("");
        this.filterFlag = false;
        let selectDom = null;
        if (this.multiple) {
          selectDom = tree.$el.querySelector(".el-tree-node.is-checked");
        } else {
          selectDom = tree.$el.querySelector(".is-current");
        }
        setTimeout(() => {
          this.$refs.select.scrollToOption({ $el: selectDom });
        }, 0);
      }
    },
    clear() {
      this.$emit("input", "");
      this.$emit("selfInput", "");
    },
    filterMethod(val) {
      //   console.log("val==", val);
      this.keyWord = val;
      if (val) {
        this.searchFlag = true;
      } else {
        this.searchFlag = false;
      }
      // console.log("val-this.searchFlag--", this.searchFlag);
      // console.log("val-this.pageFlag--", this.pageFlag);
      if (this.slefFilter) {
        // console.log("val=22=", val);
        this.$emit("selfSearch", val);
        return;
      }
      this.filterFlag = true;
      // this.$refs.tree.filter(val);
    },
    filterNode(value, data) {
      if (!value) return true;
      const label = this.props.label || "name";
      return data[label].indexOf(value) !== -1;
    },
  },
};
</script>
  
  <style lang="scss">
.search-content {
  height: 42px;
  z-index: 3;
}
.tree-select__option {
  position: relative;

  &.el-select-dropdown__item {
    height: auto;
    line-height: 1;
    padding: 0;
    background-color: #fff;
  }
}

.tree-select__tree {
  padding: 4px 20px;
  font-weight: 400;
  &.tree-select__tree--radio {
    .el-tree-node.is-current > .el-tree-node__content {
      //   color: $mainColor;
      font-weight: 700;
    }
  }
}
</style>