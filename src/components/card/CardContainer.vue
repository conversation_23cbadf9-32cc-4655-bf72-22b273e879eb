<template>
  <div class="card-container">
    <template v-for="(item, i) in cardArr">
      <template :slot="item.cardName">
        <div
          class="card-base"
          :class="[item.cardClass ? item.cardClass : item.cardName]"
          :key="i"
          @click="handleGoUrl(item.url, item.name)"
        >
          <p class="card-title">{{ item.cardTitle }}</p>
          <p class="card-number" v-if="urlFlag">{{ item.cardNumber }}</p>
          <p
            v-if="item.url || item.name"
            class="card-number url-link"
            @click="handleGoUrl(item.url, item.name)"
          >
            {{ item.cardNumber }}
          </p>
          <div
            class="icon-base"
            :class="item.cardIcon"
            @click="handleGoUrl(item.url, item.name)"
          ></div>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
export default {
  name: "CardContainer",
  components: {},
  data() {
    return {
      searchFlag: 1,
    };
  },
  props: {
    cardArr: {
      type: Array,
      default: () => {
        return [];
      },
    },

    urlFlag: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    urlJump: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
  },
  methods: {
    handleGoUrl(url, name) {
      if (!this.urlJump) {
        return;
      }
      if (name) {
        this.$router.push({
          name: name,
          params: { searchFlag: this.searchFlag },
        });
      }
      this.$router.push(url);
    },
  },
};
</script>

<style scoped lang="scss">
.card-one {
  // background: url("../../assets/img/console/card-blue.png") no-repeat;
  background-size: 100% 100%;
}
.card-two {
  // background: url("../../assets/img/console/card-blue-green.png")  no-repeat;

  background-size: 100% 100%;
}
.card-three {
  // background: url("../../assets/img/console/card-yellow.png") no-repeat;
  background-size: 100% 100%;
}
.card-four {
  // background: url("../../assets/img/console/card-purple.png") no-repeat;
  background-size: 100% 100%;
}
.card-red {
  // background: url("../../assets/img/console/card-red.png") no-repeat;
  background-size: 100% 100%;
}
.card-green {
  // background: url("../../assets/img/console/card-green.png") no-repeat;
  background-size: 100% 100%;
}
.card-base {
  height: 120px;
  width: 100%;
  border-radius: 8px;
  border: 1px solid transparent;
  padding-left: 16px;
  position: relative;
  .icon-base {
    position: absolute;
    content: "";
    bottom: 0;
    right: 0;
    width: 66px;
    height: 60px;
  }
  .one-icon {
    // https://gitlab.zsnetwork.com/unified-portal/user-center-front/-/blob/dev/src/assets/img/console/card-icon-agency.png
    background: url("../../assets/img/console/card-icon-agency.png") no-repeat;
    background-size: 100%;
  }
  .two-icon {
    background: url("../../assets/img/console/card-icon-today.png") no-repeat;
    background-size: 100%;
  }
  .three-icon {
    background: url("../../assets/img/console/card-icon-email.png") no-repeat;
    background-size: 100%;
  }
  .four-icon {
    background: url("../../assets/img/console/card-icon-check.png") no-repeat;
    background-size: 100%;
  }
  .warn-icon {
    background: url("../../assets/img/console/card-icon-warn.png") no-repeat;
    background-size: 100%;
  }
  .info-icon {
    background: url("../../assets/img/console/card-icon-info.png") no-repeat;
    background-size: 100%;
  }
  .status-icon {
    background: url("../../assets/img/console/card-icon-status.png") no-repeat;
    background-size: 100%;
  }
  .bad-icon {
    background: url("../../assets/img/console/card-icon-bad.png") no-repeat;
    background-size: 100%;
  }
  .score-icon {
    background: url("../../assets/img/console/card-icon-score.png") no-repeat;
    background-size: 100%;
  }
  .boxRight-icon {
    background: url("../../assets/img/console/card-icon-boxRight.png") no-repeat;
    background-size: 100%;
  }
  .smileStar-icon {
    background: url("../../assets/img/console/card-icon-smileStar.png")
      no-repeat;
    background-size: 100%;
  }
  .voice-icon {
    background: url("../../assets/img/console/card-icon-voice.png") no-repeat;
    background-size: 100%;
  }
  .blockstar-icon {
    background: url("../../assets/img/console/card-icon-blockstar.png")
      no-repeat;
    background-size: 100%;
  }
  .listener-icon {
    background: url("../../assets/img/console/card-icon-listener.png") no-repeat;
    background-size: 100%;
  }
  .whitehouse-icon {
    background: url("../../assets/img/console/card-icon-whitehouse.png")
      no-repeat;
    background-size: 100%;
  }
  .people-icon1 {
    background: url("../../assets/img/console/card-icon-people1.png") no-repeat;
    background-size: 100%;
  }
  .people-icon2 {
    background: url("../../assets/img/console/card-icon-people2.png") no-repeat;
    background-size: 100%;
  }
  .people-icon3 {
    background: url("../../assets/img/console/card-icon-leave.png") no-repeat;
    background-size: 100%;
  }
  .people-icon4 {
    background: url("../../assets/img/console/card-icon-late.png") no-repeat;
    background-size: 100%;
  }
  // .site-icon1 {
  //   background: url("../../assets/image/site/site-icon1.png") no-repeat;
  //   background-size: 100%;
  // }
  // .site-icon2 {
  //   background: url("../../assets/image/site/site-icon2.png") no-repeat;
  //   background-size: 100%;
  // }
  // .site-icon3 {
  //   background: url("../../assets/image/site/site-icon3.png") no-repeat;
  //   background-size: 100%;
  // }
  // .site-icon4 {
  //   background: url("../../assets/image/site/site-icon4.png") no-repeat;
  //   background-size: 100%;
  // }
  // .icon-big-house {
  //   background: url("../../assets/img/console/icon-big-house.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-permission {
  //   background: url("../../assets/img/console/icon-permission.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-two-pice {
  //   background: url("../../assets/img/console/icon-two-pice.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-window-number {
  //   background: url("../../assets/img/console/icon-window-number.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-select-book {
  //   background: url("../../assets/img/console/card-select-book.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-select-good {
  //   background: url("../../assets/img/console/card-select-good.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-select-today-doog {
  //   background: url("../../assets/img/console/card-select-today-doog.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-select-today {
  //   background: url("../../assets/img/console/card-select-today.png")
  //     no-repeat;
  //   background-size: 100%;
  // }
  // .icon-wenhao {
  //   background: url("../../assets/img/console/icon-wenhao.png") no-repeat;
  //   background-size: 100%;
  // }
  // .icon-quhao {
  //   background: url("../../assets/img/console/icon-quhao.png") no-repeat;
  //   background-size: 100%;
  // }
  // .icon-tousu {
  //   background: url("../../assets/img/console/icon-tousu.png") no-repeat;
  //   background-size: 100%;
  // }
  // .icon-xinfeng {
  //   background: url("../../assets/img/console/icon-xinfeng.png") no-repeat;
  //   background-size: 100%;
  // }
}
.card-title {
  font-size: 16px;
  font-weight: 700;
  margin-top: 15.8px;
  color: #fff;
}
.card-number {
  font-size: 18px;
  font-weight: 700;
  color: #fff;
  margin-top: 20px;
}
.url-link {
  cursor: pointer;
}
</style>
