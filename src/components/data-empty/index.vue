<!--
 * @Description: 无数据组件
 * @Author: lc
 * @Date: 2021-11-01 10:56:27
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-02-14 18:06:58
-->
<template>
  <div class="data-empty">
    <div class="module-title" :style="{ fontSize: titleSize }">{{ title }}</div>
    <avue-empty
      size="120"
      image="data:image/png;base64,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"
      :desc="desc"
    ></avue-empty>
  </div>
</template>
<script>
export default {
  name: "DataEmpty",
  props: {
    //自定义描述内容
    desc: {
      type: String,
      default: "暂无数据",
    },
    title: {
      type: String,
      default: "",
    },
    titleSize: {
      type: String,
      default: "14px",
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.data-empty {
  .module-title {
    color: #22242c;
    font-size: 18px;
    font-weight: 700;
    margin: 25px;
  }
}
</style>
