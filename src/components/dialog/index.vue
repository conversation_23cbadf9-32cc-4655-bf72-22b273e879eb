<!--
 * @Description: 自定义弹窗
 * @Author: lc
 * @Date: 2021-11-10 17:08:34
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-03-08 21:01:14
-->
<template>
  <el-dialog
    class="ss"
    :visible.sync="dialogShow"
    :v-dialogdrag="dialogdrag"
    v-bind="$attrs"
    v-on="$listeners"
    :modal-append-to-body="false"
    append-to-body
    :top="selfTop"
    :close-on-click-modal="false"
    :style="styleName"
    :class="[
      'el-dialog__wrapper avue-dialog avue-crud__dialog',
      { 'avue-dialog--fullscreen': fullscreen },
    ]"
    @closed="handleDialogClosed"
  >
    <div slot="title" class="avue-crud__dialog__header">
      <div v-if="isDefault === true" class="header-space">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu paddingRight">
          <i
            @click="handleFullScreen"
            :class="fullscreen ? 'el-icon-news' : 'el-icon-full-screen'"
            class="el-dialog__close"
          ></i>
          <!-- -->
          <i
            v-if="showClose"
            @click="dialogShow = false"
            class="el-dialog__close el-icon el-icon-close"
          ></i>
        </div>
      </div>
      <slot name="top-header" v-else class="el-dialog__title"> </slot>
      <!-- <div slot="button"></div> -->
    </div>

    <slot />
    <span slot="footer" class="dialog-footer">
      <slot name="bottom-footer"> </slot>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "CustDialog",
  inheritAttrs: false,
  props: {
    isDefault: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: "",
    },
    //是否开启拖拽
    dialogdrag: {
      type: Boolean,
      default: false,
    },
    //顶部距离
    dialogTop: {
      type: String,
      default: "15%",
    },
    selfTop: {
      type: String,
      default: "15%",
    },
    title: {
      type: String,
      default: "",
    },
    // eslint-disable-next-line vue/require-default-prop
    dialogConfirm: {
      type: Function,
    },
    showClose: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogShow: false,
      fullscreen: false,
    };
  },
  computed: {
    styleName() {
      if (!this.fullscreen) {
        return { top: this.dialogTop };
      } else {
        return { top: 0 };
      }
    },
  },
  mounted() {
    this.dialogShow = true;
    //多了一个关闭的按钮，用样式隐藏
    // this.initCloseBtn();
  },
  methods: {
    // initCloseBtn() {
    //   setTimeout(() => {
    //     let dom = document.querySelector(".el-dialog__headerbtn");
    //     if (dom) {
    //       dom.style.display = "none";
    //     }
    //     console.log("dom==", dom);
    //     this.$forceUpdate();
    //   }, 100);
    // },
    handleDialogClosed() {
      this.$emit("closed");
      this.$emit("update:dialogStatus", false);
    },
    handleDialogConfirm() {
      new Promise((resolve, reject) => {
        if (this.dialogConfirm) {
          this.dialogConfirm(resolve, reject);
        } else {
          resolve();
        }
      })
        .then((res) => {
          this.dialogShow = false;
        })
    },
    handleFullScreen() {
      if (this.fullscreen) {
        this.fullscreen = false;
      } else {
        this.fullscreen = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.header-space {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.paddingRight {
  padding-right: 20px;
}
::deep .el-dialog__header {
  min-height: 40px;
  .el-dialog__headerbtn {
    display: none;
  }
}
::v-deep .el-dialog__header {
  // padding: 0px !important;
  min-height: 40px;
  padding-top: 20px;
}
</style>

