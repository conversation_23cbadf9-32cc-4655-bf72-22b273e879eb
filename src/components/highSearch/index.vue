<template>
  <div style="margin-top: 60px">
    <div class="high-search">
      <div class="search">
        <el-input
          placeholder="请输入新闻/公告关键词"
          v-model="form.keyword"
          size="medium"
          clearable
          @focus="goHigh"
          @keyup.native.enter="queryFn"
        >
          <el-button slot="append" type="primary" size="medium" @click="queryFn"
            >搜索</el-button
          >
        </el-input>
        <el-button
          type="primary"
          size="medium"
          @click="seniorFn"
          style="margin-left: 15px"
        >
          高级检索
          <i v-if="isShow" class="el-icon-arrow-up"></i>
          <i v-else class="el-icon-arrow-down"></i>
        </el-button>
      </div>
    </div>
    <el-card class="search-card" v-if="isShow">
      <el-form
        ref="form"
        :model="form"
        label-position="left"
        size="medium"
        class="search-form"
      >
        <el-form-item v-for="(item, index) in form.searchFields" :key="index">
          <el-select
            v-model="item.operateType"
            placeholder="请选择"
            style="width: 80px; margin-right: 10px"
            v-if="index != 0"
          >
            <el-option label="和" value="0"></el-option>
            <el-option label="或" value="1"></el-option>
          </el-select>

          <div class="select-input">
            <el-select
              v-model="item.searchType"
              placeholder="请选择"
              style="width: 120px"
            >
              <el-option
                v-for="(item, index) in searchTypes"
                :key="index"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
            <el-select
              v-model="item.value"
              placeholder="请选择"
              style="width: 470px"
              v-if="item.searchType == 4"
            >
              <el-option
                v-for="(item, index) in articleTypes"
                :key="index"
                :label="item.dictLabel"
                :value="item.dictValue"
              />
            </el-select>
            <el-input
              v-else
              placeholder="请输入内容"
              v-model="item.value"
              style="width: 470px"
            />
          </div>

          <div class="btns">
            <i
              class="el-icon-minus"
              @click="minusRow(index)"
              v-if="index != 0"
            ></i>
            <i class="el-icon-plus" @click="addRow"></i>
          </div>
        </el-form-item>
        <el-form-item label="时间选择:" label-width="90px" prop="valueDate">
          <el-date-picker
            v-model="form.valueDate"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            style="width: 590px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label-width="403px"
          style="margin-top: 40px; margin-bottom: 0"
        >
          <el-button @click="reset">重置条件</el-button>
          <el-button type="primary" @click="submit">检索</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { remote } from "@/api/admin/sys/sys-dict";

export default {
  props: {
    isHome: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShow: false,
      form: {
        keyword: "",
        valueDate: "",
        searchFields: [
          {
            operateType: "0",
            searchType: "0",
            value: "",
          },
          // {
          //   operateType: "0",
          //   searchType: "1",
          //   value: "",
          // }
        ],
      },
      searchTypes: [],
      articleTypes: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  mounted() {
    this.getSearchType();
    this.getArticleType();
    this.submit();
    if (this.$route.query && this.$route.query.showHigh) {
      this.isShow = true;
    }
  },
  methods: {
    addRow() {
      this.form.searchFields.push({
        operateType: "0",
        searchType: "1",
        value: "",
      });
    },
    minusRow(index) {
      this.form.searchFields.splice(index, 1);
    },
    goHigh() {
      if (this.isHome) {
        this.$router.push("/gateway/highSearch");
        return;
      }
    },
    seniorFn() {
      if (this.isHome) {
        this.$router.push({
          path: "/gateway/highSearch",
          query: {
            showHigh: true,
          },
        });
        return;
      } else {
        this.isShow = !this.isShow;
      }
    },
    queryFn() {
      this.isShow = false;
      this.goHigh();
      this.submit();
    },
    submit() {
      if (this.isHome) return;
      this.$emit("searchFn", this.form);
    },
    reset() {
      this.form = {
        keyword: "",
        valueDate: "",
        searchFields: [
          {
            operateType: "0",
            searchType: "0",
            value: "",
          },
          // {
          //   operateType: "0",
          //   searchType: "1",
          //   value: "",
          // }
        ],
      };
    },
    async getSearchType() {
      const { data } = await remote("article_search_type");
      this.searchTypes = data.data;
    },
    async getArticleType() {
      const { data } = await remote("portal_article_type");
      this.articleTypes = data.data;
    },
  },
};
</script>
<style lang="scss" scoped>
.high-search {
  height: 180px;
  background: url("../../assets/image/search.png") no-repeat center;
  ::v-deep .search {
    width: 630px;
    display: flex;
    padding-top: 73px;
    margin: 0 auto;
    .el-input__inner {
      height: 38px;
      line-height: 38px;
    }
    .el-input-group__append {
      .el-button {
        background: #409eff;
        color: #fff;
      }
    }
    .el-popover__reference-wrapper .el-button {
      height: 38px;
      margin-left: 15px;
    }
  }
}

.search-form.el-form {
  margin: 23px auto;
  ::v-deep .el-form-item {
    &:first-child {
      .select-input {
        margin-left: 90px;
      }
    }
    .el-form-item__content,
    .select-input {
      display: flex;
    }
  }
  .btns {
    white-space: nowrap;
    margin-left: 10px;
    i {
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      &:first-child {
        margin-right: 10px;
      }
    }
  }
}
.search-card {
  width: 1320px;
  margin: 0 auto;
  .search-form.el-form {
    width: 800px;
  }
}
</style>
