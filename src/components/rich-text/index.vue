<template>
  <div class="margin-top-20">
    <textarea name="content" :id="id" v-model="outContent"></textarea>
    <input
      type="file"
      @change="selectedFile"
      style="visibility: hidden; height: 0"
      name="imgFile"
      ref="inputFile"
    />
  </div>
</template>

<script>
import "@aleen42/kindeditor/themes/default/default.css";
import "../../../node_modules/@aleen42/kindeditor/themes/default/default.css";
import "@aleen42/kindeditor/kindeditor-all-min.js";
import "@aleen42/kindeditor/lang/zh-CN.js";
// 配置文件
import items from "./config/items.js";
import htmlTags from "./config/htmlTags.js";
import fontSizeTable from "./config/fontSizeTable.js";
import otherConfig from "./config/otherConfig.js";
// import { uploadImg } from "@/http/api.js";
import { uploadImg } from "@/api/site/sitehallinfo.js";
export default {
  name: "kindEditorComponent",
  props: {
    readonlyFlag: {
      type: Boolean,
      default: false,
    },
    content: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "kindeditor_id",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    minWidth: {
      type: Number,
      default: 620,
    },
    minHeight: {
      type: Number,
      default: 200,
    },
    items: {
      type: Array,
      default: function () {
        return [...items];
      },
    },
    htmlTags: {
      type: Object,
      default: function () {
        return { ...htmlTags };
      },
    },
    fontSizeTable: {
      type: Array,
      default: function () {
        return [...fontSizeTable];
      },
    },
    langType: {
      type: String,
      default: "zh-CN",
    },
    themeType: {
      type: String,
      default: "default",
    },
    bodyClass: {
      type: String,
      default: "ke-content",
    },
    ...otherConfig,
  },
  data() {
    return {
      editor: null,
      outContent: this.content,
    };
  },
  watch: {
    content(val) {
      this.editor && val !== this.outContent && this.editor.html(val);
    },
    outContent(val) {
      this.$emit("update:content", val);
      this.$emit("on-content-change", val);
      this.$emit("input", val);
    },
  },
  mounted() {
    this.initEditor();
    this.editor.clickToolbar("image", () => {
      this.editor.hideDialog();
      this.handleOpenFile();
    });
    this.editor.clickToolbar("media", () => {
      this.editor.hideDialog();
      this.handleOpenFile();
    });
    console.log("this.editor=", this.editor);
  },
  activated() {
    this.initEditor();
  },
  deactivated() {
    this.removeEditor();
  },
  beforeDestroy() {
    this.removeEditor();
  },
  methods: {
    handleOpenFile() {
      // let input = document.getElementById("inputFile");
      let input = this.$refs.inputFile;
      input.addEventListener(
        "click",
        function () {
          this.value = "";
        },
        false
      );
      input.click();
    },
    // 图片上传前验证
    beforeUpload(file) {
      console.log("file", file);
      const isJpgOrPng =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/jpg" ||
        file.type === "video/mp4";
      if (!isJpgOrPng) {
        this.$message.error("只能上传png/jpg格式的图片或mp4格式的视频!");
        return false;
      }
      let num = 5;
      if (file.type == "video/mp4") {
        num = 500;
      }
      const isLt5M = file.size / 1024 / 1024 < num;
      if (!isLt5M) {
        if (file.type == "video/mp4") {
          this.$message.error("视频不能超过" + num + "MB!");
        } else {
          this.$message.error("图片不能超过" + num + "MB!");
        }
      }
      return isJpgOrPng && isLt5M;
    },
    async selectedFile($event) {
      let self = this;
      console.log("$event", $event.target);
      const file = $event.target.files[0];
      if (!self.beforeUpload(file)) return;
      const formData = new FormData();
      formData.append("file", file);
      let params = {
        file: formData,
      };
      // 上传文件到服务器
      uploadImg(formData).then((res) => {
        // console.log("上传文件==", res);
        let _this = this;
        if (res.data.code === 200) {
          if (res.data.data.fileType == "mp4") {
            let url = _this.$getUrlByProcess(res.data.data.uri);
            // let img = res.data.data.uri;
            setTimeout(() => {
              self.editor.insertHtml(
                `<video controls="controls" style="height:520px;width: 100%;"  src="${url}"  ></video><p><br/></p>`
              );
            }, 100);
          } else {
            let img = res.data.data.uri;
            let image = new Image();
            image.src = _this.$getUrlByProcess(img);
            // window.location.protocol + "//" + window.location.host + img;
            image.crossOrigin = "Anonymous";
            image.onload = function () {
              // let base64 = _this.getBase64Image(image);
              self.editor.insertHtml(`<img src="${image.src}" alt=\"\" />`);
            };
          }
        } else {
          self.$message.error("上传失败！");
        }
      });
    },
    async content2Url() {
      // 把html片段上传到后端服务器，拿到url，uploadHtml是自己后端上传的接口
      // try {
      //   const res = await uploadHtml(this.outContent);
      //   return res;
      // } catch (error) {
      //   console.log(error)
      // }
    },
    loadUrl(url) {
      if (url && url.length > 0) {
        console.log(url);
        // axios.get(url).then(response => {
        //   // 处理HTML显示
        //   this.outContent = response.data;
        //   this.editor.appendHtml(this.outContent);
        //   this.$emit("subLoadUrlToHtml", response.data);
        // }).catch(() => {
        //   this.outContent = "服务器数据加载失败，请重试!";
        //   this.editor.appendHtml(this.outContent);
        // });
      }
    },
    removeEditor() {
      window.KindEditor.remove(`#${this.id}`);
    },
    initEditor() {
      this.removeEditor();
      this.editor = window.KindEditor.create("#" + this.id, {
        width: this.width,
        height: this.height,
        minWidth: this.minWidth,
        minHeight: this.minHeight,
        items: this.items,
        noDisableItems: this.noDisableItems,
        filterMode: this.filterMode,
        htmlTags: this.htmlTags,
        wellFormatMode: this.wellFormatMode,
        resizeType: this.resizeType,
        themeType: this.themeType,
        langType: this.langType,
        designMode: this.designMode,
        fullscreenMode: this.fullscreenMode,
        basePath: this.basePath,
        themesPath: this.themesPath,
        pluginsPath: this.pluginsPath,
        langPath: this.langPath,
        minChangeSize: this.minChangeSize,
        loadStyleMode: this.loadStyleMode,
        urlType: this.urlType,
        newlineTag: this.newlineTag,
        pasteType: this.pasteType,
        dialogAlignType: this.dialogAlignType,
        shadowMode: this.shadowMode,
        zIndex: this.zIndex,
        useContextmenu: this.useContextmenu,
        syncType: this.syncType,
        indentChar: this.indentChar,
        cssPath: this.cssPath,
        cssData: this.cssData,
        bodyClass: this.bodyClass,
        colorTable: this.colorTable,
        afterCreate: this.afterCreate, //this.afterCreate afterCreateSelf
        // 编辑器内容改变回调
        afterChange: () => {
          this.editor ? (this.outContent = this.editor.html()) : "";
        },
        afterTab: this.afterTab,
        afterFocus: this.afterFocus,
        afterBlur: this.afterBlur,
        afterUpload: this.afterUpload,
        uploadJson: this.uploadJson,
        fileManagerJson: this.fileManagerJson,
        allowPreviewEmoticons: this.allowPreviewEmoticons,
        allowImageUpload: this.allowImageUpload,
        allowFlashUpload: this.allowFlashUpload,
        allowMediaUpload: this.allowMediaUpload,
        allowFileUpload: this.allowFileUpload,
        allowFileManager: this.allowFileManager,
        fontSizeTable: this.fontSizeTable,
        imageTabIndex: this.imageTabIndex,
        formatUploadUrl: this.formatUploadUrl,
        fullscreenShortcut: this.fullscreenShortcut,
        extraFileUploadParams: this.extraFileUploadParams,
        filePostName: this.filePostName,
        fillDescAfterUploadImage: this.fillDescAfterUploadImage,
        afterSelectFile: this.afterSelectFile,
        pagebreakHtml: this.pagebreakHtml,
        allowImageRemote: this.allowImageRemote,
        autoHeightMode: this.autoHeightMode,
        fixToolBar: this.fixToolBar,
        tabIndex: this.tabIndex,
      });
      if (this.readonlyFlag) {
        this.editor.readonly(true);
      }

      // console.log(" this.editor==", this.editor);
    },
    getBase64Image(img) {
      var canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      var ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0, img.width, img.height);
      var ext = img.src.substring(img.src.lastIndexOf(".") + 1).toLowerCase();
      var dataURL = canvas.toDataURL("image/" + ext);
      return dataURL;
    },
    afterCreateSelf() {
      setTimeout(() => {
        if (this.editor) {
          var editerDoc = this.editor.edit.doc; //得到编辑器的文档对象
          let self = this;
          editerDoc.addEventListener("paste", function (e) {
            // let cbd = e.clipboardData;
            // let ua = window.navigator.userAgent;

            var ele = e.clipboardData.items;

            for (var i = 0; i < ele.length; ++i) {
              if (
                ele[i].kind == "file" &&
                ele[i].type.indexOf("image/") !== -1
              ) {
                e.preventDefault(); //阻止默认动作
                var file = ele[i].getAsFile(); //得到二进制数据

                var formData = new FormData();
                formData.append("file", file); //name,value
                uploadImg(formData).then((res) => {
                  let _this = this;
                  if (res.data.code === 200) {
                    let img = res.data.data.uri;
                    let image = new Image();
                    image.src = img;
                    image.src =
                      window.location.protocol +
                      "//" +
                      window.location.host +
                      img;
                    image.onload = function () {
                      self.editor.insertHtml(
                        `<img src="${image.src}" alt=\"\" />`
                      );
                    };
                  } else {
                    self.$message.error("上传失败！");
                  }
                });
              }
            }
          });
        }
      }, 600);
    },
  },
};
</script>

<style lang="scss" scoped></style>
