
import { isExsit } from '@/api/admin/auth/user';
import { rule } from "@/util/validateRules.js";

export const validateUsername = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入用户名'))
  }
  let flag = new RegExp(/^([a-z\u4e00-\u9fa5\d]+?)$/).test(value)
  if (!flag) {
    callback(new Error('用户名支持小写英文、数字、中文'))
  }
  isExsit({ userName: value }).then(response => {
    if (window.boxType === 'edit') callback()
    let result = response.data.data
    if (result) {
      return callback(new Error('用户名已经存在'))
    } else {
      return callback()
    }
  })
}

// 设置密码校验规则
export const checkPassword = (rule, value, callback) => {
  if (window.boxType === 'edit') {
    return callback()
  }
  if (!value) {
    callback(new Error('请输入密码'))
  } else if (value.length <= 6) {
    callback(new Error('请输入6位以上密码'))
  } else {
    callback()
  }
}

// 设置手机号的验证规则
export const checkPhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入联系方式'))
  } else {
    const reg = /^1[3|4|5|7|8][0-9]\d{8}$/
    if (!reg.test(value)) {
      return callback(new Error('请输入正确的电话'))
    }
  }

  isExsit({ phone: value }).then(response => {
    if (window.boxType === 'edit') callback()
    let result = response.data.data
    if (result) {
      return callback(new Error('手机号已经存在'))
    } else {
      return callback()
    }
  })
}

export const tableOption = {
  border: true,
  index: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  searchMenuSpan: 6,
  dialogWidth: '50%',
  editBtn: false,
  delBtn: false,
  align: 'center',
  addBtn: false,
  labelWidth: 140,
  height: "calc(100vh - 630px)",
  column: [{
    fixed: true,
    label: 'id',
    prop: 'userId',
    span: 24,
    hide: true,
    editDisplay: false,
    addDisplay: false
  }, {
    fixed: true,
    label: '机构名称',
    prop: 'deptName',
    searchLabelWidth: 120,
    editDisabled: true,
    slot: true,
    search: true,
    span: 24,
    rules: [{
      required: true,
      message: '请输入机构名称'
    },
    {
      min: 2,
      max: 20,
      message: '长度在 2 到 20 个字符',
      trigger: 'blur'
    },
    { validator: validateUsername, trigger: 'blur' }
    ]
  }, {
    fixed: true,
    label: '联系人',
    prop: 'contact',
    slot: true,
    span: 24,
    rules: [{
      required: true,
      message: '请输入联系人'
    },
    {
      min: 2,
      max: 20,
      message: '长度在 2 到 20 个字符',
      trigger: 'blur'
    }
    ]
  }, {
    fixed: true,
    label: '联系电话',
    prop: 'contactPhone',
    span: 24,
  },
  {
    fixed: true,
    label: '地址',
    prop: 'address',
    span: 24,
  },
  {
    fixed: true,
    label: '行政区划',
    prop: 'areaName',
    span: 24,
  },
  {
    fixed: true,
    label: '排序',
    prop: 'weight',
    span: 24,
  },
  ]
}

