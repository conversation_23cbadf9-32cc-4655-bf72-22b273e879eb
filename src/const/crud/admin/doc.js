
export const tableOption = {
    border: true,
    index: true,
    indexLabel: '序号',
    stripe: true,
    menuAlign: 'center',
    menuWidth: 150,
    align: 'center',
    refreshBtn: true,
    searchMenuSpan: 4,
    showClomnuBtn: false,
    searchSize: 'mini',
    menu: false,
    addBtn: false,
    editBtn: false,
    viewBtn: false,
    column: [
        {
            type: 'input',
            label: '系统用户名',
            search: true,
            searchSpan: 5,
            width: 200,
            searchLabelWidth:90,
            prop: 'operateUsername'
        }, {
            type: 'input',
            label: '姓名',
            search: true,
            searchSpan: 4,
            width: 200,
            prop: 'operateNickname'
        }, {
            type: 'select',
            label: '操作类型',
            prop: 'operateType',
            search: true,
            searchSpan: 5,
            width: 200,
            dicUrl: '/admin/sys_dict/type/operate_type',
            props: {
                label: 'dictLabel',
                value: 'dictValue'
            },
        }, {
            label: '操作详情',
            prop: 'operateDetails',
        }, {
            width: 150,
            label: '操作时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd',
            rangeSeparator: '-',
            search: true,
            searchRange: true
        }
    ]
}
