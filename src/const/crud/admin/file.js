export const tableOption = {
  border: true,
  index: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  viewBtn:false,
  searchMenuSpan: 6,
  editBtn:false,
  saveBtn: false,
  addBtn: true,
  column: [
	  {
      label: '编号',
      prop: 'id',
      hide: true,
      addDisplay: false
    },
    {
      hide: true,
      label: '附件上传',
      prop: 'imgUrl',
      type: 'upload',
      loadText: '附件上传中，请稍等',
      span: 24,
      propsHttp: {
        res: 'data'
      },
      tip: '上传同步至文件服务器',
      action: "/storage/upload"
    },
    {
      label: '空间',
      prop: 'bucketName',
      overHidden:true,
      addDisplay: false
    },
	  {
      label: '文件名',
      prop: 'fileName',
      overHidden:true,
      addDisplay: false
    },
	  {
      label: '原文件名',
      prop: 'originalFileName',
      search: true,
      overHidden:true,
      addDisplay: false
    },
	  {
      label: '文件类型',
      prop: 'fileType',
      addDisplay: false
    },
	  {
      label: '文件大小',
      prop: 'fileSize',
      addDisplay: false
    },
    {
      label: '文件路径',
      prop: 'path',
      width: 180,
      addDisplay: false
    },
    {
      width: 120,
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      editDisabled: true,
      addDisplay: false,
      span: 24,
    }
  ]
}
