export const alreadyBindUserOption = {
  searchMenuSpan: 24,
  tip: false,
  viewBtn: false,
  dialogWidth: "72%",
  searchMenuPosition: "right",
  editBtn: false,
  delBtn: false,
  addBtn: false,
  rowKey: 'userId',
  selection: true,
  reserveSelection: true,
  menu: false,
  align: "center",
  column: [
    {
      type: "input",
      label: "用户名",
      sortable: true,
      prop: "userName",
      display: false,
      search: true,
      span: 6,
      rules: [
        {
          required: true,
          message: "请输入人员名称",
          trigger: "change",
        },
      ],
    },
    // {
    //   label: "工作照",
    //   prop: "img",
    // },
    {
      type: "input",
      label: "姓名",
      sortable: true,
      prop: "nickName",
      display: false,
      search: true,
      span: 6,
      rules: [
        {
          required: true,
          message: "请输入人员名称",
          trigger: "change",
        },
      ],
    },
    {
      type: "input",
      label: "手机号",
      sortable: true,
      prop: "phone",
      display: false,
      span: 6,
    },
    // {
    //   type: "input",
    //   label: "所属部门",
    //   sortable: true,
    //   prop: "deptName",
    //   display: false,
    //   span: 6,
    // },
    {
      type: "tree",
      label: "所属部门",
      sortable: true,
      filterable: true,
      search: true,
      display: false,
      prop: "deptId",
      props: {
        label: "name",
        value: "id",
      },
      dataType: "String",
      dicUrl: "/admin/dept/tree",
      span: 8,
    },
    {
      label: "状态",
      prop: "status",
      type: "radio",
      slot: true,
      border: true,
      span: 6,
      dicData: [
        {
          label: "有效",
          value: "0",
        },
        {
          label: "停用",
          value: "1",
        },
        {
          label: "锁定",
          value: "9",
        },
      ],
    },
  ]
};
