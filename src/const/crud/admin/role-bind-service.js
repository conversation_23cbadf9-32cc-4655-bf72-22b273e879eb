export const tableOption = {
  searchMenuSpan: 24,
  tip: false,
  viewBtn: false,
  dialogWidth: "72%",
  searchMenuPosition: "right",
  editBtn: false,
  delBtn: false,
  addBtn: false,
  menu: false,
  align: "center",
  selection: true,
  // reserveSelection: true,
  // reserveSelection:true,
  rowKey: 'staffId',
  column: [
    // {
    //   label: "工作照",
    //   sortable: true,
    //   prop: "profileImage",
    //   dataType: "string",
    //   type: "upload",
    //   display: false,
    //   clearable: true,
    //   limit: 1,
    //   showFileList: true,
    //   multiple: false,
    // },
    {
      type: "input",
      label: "角色名称",
      sortable: true,
      prop: "roleName",
      display: false,
      span: 6,
      slot: true,
    },
    {
      type: "input",
      label: "角色编码",
      sortable: true,
      prop: "roleKey",
      display: false,
      // search: true,
      span: 8,
      searchSpan:8,
      rules: [
        {
          required: true,
          message: "请输入姓名",
          trigger: "change",
        },
      ],
    },
    
   
    {
      type: "input",
      label: "说明",
      sortable: true,
      prop: "roleDesc",
      display: false,
      span: 6,
    },
  
    
    
  ]
};
