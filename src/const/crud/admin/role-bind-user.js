export const tableOption = {
  searchMenuSpan: 24,
  tip: false,
  viewBtn: false,
  dialogWidth: "72%",
  searchMenuPosition: "right",
  editBtn: false,
  delBtn: false,
  addBtn: false,
  menu: false,
  align: "center",
  selection: true,
  // reserveSelection: true,
  // reserveSelection:true,
  rowKey: 'staffId',
 
  column: [
    // {
    //   label: "工作照",
    //   sortable: true,
    //   prop: "profileImage",
    //   dataType: "string",
    //   type: "upload",
    //   display: false,
    //   clearable: true,
    //   limit: 1,
    //   showFileList: true,
    //   multiple: false,
    // },
    {
      type: "input",
      label: "系统用户名",
      sortable: true,
      prop: "userName",
      display: false,
      span: 6,
      slot: true,
      search: true,
      searchLabelWidth:120,
    },
    {
      type: "input",
      label: "姓名",
      sortable: true,
      prop: "nickName",
      display: false,
      search: true,
      span: 8,
      searchSpan:8,
      rules: [
        {
          required: true,
          message: "请输入姓名",
          trigger: "change",
        },
      ],
    },
    // {
    //   type: "input",
    //   label: "姓名",
    //   sortable: true,
    //   prop: "staffName",
    //   display: false,
    //   search: true,
    //   span: 8,
    //   searchSpan: 8,
    //   display: false,
    //   hide:true,
    //   rules: [
    //     {
    //       required: true,
    //       message: "请输入姓名",
    //       trigger: "change",
    //     },
    //   ],
    // },
   
    {
      type: "input",
      label: "手机号",
      sortable: true,
      prop: "phone",
      display: false,
      span: 6,
    },
  
    {
      type: "tree",
      label: "所属部门",
      // dicUrl: "/admin/dept/tree",
      // props: {
      //   label: "deptName",
      //   value: "id",
      // },
      prop: "deptName",
      // prop: "deptId",
      span: 6,
      display: false,
      multiple: false,
      clearable: true,
      leafOnly: true,
      overHidden: true,
      align: "left",
      search:false
    },
    
  ]
};
