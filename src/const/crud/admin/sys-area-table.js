
export const tableOption = {
    border: true,
    index: true,
    indexLabel: '序号',
    stripe: true,
    align: 'center',
    refreshBtn: false,
    columnBtn: false,
    searchSize: 'mini',
    menu: true,
    editBtn: false,
    addBtn: false,
    delBtn: false,
    viewBtn: false,
    height: "calc(100vh - 600px)",
    searchMenuSpan: 6,
    column: [
         {
           type: 'input',
           label: '节点类型',
           width: 180,
           prop: 'type',
           dicData: [{
            label: '区划',
            value: "0",
            
           },
           {
            label: '类别',
            value: "1",
            
            },
               {
                label: '机构',
                value: "2",
                
              }
            ],
        }, {
          type: 'input',
          label: '组织全称',
          prop: 'deptName',
        //   search: true,
          rules: [
            {
              required: true,
              message: '请输入区划名称',
              trigger: 'blur'
            }
          ],
        }, {
          type: 'input',
          label: '规范简称',
          prop: 'normName',
          width:120,
          formslot: true,
          // dicData: [
          //   {

          //   },
          // ],
          // props: {
          //   label: 'dictLabel',
          //   value: 'dictValue'
          // },
        }, {
          type: 'input',
          label: '习惯简称',
          width: 80,
          prop: 'usualName',
        },{
          type: 'input',
          label: '组织域名',
          width:80,
        //   search: true,
          prop: 'domainName',
          // dicUrl: '/admin/sys_dict/type/boole_status',
          // props: {
          //   label: 'dictLabel',
          //   value: 'dictValue'
          // },
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择生效状态',
        //       trigger: 'blur'
        //     }
        //   ],
        }, {
          type: 'input',
          label: '组织编码',
          width: 80,
          prop: 'orgCode',
        }, {
          type: 'input',
          label: '区域编码',
          width: 80,
          prop: 'areaCode',
        }, {
          type: 'input',
          label: '区域规格',
          width: 80,
            prop: 'unit',
          dicUrl: '/admin/sys_dict/type/area_standards',
          props: {
            label: 'dictLabel',
            value: 'dictValue'
          },
        }, {
          type: 'input',
          label: '客户端显示该组织',
          width: 80,
            prop: 'clientShow',
            dicData: [{
                label: '不显示',
                value: "0",
               },
               {
                label: '显示',
                value: "1",
                }
                ],
      }
      , {
        type: 'input',
        label: '管理员',
        width: 80,
        prop: 'leader',
        // dicUrl: '/admin/user/listUser',
         props: {
            label: 'nickName',
            value: 'id'
          },
      }
      , {
        type: 'input',
        label: '备注信息',
        width: 80,
        prop: 'remark',
      }

    ]
}
