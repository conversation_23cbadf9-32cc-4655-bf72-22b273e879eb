export const tableOption = {
  border: true,
  index: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  searchMenuSpan: 6,
  column: [
    {
      type: "input",
      label: "类型",
      prop: "dictType",
      search: true
    },
    {
      type: "input",
      label: "描述",
      prop: "description"
    },
    {
      label: '字典类型',
      prop: 'systemFlag',
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      },
      type: 'radio',
      dicUrl: '/admin/sys_dict/type/dict_type',
      rules: [{
        required: true,
        message: '请选择字典类型',
        trigger: 'blur'
      }],
      search: true
    },
    {
      type: "input",
      label: "备注",
      prop: "remark"
    },
    {
      width: 150,
      label: '创建时间',
      prop: 'createTime',
      addDisplay: false,
      editDisabled: true,
      format: 'yyyy-MM-dd HH:mm',
      valueFormat: 'yyyy-MM-dd HH:mm:ss'
    }
  ]
}

export const tableDictItemOption = {
  border: true,
  index: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  refreshBtn: false,
  showClomnuBtn: false,
  searchSize: 'mini',
  column: [{
    label: '类型',
    prop: 'dictType',
    addDisabled: true,
    editDisabled: true
  }, {
    width: 150,
    label: '数据值',
    prop: 'dictValue',
    rules: [{
      required: true,
      message: '请输入数据值',
      trigger: 'blur'
    }]
  }, {
    label: '标签名',
    prop: 'dictLabel',
    rules: [{
      required: true,
      message: '请输入标签名',
      trigger: 'blur'
    }]
  }, {
    type: "select",
    label: "颜色类型",
    prop: "colorType",
    props: {
      label: 'dictLabel',
      value: 'dictValue'
    },
    dicUrl: '/admin/sys_dict/type/label_color_type',
    rules: [{
      required: true,
      message: '请输入颜色类型',
      trigger: 'blur'
    }]
  }, {
    label: '描述',
    prop: 'description',
    rules: [{
      required: true,
      message: '请输入字典描述',
      trigger: 'blur'
    }]
  }, {
    label: '排序',
    prop: 'dictSort',
    type: 'number',
    rules: [{
      required: true,
      message: '请输入排序',
      trigger: 'blur'
    }]
  }, {
    label: '备注信息',
    prop: 'remark'
  }]
}

