
export const tableOption = {
    border: true,
    index: true,
    indexLabel: '序号',
    stripe: true,
    align: 'center',
    refreshBtn: false,
    columnBtn: false,
    searchSize: 'mini',
    menu: true,
    editBtn: false,
    addBtn: false,
    delBtn: false,
    viewBtn: false,
    height: "calc(100vh - 600px)",
    searchMenuSpan: 6,
    column: [
         {
           type: 'input',
           label: '区划编码',
           width: 180,
           prop: 'areaCode',
           rules: [
             {
               required: true,
               message: '请输入区划编码',
               trigger: 'blur'
             }
           ],
        }, {
          type: 'input',
          label: '区划名称',
          prop: 'areaName',
          search: true,
          rules: [
            {
              required: true,
              message: '请输入区划名称',
              trigger: 'blur'
            }
          ],
        }, {
          type: 'select',
          label: '区划等级',
          prop: 'areaLevel',
          width:120,
          formslot: true,
          dicData: [
            {

            },
          ],
          props: {
            label: 'dictLabel',
            value: 'dictValue'
          },
        }, {
          type: 'input',
          label: '排序',
          width: 80,
          prop: 'areaOrder',
        },{
          type: 'select',
          label: '是否生效',
          width:80,
          search: true,
          prop: 'status',
          dicUrl: '/admin/sys_dict/type/boole_status',
          props: {
            label: 'dictLabel',
            value: 'dictValue'
          },
          rules: [
            {
              required: true,
              message: '请选择生效状态',
              trigger: 'blur'
            }
          ],
        }
    ]
}
