import {rule} from "@/util/validateRules";
import {getObj<PERSON>y<PERSON><PERSON>} from '@/api/admin/sys/sysconfig.js'

var validateParam = (rule, value, callback) => {
  getObjByKey(value).then(response => {
    if (window.boxType === 'edit') callback()
    const result = response.data.data
    if (result !== null) {
      callback(new Error('参数键已经存在'))
    } else {
      callback()
    }
  })
}

export const tableOption = {
  border: true,
  index: true,
  indexLabel: "序号",
  stripe: true,
  menuAlign: "center",
  align: "center",
  searchMenuSpan: 6,
  column: [
    {
      search: true,
      label: "名称",
      prop: "configName",
      rules: [
        { required: true, message: '请输名称', trigger: 'blur' },
        { max: 30, message: '长度在 30 个字符', trigger: 'blur' },
        { validator: rule.validatorNameCn, trigger: 'blur'}
      ]
    },
    {
      label: "键",
      prop: "config<PERSON>ey",
      rules: [
        {required: true, message: '请输入键', trigger: 'blur'},
        {validator: rule.validator<PERSON>ey, trigger: 'blur'},
        {validator: validateParam, trigger: 'blur'},
      ]
    },
    {
      label: "值",
      overHidden: true,
      prop: "configValue",
      rules: [
        { required: true, message: '请输入值', trigger: 'blur' }
      ]
    },
    {
      type: 'select',
      label: "类型",
      width: 80,
      prop: "configType",
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      },
      dicUrl: '/admin/sys_dict/type/dict_type',
      rules: [{
        required: true,
        message: '请输入类型',
        trigger: 'blur'
      }],
      search: true
    },
    {
      type: "select",
      width: 80,
      label: "状态", //"0：有效，1：无效"
      prop: "status",
      props: {
        label: 'dictLabel',
        value: 'dictValue'
      },
      dicUrl: '/admin/sys_dict/type/status_type',
      rules: [
        {required: true, message: '请输入值', trigger: 'blur'}
      ]
    }
  ]
}
