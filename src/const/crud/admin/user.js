
import { isExsit } from '@/api/admin/auth/user';
import { rule } from "@/util/validateRules.js";
import {
  canUseNetDeptTree,
  canUseDeptSonTree,searchDeptByName
} from "@/api/admin/sys/dept";
export const validateUsername = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入用户名'))
  }
  let flag = new RegExp(/^([a-z\u4e00-\u9fa5\d]+?)$/).test(value)
  if (!flag) {
    callback(new Error('用户名支持小写英文、数字、中文'))
  }
  isExsit({userName: value}).then(response => {
    if (window.boxType === 'edit') callback()
    let result = response.data.data
    if (result) {
      return callback(new Error('用户名已经存在'))
    } else {
      return callback()
    }
  })
}

// 设置密码校验规则
export const checkPassword = (rule, value, callback) => {
  if (window.boxType === 'edit') {
    return callback()
  }
  if (!value) {
    callback(new Error('请输入密码'))
  } else if (value.length <= 6) {
    callback(new Error('请输入6位以上密码'))
  } else {
    callback()
  }
}

// 设置手机号的验证规则
export const checkPhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入联系方式'))
  } else {
    const reg = /^1[0-9]\d{9}$/
    if (!reg.test(value)) {
      return callback(new Error('请输入正确的电话'))
    }
  }

  isExsit({phone: value}).then(response => {
    if (window.boxType === 'edit') callback()
    let result = response.data.data
    if (result) {
      return callback(new Error('手机号已经存在'))
    } else {
      return callback()
    }
  })
}

export const tableOption = {
  border: true,
  index: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  searchMenuSpan: 6,
  dialogWidth: '50%',
  editBtn: false,
  delBtn: false,
  align: 'center',
  addBtn: false,
  labelWidth: 140,
  height: "calc(100vh - 400px)",
  column: [{
    fixed: true,
    label: 'id',
    prop: 'userId',
    span: 24,
    hide: true,
    editDisplay: false,
    addDisplay: false
  },
    {
    fixed: true,
    label: '系统用户名',
    prop: 'userName',
    searchLabelWidth:120,
    editDisabled: true,
    // display:false,
    addDisplay:false,
    slot: true,
    search: true,
    span: 24,
    // rules: [{
    //   required: true,
    //   message: '请输入系统用户名'
    // },
    //   // {
    //   //   min: 2,
    //   //   max: 20,
    //   //   message: '长度在 2 到 20 个字符',
    //   //   trigger: 'blur'
    //   // },
    //   // {validator: validateUsername, trigger: 'blur'}
    // ]
    },
    {
    fixed: true,
    label: '姓名',
    prop: 'nickName',
    slot: true,
    search: true,
    span: 24,
    // display:false,
    rules: [{
      required: true,
      message: '请输入姓名'
    },
      {
        min: 2,
        max: 20,
        message: '长度在 2 到 20 个字符',
        trigger: 'blur'
      }
    ]
    },
    {
      fixed: true,
      label: '身份证号',
      prop: 'certNo',
      // slot: true,
      // search: true,
      hide: true,
      span: 24,
      rules: [{
        required: false,
        message: '请输入身份证号'
      },
        {
          validator: rule.validateFormCardIdCanEmpty,
          message: "请输入正确的身份证号",
          trigger: "blur",
        }
      ]
      },
    // {
    // label: '密码',
    // prop: 'password',
    // type: 'password',
    // value: '',
    // hide: true,
    // span: 24,
    // rules: [{validator: checkPassword, trigger: 'blur'}]
    // },
    {
    label: '所属部门',
    prop: 'deptId',
    filterable:false,
      formslot: true,
    type:"tree",
    // slot: true,
      span: 24,
      dicUrl: `/admin/dept/currentUserCanOperateDeptTree`,
      props: {
        label: 'deptName',
        value: 'id'
      },
      cacheData:[{
        deptName:'未加载数据',
        id:-1
      }],
      
      hide: true,
      lazy: true,
      treeLoad: async (node, resolve) => {
        // console.log("node=js---=", node);
        if (node.level === 0) {
          let res1 = await canUseNetDeptTree()
          // console.log("no--else----js---",res1);
          return resolve(res1.data.data);
        } else if (node.level > 0) {
          // console.log("else---js-");
          const child = await canUseDeptSonTree({ deptId: node.data.id });
          // console.log("child==", child);
          node.data.children = child.data.data;

          return resolve(child.data.data);
        }
       
      },
      // filterNodeMethod: (value, data) => {
      //   console.log("value==",value)
      //   console.log("value=data=",data)
      // },
    rules: [{
      required: true,
      message: '请选择部门',
      trigger: 'change'
    }]
  }, {
    label: '手机号',
    prop: 'phone',
    value: '',
    span: 24,
    rules: [{
      required: true,
      message: '手机号不能为空',
      trigger: 'blur'
    }, {
      validator: checkPhone,
      trigger: 'blur'
    }]
    },
    // {
    // label: '角色',
    // prop: 'role',
    // formslot: true,
    // slot: true,
    // overHidden: true,
    // span: 24,
    // rules: [{
    //   required: true,
    //   message: '请选择角色',
    //   trigger: 'blur'
    // }]
    // },
    {
    label: '部门',
    prop: 'deptName',
    overHidden: true,
    addDisplay: false,
    editDisplay: false,
    span: 24,
    },
    {
    label: '岗位',
    prop: 'post',
    width: 168,
    overHidden: true,
    formslot: true,
      slot: true,
    hide:true,
    span: 24,
    rules: [{
      required: true,
      message: '请选择岗位',
      trigger: 'change'
    }]
    },
    {
   
      label: '所属处室',
      prop: 'office',
   
      hide:true,
      span: 24,
     
      },
    {
    label: '状态',
    prop: 'status',
    type: 'radio',
    slot: true,
    border: true,
    span: 24,
    rules: [{
      required: true,
      message: '请选择状态',
      trigger: 'change'
    }],
    dicData: [{
      label: '有效',
      value: '1'
    }, {
      label: '锁定',
      value: '0'
    }]
  }, {
    width: 120,
    label: '创建时间',
    prop: 'createTime',
    type: 'datetime',
    format: 'yyyy-MM-dd',
    editDisabled: true,
    addDisplay: false,
    span: 24
  }]
}
