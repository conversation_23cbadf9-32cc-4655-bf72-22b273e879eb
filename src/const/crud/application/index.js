
export const tableOption = {
    border: true,
    index: true,
    indexLabel: '序号',
    stripe: true,
    align: 'center',
    refreshBtn: false,
    columnBtn: false,
    searchSize: 'mini',
    menu: true,
    editBtn: false,
    addBtn: false,
    delBtn: false,
    viewBtn: false,
    // height: "calc(100vh - 600px)",
    searchMenuSpan: 6,
    column: [
         {
           type: 'input',
           label: '应用名称',
           width: 180,
           prop: 'name',
           rules: [
             {
               required: true,
               message: '请输入',
               trigger: 'blur'
             }
        ],
        search: true,
        }, {
          type: 'input',
          label: '应用图标',
          prop: 'logo',
          slot:true,
          rules: [
            {
              required: true,
              message: '请输入',
              trigger: 'blur'
            }
          ],
        }, {
          type: 'select',
          label: '来源',
          prop: 'source',
          width:120,
          // formslot: true,
          // dicData: [
          //   {

          //   },
          // ],
          // props: {
          //   label: 'dictLabel',
          //   value: 'dictValue'
          // },
        }, {
          type: 'input',
          label: '版本号',
          width: 80,
          prop: 'appVersion',
        },{
          type: 'select',
          label: '接入类型',
          width:80,
          // search: true,
        prop: 'accessType',
          dicData: [
            
              { label: "站点链接", value: 0 },
              { label: "平台级对接", value: 1 },
              { label: "系统级对接", value: 2 },
            
          ],
          // dicUrl: '/admin/sys_dict/type/boole_status',
          // props: {
          //   label: 'dictLabel',
          //   value: 'dictValue'
          // },
          // rules: [
          //   {
          //     required: true,
          //     message: '请选择生效状态',
          //     trigger: 'blur'
          //   }
          // ],
      }
      , {
        type: 'input',
        label: '创建时间',
        width: 80,
        prop: 'createTime',
      }, {
        type: 'input',
        label: '状态',
        width: 80,
        prop: 'status',
      },
      {
        type: 'input',
        label: '是否启用',
        width: 80,
        prop: 'status1',
        slot: true,
        hide:true
      }
    ]
}
