export const tableOption = {
    dialogWidth: "85%",
    index: true,
    indexLabel: "序号",
    selection: true,
    selectionFixed: true,
    stripe: false,
    menuAlign: "left",
    align: "left",
    menuWidth: 300,
    tip: false,
    viewBtn: false,
    copyBtn: false,
    emptyBtn: true,
    searchMenuPosition: "right",
    searchMenuSpan: 3,
    searchSpan: 5,
    menu: false,
    column: [
        {
            type: "input",
            label: "通知内容",
            prop: "content",
            span: 6,
            width: 600
        },
        {
            type: "input",
            label: "用户昵称",
            prop: "receiverName",
            span: 6,
        },
        {
            type: "input",
            label: "接收手机号",
            prop: "mobile",
            span: 6,
            search: true
        },
        {
            type: "input",
            label: "通知类型",
            prop: "remindType",
            slot: true,
            span: 6,
        },
        {
            type: "input",
            label: "业务类型",
            prop: "messageType",
            span: 6,
        },
        {
            type: "input",
            label: "发送时间",
            prop: "sendTime",
            search: true,
            searchslot: true,
            searchSpan: 6,
            span: 6,
        },
        {
            type: "select",
            label: "通知类型",
            prop: "sendChannel",
            search: true,
            hide: true,
            display: false,
            slot: true,
            span: 6,

            props: {
                label: "label",
                value: "value",
            },
            dicUrl: "/common/commDict/type/remind_type",
        },
        {
            type: "select",
            label: "业务类型",
            prop: "sendType",
            search: true,
            hide: true,
            display: false,
            slot: true,
            span: 6,
            props: {
                label: "label",
                value: "value",
            },
            dicUrl: "/common/commDict/type/message_type",
        },
    ],
};
