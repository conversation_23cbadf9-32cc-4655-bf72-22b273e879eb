export const tableOption = {
  dialogWidth: "65%",
  index: true,
  indexLabel: "序号",
  selection: true,
  selectionFixed: true,
  menuAlign: "center",
  menuHeaderAlign:'center',
  align: "center",
  menuWidth: 150,
  sortable: true,
  // border:true,
  tip: false,
  viewBtn: false,
  editBtn: false,
  addBtn: false,
  copyBtn: false,
  delBtn: false,
  searchMenuPosition: "right",
  searchMenuSpan: 3,
  searchSpan: 6,
  menu: true,
  labelWidth: 120,
  column: [
    // {
    //   type: "input",
    //   label: "消息模板表",
    //   prop: "templateId",
    //   editDisplay: false,
    //   addDisplay: false,
    //   span: 6,
    // },
    {
      type: "select",
      label: "推送类型",
      prop: "sendType",
      rules: [
        {
          required: true,
          message: "请选择推送类型",
          trigger: "blur",
        },
      ],
      span: 6,
    },
    {
      type: "input",
      label: "通知内容",
      prop: "message",
      width: 500,
      rules: [
        {
          required: true,
          message: "请输入通知内容",
          trigger: "blur",
        },
      ],
      span: 24,
    },
    {
      type: "select",
      label: "通知类型",
      prop: "templateTypes",
      hide: true,
      slot: true,
      rules: [
        {
          required: true,
          message: "请选择通知类型",
          trigger: "blur",
        },
      ],
      span: 6,
    },
    {
      type: "input",
      label: "可用通知方式",
      prop: "templateTypes",
      editDisplay: false,
      addDisplay: false,
      slot: true,
      span: 6,
      width: 110
    },
    {
      type: "input",
      label: "短信模板code",
      prop: "templateCode",
      slot: true,
      span: 24,
      width: 160
    },
    {
      type: "input",
      label: "公众号消息模板code",
      prop: "mpTemplateId",
      slot: true,
      span: 24,
      width: 160
    },
    {
      type: "input",
      label: "公众号模板",
      prop: "mpTemplateContent",
      slot: true,
      span: 24,
      width: 500
    },
    {
      type: "input",
      label: "创建时间",
      prop: "createTime",
      editDisplay: false,
      addDisplay: false,
      span: 6,
      width: 160
    },
    {
      type: "input",
      label: "启用状态",
      prop: "status",
      editDisplay: false,
      addDisplay: false,
      slot: true,
      span: 6,
    },
    {
      type: "input",
      label: "发送类型",
      prop: "msgType",
      editDisplay: false,
      addDisplay: false,
      slot: true,
      span: 6,
    },
    // {
    //     type: "input",
    //     label: "",
    //     prop: "byzd8",
    //     search: true,
    //     searchslot: true,
    //     span: 6,
    //     width:5,
    // },
    // {
    //     type: "input",
    //     label: "",
    //     prop: "byzd9",
    //     search: true,
    //     searchslot: true,
    //     span: 6,
    //     width:5,
    // },
    // {
    //     type: "input",
    //     label: "",
    //     prop: "byzd10",
    //     search: true,
    //     searchslot: true,
    //     span: 6,
    //     width:5,
    // },
    // {
    //     type: "input",
    //     label: "",
    //     prop: "byzd11",
    //     search: true,
    //     searchslot: true,
    //     span: 6,
    //     width:5,
    // },
    //   {
    //     type: "select",
    //     label: "所属大厅",
    //     prop: "hallId",
    //     search: "true",
    //     filterable: true,
    //     hide: true,
    //     columnHide: true,
    //     span: 6,
    //     props: {
    //       label: "hallName",
    //       value: "hallId",
    //     },
    //     dicUrl: "/site/sitehallinfo/hallList",
    //     rules: [
    //       {
    //         required: true,
    //         message: "请输入所属大厅",
    //         trigger: "blur",
    //       },
    //     ],
    //   },
    //   {
    //     type: "select",
    //     label: "所属楼层",
    //     search: "true",
    //     filterable: true,
    //     span: 6,
    //     hide: true,
    //     columnHide: true,
    //     prop: "floorId",
    //     props: {
    //       label: "floorName",
    //       value: "floorId",
    //     },
    //     cascaderItem: ["regionId"],
    //     dicUrl: "/site/sitefloorinfo/floorList",
    //     dicMethod: "get",
    //     rules: [
    //       {
    //         required: true,
    //         message: "请输入所属楼层",
    //         trigger: "blur",
    //       },
    //     ],
    //   },
    //   {
    //     type: "select",
    //     label: "所属分区",
    //     search: true,
    //     filterable: true,
    //     prop: "regionId",
    //     hide: true,
    //     columnHide: true,
    //     span: 6,
    //     props: {
    //       label: "regionName",
    //       value: "regionId",
    //     },
    //     dicUrl: "/site/siteregioninfo/regionListByFloorId/{{key}}",
    //     dicMethod: "get",
    //     cascaderIndex: 0,
    //     rules: [
    //       {
    //         required: true,
    //         message: "请输入所属分区",
    //         trigger: "blur",
    //       },
    //     ],
    //   },
    //   {
    //     type: "select",
    //     label: "所属部门",
    //     filterable: true,
    //     span: 6,
    //     hide: true,
    //     columnHide: true,
    //     prop: "departmentId",
    //     props: {
    //       label: "deptName",
    //       value: "deptId",
    //     },
    //     dicUrl: "/person/personmngstaffinfo/deptList",
    //     rules: [
    //       {
    //         required: true,
    //         message: "请输入所属部门",
    //         trigger: "blur",
    //       },
    //     ],
    //   },
    //   {
    //     type: "select",
    //     label: "所属窗口",
    //     prop: "windowId",
    //     filterable: true,
    //     hide: true,
    //     columnHide: true,
    //     props: {
    //       label: "windowName",
    //       value: "windowNo",
    //     },
    //     dicUrl: "/site/sitewindowinfo/windowList",
    //     dicMethod: "get",
    //     span: 6,
    //     rules: [
    //       {
    //         required: true,
    //         message: "请输入所属窗口",
    //         trigger: "blur",
    //       },
    //     ],
    //   },
    //   {
    //     type: "input",
    //     label: "大厅",
    //     prop: "hallName",
    //     display: false,
    //   },
    //   {
    //     type: "input",
    //     label: "楼层",
    //     prop: "floorName",
    //     display: false,
    //   },
    //   {
    //     type: "input",
    //     label: "分区",
    //     prop: "regionName",
    //     display: false,
    //   },
    //   {
    //     type: "input",
    //     label: "部门",
    //     prop: "departmentName",
    //     display: false,
    //   },
    //   {
    //     type: "input",
    //     label: "窗口",
    //     prop: "windowName",
    //     display: false,
    //   },
    //   {
    //     type: "textarea",
    //     label: "备注",
    //     prop: "remark",
    //     span: 24,
    //     maxlength: 200,
    //     minRows: 4,
    //     showWordLimit: true,
    //     maxRows: 6,
    //     hide: true,
    //     columnHide: true,
    //   },
  ],
};
