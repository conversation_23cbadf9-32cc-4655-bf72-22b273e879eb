/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-08-02 16:29:59
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-03-04 11:17:20
 */
export const tableOption = {
  // height: "auto", // 高度自适应
  calcHeight: 80, // 高度要减去搜索栏的高度
  border: false,
  index: true,
  indexLabel: "序号",
  stripe: true,
  searchMenuPosition: "right",
  searchMenuSpan: 6,
  menuAlign: "center",
  align: "center",
  addTitle: "发布公告",
  addBtnText: "发布公告",
  dialogWidth: "85%",
  column: [
    {
      type: "input",
      label: "文章标题",
      prop: "title",
      span: 12,
      minWidth: 450,
      searchSpan: 8,
      align: "center",
      search: true,
      addDisplay: false,
      editDisplay: false,
    },
    // {
    //   label: "缩略图",
    //   prop: "picUrl",
    //   type: "upload",
    //   span: 12,
    //   width: 180,
    //   addDisplay: false,
    //   editDisplay: false,
    //   showFileList: true,
    //   multiple: true,
    //   limit: 6,
    //   propsHttp: {
    //     res: "data",
    //   },
    //   dataType: "string",
    //   canvasOption: {},
    //   action: "/file/storage/upload",
    //   accept: "image/*,application/pdf",
    //   headers: {},
    //   listType: "picture-card",
    //   tip: "（文件格式支持jpg、png、svg、pdf格式，文件容量100M以内。)",
    // },
    {
      type: "radio",
      label: "正文类型",
      prop: "type",
      span: 12,
      dataType: "string",
      // dicUrl: "/admin/sys_dict/type/article_contenttype",
      props: {
        label: "label",
        value: "value",
      },
      dicData: [
        {
          label: "纯文件",
          value: 1,
        },
        {
          label: "富文本",
          value: 0,
        },
      ],
    },
    // {
    //   type: "input",
    //   label: "浏览次数",
    //   prop: "visitcount",
    //   span: 12,
    //   width: 180,
    //   addDisplay: false,
    //   editDisplay: false,
    // },
    {
      type: "select",
      label: "状态",
      prop: "pubStatus",
      span: 12,
      searchSpan: 8,
      width: 80,
      props: {
        label: "label",
        value: "value",
      },
      fixed: "right",
      addDisplay: false,
      editDisplay: false,
      search: true,
      // searchValue: '2',
      dicData: [
        {
          label: "待提交",
          value: '0',
        },
        {
          label: "待审核",
          value: '1',
        },
        {
          label: "已发布",
          value: '2',
        },
        {
          label: "已驳回",
          value: "-1",
        },
        {
          label: "已下线",
          value: "-2",
        },
      ],
      // slot: true,
    }, {
      type: "select",
      label: "地市选项",
      prop: "areaCode",
      span: 12,
      searchSpan: 8,
      width: 80,
      props: {
        label: "areaName",
        value: "areaCode",
      },
      fixed: "right",
      addDisplay: false,
      editDisplay: false,
      search: true,
      searchValue: '410000000000', //搜索项初始化值
      // 监听变化事件
      change: (value) => window.updateAreasOptions(value),

      // dicUrl: '/sysArea/getChildrenById?id=410000000000',    //数据字典接口url地址
      dicData: [{ areaName: '河南省', areaCode: '410100000000' }],
      // slot: true,
    }, {
      type: "select",
      label: "发布部门",
      prop: "pubDeptId",
      change: (value) => window.updatedeptIdOptions(value),
      span: 12,
      searchSpan: 8,
      width: 80,
      props: {
        label: "deptName",
        value: "id",
      },
      fixed: "right",
      addDisplay: false,
      editDisplay: false,
      search: true,
      // searchValue:'2',
      dicData: [],
      // slot: true,
    },
  ],
};
