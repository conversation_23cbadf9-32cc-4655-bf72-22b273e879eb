export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  "searchMenuSpan": 6,
  addBtn: false,
  editBtn: false,
  delBtn:false,
  "column": [
	  // {
    //   "type": "input",
    //   "label": "主键自增",
    //   "prop": "id",
    //   "span": 12
    // },	  
     
    {
      "type": "input",
      "label": "标题",
      "prop": "title",
      "span": 12,
      overHidden: true,
      search: true,
      searchSpan: 4,
    },	  {
      "type": "select",
      "label": "内容类型",
      "prop": "articleType",
      "span": 12,
           props: {
          label: "dictLabel",
          value: "dictValue",
        },
      dicUrl: "/admin/sys_dict/type/portal_article_type",
      search: true,
      searchSpan: 4,
    },
    {
      "type": "input",
      "label": "栏目",
      "prop": "categoryId",
      "span": 12,
      slot:true
    },	  {
      "type": "select",
      "label": "审核状态",
      "prop": "pubStatus",
      "span": 12,
      slot: true,
      search: true,
      searchSpan: 4,
      props: {
        label: "label",
        value: "value",
      },
      dicData: [
        {
          label: "待提交",
          value: 0,
        },
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "已发布",
          value: 2,
        },
        {
          label: "已驳回",
          value: "-1",
        },
        {
          label: "已下线",
          value: "-2",
        },
        
      ],
    },	  {
      "type": "input",
      "label": "创建人",
      "prop": "createBy",
      "span": 12
    },	  {
      "type": "input",
      "label": "创建时间",
      "prop": "createTime",
      "span": 12,
      search: true,
      searchSpan: 6,
    },	  {
      "type": "input",
      "label": "审核人",
      "prop": "auditBy",
      "span": 12,
      // slot:true
    },	  {
      "type": "input",
      "label": "审核时间",
      "prop": "auditTime",
      "span": 12,
      // slot:true
    },
    
    	   ]
}
