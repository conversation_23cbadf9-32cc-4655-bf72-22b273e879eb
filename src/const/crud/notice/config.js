/*
 * @Description:
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-03-09 10:38:50
 * @LastEditors: zhangruiyang
 * @LastEditTime: 2022-03-10 19:05:01
 */
export const tableOption = {
  dialogWidth: "90%",
  menuWidth:330,
  index: true,
  indexLabel: "序号",
  indexFixed: false,
  selection: true,
  selectionFixed: false,
  stripe: false,
  tip: false,
  addTitle: "工位新增",
  editTitle: "工位编辑",
  searchMenuPosition: "right",
  searchMenuSpan: 12,
  addBtn: false,
  delBtn: false,
  viewBtn: false,
  column: [
    { label: "通知标题", prop: "noticeName", searchSpan: 6, search: true },
    {
      label: "通知类型",
      prop: "noticeType",
      type: "select",
      dicUrl: "/common/commDict/type/notice_type",
      props: {
        label: "label",
        value: "value",
      },
      searchSpan: 6,
      search: true,
    },
    // { label: "提交时间", prop: "submitTime" },
  ],
};
