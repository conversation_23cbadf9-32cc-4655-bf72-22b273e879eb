/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-04-23 09:42:04
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-05-15 15:41:50
 */
export const tableOption = {
  border: false,
  index: true,
  indexLabel: "序号",
  stripe: true,
  searchMenuPosition: "right",
  searchMenuSpan: 9,
  menuAlign: "center",
  align: "center",
  addTitle: "消息中心",
  // addBtnText: "发布公告",
  dialogWidth: "75%",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  column: [
    {
      type: "input",
      label: "标题",
      prop: "templateName",
      span: 12,
      // width: 180,
      search: true,
      // slot: true,
      overHidden: true,
      // slot: true,
      rules: [
        {
          required: true,
          message: "请填写公告标题",
          trigger: "blur",
        },
      ],
    },
    {
      type: "input",
      label: "内容",
      prop: "content",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      slot: true,
      width: 580,
    },
    {
      type: "input",
      label: "状态",
      prop: "mutualStatus",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      slot:true,
      width: 60,
    },
    {
      type: "datetime",
      label: "接收时间",
      prop: "sendTime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      search: true,
      width: 140,
    },
    {
      type: "input",
      label: "来源",
      prop: "clientName",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      slot: true,
      // width: 180,
    },
  ],
};
