
export const tableOption = {
    border: true,
    index: true,
    indexLabel: '序号',
    stripe: true,
    menuAlign: 'center',
    menuWidth: 150,
    align: 'center',
    refreshBtn: true,
    searchMenuSpan: 4,
    showClomnuBtn: false,
    searchSize: 'mini',
    menu: false,
    addBtn: false,
    editBtn: false,
    viewBtn: false,
    column: [
         {
            type: 'input',
            label: '应用名称',
            search: true,
            searchSpan: 4,
            width: 150,
            prop: 'clientName',
            searchLabelWidth:90,
        }, {
          type: 'input',
          label: '数据类型',
          width: 120,
          prop: 'dataType'
        }, {
          type: 'input',
          label: '推送内容',
          searchSpan: 5,
          prop: 'pushParam'
        }, {
            type: 'input',
            label: '推送结果',
            prop: 'pushStatus',
            search: true,
            searchSpan: 5,
            width: 80,
        },  {
            type: 'input',
            label: '结果报文',
            searchSpan: 5,
            prop: 'pushResult'
          }, {
            width: 200,
            label: '推送时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd',
            rangeSeparator: '-',
            search: true,
            searchRange: true
        }
    ]
}
