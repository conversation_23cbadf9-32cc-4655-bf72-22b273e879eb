/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-05-09 15:48:52
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-05-25 11:16:06
 */
export const tableOption = {
  border: false,
  index: true,
  indexLabel: "序号",
  stripe: true,
  searchMenuPosition: "right",
  searchMenuSpan: 9,
  menuAlign: "center",
  align: "center",
  addTitle: "发布公告",
  // addBtnText: "发布公告",
  dialogWidth: "75%",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  column: [
    // {
    //   type: "input",
    //   label: "模板编码",
    //   prop: "templateCode",
    //   addDisplay: false,
    //   editDisplay: false,
    //   span: 12,
    // },
    {
      type: "input",
      label: "模板名称",
      prop: "templateName",
      span: 12,
      // width: 180,
      search: true,
      slot: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: "请填写公告标题",
          trigger: "blur",
        },
      ],
    },
    {
      type: "input",
      label: "消息类型",
      prop: "messageType",
      addDisplay: false,
      editDisplay: false,
      span: 12,
    },
    {
      type: "input",
      label: "用户名",
      prop: "receiverName",
      search: true,
      span: 12,
      formatter: function (row, value, label, column) {
        if (value || value == 0) {
          return value;
        } else {
          return "--";
        }
      },
    },
    {
      type: "input",
      label: "手机号",
      prop: "mobile",
      search: true,
      span: 12,
      formatter: function (row, value, label, column) {
        if (value || value == 0) {
          return value;
        } else {
          return "--";
        }
      },
    },
    {
      type: "input",
      label: "openId",
      prop: "openId",
      span: 12,
      formatter: function (row, value, label, column) {
        if (value || value == 0) {
          return value;
        } else {
          return "--";
        }
      },
    },
    {
      type: "input",
      label: "通知内容",
      prop: "content",
      search: true,
      span: 12,
      overHidden: true,
    },
    {
      type: "input",
      label: "发送应用",
      prop: "clientName",
      span: 12,
    },

    {
      type: "select",
      label: "通知渠道",
      prop: "sendChannel",
      search: true,
      span: 12,
      dicData: [
        {
          label: "微信公众号",
          value: 2,
        },
        {
          label: "站内消息",
          value: 1,
        },
        {
          label: "短信",
          value: 3,
        },
      ],
    },
    // {
    //   label: "时间日期范围",
    //   type: "daterange",
    //   prop: "datetimerange",
    //   format: "yyyy-MM-dd HH:mm:ss",
    //   valueFormat: "yyyy-MM-dd HH:mm:ss",
    //   startPlaceholder: "时间日期开始范围自定义",
    //   endPlaceholder: "时间日期结束范围自定义",
    //   search: true,
    // },
    {
      type: "input",
      label: "发送时间",
      prop: "sendTime",
      search: true,
      span: 12,
    },
    {
      type: "input",
      label: "发送状态",
      prop: "sendStatus",
      span: 12,
    },
    {
      type: "input",
      label: "交互状态",
      prop: "mutualStatus",
      span: 12,
    },
  ],
};
