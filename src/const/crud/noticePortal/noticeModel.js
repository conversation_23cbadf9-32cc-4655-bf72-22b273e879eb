export const tableOption = {
  border: false,
  index: true,
  indexLabel: "序号",
  stripe: true,
  searchMenuPosition: "right",
  searchMenuSpan: 6,
  menuAlign: "center",
  align: "center",
  addTitle: "发布公告",
  // addBtnText: "发布公告",
  dialogWidth: "75%",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  column: [
    {
      type: "input",
      label: "模板编码",
      prop: "templateCode",
      span: 12,
      // width: 180,
      // search: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: "请填写公告标题",
          trigger: "blur",
        },
      ],
    },
    {
      type: "input",
      label: "模板名称",
      prop: "templateName",
      search: true,
      span: 24,
      // width: 180,
      // hide: true,
      // columnHide: true,
      overHidden: true,
      slot: true,
      rules: [
        {
          required: true,
          message: "请填写内容",
          trigger: "blur",
        },
      ],
    },
    {
      type: "input",
      label: "模板内容",
      prop: "messageContent",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      overHidden: true,
      // width: 180,
    },
    {
      type: "input",
      label: "通知渠道",
      prop: "sendChannelName",

      // search: true,
      addDisplay: false,
      editDisplay: false,
      span: 12,
      // width: 150,
    },
    {
      type: "select",
      label: "通知渠道",
      prop: "sendChannel",
      dicData: [
        {
          label: "微信公众号",
          value: 2,
        },
        {
          label: "站内消息",
          value: 1,
        },
        {
          label: "短信",
          value: 3,
        },
      ],
      search: true,
      addDisplay: false,
      editDisplay: false,
      span: 12,
      hide: true,
      // width: 150,
    },
    {
      type: "input",
      label: "消息类型",
      prop: "messageTypeName",
      // search: true,
      addDisplay: false,
      editDisplay: false,

      span: 12,
      // width: 180,
    },
    {
      type: "select",
      label: "消息类型",
      prop: "message_template_type",
      search: true,
      addDisplay: false,
      editDisplay: false,
      dicUrl: "/admin/sys_dict/type/message_template_type",
      span: 12,
      hide: true,
      dicData:[
        {
          label: "预警提醒",
          value: 1,
        },
        {
          label: "待办事项",
          value: 0,
        },
        {
          label: "其他消息",
          value: 2,
        },
      ]
      // width: 180,
    },
    {
      type: "input",
      label: "是否启用",
      prop: "status",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      slot: true,
      // width: 180,
    },
    {
      type: "input",
      label: "创建时间",
      prop: "createTime",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      // width: 180,
    },
  ],
};
