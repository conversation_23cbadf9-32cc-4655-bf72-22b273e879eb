export const tableOption = {
  border: false,
  index: true,
  indexLabel: "序号",
  stripe: true,
  searchMenuPosition: "right",
  searchMenuSpan: 9,
  menuAlign: "center",
  align: "center",
  addTitle: "发布公告",
  // addBtnText: "发布公告",
  dialogWidth: "75%",
  addBtn: true,
  editBtn: false,
  delBtn: false,
  column: [
    {
      type: "input",
      label: "公告标题",
      prop: "title",
      span: 12,
      // width: 180,
      search: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: "请填写公告标题",
          trigger: "blur",
        },
      ],
    },
    {
      type: "input",
      label: "内容",
      prop: "content",
      span: 24,
      // width: 180,
      // hide: true,
      // columnHide: true,
      // overHidden: true,
      slot: true,
      rules: [
        {
          required: true,
          message: "请填写内容",
          trigger: "blur",
        },
      ],
    },
    {
      type: "input",
      label: "是否启用",
      prop: "status",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      slot: true,
      // width: 180,
    },

    {
      type: "input",
      label: "创建人",
      prop: "createStaffName",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      // width: 180,
    },
    {
      type: "datetime",
      label: "创建时间",
      prop: "createTime",
      format: "yyyy-MM-dd HH:mm:ss",
      valueFormat: "yyyy-MM-dd HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      // width: 150,
    },
    {
      type: "input",
      label: "更新时间",
      prop: "updateTime",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      // width: 180,
    },
    {
      type: 'radio',
      label: "可见范围",
      prop: "visibleRange",
      span: 24,
      columnHide: true,
      hide: true,
      slot: true,
      dicData: [],
      rules: [
        {
          required: true,
          message: "请选择可见范围",
          trigger: "blue",
        },
      ],
    },
    {
      label: "上传附件",
      prop: "attachmentList",
      type: "upload",
      span: 12,
      display: true,
      showFileList: true,
      multiple: true,
      limit: 6,
      dataType: "string",
      propsHttp: {
        res: "data",
      },
      hide: true,
      columnHide: true,
      accept: "image/*,application/pdf",
      action: "/file/storage/upload",
      headers: {},
      listType: "text",
      slot: true,
    },
  ],
};
