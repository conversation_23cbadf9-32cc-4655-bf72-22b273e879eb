export const tableOption = {
  border: false,
  index: true,
  indexLabel: "序号",
  stripe: true,
  searchMenuPosition: "right",
  searchMenuSpan: 9,
  menuAlign: "center",
  align: "center",
  addTitle: "发布公告",
  // addBtnText: "发布公告",
  dialogWidth: "75%",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu:false,
  column: [
    {
      type: "input",
      label: "公告标题",
      prop: "title",
      span: 12,
      // width: 180,
      search: true,
      slot: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: "请填写公告标题",
          trigger: "blur",
        },
      ],
    },
    {
      type: "input",
      label: "创建人",
      prop: "createStaffName",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      // width: 180,
    },
    {
      type: "input",
      label: "状态",
      prop: "readStatus",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      slot:true
      // width: 180,
    },
    {
      type: "datetime",
      label: "创建时间",
      prop: "createTime",
      format: "yyyy-MM-dd hh:mm:ss",
      valueFormat: "yyyy-MM-dd hh:mm:ss",
      addDisplay: false,
      editDisplay: false,
      span: 12,
      // width: 150,
    },
  ],
};
