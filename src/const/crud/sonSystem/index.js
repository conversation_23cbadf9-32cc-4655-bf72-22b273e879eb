export const tableOption = {
  "border": false,
  "index": true,
  "indexLabel": "序号",
  "stripe": true,
  "menuAlign": "center",
  "align": "center",
  "searchMenuSpan": 6,
  addBtn: true,
  editBtn: true,
  delBtn: true,
  menuWidth: 260,
  menuHeaderAlign: "center",
  labelWidth: 100,
  "column": [
	  // {
    //   "type": "input",
    //   "label": "主键自增",
    //   "prop": "id",
    //   "span": 12
    // },	  
     
    {
      type: "input",
      label: "子系统名称",
      prop: "sysName",
      span: 18,
      overHidden: true,
      search: true,
  
      // width: 220,
      // searchSpan: 6,
      searchLabelWidth: "100px",
      rules: [
        {
          required: true,
          message: '请输入子系统名称',
          trigger: 'blur'
        }
      ]
      // width: "120px",
      // searchWidth:"120px",
      // labelWidth:"180px"
    },
    // {
    //   "type": "select",
    //   "label": "子系统编码",
    //   "prop": "articleType",
    //   "span": 12,
    //        props: {
    //       label: "dictLabel",
    //       value: "dictValue",
    //     },
    //   dicUrl: "/admin/sys_dict/type/portal_article_type",
    //   search: true,
    //   searchSpan: 4,
    // },
    {
      type: "input",
      label: "子系统编码",
      prop: "sysCode",
      span: 18,
      search: true,
      searchLabelWidth: "120px",
      rules: [
        {
          required: true,
          message: '请输入子系统编码',
          trigger: 'blur'
        }
      ]
      // slot:true
    },
    {
      type: "input",
      label: "系统路径",
      prop: "sysPath",
      span: 18,
      rules: [
        {
          required: true,
          message: '请输入系统路径',
          trigger: 'blur'
        }
      ]
      // slot:true
    },
   
    {
      type: "textarea",
      label: "说明",
      prop: "remark",
      span: 18,
      slot:true,
      maxlength:50
    },	
    
    	   ]
}
