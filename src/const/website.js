export default {
  year: "2022", // 首页显示的时间
  version: "3.5.1", // 控制首页显示的版本号
  logo: "tiny",   // 侧边栏搜索的时候顶部展示的文案
  key: "tiny", //配置主键,目前用于存储
  title: "",
  validateCode: false,//是否开启验证码校验
  register: true, //是否开启注册
  formLoginClient: 'admin:admin',// 用户名密码登录的 client 信息
  smsLoginClient: 'app:app',// 验证码登录的 client 信息
  remainingTime: 1800000, // token 剩余多少毫秒执行刷新
  whiteList: ["/login", "/404", "/401", "/lock"], // 配置无权限可以访问的页面
  whiteTagList: ["/login", "/404", "/401", "/lock"], // 配置不添加tags页面 （'/advanced-router/mutative-detail/*'——*为通配符）
  lockPage: "/lock",
  tokenTime: 6000,
  statusWhiteList: [428],
  // 配置首页可关闭
  isFirstPage: true,
  fistPage: {
    label: "首页",
    value: "/home/<USER>",
    value: "/main/home",
    // value: "/wel/index",
    params: {},
    query: {},
    group: [],
    close: false
  },
  // 配置菜单的属性
  menu: {
    props: {
      label: "label",
      name:"menuName",
      // 访问路径地址
      path: "path",
      // 组件地址
      component: "component",
      icon: "icon",
      children: "children"
    }
  }
};
