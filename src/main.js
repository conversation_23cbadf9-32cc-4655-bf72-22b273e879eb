import "babel-polyfill";
import "classlist-polyfill";
import Vue from "vue";
import axios from "./router/axios";
import VueAxios from "vue-axios";
import App from "./App";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import animate from "animate.css";
import Avue from "@smallwei/avue";
import "@smallwei/avue/lib/index.css";
import AvueFormDesign from "@sscfaith/avue-form-design";
import "./permission"; // 权限
import "./error"; // 日志
import router from "./router/router";
import store from "./store";
import * as filters from "./filters"; // 全局filter
import "./styles/common.scss";
import basicContainer from "./components/basic-container/main";
import RightToolbar from "./components/RightToolbar"
import Pagination from "@/components/Pagination";

import plugins from './plugins' // plugins
import webSocketGlobal from "@/WebSocketGlobal";
import scroll from 'vue-seamless-scroll'
Vue.use(scroll)
Vue.use(plugins)
// 字典标签组件
import DictTag from '@/components/DictTag'

// 导入全局字典匹配方法
import { loadStyle, downBlobFile, resetForm } from "@/util/util";
import { validatenull } from "@/util/validate";
import { DICT_TYPE, getDictData, getDictDataByType, getDictDatas2 } from '@/util/dict-util'

import CustDialog from "@/components/dialog/index.vue";


// 挂载常用全局方法，import 引入
Vue.prototype.validatenull = validatenull;
Vue.prototype.downBlobFile = downBlobFile;
Vue.prototype.getDictData = getDictData;
Vue.prototype.getDictDatas2 = getDictDatas2;
Vue.prototype.getDictDataByType = getDictDataByType;
Vue.prototype.DICT_TYPE = DICT_TYPE;
Vue.prototype.resetForm = resetForm;

// 挂载websocket到全局
Vue.prototype.$webSocketGlobal = webSocketGlobal;

window.axios = axios;
Vue.use(VueAxios, axios);

Vue.use(ElementUI, {
  size: "small",
  menuType: "text"
});

Vue.use(Avue, {
  size: "small",
  menuType: "text"
});

Vue.use(router);

Vue.use(animate);

Vue.use(AvueFormDesign);

/**
 * Vue.use与vue.component的区别
 * Vue.use是注册插件,而vue.component是注册组件.差别就像是Vue.use=只能穿戴,vue.component=手机.Vue.use比vue.component更强大,一般是由多个组件组成.
 */

// 注册全局容器
Vue.component("basicContainer", basicContainer);
Vue.component('DictTag', DictTag)
Vue.component('RightToolbar', RightToolbar)
Vue.component('Pagination', Pagination)
Vue.component("CustDialog", CustDialog);

// import TreeNode from '@/views/daohang/compontents/TreeNode.vue';
// Vue.component("TreeNode", TreeNode);
// Vue.directive('tree', {
//   bind(el, binding) {
//     const root = new Vue({
//       render: h => h(TreeNode, {props: {nodeData: binding.value}})
//     }).$mount();

//     el.appendChild(root.$el);
//   }
// });

//加载过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key]);
});

Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");

//设置主题为 DB2
document.body.className = "theme-iview";

Vue.prototype.customMethod = function () {
  let theType = localStorage.getItem("themeSelected");
  switch (theType) {
    case "0":
      document.body.className = "theme-iview";
      break;
    case "1":
      document.body.className = "theme-iview1"
      break;
    case "2":
      document.body.className = "theme-iview2"
      break;
    case "3":
      document.body.className = "theme-iview3"
      break;
    case "4":
      document.body.className = "theme-iview4"
      break;
    default:
      document.body.className = "theme-iview";
      break;
  }
}


Vue.prototype.$getUrlByProcess = function (value) {
  let url = process.env.VUE_APP_FILE_URL + value;
  if (process.env.NODE_ENV == "development") {
    if (window.location.host.includes("localhost:")) {
      url = process.env.VUE_APP_FILE_URL + value;
    } else {
      url = window.location.protocol + "//" + window.location.host + value;
    }
  } else {
    url = window.location.protocol + "//" + window.location.host + value;
  }
  // url = process.env.VUE_APP_FILE_URL + value;

  return url
}
Vue.prototype.$getVerifycodeUrlByProcess = function (value) {
  let url = process.env.VUE_APP_BASE_HOST + value;
  if (process.env.NODE_ENV == "development") {
    if (window.location.host.includes("localhost:")) {
      url = process.env.VUE_APP_BASE_HOST + value;
    }
  } else {
    url =
      window.location.protocol + "//" + window.location.host + value;
  }
  return url
}
