<template>
  <div class="mainWrap">
    <div class="block">
      <!-- <el-carousel
        height="270px"
        :loop="true"
        indicator-position="none"
        arrow="never"
      >
        <el-carousel-item v-for="(item, index) in bannerList" :key="index">
          <el-image
            class="img"
            style="width: 100%; height: 100%"
            :src="item.src"
          ></el-image>
        </el-carousel-item>
      </el-carousel> -->

      <div class="contentWrap">
        <div class="importNews">
          <div class="leftWrap">
            <el-carousel
              class="carousel"
              :loop="true"
              :autoplay="true"
              indicator-position="outside"
              arrow="never"
              height="340px"
            >
              <el-carousel-item
                v-for="(item, index) in newsBannerList"
                :key="index"
              >
                <div class="bannerWrap" @click="handleToDetail(item)">
                  <el-image
                    class="img"
                    fit="cover"
                    style="width: 100%; height: 100%"
                    :src="item.pic ? $getUrlByProcess(item.pic) : ''"
                  ></el-image>
                  <div class="titleWrap">{{ item.title }}</div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div class="text">
              <div class="titleWrap">
                <div class="titleLeft">
                  <el-tabs
                    @tab-click="(value) => handleClick(value, 'one')"
                    v-model="newsInfo.one.activeName"
                  >
                    <el-tab-pane
                      v-for="(item, index) in newsInfo.one.titleList"
                      :key="index"
                      :label="item.name"
                      :name="item.code"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="titleRight">
                  <el-button type="text" @click="handleMore('one')" class="btn"
                    >查看更多 ></el-button
                  >
                </div>
              </div>
              <InfoWrap :newsList="newsInfo.one.newsList"></InfoWrap>
            </div>
          </div>
          <!-- <Login class="rightWrap2"></Login> -->
        </div>
        <Special class="special"></Special>
      </div>
      <div class="footWrap">
        <div class="wrap">
          <div class="column">
            <div class="leftWrap">
              <div class="titleWrap">
                <div class="titleLeft">
                  <el-tabs
                    @tab-click="(value) => handleClick(value, 'two')"
                    v-model="newsInfo.two.activeName"
                  >
                    <el-tab-pane
                      v-for="(item, index) in newsInfo.two.titleList"
                      :key="index"
                      :label="item.name"
                      :name="item.code"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="titleRight">
                  <el-button type="text" @click="handleMore2('two')" class="btn"
                    >查看更多 ></el-button
                  >
                </div>
              </div>
              <InfoWrap :newsList="newsInfo.two.newsList" :type="1"></InfoWrap>
            </div>
            <div class="rightWrap">
              <div class="titleWrap">
                <div class="titleLeft">
                  <el-tabs
                    @tab-click="(value) => handleClick(value, 'three')"
                    v-model="newsInfo.three.activeName"
                  >
                    <el-tab-pane
                      v-for="(item, index) in newsInfo.three.titleList"
                      :key="index"
                      :label="item.name"
                      :name="item.code"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="titleRight">
                  <el-button
                    type="text"
                    @click="handleMore2('three')"
                    class="btn"
                    >查看更多 ></el-button
                  >
                </div>
              </div>
              <InfoWrap
                :newsList="newsInfo.three.newsList"
                :type="1"
              ></InfoWrap>
            </div>
          </div>
          <div class="column">
            <div class="leftWrap liyu3">
              <div class="titleWrap">
                <div class="titleLeft">
                  <el-tabs
                    @tab-click="(value) => handleClick(value, 'four')"
                    v-model="newsInfo.four.activeName"
                  >
                    <el-tab-pane
                      v-for="(item, index) in newsInfo.four.titleList"
                      :key="index"
                      :label="item.name"
                      :name="item.code"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="titleRight">
                  <el-button type="text" @click="handleMore('four')" class="btn"
                    >查看更多 ></el-button
                  >
                </div>
              </div>
              <InfoWrap :newsList="newsInfo.four.newsList"></InfoWrap>
            </div>
            <div class="rightWrap">
              <div class="titleWrap">
                <div class="titleLeft">
                  <el-tabs
                    @tab-click="(value) => handleClick(value, 'five')"
                    v-model="newsInfo.five.activeName"
                  >
                    <el-tab-pane
                      v-for="(item, index) in newsInfo.five.titleList"
                      :key="index"
                      :label="item.name"
                      :name="item.code"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="titleRight">
                  <el-button
                    type="text"
                    @click="handleMore2('five')"
                    class="btn"
                    >查看更多 ></el-button
                  >
                </div>
              </div>
              <InfoWrap :newsList="newsInfo.five.newsList"></InfoWrap>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import InfoWrap from "../components/InfoWrap";
import Special from "./components/Special";
import Login from "./components/Login";
import { categoryPage } from "@/api/gateway";
import { getBannerList, getNoLoginBannerList } from "@/api/admin/banner";

export default {
  components: {
    InfoWrap,
    Special,
    Login,
  },
  data() {
    return {
      bannerList: [
        {
          src: require("@/assets/banner.png"),
        },
      ],
      newsBannerList: [],
      newsInfo: {
        one: {
          activeName: "XWDT_SZYW",
          titleList: [
            {
              code: "XWDT_SZYW",
              name: "时政要闻",
            },
            {
              code: "XWDT_SJYW",
              name: "省局要闻",
            },
            {
              code: "XWDT_LDJH",
              name: "领导讲话",
            },
          ],
          newsList: [],
        },
        two: {
          activeName: "GGGS_TZGG",
          titleList: [
            {
              code: "GGGS_TZGG",
              name: "通知公告",
            },
            {
              code: "GGGS_MCDB",
              name: "明传电报",
            },
          ],
          newsList: [],
        },
        three: {
          activeName: "GGGS_FW",
          titleList: [
            {
              code: "GGGS_FW",
              name: "发文",
            },
            {
              code: "GGGS_LW",
              name: "来文",
            },
          ],
          newsList: [],
        },
        four: {
          activeName: "XWDT_PAJS",
          titleList: [
            {
              code: "XWDT_PAJS",
              name: "平安建设",
            },
          ],
          newsList: [],
        },
        five: {
          activeName: "GGGS_JYJL",
          titleList: [
            {
              code: "GGGS_JYJL",
              name: "经验交流",
            },
          ],
          newsList: [],
        },
      },
    };
  },
  mounted() {
    let arr = Object.keys(this.newsInfo);
    arr.forEach((item) => {
      this.getData({ code: this.newsInfo[item].activeName }, item);
    });
  },

  methods: {
    handleToDetail(item) {
      if (item.type == 0) {
        this.$router.push("/gateway/news/detail?id=" + item.articleId);
      } else {
        if (item.webUrl) {
          var newOpen = window.open();
          newOpen.opener = null;
          newOpen.location = item.webUrl;
        }
      }
      // this.$router.push("/gateway/news/detail?id=" + item.id);
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
    },
    async getBanner() {
      // let res = await getBannerList();
      let res = await getNoLoginBannerList();
      console.log("banner----", res.data.data.records);
      this.newsBannerList = res.data.data.records;
      // this.newsBannerList = this.newsInfo.one.newsList.filter((item) => {
      //   return item.pic;
      // });
      // console.log(this.newsBannerList, 26222);
    },
    handleClick(value, key) {
      this.getData(
        {
          code: value.name,
        },
        key
      );
    },
    async getData(data, value) {
      let res = await categoryPage({
        ...data,
        current: 1,
        size: 8,
      });
      this.newsInfo[value].newsList = res.data.data.records;
      if (value == "one") {
        this.getBanner();
      }
    },
    handleMore(value) {
      this.$router.push(
        `/gateway/news?type=${this.newsInfo[value].activeName}`
      );
    },
    handleMore2(value) {
      this.$router.push(
        `/gateway/publicity?type=${this.newsInfo[value].activeName}`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.mainWrap {
  box-sizing: border-box;
  .block {
    .contentWrap {
      width: 1320px;
      margin: 0 auto;
      .importNews {
        width: 100%;
        margin-top: 40px;

        display: flex;
        height: 384px;
        overflow: hidden;
        justify-content: space-between;
        .leftWrap {
          // width: 900px;
          width: 100%;
          padding: 20px;
          box-sizing: border-box;
          // margin-right: 20px;
          background: #ffffff;
          display: flex;
          border-radius: 4px 4px 4px 4px;
          justify-content: space-between;
          .carousel {
            // width: 380px;
            width: calc(50% - 20px);
            .bannerWrap {
              width: 100%;
              height: 100%;
              position: relative;
              cursor: pointer;
              .img {
                border-radius: 4px;
              }
              .titleWrap {
                position: absolute;
                width: 100%;
                // background: red;

                background: rgba(0, 6, 40, 0.35);
                color: #fff;
                z-index: 1000;
                white-space: nowrap;
                text-overflow: ellipsis;
                bottom: 0;
                padding: 5px;
                box-sizing: border-box;
              }
            }
          }
          .text {
            // width: calc(100% - 400px);
            width: 50%;
            .titleWrap {
              display: flex;
              justify-content: space-between;
              border-bottom: 1px solid #e4e7ed;
              .titleLeft {
                display: flex;
                .title {
                  font-size: 22px;
                  font-weight: bold;
                  margin-right: 20px;
                }
              }
            }
          }
        }
        .rightWrap {
          padding: 20px;
          box-sizing: border-box;
          width: calc(100% - 900px);
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          height: 386px;
        }
        .rightWrap2 {
          padding: 10px 20px;
          box-sizing: border-box;
          width: calc(100% - 900px);
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          height: 386px;
        }
      }
      .special {
        width: 100%;
        height: 324px;
        background: #ffffff;
        margin-top: 20px;
        border-radius: 4px 4px 4px 4px;
        padding: 20px 20px;
        box-sizing: border-box;
      }
    }
    .footWrap {
      width: 100%;
      // background: #ffffff;
      padding: 20px 0 0;
      // margin-top: 40px;
      .wrap {
        width: 1320px;
        margin: 0 auto;

        .column {
          width: 100%;
          display: flex;
          justify-content: space-between;
          padding-bottom: 20px;
          .leftWrap {
            padding: 20px;
            box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.6);
            border-radius: 4px 4px 4px 4px;
            box-sizing: border-box;
            width: 650px;
            background: #ffffff;
            height: 380px;
            .titleWrap {
              display: flex;
              justify-content: space-between;
              border-bottom: 1px solid #e4e7ed;
              .titleLeft {
                display: flex;
                .title {
                  font-size: 22px;
                  font-weight: bold;
                  margin-right: 20px;
                }
              }
            }
          }
          .rightWrap {
            box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.6);
            border-radius: 4px 4px 4px 4px;
            .titleWrap {
              display: flex;
              justify-content: space-between;
              border-bottom: 1px solid #e4e7ed;
              .titleLeft {
                display: flex;
                .title {
                  font-size: 22px;
                  font-weight: bold;
                  margin-right: 20px;
                }
              }
            }
            padding: 20px;
            box-sizing: border-box;
            width: 650px;
            background: #ffffff;
            height: 380px;
          }
        }
      }
    }
  }
}

.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

::v-deep .el-tabs__header {
  margin: 0 !important;
}
// ::v-deep .el-carousel__indicators--horizontal {
//   bottom: 6px;
//   width: 100%;
//   display: flex;
//   // flex-direction: row-reverse;
//   // left: 47% !important;
//   margin-left: 85%;
// }
// ::v-deep .el-carousel__button {
//   background-color: white;
//   width: 6px;
//   height: 6px;
//   opacity: 1;
// }
// ::v-deep .el-carousel__indicator.is-active button {
//   background-color: blue;
// }
.carousel {
  ::v-deep .el-carousel__button {
    background-color: white;
    width: 6px;
    height: 6px;
    opacity: 1;
  }
  ::v-deep .el-carousel__indicator.is-active button {
    background-color: blue;
  }
  ::v-deep .el-carousel__indicators--outside {
    position: absolute;
    bottom: 2px;
    right: 20px;
    text-align: right;
  }
}
::v-deep .el-tabs__nav-wrap::after {
  height: 0 !important;
}
::v-deep .el-tabs__active-bar {
  height: 3px;
}
::v-deep .el-tabs__item {
  font-size: 18px;
  font-weight: bold;
}
::v-deep .is-active {
  color: #0357ca;
}
.btn {
  font-weight: bold;
  color: #0357ca !important;
  font-size: 16px;
}
// ::v-deep .el-carousel__arrow {
//   display: none !important;
// }
</style>
