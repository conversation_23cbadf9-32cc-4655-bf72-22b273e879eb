<template>
  <div class="carouselBox">
    <div class="top">专题专栏</div>
    <el-carousel :loop="false" :autoplay="false" class="carousel1">
      <el-carousel-item
        class="el-car-item"
        v-for="(list, index) in dataList"
        :key="index"
      >
        <div
          @click="handleClick(imgList)"
          v-for="(imgList, index1) in list"
          :key="index1"
        >
          <img class="img" :src="imgList.img" />
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataList: [
        {
          img: require("@/assets/a.png"),
          title: "标题",
          code: "ZTJY",
        },
        {
          img: require("@/assets/b.png"),
          title: "标题",
          code: "NLZFJSN",
        },
      ],
    };
  },
  mounted() {
    this.byEvents();
  },
  methods: {
    handleClick(data) {
      console.log(data, 4222);
      this.$router.push(`/gateway/subject?code=${data.code}`);
    },
    byEvents() {
      let newDataList = [];
      let current = 0;
      if (this.dataList && this.dataList.length > 0) {
        for (let i = 0; i <= this.dataList.length - 1; i++) {
          if (i % 2 !== 0 || i === 0) {
            //数据处理成几张展示
            if (!newDataList[current]) {
              newDataList.push([this.dataList[i]]);
            } else {
              newDataList[current].push(this.dataList[i]);
            }
          } else {
            current++;
            newDataList.push([this.dataList[i]]);
          }
        }
      }
      this.dataList = [...newDataList];
    },
  },
};
</script>
<style lang="scss" scoped>
.carouselBox {
  width: calc(100%);
  height: 100%;
  overflow: hidden;
  .top {
    font-size: 28px;
    font-weight: bold;
  }
  .carousel {
    width: 100%;
    height: 200px;
    margin-top: 20px;
    overflow: hidden;
  }
  .el-car-item {
    width: 100%;
    display: flex;
    height: 100%;
    overflow: hidden;
    justify-content: space-between;
    .img {
      width: calc(100% - 5px);
      height: 200px;
      overflow: hidden;
      cursor: pointer;
    }
  }
}
</style>

