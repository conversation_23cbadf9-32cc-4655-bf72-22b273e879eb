<template>
  <div class="content">
    <div class="headerWrap lyheaderWrap">
      <div class="header">
        <div class="logo">
          <!-- <template v-if="logoObj && logoObj.sysLogo">
            <img class="img m-r-20" :src="$getUrlByProcess(logoObj.sysLogo)" />
          </template>
          <img v-else class="img m-r-20" src="@/assets/logo.jpg" /> -->

          {{ logoObj && logoObj.sysTitle ? logoObj.sysTitle : website.title }}
        </div>

        <el-menu :default-active="activeIndex" class="el-menu-demo" text-color="#fff" mode="horizontal" @select="handleSelect">
          <template v-for="(item, index) in routerList">
            <el-submenu popper-class="my-popmenu" :index="item.routeUrl" :key="index" v-if="item.children && item.children.length > 0" :class="{ 'is-active': activeIndex == item.routeUrl }">
              <template slot="title">
                <span class="m-title" @click="handleClickTitle(item)" :title="item.name">
                  {{ item.name }}
                </span>
              </template>

              <template v-for="(mItem, i) in item.children">
                <el-submenu class="my-submenu2" :index="mItem.routeUrl" :key="i + mItem.routeUrl" v-if="mItem.children && mItem.children.length > 0">
                  <template slot="title">{{ mItem.name }}</template>
                  <!-- <el-menu-item index="2-4-1">选项1</el-menu-item> -->

                  <template v-for="(nItem, j) in mItem.children">
                    <el-submenu :index="nItem.routeUrl" :key="j + nItem.routeUrl" v-if="nItem.children && nItem.children.length > 0">
                      <template slot="title">{{ nItem.name }}</template>
                    </el-submenu>
                    <el-menu-item v-else :index="nItem.routeUrl" :key="j + nItem.routeUrl">{{ nItem.name }}</el-menu-item>
                  </template>

                  <!-- ------------------------ -->
                </el-submenu>
                <el-menu-item v-else :index="mItem.routeUrl" :key="i + mItem.routeUrl">{{ mItem.name }}</el-menu-item>
              </template>
            </el-submenu>
            <el-menu-item :key="index" :index="item.routeUrl" v-else>{{
              item.name
            }}</el-menu-item>
          </template>
        </el-menu>
        <div>
          <div class="login-btn" @click="ssoLogin" style="color: #fff" v-if="!loginFlag">
            登录
          </div>
          <div class="login-btn" v-else>
            <el-dropdown>
              <div style="
                  font-size: 14px;

                  display: flex;
                  justify-content: center;
                  align-items: center;
                  color: #fff;
                ">
                {{ sysUserObj.nickName }}
                <i class="el-icon-caret-bottom"></i>
                <!-- <img
                  style="width: 16px; height: 16px"
                  src="@/assets/image/common/sanjiao.png"
                  alt=""
                /> -->
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="handleGoGongzuotai">进入工作台
                </el-dropdown-item>

                <el-dropdown-item @click.native="handleLogout" divided>退出系统
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    <keep-alive>
      <router-view v-if="$route.meta.$keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.$keepAlive" />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { getNoLoginMenuList } from "@/api/admin/sys/portalMenu";
import { getFetchList } from "@/api/admin/sys/system";
import { getUserInfo } from "@/api/login";
export default {
  data() {
    return {
      logoObj: {},
      activeIndex: "/gateway/home",
      loginFlag: false,
      sysUserObj: {},
      routerList: [
        // {
        //   routeUrl: "/gateway/home",
        //   name: "首页",
        //   // component: () => import("@/views/gateway/home"),
        // },
        // {
        //   routeUrl: "/gateway/news",
        //   name: "新闻动态",
        //   // component: () => import("@/views/gateway/news"),
        // },
        // {
        //   routeUrl: "/gateway/publicity",
        //   name: "公示公告",
        //   // component: () => import("@/views/gateway/publicity"),
        // },
        // {
        //   path: "/gateway/policy",
        //   name: "政策法规",
        //   component: () => import("@/views/gateway/policy"),
        // },
      ],
    };
  },
  computed: {
    ...mapGetters(["website"]),
    showPortal() {
      return this.vaildData(this.permissions.portal_button, false);
    },
    showUserCenter() {
      return this.vaildData(this.permissions.user_center_button, false);
    },
    themeClass() {
      return `theme-${this.theme}`;
    },
  },
  created() {
    if (process.env.NODE_ENV == "development") {
      // console.log("route==", this.$route);
      if (this.$route && this.$route.query && this.$route.query.token) {
        this.$store.commit("SET_ACCESS_TOKEN", this.$route.query.token);
      }
    }
    this.handleGetRouteList();
    this.getUserInfoDataFn();
    this.getLogoFn();
  },
  mounted() {
    // this.axiosTest();
    console.log(this.$route.path, this.activeIndex, 611111);
    if (this.$route.path != this.activeIndex) {
      this.activeIndex = this.$route.path;
    }
    document.addEventListener("visibilitychange", this.visibilityState); //监听页面显隐
  },
  beforeDestroy() {
    document.removeEventListener("visibilitychange", this.visibilityState); //移除监听
  },
  methods: {
    visibilityState(e) {
      let visibilityState = e.target.visibilityState;
      switch (visibilityState) {
        case "visible": //显示
          // console.log("显示了---");
          this.getUserInfoDataFn();
          break;
        case "hidden": //隐藏
          break;
        default:
      }
    },
    getUserInfoDataFn() {
      getUserInfo().then((res) => {
        // console.log("res=login==获取用户信息=22=", res);
        if (!res) {
          localStorage.removeItem("userInfo");
          this.userInfo = null;
          this.loginFlag = false;
          this.sysUserObj = {};
        } else {
          if (res.data.code == 200) {
            this.loginFlag = true;
            this.sysUserObj = res.data.data.sysUser;
          }
        }
      });
    },
    handleGoGongzuotai() {
      this.$router.push("/home/<USER>");
    },
    change_icon(iconUrl) {
      const changeFavicon = (link) => {
        let $favicon = document.querySelector('link[rel="icon"]');
        if ($favicon !== null) {
          $favicon.href = link;
        } else {
          $favicon = document.createElement("link");
          $favicon.rel = "icon";
          $favicon.href = link;
          document.head.appendChild($favicon);
        }
      };
      // 设置图标地址
      // let iconUrl = `./icoImg/1.ico`;
      // 动态修改网站图标
      changeFavicon(iconUrl);
    },
    getLogoFn() {
      // console.log("getListgetListgetList==");

      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        // console.log("logoObj==", response);
        if (response.data.code == 200) {
          this.logoObj = response.data.data || {};
          if (this.logoObj && this.logoObj.sysIcon) {
            let path = this.$getUrlByProcess(this.logoObj.sysIcon);
            this.change_icon(path);

            this.$store.commit("SET_LOGO_OBJ", this.logoObj);
          }
          if (this.logoObj && this.logoObj.sysTitle) {
            document.title = this.logoObj.sysTitle;
          }
        }
      });
    },
    handleGetRouteList() {
      getNoLoginMenuList().then((res) => {
        console.log("获取路由res==", res);
        if (res.data.code == 200) {
          this.routerList = res.data.data;
          console.log("this.routerList==", this.routerList);
        }
      });
    },
    // axiosTest() {
    //   console.log("data==lzs=---------axiosTest------=");
    //   // //创建ajax对象;
    //   var xhr = new XMLHttpRequest();
    //   // 配置ajax对象
    //   // xhr.open("post", "http://localhost:9898/camera");
    //   xhr.open(
    //     "get",
    //     "/portal/portal/article/api/article/page?code=GGGS_FW&current=1&size=5",
    //     true
    //   );
    //   xhr.setRequestHeader(
    //     "Authorization",
    //     "Bearer 11e8a8a106d04061946acfbf5e4dbe4a"
    //   );
    //   xhr.send(); //JSON.stringify(data)

    //   let that = this;
    //   xhr.onload = () => {
    //     console.log("data==lzs=---mmmm------------=", xhr);
    //     let data = JSON.parse(xhr.responseText);
    //     console.log("data==lzs=---------------=", data);
    //   };
    // },
    handleSelect(key, keyPath) {
      console.log("key====", key);

      this.activeIndex = key;
      this.$router.push(key);
      // this.$router.push("/gateway/news");
    },
    handleClickTitle(row) {
      // console.log("row---", row);
      this.activeIndex = row.routeUrl;
      // console.log("activeIndex==", this.activeIndex);
      this.$router.push(row.routeUrl);
    },
    ssoLogin() {
      this.$router.push({ path: "/sso" });
      // this.$router.push({ path: "/login" });
    },
    handleLogout() {
      this.$store.dispatch("LogOut").then(() => {
        // this.$router.push({ path: "/" });
        localStorage.clear();
        this.userInfo = {};
        this.$message.success("退出成功！");

        this.loginFlag = false;
        this.sysUserObj = {};
      });
      // localStorage.clear();
    },
  },
  watch: {
    $route(newRoute) {
      if (this.$route.path != newRoute) {
        this.activeIndex = this.$route.path;
      }
      console.log(newRoute, 7552);
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  background: #eef2fb;
  .headerTop {
    background: #ffffff;
    .top {
      width: 1320px;
      height: 70px;
      margin: 0 auto;
      line-height: 70px;
      font-size: 28px;
      font-weight: bold;
    }
  }
  .headerWrap {
    width: 100%;
    height: 60px;
    background: linear-gradient(90deg, #0357ca 0%, #4398ff 100%);
    .header {
      width: 1320px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .logo {
        height: 100%;
        line-height: 60px;
        color: #ffffff;
        font-size: 28px;
        display: flex;
        align-items: center;
        .img {
          height: 40px;
          width: auto;
          margin-right: 10px;
        }
      }
      .m-title {
        display: inline-block;
        height: 60px;
        line-height: 60px;
        font-size: 16px;
        font-weight: bold;
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
::v-deep .header .is-active {
  background: #3478ff !important;
  border-bottom: #3478ff !important;
  color: #ffffff !important;
}
::v-deep .header .el-menu-item {
  font-size: 16px;
  font-weight: bold;
  max-width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
::v-deep .header .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover {
  background: #3478ff !important;
}
::v-deep .header .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus {
  background: #3478ff !important;
}

// ::v-deep .el-menu--horizontal .el-menu .el-menu-item,
// .el-menu--horizontal .el-menu .el-submenu__title {
//   background-color: #666 !important;
// }
// ::v-deep .el-submenu__title {
//   background-color: #666 !important;
// }

::v-deep .el-submenu:hover {
  background: #3478ff !important;
}
// ::v-deep .el-menu--popup .el-menu-item {
//   background-color: #001525 !important;
//   color: #666;
//   &:hover {
//     background-color: #666;
//   }
// }
.my-popmenu {
  position: relative;

  .el-menu--popup .el-menu-item {
    background-color: #fff !important;
    color: #3272ce !important;
    /* &:hover {
    background-color: #666;
  } */
  }
}
// .my-submenu2 {
//   .el-submenu__title {
//     color: #3272ce !important;
//   }
// }
.login-btn {
  cursor: pointer;
  color: #fff;
}
</style>
<style lang="scss">
.my-popmenu .el-menu--popup {
  min-width: 100px !important;
}
.my-submenu2 {
  .el-submenu__title {
    color: #3272ce !important;
  }
}
</style>

