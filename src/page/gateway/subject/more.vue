<template>
    <div class="main">
        <div class="contentTop">
            <img :src="imgSrc" alt="图片" />
        </div>
        <div class="breadcrumb-nav">
            <span>当前位置：</span>
            <router-link to="/gateway/home">
                <span>首页</span>
            </router-link>
            <span style="margin: 0 10px">></span>
            <router-link to="/gateway/subject?code=ZTJY" v-if="this.paramsCode == 'ZTJY'">
                <span>专题专栏</span>
            </router-link>
            <router-link to="/gateway/subject?code=NLZFJSN" v-if="this.paramsCode == 'NLZFJSN'">
                <span>专题专栏</span>
            </router-link>

            <span style="margin: 0 10px">></span>
            <span v-if="queryRouter == 'ZTJY'">主题教育</span>
            <span v-else>能力作风建设年</span>
        </div>
        <div class="contentCenter">
            <div class="leftMun">
                <div v-for="(item, index) in meunArr" :key="index" class="left-list"
                    :class="{ active: currentIndex === index }" @click="classificationFn(index, item)">
                    <span>{{ item.name }}</span>
                </div>
            </div>
            <div class="rightMain" v-if="rightContentArr && rightContentArr.length > 0">
                <div class="rightContent" v-for="(item, index) in rightContentArr" :key="index" @click="detailFn(item)">
                    <div class="left">

                        <img :src="item.pic ? $getUrlByProcess(item.pic) : ''" alt="图片" />
                    </div>
                    <div class="right">
                        <div class="title" :title="item.title">{{ item.title }}</div>
                        <div class="titleList" :title="item.memo">{{ item.memo }}</div>
                        <div class="titleBottom">
                            <div class="titleBottomLeft">
                                <div style="margin-right: 12px">{{ item.source }}</div>
                                <div>{{ item.createTime }}</div>
                            </div>
                            <div class="titleBottomRight">
                                <span style="margin-right: 4px">阅读</span>
                                {{ item.viewNum }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="paginationStyle">
                    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page.sync="currentPage" :page-size="pageSize" layout="total, prev, pager, next, jumper"
                        :total="totalPage">
                    </el-pagination>
                </div>
            </div>
            <div style="margin: 0 auto" v-if="rightContentArr.length == 0">
                <el-empty description="暂无数据" style="margin: 40px 0" />
            </div>
        </div>
    </div>
</template>
<script>
    import {
        categoryAll,
        categoryCode,
        categoryChild,
        categoryPage,
    } from "@/api/gateway/index";
    export default {
        data() {
            return {
                rightContentArr: [],
                imgSrc: require("@/assets/image/news/banner.png"),
                newsList: [],
                queryRouter: "",
                paramsCode: "",
                meunArr: [],
                currentIndex: 0
            };
        },
        created() {
            let params = this.$route.query;
            this.queryRouter = this.$route.query.code;
            this.paramsCode = this.$route.query.paramsCode;
            this.categoryCodeFn(params.paramsCode);
            if (params.code == "ZTJY") {
                this.imgSrc = require("@/assets/image/news/banner1.png");
            } else {
                this.imgSrc = require("@/assets/image/news/banner.png");
            }
        },
        methods: {
            // 根据栏目编码查询子栏目列表
            categoryCodeFn(code) {
                categoryCode(code)
                    .then((response) => {
                        let paramsArr = response.data.data;
                        this.meunArr = paramsArr;
                        if (this.$route.query.code) {
                            this.meunArr.forEach((item, index) => {
                                if (item.code == this.$route.query.code) {
                                    this.currentIndex = index
                                    this.categoryPageFn(this.$route.query.code);
                                }
                            })
                        } else {
                            if (this.meunArr.length > 0) {
                                this.categoryPageFn(this.meunArr[0].code);
                            }
                        }
                    })
                    .catch(() => { });
            },
            classificationFn(index, item) {
                // if (index == this.currentIndex) return;
                this.currentIndex = index;
                this.categoryPageFn(item.code);
                this.code = item.code;
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
                this.categoryPageFn(this.code, val);
            },
            // 分页查询内容
            categoryPageFn(code, val) {
                var params = {
                    current: val ? val : this.currentPage,
                    size: this.pageSize,
                    code: code ? code : this.meunArr[0].code,
                };
                categoryPage(params)
                    .then((response) => {
                        this.rightContentArr = response.data.data.records;
                        this.totalPage = Number(response.data.data.total)
                            ? Number(response.data.data.total)
                            : 0;
                    })
                    .catch(() => { });
            },
            detailFn(item) {
                console.log(item);
                this.$router.push({
                    path: "/gateway/news/detail",
                    query: {
                        id: item.id,
                    },
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .main {
        .contentTop {
            width: 100%;
            height: 330px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .breadcrumb-nav {
            margin: 15px 300px 15px 300px;
        }

        .contentCenter {
            margin: 15px 300px 15px 300px;
            display: flex;

            .leftMun {
                width: 230px;
                min-height: calc(100vh - 120px);
                background: #ffffff;
                box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
                border-radius: 6px 6px 6px 6px;
                opacity: 1;
                padding: 10px;

                .active {
                    background: url("~@/assets/image/bg-menu.png") 0 0 no-repeat;
                    background-size: cover;

                    span {
                        color: #ffffff !important;
                    }
                }

                .left-list {
                    cursor: pointer;
                    height: 80px;
                    border-radius: 12px 12px 12px 12px;
                    background-color: #f2f6ff;
                    margin-bottom: 11px;
                    line-height: 80px;
                    text-align: center;
                    padding: 0 5px;

                    span {
                        font-size: 16px;
                        font-family: PingFang SC-Medium, PingFang SC;
                        font-weight: 400;
                        color: #00102f;

                        width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        display: inline-block;
                    }
                }
            }

            .leftMun::-webkit-scrollbar {
                display: none;
            }

            .rightMain {
                width: calc(100% - 243px);
                padding: 10px 20px;
                background: #ffffff;
                box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.8);
                border-radius: 4px 4px 4px 4px;
                opacity: 1;

                .rightContent {
                    padding: 20px;
                    border-bottom: 1px solid #e8e8e8;
                    margin-top: 5px;
                    cursor: pointer;
                    display: flex;

                    .left {
                        width: 173px;
                        height: 108px;
                        border-radius: 0px 0px 0px 0px;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .right {
                        width: calc(100% - 190px);
                        flex: 1;
                        margin-left: 15px;

                        .title {
                            font-size: 16px;
                            font-family: PingFang SC, PingFang SC;
                            font-weight: 700;
                            color: #333333;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }

                        .titleList {
                            font-size: 14px;
                            font-family: PingFang SC, PingFang SC;
                            font-weight: 400;
                            color: #666666;
                            margin: 12px 0;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 3;
                            -webkit-box-orient: vertical;
                        }

                        .titleBottom {
                            display: flex;
                            justify-content: space-between;
                            font-size: 12px;
                            font-family: PingFang SC, PingFang SC;
                            font-weight: 400;
                            color: #999999;

                            .titleBottomLeft {
                                display: flex;
                            }

                            .titleBottomRight {
                                float: right;
                            }
                        }
                    }
                }

                .paginationStyle {
                    margin-top: 50px;
                    text-align: center;
                }
            }

            .rightMain::-webkit-scrollbar {
                display: none;
            }
        }
    }
</style>
