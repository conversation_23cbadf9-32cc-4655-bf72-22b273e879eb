<template>
  <div class="menu-wrapper">
    <template v-for="item in menu">
      <el-menu-item
        v-if="validatenull(item[childrenKey]) && vaildRoles(item)"
        :index="item.path + ''"
        :class="{ 'is-active': vaildAvtive(item) }"
        @click="open(item)"
        :key="item[labelKey]"
      >
        <i :class="item[iconKey]"></i>
        <span slot="title" :alt="item[pathKey]">{{ item[labelKey] }}</span>
      </el-menu-item>
      <el-submenu
        v-else-if="!validatenull(item[childrenKey]) && vaildRoles(item)"
        :index="item.path + ''"
        :key="item[labelKey]"
      >
        <template slot="title">
          <i :class="item[iconKey]"></i>
          <span
            slot="title"
            :class="{ 'el-menu--display': collapse && first }"
            >{{ item[labelKey] }}</span
          >
        </template>
        <template v-for="(child, cindex) in item[childrenKey]">
          <el-menu-item
            :index="child[pathKey] + cindex + ''"
            :class="{ 'is-active': vaildAvtive(child) }"
            @click="open(child)"
            v-if="validatenull(child[childrenKey])"
            :key="child[labelKey]"
          >
            <i :class="child[iconKey]"></i>
            <span slot="title">{{ child[labelKey] }}</span>
          </el-menu-item>
          <sidebar-item
            v-else
            :menu="[child]"
            :key="cindex"
            :props="props"
            :screen="screen"
            :collapse="collapse"
          ></sidebar-item>
        </template>
      </el-submenu>
    </template>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { validatenull } from "@/util/validate";
import config from "./config.js";

export default {
  name: "sidebarItem",
  data() {
    return {
      config: config,
    };
  },
  props: {
    menu: {
      type: Array,
    },
    screen: {
      type: Number,
    },
    first: {
      type: Boolean,
      default: false,
    },
    props: {
      type: Object,
      default: () => {
        return {};
      },
    },
    collapse: {
      type: Boolean,
    },
  },
  created() {},
  mounted() {},
  computed: {
    ...mapGetters(["roles"]),
    labelKey() {
      return this.props.label || this.config.propsDefault.label;
    },
    pathKey() {
      return this.props.path || this.config.propsDefault.path;
    },
    iconKey() {
      return this.props.icon || this.config.propsDefault.icon;
    },
    childrenKey() {
      return this.props.children || this.config.propsDefault.children;
    },
    nowTagValue() {
      return this.$router.$avueRouter.getValue(this.$route);
    },
  },
  methods: {
    vaildAvtive(item) {
      // console.log("this.nowTagValue==", this.nowTagValue);
      const groupFlag = (item["group"] || []).some((ele) =>
        this.$route.path.includes(ele)
      );

      return this.nowTagValue === item[this.pathKey] || groupFlag;
    },
    vaildRoles(item) {
      item.meta = item.meta || {};
      return item.meta.roles ? item.meta.roles.includes(this.roles) : true;
    },
    validatenull(val) {
      return validatenull(val);
    },
    open(item) {
      console.log("item==", item);
      // if (item.name == "首页" || item.path == "/home2/index") {
      //   this.$router.push("/home/<USER>");
      //   return;
      // }
      if (this.screen <= 1) this.$store.commit("SET_COLLAPSE");
      this.$router.$avueRouter.group = item.group;
      console.log("菜单配置的路由参数", item.query);
      let params = item.query ? JSON.parse(item.query) : null;
      console.log("JSON化后的菜单配置的路由参数", params);

      this.$router
        .push({
          path: this.$router.$avueRouter.getPath({
            name: item[this.labelKey],
            src: item[this.pathKey],
          }),
          query: params,
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
// .avue-sidebar .el-submenu .el-menu-item.is-active:hover {
//   // background-color: rgba(0, 0, 0, 0.8);
// }
.avue-sidebar .el-submenu .el-menu-item.is-active {
  background: linear-gradient(90deg, #172b8a 0%, #009ee7 100%);
}
.el-menu-item.is-active {
  background: linear-gradient(90deg, #172b8a 0%, #009ee7 100%);
}
.el-menu-item {
  &:before {
    display: none;
  }
}
</style>

