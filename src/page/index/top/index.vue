<template>
  <div class="avue-top ac-top">
    <div class="top-bar__left" v-if="isPortal">
      <div
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          cursor: pointer;
        "
        @click="goIndexPage"
      >
        <div v-if="logoObject && logoObject.sysLogo">
          <img class="in-logo" :src="$getUrlByProcess(logoObject.sysLogo)" />
        </div>
        <div v-else class="zs-logo"></div>
        <div style="margin-left: 10px; font-weight: bold; font-size: 22px">
          {{ logoObject.sysTitle ? logoObject.sysTitle : website.title }}
        </div>
      </div>
    </div>
    <div class="top-bar__left" v-if="!isPortal">
      <div
        class="avue-breadcrumb"
        :class="[{ 'avue-breadcrumb--active': isCollapse }]"
        v-if="showCollapse"
      >
        <i class="icon-navicon" @click="setCollapse"></i>
      </div>
    </div>
    <div class="top-bar__title">
      <div class="top-bar__item top-bar__item--show" v-if="showMenu">
        <top-menu></top-menu>
      </div>
    </div>
    <div class="top-bar__right">
      <el-tooltip
        v-if="showLock"
        effect="dark"
        content="锁屏"
        placement="bottom"
      >
      </el-tooltip>
      <el-tooltip
        v-if="showFullScren"
        effect="dark"
        :content="isFullScreen ? '退出全屏' : '全屏'"
        placement="bottom"
      >
        <div class="top-bar__item">
          <i
            :class="isFullScreen ? 'icon-zuixiaohua' : 'icon-quanpingzuidahua'"
            @click="handleScreen"
          ></i>
        </div>
      </el-tooltip>
      <el-dropdown v-if="isPortal && (showPortal || showUserCenter)">
        <div
          style="
            font-size: 14px;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 16px;
            cursor: pointer;
          "
        >
          <img
            style="width: 16px; height: 16px"
            src="@/assets/image/common/p_shezhi.png"
            alt=""
          />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-if="showPortal">
            <router-link to="/manage/index" target="_blank" tag="a"
              >门户管理</router-link
            >
          </el-dropdown-item>
          <el-dropdown-item v-if="showUserCenter">
            <div @click="goUserCenter">用户管理</div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div
        style="
          font-size: 14px;
          font-weight: bold;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 16px;
          cursor: pointer;
        "
        v-if="isPortal"
      >
        <top-message></top-message>
      </div>
      <el-dropdown>
        <div
          style="
            font-size: 14px;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          {{ userInfo.nickName }}
          <img
            style="width: 16px; height: 16px"
            src="@/assets/image/common/sanjiao.png"
            alt=""
          />
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- <el-dropdown-item >
            <router-link to="/main/home">首页</router-link>
          </el-dropdown-item> -->
          <el-dropdown-item v-if="consoleFlag" @click.native="changeConsole"
            >设置个人工作台
          </el-dropdown-item>
          <el-dropdown-item v-if="isPortal" @click.native="goInfoView"
            >个人信息
          </el-dropdown-item>
          <el-dropdown-item @click.native="changeThems"
            >切换主题
          </el-dropdown-item>

          <!-- <el-dropdown-item v-if="!consoleFlag" @click.native="changeConsole"
            >关闭个人工作台
          </el-dropdown-item> -->
          <el-dropdown-item @click.native="logout" divided
            >退出系统
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-drawer
        title="切换主题"
        class="elDrawer"
        size="34%"
        :append-to-body="true"
        :visible.sync="drawer"
      >
        <div class="changeThems">
          <div
            class="changeThems-item"
            :class="activeIndex === index ? 'activeThems' : ' '"
            v-for="(item, index) in themesBoxs"
            :key="index"
            @click="changeThemsItem(item, index)"
          >
            <div class="changeThems-item-icon">
              <img :src="item.imgSrc" />
            </div>
            <div class="changeThems-item-text">{{ item.name }}</div>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapState } from "vuex";
import { fullscreenToggel, listenfullscreen } from "@/util/util";
import topMenu from "./top-menu";
import topMessage from "@/page/index/top/top-message";
import { configTheme, getTicket } from "@/api/login";
import { getFetchList } from "@/api/admin/sys/system";
export default {
  props: {
    isPortal: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    topMenu,
    topMessage,
  },
  name: "top",
  data() {
    return {
      logoObject: {},
      drawer: false,
      consoleFlag: true,
      themesBoxs: [
        {
          id: "blueThem",
          name: "默认主题",
          imgSrc: require("@/assets/image/themesImgs/blueThem.png"),
          imgUrl: "../../assets/image/themesImgs/blueThem.png",
        },
        {
          id: "greenThem",
          name: "绿色主题",
          imgSrc: require("@/assets/image/themesImgs/greenThem.png"),
          imgUrl: "../../assets/image/themesImgs/greenThem.png",
        },
        {
          id: "redThem",
          name: "红色主题",
          imgSrc: require("@/assets/image/themesImgs/redThem.png"),
          imgUrl: "../../assets/image/themesImgs/redThem.png",
        },
        {
          id: "skyThem",
          name: "浅蓝主题",
          imgSrc: require("@/assets/image/themesImgs/skyThem.png"),
          imgUrl: "../../assets/image/themesImgs/skyThem.png",
        },
        {
          id: "yelThem",
          name: "黄色主题",
          imgSrc: require("@/assets/image/themesImgs/yelThem.png"),
          imgUrl: "../../assets/image/themesImgs/yelThem.png",
        },
      ],
      activeIndex: null,
      theme: "default",
      defaultImg: "url(../../assets/image/themesImgs/blueThem.png) no-repeat;",
    };
  },
  filters: {},
  created() {
    if (this.$route.path == "/main/home") {
      this.consoleFlag = true;
    } else {
      this.consoleFlag = false;
    }
    this.$root.$on("handleSonMenu", this.handleSonMenu);
    this.logoObject = this.logoObj;
  },
  mounted() {
    listenfullscreen(this.setScreen);
    setTimeout(() => {
      if (
        this.userInfo &&
        this.userInfo.config &&
        this.userInfo.config.theme &&
        this.userInfo.config.theme.themeId
      ) {
        if (!this.userInfo.config.theme.themeId) {
          localStorage.setItem(
            "themeSelected",
            this.userInfo.config.theme.themeId
          );
        }
      }
      window.document.documentElement.setAttribute("data-theme", "dark");
      if (this.$parent && this.$parent.changeThemesImg) {
        this.$parent.changeThemesImg();
      }
      this.$root.customMethod();
    }, 100);
    // console.log("logoObj=3322=", this.logoObj);
    if (!this.logoObject || !this.logoObject.sysTitle) {
      this.getLogoFn();
    }
    // setTimeout(() => {
    //   console.log("logoObj=332211=", this.$store.getters.logoObj);
    //   console.log("logoObj=33221100=", this.logoObj);
    // }, 500);
  },
  computed: {
    ...mapState({
      showLock: (state) => state.common.showLock,
      showFullScren: (state) => state.common.showFullScren,
      showCollapse: (state) => state.common.showCollapse,
      showMenu: (state) => state.common.showMenu,
      logoObj: (state) => state.user.logoObj,
    }),
    ...mapGetters([
      "userInfo",
      "isFullScreen",
      "tagWel",
      "tagList",
      "isCollapse",
      "tag",
      "logsLen",
      "logsFlag",
      "permissions",
      "website",
    ]),
    showPortal() {
      return this.vaildData(this.permissions.portal_button, false);
    },
    showUserCenter() {
      return this.vaildData(this.permissions.user_center_button, false);
    },
    themeClass() {
      return `theme-${this.theme}`;
    },
  },
  methods: {
    getLogoFn() {
      // console.log("getListgetListgetList==");

      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        if (response.data.code == 200) {
          this.logoObject = response.data.data || null;

          if (this.logoObject && this.logoObject.sysIcon) {
            let path = this.$getUrlByProcess(this.logoObject.sysIcon);
            this.change_icon(path);

            this.$store.commit("SET_LOGO_OBJ", this.logoObject);
          }
          if (this.logoObject && this.logoObject.sysTitle) {
            document.title = this.logoObject.sysTitle;
          }
        }
      });
    },
    change_icon(iconUrl) {
      const changeFavicon = (link) => {
        let $favicon = document.querySelector('link[rel="icon"]');
        if ($favicon !== null) {
          $favicon.href = link;
        } else {
          $favicon = document.createElement("link");
          $favicon.rel = "icon";
          $favicon.href = link;
          document.head.appendChild($favicon);
        }
      };
      // 设置图标地址
      // let iconUrl = `./icoImg/1.ico`;
      // 动态修改网站图标
      changeFavicon(iconUrl);
    },
    handleSonMenu() {
      if (this.$route.path == "/main/home") {
        this.consoleFlag = true;
      } else {
        this.consoleFlag = false;
      }
    },
    goIndexPage() {
      if (this.logoObject.cmsInfoPortal == "1") {
        window.location.href='/portal-web/#/gateway/home'
        // this.$router.push({
        //   path: "/gateway/home",
      
        // });
      }
    },
    goMenhuFn() {
      this.$router.push("/main/home");
    },
    changeConsole() {
      this.consoleFlag = true;
      this.$root.$emit("handleSetGrid", this.consoleFlag);
    },
    changeThems() {
      this.drawer = true;
    },
    changeThemsItem(it, index) {
      console.log(it, index);
      this.activeIndex = index;
      localStorage.setItem("themeSelected", index);
      if (this.$parent && this.$parent.changeThemesImg) {
        this.$parent.changeThemesImg();
      }
      this.$root.customMethod();
      this.$root.$emit("getThemeIndex", index);
      this.$root.$emit("changeThemesImg");
      configTheme({
        themeId: index,
      }).then((res) => {});
    },
    handleScreen() {
      fullscreenToggel();
    },
    setCollapse() {
      this.$store.commit("SET_COLLAPSE");
    },
    setScreen() {
      this.$store.commit("SET_FULLSCREN");
    },
    logout() {
      this.$confirm("是否退出系统, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          sessionStorage.clear();
          this.$router.push({ path: "/sso" });
          localStorage.clear();
        });
      });
    },
    goUserCenter() {
      // let params = {
      //   clientId: "admin",
      // };
      // getTicket(params).then((res) => {
      //   let pm = "?ticket=" + res.data.data.ticket + "&client_id=admin";
      //   this.openUserCenter(pm);
      // });
      var newOpen = window.open();
      newOpen.opener = null;
      newOpen.location = this.openUserCenter();
    },
    openUserCenter(param) {
      let yao = "1",
        er = "2",
        san = "3",
        si = "4",
        wu = "5",
        liu = "6",
        qi = "7",
        ba = "8",
        jiu = "9",
        ling = "0";
      let dian = ".";
      let url5 =
        wu +
        jiu +
        dian +
        er +
        er +
        qi +
        dian +
        yao +
        wu +
        san +
        dian +
        er +
        er +
        ba;
      let url_zs =
        wu +
        jiu +
        dian +
        er +
        er +
        qi +
        dian +
        yao +
        wu +
        san +
        dian +
        er +
        san +
        ling;
      let user_center_index = "";
      let url10 = yao + ling + dian + ba + dian + yao + dian + yao + si + si;
      let url148 =
        yao +
        ling +
        dian +
        san +
        ling +
        dian +
        yao +
        liu +
        er +
        dian +
        yao +
        si +
        ba;
      let url146 = yao + ling + dian + ba + dian + yao + dian + yao + si + liu;
      switch (window.location.hostname) {
        case "*************":
          user_center_index =
            window.location.protocol + "//" + "*************:8003/#/wel/index";
          break;
        case url5:
          user_center_index =
            window.location.protocol + "//" + url5 + ":8060/#/wel/index";
          break;
        case url10:
          user_center_index =
            window.location.protocol + "//" + url10 + ":8060/#/wel/index";
          break;
        case url148:
          user_center_index =
            window.location.protocol + "//" + url148 + ":8060/#/wel/index";
          break;
        case "**************":
          user_center_index =
            window.location.protocol + "//" + "**************:8081/#/wel/index";
          break;
        /*正式环境*/
        case url_zs:
          user_center_index =
            window.location.protocol + "//" + url_zs + ":8065/#/wel/index";
          break;
        case url146:
          user_center_index =
            window.location.protocol + "//" + url146 + ":8065/#/wel/index";
          break;
        case "*************":
          user_center_index =
            window.location.protocol + "//" + "*************:8085/#/wel/index";
          break;
        default:
          break;
      }
      if (param) {
        user_center_index = user_center_index + param;
      }
      return user_center_index;
    },
    goInfoView() {
      this.$router.push({ path: "/info/index" });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/theme/themesNew.scss";

.ac-top {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  background: #fff;
}

.activeThems {
  border: 6px solid black !important;
}

.changeThems {
  display: grid;
  grid-auto-rows: 130px;
  grid-template-columns: repeat(auto-fill, 200px);
  grid-gap: 30px;
  padding-left: 45px;
}

.changeThems-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 10px;
  background: #fff;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
  transition: all 0.3s;
}

.changeThems-item-icon {
  width: 200px;
  height: 100px;
  margin-bottom: 10px;
}

.changeThems-item-icon img {
  width: 200px;
  height: 100px;
}

.changeThems-item-text {
  font-size: 14px;
  font-weight: bold;
}

.el-drawer__header {
  text-align: center !important;
}

.el-drawer__header {
  text-align: center !important;
}

.message-svg {
}

.right-menu-item {
  display: inline-block;
  padding: 0 8px;
  height: 100%;
  font-size: 18px;
  color: #5a5e66;
  vertical-align: text-bottom;

  &.hover-effect {
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
}

.zs-logo {
  display: inline-block;
  width: 52px;
  height: 52px;
  background: url("/img/zs-logo.png") no-repeat;
  background-size: 100%;
  position: relative;
}
.in-logo {
  width: 42px;
  height: 42px;
}
</style>
