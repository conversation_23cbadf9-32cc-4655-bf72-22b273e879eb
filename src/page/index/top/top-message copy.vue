<template>
  <span @click="handleOpen()">
    <!-- <el-badge
      :value="unreadCount"
      :max="99">
      <i class="icon-rizhi"></i>
    </el-badge> -->
    <el-dialog :visible.sync="box" title="消息提醒" width="60%" append-to-body>
      <!-- 弹出列表 -->
      <el-table v-loading="loading" :data="list">
        <el-table-column width="120" property="fromUsername" label="发送人" />
        <el-table-column width="140" property="createTime" label="发送时间">
          <template v-slot="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="类型"
          align="center"
          prop="templateType"
          width="100"
        >
          <template v-slot="scope">
            <dict-tag
              :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE"
              :value="scope.row.notifyTemplateType"
            />
          </template>
        </el-table-column>
        <el-table-column property="notifyContent" label="内容" />
      </el-table>
      <div style="text-align: right; margin-top: 30px">
        <el-button type="primary" icon="el-icon-upload" @click="getAllNotify"
          >查看全部</el-button
        >
      </div>
    </el-dialog>
  </span>
</template>

<script>
import { getUnreadNotifyList } from "@/api/admin/notify/sysmsgnotify";

export default {
  name: "top-logs",
  data() {
    return {
      loading: false,
      box: false,
      unreadCount: 0,
      list: [],
    };
  },
  created() {
    // 首次加载小红点
    // this.getList()
  },
  props: [],
  methods: {
    handleOpen() {
      this.box = true;
    },
    getList() {
      this.loading = true;
      getUnreadNotifyList().then((response) => {
        this.list = response.data.data.records;
        this.loading = false;
        // 强制设置 unreadCount 为 0，避免小红点因为轮询太慢，不消除
        this.unreadCount = response.data.data.total;
      });
    },
    getAllNotify() {
      this.box = false;
      this.$router.push({
        name: "我的站内信", //  /admin/msg/notify/mine/index
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.code {
  font-size: 12px;
  display: block;
  font-family: monospace;
  white-space: pre;
  margin: 1em 0px;
}
</style>
