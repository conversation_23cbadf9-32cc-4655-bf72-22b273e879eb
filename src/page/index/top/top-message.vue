<template>
  <span @click="handleOpen()" class="my-msg" style="cursor: pointer">
    <el-badge v-if="unreadCount" :value="unreadCount" :max="99">
      <img
        style="
          width: 17px;
          height: 21px;
          margin-right: 0px;
          margin-bottom: -8px;
        "
        src="@/assets/image/common/p_xiaoxi.png"
        alt=""
      />
    </el-badge>
  </span>
</template>

<script>
import { getUnreadNotifyList } from "@/api/admin/notify/sysmsgnotify";

export default {
  name: "top-logs",
  computed: {
    originUrl() {
      return window.location.origin;
    },
  },
  data() {
    return {
      loading: false,
      box: false,
      unreadCount: 0,
      list: [],
    };
  },
  created() {
    // 首次加载小红点
    // this.getList();
  },
  props: [],
  methods: {
    handleOpen() {
      var newOpen = window.open();
          newOpen.opener = null;
          newOpen.location = this.originUrl + "/#/noticeCenter/index";
    },
    getList() {
      this.loading = true;
      getUnreadNotifyList().then((response) => {
        if (response.data.code == 200) {
          // this.list = response.data.data.records;
          this.loading = false;
          // 强制设置 unreadCount 为 0，避免小红点因为轮询太慢，不消除
          // this.unreadCount = response.data.data.todoCount + response.data.data.warnCount + response.data.data.otherCount
          this.unreadCount = response.data.data.todoCount;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.my-msg {
  // display: inline-block;
  // min-width: 17px;
  // min-height: 21px;
  // border: 1px solid red;
}
.code {
  font-size: 12px;
  display: block;
  font-family: monospace;
  white-space: pre;
  margin: 1em 0px;
}
</style>
