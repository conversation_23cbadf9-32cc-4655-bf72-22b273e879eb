<template>
  <div class="code-container">
    <el-form
      class="login-form"
      status-icon
      :rules="loginRules"
      ref="loginForm"
      :model="loginForm"
      label-width="0"
    >
      <el-form-item prop="username">
        <el-input
          size="small"
          @keyup.enter.native="handleLogin"
          v-model="loginForm.username"
          auto-complete="off"
          placeholder="请输入用户名"
        >
          <i slot="prefix" class="icon-yonghu"></i>
        </el-input>
      </el-form-item>
      <el-form-item prop="password" class="it-top">
        <el-input
          size="small"
          @keyup.enter.native="handleLogin"
          :type="passwordType"
          v-model="loginForm.password"
          auto-complete="off"
          placeholder="请输入密码"
        >
          <i
            class="el-icon-view el-input__icon"
            slot="suffix"
            @click="showPassword"
          ></i>
          <i slot="prefix" class="icon-mima"></i>
        </el-input>
      </el-form-item>
      <!-- <el-form-item
        class="it-top"
        prop="captcha"
        v-if="website.validateCode && !smsFlag"
      >
        <el-row :span="24">
          <el-col :span="16">
            <el-input
              size="small"
              @keyup.enter.native="handleLogin"
              v-model="loginForm.captcha"
              auto-complete="off"
              placeholder="请输入验证码"
            >
              <i slot="prefix" class="icon-yanzhengma"></i>
            </el-input>
          </el-col>
          <el-col :span="8">
            <div class="login-code">
              <img
                class="login-code-img"
                :src="captchaPath"
                @click="getCaptcha()"
                alt
              />
            </div>
          </el-col>
        </el-row>
      </el-form-item> -->
      <el-form-item class="it-top">
        <el-row :span="24">
          <el-col :span="24">
            <slider-verify
              ref="silderverify"
              v-model="loginForm.isLock"
              @change="handlerLock"
            ></slider-verify>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item>
        <!-- <el-button @click.native.prevent="getCode">测试拖拉 </el-button> -->
        <el-button
          type="primary"
          @click.native.prevent="handleLogin"
          class="login-submit"
          >登录
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getUUID } from "@/util/util";
import { mapGetters } from "vuex";
import { getSmsCode } from "@/api/login";
import SliderVerify from "@/components/Verifition/SliderVerify";

export default {
  name: "calogin",
  components: {
    SliderVerify,
  },
  data() {
    return {
      loginForm: {
        username: "",
        password: undefined,
        uuid: "",
        captcha: "",
        smsCode: "",
        isLock: false,
      },
      show: true,
      loginRules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          // { min: 6, message: "密码长度最少为6位", trigger: "blur" },
        ],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      passwordType: "password",
      captchaPath: "",
      redirect: "",
      count: "", //初始化次数
      smsFlag: false,
      captchaEnable: true,
    };
  },
  created() {
    if (this.website.validateCode) {
      this.getCaptcha();
    }
    this.redirect = this.$route.query.redirect
      ? this.$route.query.redirect
      : undefined;
    // console.log("this.redirect==", this.redirect);
    if (this.redirect) {
      window.localStorage.setItem("redirect", this.redirect);
    }
  },
  computed: {
    ...mapGetters(["tagWel", "website"]),
  },
  methods: {
    //滑块验证成功的回调
    handlerLock(data) {
      let that = this;
      //验证成功后 data值为true
      if (data) {
        console.log("验证通过==", this.loginForm);
        // setTimeout(function () {
        //   that.$refs.verify.show();
        // }, 1000);
      }
    },

    getCode() {
      // 情况一，未开启：则直接登录
      if (!this.captchaEnable) {
        this.getVerificationCode({});
        return;
      }
      if (this.loginForm.username == "") {
        this.$message({
          showClose: true,
          message: "请输入用户名",
          type: "error",
        });
        return;
      }

      // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    },
    showPassword() {
      this.passwordType == ""
        ? (this.passwordType = "password")
        : (this.passwordType = "");
    },
    handleLogin() {
      if (!this.loginForm.isLock) {
        this.$message({
          showClose: true,
          message: "请先完成向右拖动验证",
          type: "error",
        });
        return;
      }
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.$message({
            showClose: true,
            message: "该证书查询不到对应账户信息，无法登陆，请先注册该单位账号",
            type: "error",
          });
          // this.$store
          //   .dispatch("LoginByUsername", this.loginForm)
          //   .then(() => {
          //     // console.log("登录后的跳转---", this.redirect);
          //     // console.log("登录后的跳转2---", this.tagWel.value);
          //     this.$router.push({ path: this.redirect || this.tagWel.value });
          //   })
          //   .catch(() => {});
        }
      });
    },
    // 获取验证码
    getCaptcha() {
      this.loginForm.uuid = getUUID();
      this.captchaPath =
        window.location.protocol +
        "//" +
        window.location.hostname +
        ":" +
        window.location.port +
        "/uc-api/admin/auth/createCaptcha" +
        `?uuid=${this.loginForm.uuid}`;
    },
    //获取手机短信验证码并进入60s倒计时
    getVerificationCode(captchaParams) {
      let params = {
        username: this.loginForm.username,
        // captcha: this.loginForm.captcha,
        uuid: this.loginForm.uuid,
        captchaVerification: captchaParams.captchaVerification,
      };

      getSmsCode(params).then((res) => {
        // console.log("获取验证码==", res);
        if (res.data.code == 200) {
          this.smsFlag = true;
        }
      });
      // if (this.smsObj.mobile == "" && this.smsObj.captcha == "") {
      //   this.$message({
      //     showClose: true,
      //     message: "手机号和图片验证码不能为空",
      //     type: "error",
      //     center: true,
      //   });
      // } else if (this.smsObj.mobile == "") {
      //   this.$message({
      //     showClose: true,
      //     message: "请输入手机号",
      //     type: "error",
      //     center: true,
      //   });
      // } else if (!/^[1][0-9]{10}$/.test(this.smsObj.mobile)) {
      //   this.$message({
      //     showClose: true,
      //     message: "请输入正确的手机号",
      //     type: "error",
      //     center: true,
      //   });
      // } else if (this.smsObj.captcha == "") {
      //   this.$message({
      //     showClose: true,
      //     message: "请输入图形码",
      //     type: "error",
      //     center: true,
      //   });
      // } else {
      //   // if (!this.timer) {
      //   // }
      //   this.getMobileCode();
      // }
      this.getMobileCode();
    },
    //获取手机短信验证码接口
    getMobileCode() {
      const self = this;
      // let param = {
      //   mobile: self.smsObj.mobile,
      //   captcha: self.smsObj.captcha,
      // };
      // self.$http.mobileReceiveCode(param, (res) => {
      //   if (res.code === 0) {
      //     const TIME_COUNT = 60;
      //     self.count = TIME_COUNT;
      //     self.show = false;
      //     self.timer = setInterval(() => {
      //       if (self.count > 0 && self.count <= TIME_COUNT) {
      //         self.count--;
      //       } else {
      //         self.show = true;
      //         clearInterval(self.timer); // 清除定时器
      //         self.timer = null;
      //       }
      //     }, 1000);
      //   }
      // });
      const TIME_COUNT = 60;
      self.count = TIME_COUNT;
      self.show = false;
      self.timer = setInterval(() => {
        if (self.count > 0 && self.count <= TIME_COUNT) {
          self.count--;
        } else {
          self.show = true;
          clearInterval(self.timer); // 清除定时器
          self.timer = null;
        }
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scope>
.code-container {
  position: relative;
}
.it-top {
  margin-top: 20px;
}

@media screen and (max-height: 1079px) {
  /* 在这里添加您的样式 */
  /* .login-mains {
    transform: scale(0.8);
  } */
  .new-title {
    margin-top: 90px !important;
  }
}

.phone-code {
  border: 1px solid #ebeef5;
  height: 42px;
  line-height: 42px;
  text-align: center;
  margin-left: 2px;
  cursor: pointer;
}
</style>
