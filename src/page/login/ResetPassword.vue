<template>
  <div class="reset-container">
    <div class="reset-top">
      <div class="top-one">
        <div v-if="logoObj.sysLogo">
          <img class="self-bg" :src="$getUrlByProcess(logoObj.sysLogo)" />
        </div>
        <span v-else class="top-bg"></span>
        <span class="top-title">{{
          logoObj.sysTitle ? logoObj.sysTitle : ""
        }}</span>
      </div>
      <div class="top-two" @click="handleGoLogin">
        返回登录页 <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    <div class="reset-password">
      <div class="reset-title">重置密码</div>
      <el-form
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="旧密码" prop="password">
          <el-input
            type="password"
            v-model="ruleForm.kouling"
            autocomplete="off"
            show-password
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="newpassword1">
          <el-input
            type="password"
            v-model="ruleForm.newpassword1"
            autocomplete="off"
            show-password
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="checkPass">
          <el-input
            type="password"
            v-model="ruleForm.checkPass"
            autocomplete="off"
            show-password
            clearable
          ></el-input>
        </el-form-item>
        <div>
          <div class="reset-tip">
            温馨提示： 1、首次登录需要进行密码重置操作。
          </div>
          <div class="password-tip">
            2、超过90天未更新密码口令，需要进行密码重置操作。
          </div>
          <div class="password-tip">
            3、密码长度至少8位字符，复杂性要求至少包含以下四种类别中的3种：大写字母、小写字母、数字、特殊符合。
          </div>
          <div class="password-tip">
            4，请不要以姓名拼音、电话号码以及出生日期等作为口令或者口令的组成部分。
          </div>
        </div>
        <el-form-item>
          <div class="btn-container">
            <div class="btn-sty btn-submit" @click="submitForm('ruleForm')">
              提交
            </div>
            <div class="btn-sty btn-reset" @click="resetForm('ruleForm')">
              重置
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { encryption } from "@/util/util";
import { firstUpdatePassword } from "@/api/admin/auth/user.js";

import { mapGetters, mapState } from "vuex";
import { rule } from "@/util/validateRules.js";
import { getFetchList } from "@/api/admin/sys/system";
export default {
  name: "ResetPassword",
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapState({
      logoObj: (state) => state.user.logoObj,
    }),
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.newpassword1) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };

    return {
      logoObject: {},
      ruleForm: {
        kouling: "",
        checkPass: "",
        oldKouling: "",
        userName: "",
      },
      rules: {
        password: [{ validator: validatePass, trigger: "blur" }],
        newpassword1: [{ validator: rule.validatePassword, trigger: "blur" }],
        checkPass: [{ validator: validatePass2, trigger: "blur" }],
      },
    };
  },
  created() {
    console.log("this.$route.query===", this.$route.query);
    if (this.$route && this.$route.query && this.$route.query.type == 2) {
      this.$message({
        showClose: true,
        message: "超过90天未更新密码口令，请重置密码！",
        type: "error",
      });
    }
  },
  mounted() {
    // console.log("mapgetters===");
    setTimeout(() => {
      // console.log("mapgetters==3355=", this.logoObj);
      if (this.logoObj && this.logoObj.sysTitle) {
        this.logoObject = Object.assign({}, this.logoObj);
      }
      if (!this.logoObj || !this.logoObj.sysTitle) {
        this.getLogoFn();
      }
    }, 600);
  },
  methods: {
    getLogoFn() {
      // console.log("getListgetListgetList==");

      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        // console.log("logoObj==", response);
        if (response.data.code == 200) {
          this.logoObject = response.data.data || null;
          if (this.logoObject && this.logoObject.sysIcon) {
            let path = this.$getUrlByProcess(this.logoObject.sysIcon);
            this.change_icon(path);

            this.$store.commit("SET_LOGO_OBJ", this.logoObject);
          }
          if (this.logoObject && this.logoObject.sysTitle) {
            document.title = this.logoObject.sysTitle;
          }
        }
      });
    },
    change_icon(iconUrl) {
      const changeFavicon = (link) => {
        let $favicon = document.querySelector('link[rel="icon"]');
        if ($favicon !== null) {
          $favicon.href = link;
        } else {
          $favicon = document.createElement("link");
          $favicon.rel = "icon";
          $favicon.href = link;
          document.head.appendChild($favicon);
        }
      };
      // 设置图标地址
      // let iconUrl = `./icoImg/1.ico`;
      // 动态修改网站图标
      changeFavicon(iconUrl);
    },
    handleGoLogin() {
      window.sessionStorage.clear();
      // window.localStorage.clear();
      setTimeout(() => {
        this.$store.dispatch("LogOut").then(() => {
          let redirect = localStorage.getItem("redirect");
          if (redirect) {
            this.$router.push({
              path: "/sso",
              query: {
                redirect: redirect,
              },
            });
          } else {
            this.$router.push({ path: "/sso" });
          }
        });
      }, 500);
    },
    submitForm(formName) {
      // let params = {
      //   newPassword: this.ruleForm.checkPass,
      //   oldPassword: this.ruleForm.password,
      // };
      this.ruleForm.password = this.ruleForm.kouling;
      const myUserInfo = {
        newPassword: this.ruleForm.checkPass,
        oldPassword: this.ruleForm.kouling,
      };
      const user = encryption({
        data: myUserInfo,
        key: "user_center_2023",
        param: ["newPassword"],
      });
      const user2 = encryption({
        data: myUserInfo,
        key: "user_center_2023",
        param: ["oldPassword"],
      });

      let params = {
        newPassword: user.newPassword,
        oldPassword: user2.oldPassword,
      };
      // console.log("res=params=", params);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          firstUpdatePassword(params).then((res) => {
            // console.log("res=xiugai=", res);
            if (res.data.code == 200) {
              this.$message.success("修改成功");

              // 修改后注销当前token,重新登录
              setTimeout(() => {
                this.$store.dispatch("LogOut").then(() => {
                  // this.$router.push({ path: "/login" });
                  let redirect = localStorage.getItem("redirect");
                  if (redirect) {
                    this.$router.push({
                      path: "/sso",
                      query: {
                        redirect: redirect,
                      },
                    });
                  } else {
                    this.$router.push({ path: "/sso" });
                  }
                });
              }, 500);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.reset-container {
  width: 100%;
  height: 100vh;
  background: rgba(240, 242, 245, 1);
  // margin-top: -50px;
  position: relative;
  border: 1px solid transparent;
  .reset-top {
    width: 900px;
    height: 23px;
    margin: 30px auto;
    display: flex;
    justify-content: space-between;
    .top-one {
      display: flex;
      align-items: center;
    }

    .top-two {
      color: rgba(50, 114, 206, 1);
      font-size: 14px;
      font-weight: 700;
      margin-top: 6px;
      &:hover {
        cursor: pointer;
      }
    }
    .top-bg {
      width: 60px;
      height: 60px;
      display: inline-block;
      background: url("../../assets/image/guohui.png") no-repeat;
      background-size: 100%;
    }
    .self-bg {
      width: 60px;
      height: 60px;
    }
    .top-title {
      color: #22242c;
      font-size: 21.6px;
      font-weight: bold;
      margin-left: 9px;
      vertical-align: super;
    }
  }
}
.reset-password {
  width: 900px;
  margin: auto;
  height: 520px;
  background: #ffffff;

  // margin-top: 30vh;
  .reset-title {
    // margin: 50px 0;
    // text-align: center;
    width: 900px;
    height: 53px;
    line-height: 53px;
    background: url("../../assets/image/reset-bg.png") no-repeat;
    background-size: 100%;
    text-align: center;
    color: #ffffff;
    font-weight: 700;
    margin-bottom: 24px;
  }
  .reset-tip {
    margin: 20px 0 12px 24px;
    // text-align: center;
    color: #e94545;
    font-size: 14px;
    font-weight: 700;
  }
  .password-tip {
    color: #e94545;
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 12px 24px;
    padding-left: 75px;
  }
}
.reset-back {
  // color: #437fff;
  &:hover {
    color: #437fff;
    cursor: pointer;
  }
}
.btn-sty {
  width: 90px;
  height: 42px;
  line-height: 42px;
  text-align: center;

  border-radius: 4px;
  opacity: 1;

  margin-top: 12px;
  &:hover {
    cursor: pointer;
  }
}
.btn-submit {
  color: #ffffff;
  background: #3272ce;
}
.btn-reset {
  margin-left: 16px;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #22242c;
}
.btn-container {
  display: flex;
}
.demo-ruleForm {
  // width: 444px;
  // margin: 0 auto;
  padding: 0 20px;
  margin-left: 200px;
  ::v-deep .el-input {
    width: 336px;
  }
}
</style>
