<template>
  <div class="login-container">
    <div class="title-content">
      <div class="logo-img">
        <template v-if="logoObj && logoObj.sysLogo">
          <img
            class="lg-img img m-r-20"
            :src="$getUrlByProcess(logoObj.sysLogo)"
          />
        </template>
        <!-- <img v-else class="img m-r-20" src="@/assets/logo.jpg" /> -->
      </div>
      <div class="title-word">
        {{ logoObj.sysTitle ? logoObj.sysTitle : website.title }}
      </div>
    </div>

    <div class="login-weaper animated bounceInDown">
      <div class="login-mains">
        <!-- <div class="new-title">
          {{ logoObj.sysTitle ? logoObj.sysTitle : website.title }}
        </div> -->
        <!-- <div class="m-left">
          <div class="le-title">河南省xxx局</div>
          <div class="le-fu">综合管理平台</div>
        </div> -->
        <div class="m-top">
          <div class="le-fu">统一身份认证</div>
        </div>
        <div class="m-right">
          <!-- first-row -->
          <div class="ri-tips">
            <el-tabs
              v-model="loginType"
              @tab-click="handleClick"
              class="login-tabs"
            >
              <el-tab-pane label="账号登录" name="first"></el-tab-pane>
              <el-tab-pane label="CA登录" name="second"></el-tab-pane>
            </el-tabs>
            <!-- <div class="dl-title dl-active">账号登录</div> -->
            <!-- <div class="dl-title">手机号登录</div> -->
            <!-- <div class="dl-title">CA登录</div> -->
            <!-- 账号密码 -->
            <!-- <el-menu
              :default-active="activeIndex"
              class="el-menu-demo"
              mode="horizontal"
              @select="handleSelect"
              text-color="#606266"
              active-text-color="#3272CE"
            >
              <el-menu-item class="item-two" index="1"
                ><span v-if="this.changeKey == '1'" class="text-active"
                  >账号登陆</span
                >
                <span v-else>账号登陆</span></el-menu-item
              >
            </el-menu> -->
          </div>
          <!-- <div class="row-line"></div> -->

          <userLogin v-if="loginType === 'first'"></userLogin>
          <ca-login v-else-if="loginType === 'second'"></ca-login>
        </div>
      </div>
      <!-- <div class="login-left animate__animated animate__fadeInLeft">
        <img class="img" src="/img/logo.png" alt=""/>
        <p class="title">{{ website.title }}</p>
        <p>©{{ website.year }} {{ website.version }}</p>
      </div> -->
      <!-- <div class="login-border animate__animated animate__fadeInRight">
        <div class="login-main">
          <userLogin v-if="activeName === 'user'"></userLogin>
          <codeLogin v-else-if="activeName==='code'"></codeLogin>
          <user-register v-else-if="activeName==='register'" @ok="activeName='user'"></user-register>
          <div class="login-menu">
            <a href="#"
               @click.stop="activeName='user'">账号密码</a>
            <a href="#"
               @click.stop="activeName='code'">短信登录</a>
            <a href="#" v-if="website.register"
               @click.stop="activeName='register'">用户注册</a>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
import "@/styles/login.scss";
import userLogin from "./userlogin";

import codeLogin from "./codelogin";
import userRegister from "@/page/login/userRegister";
import { mapGetters } from "vuex";
import { getFetchList } from "@/api/admin/sys/system";
import CaLogin from "./CaLogin.vue";
export default {
  name: "login",
  components: {
    userLogin,
    codeLogin,
    userRegister,
    CaLogin,
  },
  data() {
    return {
      activeName: "user",
      activeIndex: "1",
      changeKey: "1",
      logoObj: {},
      loginType: "first",
    };
  },
  computed: {
    ...mapGetters(["website"]),
  },
  created() {
    this.getLogoFn();
  },
  methods: {
    handleClick() {},
    getLogoFn() {
      // console.log("getListgetListgetList==");

      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        // console.log("logoObj=222=", response);
        if (response.data.code == 200) {
          this.logoObj = response.data.data || null;
          if (this.logoObj && this.logoObj.sysIcon) {
            let path = this.$getUrlByProcess(this.logoObj.sysIcon);
            this.change_icon(path);

            this.$store.commit("SET_LOGO_OBJ", this.logoObj);
          }
          if (this.logoObj && this.logoObj.sysTitle) {
            document.title = this.logoObj.sysTitle;
          }
        }
      });
    },
    change_icon(iconUrl) {
      const changeFavicon = (link) => {
        let $favicon = document.querySelector('link[rel="icon"]');
        if ($favicon !== null) {
          $favicon.href = link;
        } else {
          $favicon = document.createElement("link");
          $favicon.rel = "icon";
          $favicon.href = link;
          document.head.appendChild($favicon);
        }
      };
      // 设置图标地址
      // let iconUrl = `./icoImg/1.ico`;
      // 动态修改网站图标
      changeFavicon(iconUrl);
    },
    handleSelect(key, keyPath) {
      this.changeKey = key;
    },
  },
};
</script>
<style scoped lang="scss">
.login-tabs {
  ::v-deep .el-tabs__item {
    font-size: 22px !important;
  }
  ::v-deep .el-tabs__item.is-active {
    color: #0357ca;
  }
  ::v-deep .el-tabs__item:hover {
    color: #0357ca;
    cursor: pointer;
  }
  ::v-deep .el-tabs__active-bar {
    background-color: #0357ca;
  }
}
</style>