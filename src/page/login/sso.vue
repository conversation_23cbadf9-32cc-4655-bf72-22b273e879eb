<template>
  <div>
    <!--    授权中-->
  </div>
</template>

<script>
export default {
  name: "sso",
  data() {
    return {
      //客户端id
      clientId: process.env.VUE_APP_SSO_CLIENT_ID,
      //回调地址
      // redirectUri:
      //   window.location.protocol +
      //   "//" +
      //   window.location.host +
      //   process.env.VUE_APP_BASE_SERVER_PATH +
      //   "/#/sso-callback",

      redirectUri:
        window.location.protocol +
        "//" +
        "**************:8063/Portal/ctrl/login",
      // / 1）授权码模式，对应 code；2）简化模式，对应 token
      responseType: "code",
      //'http://**************:8065/guum/sso/auth/' 现在是写死的59地址
      authUrl: process.env.VUE_APP_AUTH_URL,
    };
  },
  created() {
    sessionStorage.removeItem("menuApiCount"); //记录刷新的次数，第一次会加载动态路由
    let ip59 = "http://59." + "227.153.230:8065/guum/sso/auth/";
    let ip222 = "http://222." + "143.68.56:8083/guum/sso/auth/";
    let querynettype = this.$route.query.netType;
    if (querynettype) {
      if (querynettype == "1") {
        this.authUrl = ip222;
      }
      if (querynettype == "2") {
        this.authUrl = ip59;
      }
    }

    this.ssoLogin();
  },
  methods: {
    ssoLogin() {
      // 注意，需要使用 encodeURIComponent 编码地址
      const redirectUri = encodeURIComponent(this.redirectUri);
      // 重定向到授权地址(前后端分离)
      window.location.href = this.authUrl + "?redirect=" + redirectUri;
    },
  },
};
</script>

<style scoped></style>
