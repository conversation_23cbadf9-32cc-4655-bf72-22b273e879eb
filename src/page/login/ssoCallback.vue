<template>
  <div>
    <!--    回调-->
  </div>
</template>

<script>
import { ssoLoginByTicket } from "@/api/admin/auth/token";
import store from "@/store";

export default {
  name: "ssoCallback.vue",
  data() {
    return {
      //客户端id
      clientId: process.env.VUE_APP_SSO_CLIENT_ID,
    };
  },
  created() {
    let query = this.$route.query;

    const href = window.location.href;
    // 获取查询参数中的 ticket 值
    let ticket = query.ticket;
    console.log("单点登录回调ticket", ticket);
    if (!ticket) {
      // 创建一个新的 URL 对象
      const url = new URL(href);
      const params = url.searchParams;
      ticket = params.get("ticket");
      console.log("searchParams获取参数", ticket);
    }

    if (ticket) {
      this.ssoLoginByCode(ticket);
    }
  },
  methods: {
    ssoLoginByCode(ticket) {
      // this.$store.dispatch("LogOut").then(() => {
      //   sessionStorage.clear();
      //   localStorage.clear();
      // 将ticket放在请求体中而不是URL参数
      ssoLoginByTicket({ ticket })
        .then((res) => {
          if (res) {
            if (res.data.code !== 200) {
              this.$message.error(res.data.msg + "刷新登录后重试");
              window.location.reload();
            } else {
              this.$store
                .dispatch("StoreAccessToken", res.data.data.accessToken)
                .then((res) => {
                  // this.$router.push({path: store.getters.tagWel.value});
                  // this.$router.push({ path: "/home/<USER>" });
                  // GetUserInfo
                  this.$store.dispatch("GetUserInfo"); // 回调后调用用户信息接口

                  let query = this.$route.query;
                  let type = query.type;
                  if (type == "admin") {
                    // this.$router.push({ path: "/message/publish/index" });
                    this.$router.push({ path: "/gateway/home?type=admin" });
                  } else {
                    this.$router.push({ path: "/gateway/home" });
                  }
                });
            }
          } else {
            this.$message.error("服务器异常:请刷新登录后重试");
            window.location.reload();
          }
        })
        .catch((res) => {
          console.log("ssoLoginByTicket catch4", res);
          this.$message.error("服务器异常:请刷新后重试");
          window.location.reload();
        });
      // });
    },
  },
};
</script>

<style scoped></style>
