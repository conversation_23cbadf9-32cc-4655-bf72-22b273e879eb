<template>
  <div class="echart-container">
    <!-- <div class="echart-content">
      
    </div> -->
    <div class="avue-layout">
      <!-- 欢迎使用系统 -->
      <div class="main-left">
        <!-- 左侧导航栏 -->
        <!-- <sidebar /> -->
      </div>
      <div class="avue-main">
        <!-- 顶部标签卡 -->
        <!-- <tags /> -->
        <!-- 主体视图层 -->
        <!-- <el-scrollbar style="margin-top: 40px">
          <keep-alive>
            <router-view class="avue-view" v-if="$route.meta.$keepAlive" />
          </keep-alive>
          <router-view class="avue-view" v-if="!$route.meta.$keepAlive" />
        </el-scrollbar> -->
      </div>
    </div>
  </div>
</template>
<script>
import top from "@/page/index/top/";
import catalogue from "@/page/index/catalogues/index.vue";
import sidebar from "./compontents/sidebar/index.vue";
export default {
  components: {
    top,
    catalogue,
    sidebar,
  },
  data() {
    return {
      topFlag: true,
    };
  },
};
</script>
<style lang="scss" scoped>
.echart-container {
  width: 100%;
  height:100%;
  overflow-y: scroll;

  .echart-content {
    width: 100%;
    margin: 0 auto;
    // height: calc(100vh - 200px);
    height: 100%;
    // border: 1px solid red;
  }
  .main-left {
    width: 220px;
    position: fixed;
    height: 100%;
  }
}
</style>