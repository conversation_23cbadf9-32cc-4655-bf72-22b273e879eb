<template>
  <div class="avue-sidebar side-container lymenu">
    <!-- <logo></logo> -->
    <el-scrollbar view-style="font-weight: bold;height:100%;" style="height: 100%" :native="false">
      <div v-if="validatenull(menu)" class="avue-sidebar--tip">
        没有发现菜单
      </div>
      <el-menu unique-opened :default-active="nowTagValue" mode="vertical" :show-timeout="200" :collapse="keyCollapse" ref="elMenuRef">
        <sidebar-item :menu="menu2" :screen="screen" first :props="website.menu.props" :collapse="keyCollapse"></sidebar-item>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
// import logo from "../logo";
import sidebarItem from "./sidebarItem";
import { findItem } from "@/util/util";
export default {
  name: "sidebar",
  components: { sidebarItem },
  data() {
    return {
      menu2: [],
    };
  },
  created() {
    // let arouteObj = {
    //   name: "测试",
    //   component: "@/page/main/TabIndex",
    //   label: "测试1",
    //   id: "1001",
    //   parentId: "-1",
    //   path: "/main/index",
    // };
    // let arouteObj2 = {
    //   name: "测试2",
    //   component: "/test/index",
    //   label: "测试2",
    //   id: "1002",
    //   parentId: "-1",
    //   path: "/test/index",
    // };
    // this.menu2.push(arouteObj);
    // this.menu2.push(arouteObj2);
    // this.$router.$avueRouter.formatMainRoutes(this.menu2, true);
    // console.log("打印一下002-created--");
    // this.handleGetMenu();
  },

  computed: {
    ...mapGetters(["website", "menu", "tag", "keyCollapse", "screen"]),
    nowTagValue: function () {
      return this.$router.$avueRouter.getValue(this.$route);
    },
  },
  mounted() {
    // this.refreshMenu();
    setTimeout(() => {
      this.dealOpenMenu();
    }, 100);
  },

  methods: {
    refreshMenu() {
      let menuStr2 = localStorage.getItem("currentMenu");
      if (this.menu2 && this.menu2.length > 0) {
      } else {
        if (menuStr2) {
          let currentMenu = JSON.parse(menuStr2) || [];
          if (
            currentMenu &&
            currentMenu.children &&
            currentMenu.children.length > 0
          ) {
            this.menu2 = currentMenu.children;
          }
        }
      }
    },
    handleGetMenu() {
      this.$store.dispatch("GetMenu", { type: true, id: -1 }).then((data) => {
        if (data.length === 0) return;
        // console.log("log---lzs--", data);
        this.$router.$avueRouter.formatMainRoutes(data, true);
        this.menu2 = data;
      });
    },

    handleSetMenu(menu) {
      // this.menu2 = [];
      // console.log("sidebar--lzs---", menu);
      this.menu2 = menu;
      // localStorage.setItem("menu2", JSON.stringify(this.menu2));
      setTimeout(() => {
        this.dealOpenMenu();
      }, 1000);
    },
    dealOpenMenu() {
      let arr = findItem(this.menu2, "path", this.$route.path);

      if (arr && arr.length > 0) {
        this.$nextTick(() => {
          if (this.$refs.elMenuRef) {
            this.$refs.elMenuRef.open(arr[0]);
          }
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.scroll-container {
}
.side-container {
  min-height: calc(100vh - 124px);
}
</style>
