<template>
  <div :class="['menu-container', 'my-cates', 'mainBG']">
    <ul class="ul-content">
      <!-- <li class="li-block">首页1</li> -->
      <div class="swiper-button-prev" slot="button-prev"></div>
      <!--  @click="handlePrev" -->

      <li
        class="li-block lyly"
        style="display: none"
        :class="chooseMenu && chooseMenu.id == item.id ? 'li-active' : ''"
        v-for="item in topMenuArr"
        :key="item.id"
        @click="handleClickTab(item)"
        @mouseover="handleMouseOver(item)"
        @mouseleave="handleMouseLeave(item)"
      >
        {{ item.name }}
        <template v-if="item.children && item.children.length > 0">
          <div class="lb-menu" v-if="item.sonMenuFlag">
            <div
              class="lm-content"
              v-for="cItem in item.children"
              :key="cItem.id"
              @click.stop="handleSonItem(cItem)"
              @mouseover.stop="handleMouseOverSon(cItem)"
            >
              {{ cItem.name }}
            </div>
          </div>
        </template>
      </li>
      <swiper :options="swiperOption" class="swiper-content" ref="mySwiper">
        <!-- <swiper-slide class="swiper-block">
          <div class="bg-block">工作台2</div>
        </swiper-slide> -->

        <!-- <swiper-slide class="swiper-block" v-for="nItem in 26" :key="nItem">
          <div class="bg-block">工作台4{{ nItem }}</div>
        </swiper-slide> -->
        <swiper-slide
          class="swiper-block lyonemenu"
          v-for="mitem in menusArr"
          :key="mitem.id"
        >
          <li
            class="li-block"
            :class="chooseMenu && chooseMenu.id == mitem.id ? 'li-active' : ''"
            @click="handleClickTab(mitem)"
          >
            {{ mitem.menuName }}
          </li>
        </swiper-slide>

        <!-- <div class="swiper-pagination" slot="pagination"></div> -->

        <!-- <div class="swiper-scrollbar" slot="scrollbar"></div> -->
      </swiper>
      <div class="swiper-button-next" slot="button-next"></div>
      <!--  @click="handleNext" -->
    </ul>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";

import {
  getAdminMenuTree,
  getDaohangMenuTree,
  getMenusById,
} from "@/api/admin/sys/menu";
import { getMenu } from "@/api/admin/sys/clientMenu";
import { getListMenu } from "@/api/admin/sys/menu";

// 引入Swiper组件
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
// 引入Swiper样式
// import "swiper/swiper-bundle.css";
// import "swiper/css/swiper.css";
import "swiper/swiper.scss";
import { findItem } from "@/util/util";
export default {
  name: "index",
  components: {
    Swiper,
    SwiperSlide,
  },
  data() {
    return {
      activeIndex: 1,
      sonMenuFlag: false,
      topMenuArr: [],
      menusArr: [],
      fixTopMenu: [
        {
          parentId: -1,
          name: "首页",
          id: "2001",
          menuType: "T",
        },
        // {
        //   parentId: -1,
        //   name: "工作台",
        //   id: "2002",
        // },
      ],
      topAllMenuAll: [],
      selfThemeIndex: "",
      topFlag: true,
      chooseMenu: {
        parentId: -1,
        name: "统一门户",
        id: "1900073367291609089",
        menuType: "0",
      },
      menuIndex: 0,
      swiperOption: {
        loop: false,
        // spaceBetween: 3,
        speed: 1000,
        // autoplay: {
        //   delay: 10000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false,
        // },

        loopedSlides: 1,
        loopAdditionalSlides: 0,
        //一个屏幕展示的数量
        // slidesPerView: 20,
        slidesPerView: "auto",

        slidesPerGroup: 1,
        slidesPerGroupAuto: false,
        // autoplay: false,

        // 显示分页
        pagination: {
          el: ".swiper-pagination",
          clickable: true, //轮播按钮支持点击
        },
        // 设置点击箭头
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        // on: {
        //   click: (data) => {
        //     console.log("click-单-", data);
        //     // this.hancleClickReginFn(item);
        //   },

        //   doubleTap: function (swiper) {
        //     console.log("click---双击-");
        //     console.log(swiper);
        //   },
        // },
        lockInterval: null, //锁定时间interval
        lockTime: 20000, //锁定刷新间隔时间
        switchTime: 10000, //屏幕按钮滚动时间
      },
    };
  },
  computed: {
    ...mapGetters(["website", "userInfo", "menu"]),
    themIndex: function () {
      return localStorage.getItem("themeSelected");
    },

    // originUrl() {
    //   return window.location.origin;
    // },
    // token() {
    //   return this.$store.getters.access_token;
    // },
    // cloudPivotToken() {
    //   return localStorage.getItem("cloudPivotToken");
    // },
  },
  watch: {
    menu(newVal) {
      if (newVal) {
        // menu数据已经到达，执行所需的操作
        this.initializeMenu(newVal[0]);
      }
    },
  },
  created() {
    setTimeout(() => {
      console.log("menu==catalogues=", this.menu);

      this.handleClickTab(this.menu[0]);
    }, 800);
    this.$root.$on("getThemeIndex", this.getThemeIndexFn);
    this.$root.$on("updateDaohangDataFn", this.updateDaohangTreeFn);
    // this.initTopMenu();
    // this.handleGetMenuListFn();
    this.getListMenuInfo();
    this.getDaohangTreeFn();
  },
  mounted() {
    this.selfThemeIndex = this.themIndex;
    let currentMenuStr = localStorage.getItem("currentMenu");
    // if (currentMenuStr) {
    //   this.chooseMenu = JSON.parse(currentMenuStr);
    // }
    if (this.$route.path == "/main/home") {
      // this.chooseMenu = {
      //   parentId: -1,
      //   name: "首页",
      //   id: "2001",
      //   menuType: "T",
      // };
    }
  },
  methods: {
    getListMenuInfo() {
      getListMenu().then((res) => {
        console.log("获取的菜单lzs002==", res);
        if (res.data.code == 200) {
          this.menusArr = res.data.data;

          if (this.menusArr && this.menusArr.length > 0) {
            let menuStr2 = localStorage.getItem("currentMenu");
            if (menuStr2) {
              let currentMenu = JSON.parse(menuStr2) || [];
              if (currentMenu) {
                let fObj = this.menusArr.find((item) => {
                  return item.id == currentMenu.id;
                });
                if (fObj) {
                  localStorage.setItem("currentMenu", JSON.stringify(fObj));
                  this.$emit("initMenu");
                }
              }
            }
            //将新的currentMenu替换

            let menuApiCount = sessionStorage.getItem("menuApiCount");
            if (!menuApiCount) {
              sessionStorage.setItem("menuApiCount", 1);
              // 请求第一次 加载动态路由
              this.$router.$avueRouter.formatRoutes(this.menusArr, true);
            } else {
              menuApiCount++;
              sessionStorage.setItem("menuApiCount", menuApiCount);
            }
          } else {
            sessionStorage.removeItem("menuApiCount");
          }

          let obj = { type: true, menu: this.menusArr };
          this.$store.commit("SET_MENU", obj);
        }
        // console.log("获取的菜单lzs=menusArr=", this.menusArr);
      });
    },
    updateDaohangTreeFn() {
      // console.log("刷新updateDaohangTreeFn==");
      this.getDaohangTreeFn();
    },
    getDaohangTreeFn() {
      getDaohangMenuTree().then((res) => {
        console.log("res-lzs222===--", res);
        let topArr = [];
        if (res.data.code == 200) {
          topArr = res.data.data || [];
        }
        this.topMenuArr = [...this.fixTopMenu, ...topArr];
        // this.topMenuArr = topMenuArr.filter((item) => {
        //   return item.menuType == "T";
        // });
        // console.log("this.topMenuArr==", this.topMenuArr);
        // if (this.topMenuArr && this.topMenuArr.length > 0) {
        //   console.log("刷新时");
        //   this.handleClickTab(this.topMenuArr[0]);
        // }
      });
    },
    handleClickTab(item) {
      // console.log("this.$route==", this.$route);
      console.log("item=====点击顶部一级菜单---=", item);
      let nPath = this.$route.path;
      let queryObj = this.$route.query;
      console.log("2222", this.$route, item.name);

      // 消息中心页面单独处理
      if (
        this.$route.path != "/main/home" &&
        item.path != "/noticeCenter/noticeIndex/index"
      ) {
        setTimeout(() => {
          if (queryObj && Object.keys(queryObj).length > 0) {
            localStorage.setItem("previousPathQuery", JSON.stringify(queryObj));
          } else {
            localStorage.setItem("previousPathQuery", "");
          }
          localStorage.setItem("previousPath", nPath);
        }, 1000);
      }
      localStorage.setItem("currentMenu", JSON.stringify(item));
      // console.log("item=iroute.query.idd=", this.$route.query.id);
      // console.log("item=id=", item.id);
      this.chooseMenu = item;
      if (item.name == "首页") {
        this.$root.$emit("showHomeInShow");

        this.$router.push("/main/home");
      } else if (item.path == "/noticeCenter/noticeIndex/index") {
        // item.routeType == 1
        // console.log("我要跳转了---", item.path);
        this.$router.push(item.path);
        // this.$router.push("/main/moreInfo");
        this.$root.$emit("handleSonMenu", item);

        this.$router.push({
          path: "/main/index",
          query: {
            routeType: 1,
          },
        });
      }
      // else if (item.name == "工作台") {
      //   this.$root.$emit("handleSonMenu", item);
      //   this.$router.push({ path: "/main/index", query: { id: "guozuotai" } });
      // }
      else {
        let route = this.$route;
        // console.log("route==", route);
        if (route.path == "/main/index" && route.query.id == item.id) {
          // console.log("no-----");
          this.$root.$emit("handleSonMenu", item);
          return;
        }

        // this.$router.push({ path: "/main/index/" + item.id });
        this.$router.push({ path: "/main/index" });
        setTimeout(() => {
          this.$root.$emit("handleSonMenu", item);
        }, 100);
      }
    },
    dealSelectItem(path) {
      console.log("path==", path);
      // let path2 = path.replace("/index", "");
      let arr = findItem(this.menusArr, "path", path);
      console.log("arr=lzs--3333--------=", arr);
      if (arr && arr.length > 0) {
        let fObj = this.menusArr.find((item) => {
          return item.path == arr[0];
        });
        console.log("fObj==", fObj);
        if (fObj) {
          this.chooseMenu = fObj;
        }
      }
    },
    handleMouseOver(item) {
      if (item && item.children && item.children.length > 0) {
        item.sonMenuFlag = true;
        this.$forceUpdate();
      }
    },
    handleMouseLeave(item) {
      if (item && item.children && item.children.length > 0) {
        item.sonMenuFlag = false;
        this.$forceUpdate();
      }
    },
    handleMouseOverSon(item) {},
    handleSonItem(item) {
      console.log("点击子菜单---==", item);
    },
    getThemeIndexFn(index) {
      // console.log("index---", index);
      this.selfThemeIndex = index;
    },
    handleGetMenuListFn() {
      getAdminMenuTree().then((res) => {
        // console.log("获取所有的菜单==", res);
        if (res.data.code == 200) {
          this.topAllMenuAll = res.data.data;
          this.initTopMenu();
        }
      });
    },
    initTopMenu() {
      // console.log("333333=====", this.menu);
      getMenu(-1).then((res) => {
        // console.log("获取的菜单11-33---", res);
        if (res.data.code == 200) {
          let menuArr = res.data.data;
          if (menuArr && menuArr.length > 0) {
            this.topMenuArr = menuArr.filter((item) => {
              return item.menuType == "T";
            });
            if (this.topMenuArr && this.topMenuArr.length > 0) {
              this.activeIndex = this.topMenuArr[0].id;
            }
          }
          console.log("this.topMenuArr==", this.topMenuArr);
        }
      });

      // if (this.menu && this.menu.length > 0) {
      //   console.log("333333===1111==");
      //   this.topMenuArr = this.menu.filter((item) => {
      //     return item.menuType == "T";
      //   });
      //   if (this.topMenuArr && this.topMenuArr.length > 0) {
      //     this.activeIndex = this.topMenuArr[0].id;
      //   }
      //   // console.log("this.topMenuArr==", this.topMenuArr);
      // } else {
      //   console.log("333333===222==");
      //   // this.$store.dispatch("GetMenu", { type: true, id: -1 }).then((data) => {
      //   //   console.log("获取的菜单-22--", data);
      //   //   if (data && data.length > 0) {
      //   //     this.topMenuArr = data.filter((item) => {
      //   //       return item.menuType == "T";
      //   //     });
      //   //     if (this.topMenuArr && this.topMenuArr.length > 0) {
      //   //       this.activeIndex = this.topMenuArr[0].id;
      //   //     }
      //   //   }
      //   // });
      // }
    },
    handleSelect(key, keyPath) {
      // 2024年1月29日 在子系统菜单管理配置的参数地址 {"path":"http://192.168.117.5:8000/#/login"}
      // console.log("handleSelect====", key);
      // console.log("keyPath====", keyPath);
      // console.log(key, keyPath);
      this.activeIndex = key;
      // console.log("===", this.activeIndex);
      let fObj = this.topMenuArr.find((item) => {
        return item.id == key;
      });
      // console.log("fobj==", fObj);

      if (fObj && fObj.query) {
        //str.replace(/'/g, '"')

        let queryObj = JSON.parse(fObj.query);
        // console.log("query==", queryObj);
        if (queryObj && queryObj.type == 1) {
          window.open(queryObj.path, "_target");
        } else if (queryObj && queryObj.type == 0) {
          // console.log("this.$route==", this.$route);
          if (this.$route.path == queryObj.path) {
            return;
          }
          this.$router.push(queryObj.path);
        } else if (queryObj && queryObj.type == 3) {
          this.$router.push({
            path: "/home/<USER>",
            query: {
              ip: queryObj.path,
            },
          });
        } else if (queryObj && queryObj.type == 2) {
          this.$router.push({
            path: "/main/index",
            query: {
              ip: queryObj.path,
            },
          });
        }
      }
      // setTimeout(() => {
      //   this.$forceUpdate();
      //   console.log("样式---");
      // }, 1000);
    },
    handleShowFlag(flag) {
      this.topFlag = flag;
      this.$root.$emit("dealTopShow2", flag);
      this.$root.$emit("dealTopHeight", flag);
    },
    handlePrev() {
      this.menuIndex--;
      this.$refs.mySwiper.$swiper.slideTo(this.menuIndex);
    },
    handleNext() {
      console.log("handleNext", this.menuIndex);
      this.menuIndex++;
      this.$refs.mySwiper.$swiper.slideTo(this.menuIndex);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/theme/themesNew.scss";
// @import "@/styles/theme/iview.scss";

.catalogues {
  // background: linear-gradient(90deg, #1D92CF 0%, #3AB5E0 100%);//$black;//
  display: flex;
  justify-content: center;
}

.el-menu--horizontal {
  min-width: 1552px;
  // max-width: 1150px;
  position: relative;
}

.el-menu--horizontal > .el-menu-item.is-active {
  color: #fff !important;
  border-bottom: none;
}

.gzt-content {
  min-width: 1280px;
  height: 40px;
  color: #fff;
}

.my-cates {
  position: fixed;
  top: 60px;
  width: 100%;
  z-index: 96;

  // height: 60px;
  .my-top {
    position: absolute;
    top: 20px;
    right: 30px;
    color: #fff;
    font-size: 16px;
  }
}

ul {
  list-style-type: none;
}

.menu-container {
  .ul-content {
    margin: 0;
    display: flex;
    height: 60px;
    padding-left: 0px !important;

    // border: 1px solid red;
    .li-block {
      // margin-right: 10px;
      position: relative;
      height: 60px;
      line-height: 60px;
      padding: 0 10px;
      color: #fff;
      cursor: pointer;

      &:hover {
        // opacity: 0.5;
        background: rgba(0, 0, 0, 0.2);
      }

      .lb-menu {
        // position: absolute;
        // left: -10px;
        position: fixed;
        z-index: 9999;
        left: 0;

        // width: 500px;
        width: 100%;
        min-height: 60px;
        // border: 1px solid red;
        display: flex;
        // background: #437fff;
        background: rgba(0, 65, 153, 0.9);

        .lm-content {
          padding-left: 10px;
          width: 80%;
          margin: 0 auto;
        }
      }
    }

    .li-active {
      background: rgba(0, 0, 0, 0.2);
    }

    .swiper-content {
      display: flex;
      // min-width: 700px;
      // min-width: 800px;

      width: calc(100vw - 136px);
      // border: 1px solid red;
      margin-left: 12px;
    }

    .swiper-block {
      // width: 120px;
      width: auto !important;
      margin-right: 20px;

      .bg-block {
        width: 80px;
        height: 60px;
        line-height: 60px;
        color: #fff;
        cursor: pointer;
        // padding-right: 12px;
      }
    }
  }
}

::v-deep .swiper-button-disabled {
  display: none;
}
</style>
