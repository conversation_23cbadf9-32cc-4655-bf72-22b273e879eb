<template>
  <div :class="['menu-container', 'my-cates', 'mainBG']">
    <ul class="ul-content">
      <li
        class="li-block"
        :class="chooseMenu && chooseMenu.id == item.id ? 'li-active' : ''"
        v-for="item in topMenuArr"
        :key="item.id"
        @click="handleClickTab(item)"
        @mouseover="handleMouseOver(item)"
        @mouseleave="handleMouseLeave(item)"
      >
        {{ item.name }}
        <template v-if="item.children && item.children.length > 0">
          <div class="lb-menu" v-if="item.sonMenuFlag">
            <div
              class="lm-content"
              v-for="cItem in item.children"
              :key="cItem.id"
              @click.stop="handleSonItem(cItem)"
              @mouseover.stop="handleMouseOverSon(cItem)"
            >
              {{ cItem.name }}
            </div>
          </div>
        </template>
      </li>
    </ul>
    <!-- <div class="mainBGColor gzt-content">工作台</div> -->
    <!-- <el-menu
      :default-active="activeIndex"
      text-color="#fff"
      :class="'my-top-menu' + selfThemeIndex"
      mode="horizontal"
      @select="handleSelect"
    > -->
    <!-- <el-menu-item :index="1" class="mainBGColor">工作台</el-menu-item> -->
    <!-- :class="activeIndex == 1 ? 'mainBGColor' : ''" -->
    <!-- <el-menu-item index="2" :class="activeIndex == 2 ? 'mainBGColor' : ''"
        >应用中心</el-menu-item
      >
      <el-menu-item index="3" :class="activeIndex == 3 ? 'mainBGColor' : ''"
        >新闻资讯</el-menu-item
      > -->
    <!-- <el-menu-item
        v-for="(menuObj, i) in topMenuArr"
        :key="i"
        :index="menuObj.id"
        >{{ menuObj.name }}</el-menu-item
      >
    </el-menu> -->
    <!--  :class="activeIndex == menuObj.id ? 'mainBGColor' : ''" -->
    <!-- <div class="my-top">
      <i
        v-if="topFlag"
        class="el-icon-arrow-up"
        @click="handleShowFlag(false)"
      ></i>
      <i v-else class="el-icon-arrow-down" @click="handleShowFlag(true)"></i>
    </div> -->
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";

import {
  getAdminMenuTree,
  getDaohangMenuTree,
  getMenusById,
} from "@/api/admin/sys/menu";
import { getMenu } from "@/api/admin/sys/clientMenu";

export default {
  name: "index",
  data() {
    return {
      activeIndex: 1,
      sonMenuFlag: false,
      topMenuArr: [],
      fixTopMenu: [
        {
          parentId: -1,
          name: "首页",
          id: "2001",
        },
        {
          parentId: -1,
          name: "工作台",
          id: "2002",
        },
      ],
      topAllMenuAll: [],
      selfThemeIndex: "",
      topFlag: true,
      chooseMenu: {},
    };
  },
  computed: {
    ...mapGetters(["website", "userInfo", "menu"]),
    themIndex: function () {
      return localStorage.getItem("themeSelected");
    },

    // originUrl() {
    //   return window.location.origin;
    // },
    // token() {
    //   return this.$store.getters.access_token;
    // },
    // cloudPivotToken() {
    //   return localStorage.getItem("cloudPivotToken");
    // },
  },
  created() {
    // console.log("menu==catalogues=", this.menu);

    this.$root.$on("getThemeIndex", this.getThemeIndexFn);
    this.$root.$on("updateDaohangDataFn", this.updateDaohangTreeFn);
    // this.initTopMenu();
    // this.handleGetMenuListFn();
    this.getDaohangTreeFn();
  },
  mounted() {
    this.selfThemeIndex = this.themIndex;
  },
  methods: {
    updateDaohangTreeFn() {
      // console.log("刷新updateDaohangTreeFn==");
      this.getDaohangTreeFn();
    },
    getDaohangTreeFn() {
      getDaohangMenuTree().then((res) => {
        // console.log("res---", res);
        if (res.data.code == 200) {
          this.topMenuArr = res.data.data;
        }
        this.topMenuArr = [...this.fixTopMenu, ...this.topMenuArr];
      });
    },
    handleClickTab(item) {
      // console.log("item==", item);
      this.chooseMenu = item;
      if (item.name == "首页") {
        this.$router.push("/main/home");
      } else if (item.name == "工作台") {
        this.$root.$emit("handleSonMenu", "guozuotai");
        this.$router.push({ path: "/main/index", query: { id: "guozuotai" } });
      } else {
        this.$root.$emit("handleSonMenu", item.id);
        this.$router.push({ path: "/main/index", query: { id: item.id } });
      }
    },
    handleMouseOver(item) {
      if (item && item.children && item.children.length > 0) {
        item.sonMenuFlag = true;
        this.$forceUpdate();
      }
    },
    handleMouseLeave(item) {
      if (item && item.children && item.children.length > 0) {
        item.sonMenuFlag = false;
        this.$forceUpdate();
      }
    },
    handleMouseOverSon(item) {},
    handleSonItem(item) {
      // console.log("点击子菜单---==", item);
    },
    getThemeIndexFn(index) {
      // console.log("index---", index);
      this.selfThemeIndex = index;
    },
    handleGetMenuListFn() {
      getAdminMenuTree().then((res) => {
        // console.log("获取所有的菜单==", res);
        if (res.data.code == 200) {
          this.topAllMenuAll = res.data.data;
          this.initTopMenu();
        }
      });
    },
    initTopMenu() {
      // console.log("333333=====", this.menu);
      getMenu(-1).then((res) => {
        // console.log("获取的菜单11-33---", res);
        if (res.data.code == 200) {
          let menuArr = res.data.data;
          if (menuArr && menuArr.length > 0) {
            this.topMenuArr = menuArr.filter((item) => {
              return item.menuType == "T";
            });
            if (this.topMenuArr && this.topMenuArr.length > 0) {
              this.activeIndex = this.topMenuArr[0].id;
            }
          }
          console.log("this.topMenuArr==", this.topMenuArr);
        }
      });

      // if (this.menu && this.menu.length > 0) {
      //   console.log("333333===1111==");
      //   this.topMenuArr = this.menu.filter((item) => {
      //     return item.menuType == "T";
      //   });
      //   if (this.topMenuArr && this.topMenuArr.length > 0) {
      //     this.activeIndex = this.topMenuArr[0].id;
      //   }
      //   // console.log("this.topMenuArr==", this.topMenuArr);
      // } else {
      //   console.log("333333===222==");
      //   // this.$store.dispatch("GetMenu", { type: true, id: -1 }).then((data) => {
      //   //   console.log("获取的菜单-22--", data);
      //   //   if (data && data.length > 0) {
      //   //     this.topMenuArr = data.filter((item) => {
      //   //       return item.menuType == "T";
      //   //     });
      //   //     if (this.topMenuArr && this.topMenuArr.length > 0) {
      //   //       this.activeIndex = this.topMenuArr[0].id;
      //   //     }
      //   //   }
      //   // });
      // }
    },
    handleSelect(key, keyPath) {
      // 2024年1月29日 在子系统菜单管理配置的参数地址 {"path":"http://192.168.117.5:8000/#/login"}
      console.log("handleSelect====", key);
      console.log("keyPath====", keyPath);
      // console.log(key, keyPath);
      this.activeIndex = key;
      // console.log("===", this.activeIndex);
      let fObj = this.topMenuArr.find((item) => {
        return item.id == key;
      });
      console.log("fobj==", fObj);

      if (fObj && fObj.query) {
        //str.replace(/'/g, '"')

        let queryObj = JSON.parse(fObj.query);
        console.log("query==", queryObj);
        if (queryObj && queryObj.type == 1) {
          window.open(queryObj.path, "_target");
        } else if (queryObj && queryObj.type == 0) {
          // console.log("this.$route==", this.$route);
          if (this.$route.path == queryObj.path) {
            return;
          }
          this.$router.push(queryObj.path);
        } else if (queryObj && queryObj.type == 3) {
          this.$router.push({
            path: "/home/<USER>",
            query: {
              ip: queryObj.path,
            },
          });
        } else if (queryObj && queryObj.type == 2) {
          this.$router.push({
            path: "/main/index",
            query: {
              ip: queryObj.path,
            },
          });
        }
      }
      // setTimeout(() => {
      //   this.$forceUpdate();
      //   console.log("样式---");
      // }, 1000);
    },
    handleShowFlag(flag) {
      this.topFlag = flag;
      this.$root.$emit("dealTopShow2", flag);
      this.$root.$emit("dealTopHeight", flag);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/styles/theme/themesNew.scss";
// @import "@/styles/theme/iview.scss";

.catalogues {
  // background: linear-gradient(90deg, #1D92CF 0%, #3AB5E0 100%);//$black;//
  display: flex;
  justify-content: center;
}

.el-menu--horizontal {
  min-width: 1552px;
  // max-width: 1150px;
  position: relative;
}

.el-menu--horizontal > .el-menu-item.is-active {
  color: #fff !important;
  border-bottom: none;
}

.gzt-content {
  min-width: 1280px;
  height: 40px;
  color: #fff;
}
.my-cates {
  position: fixed;
  top: 60px;
  width: 100%;
  z-index: 96;
  // height: 60px;
  .my-top {
    position: absolute;
    top: 20px;
    right: 30px;
    color: #fff;
    font-size: 16px;
  }
}
ul {
  list-style-type: none;
}
.menu-container {
  .ul-content {
    margin: 0;
    display: flex;
    // border: 1px solid red;
    .li-block {
      // margin-right: 10px;
      position: relative;
      height: 60px;
      line-height: 60px;
      padding: 0 10px;
      color: #fff;
      cursor: pointer;
      &:hover {
        // opacity: 0.5;
        background: rgba(0, 0, 0, 0.2);
      }

      .lb-menu {
        // position: absolute;
        // left: -10px;
        position: fixed;
        z-index: 9999;
        left: 0;

        // width: 500px;
        width: 100%;
        min-height: 60px;
        // border: 1px solid red;
        display: flex;
        // background: #437fff;
        background: rgba(0, 65, 153, 0.9);

        .lm-content {
          padding-left: 10px;
          width: 80%;
          margin: 0 auto;
        }
      }
    }
    .li-active {
      background: rgba(0, 0, 0, 0.2);
    }
  }
}
</style>

