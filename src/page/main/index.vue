<template>
  <div class="echart-container">
    <top :isPortal="true" ref="topContent" v-if="topFlag"> </top>
    <top-menu
      :class="topFlag ? '' : 'top-content'"
      ref="topMenuRef"
      @initMenu="handleInitMenu"
    ></top-menu>
    <!-- <catalogue></catalogue> -->
    <!-- <div class="echart-content">
      <h2>待开发功能</h2>
    </div> -->
    <template v-if="homeFlag">
      <keep-alive>
        <router-view
          v-if="$route.meta.$keepAlive"
          class="outer-bg"
          :class="topFlag ? 'outer-bg-top1' : 'outer-bg-top2'"
          :style="{ backgroundImage: 'url(' + backgroundImageUrl + ')' }"
        />
      </keep-alive>
      <router-view
        v-if="!$route.meta.$keepAlive"
        class="outer-bg"
        :class="topFlag ? 'outer-bg-top1' : 'outer-bg-top2'"
        :style="{ backgroundImage: 'url(' + backgroundImageUrl + ')' }"
      />
    </template>
    <div
      class="avue-layout main-container"
      :class="topFlag ? 'outer-bg-top1' : 'outer-bg-top2'"
      v-else
    >
      <div class="main-left">
        <!-- 左侧导航栏 -->
        <sidebar ref="sidebarRef" />
      </div>
      <div class="avue-main main-right" :class="topFlag ? '' : 'main-high'">
        <!-- 顶部标签卡 -->
        <tags ref="tagsRef" />
        <!-- 主体视图层 -->
        <el-scrollbar style="margin-top: 40px">
          <keep-alive>
            <router-view class="avue-view" v-if="$route.meta.$keepAlive" />
          </keep-alive>
          <router-view class="avue-view" v-if="!$route.meta.$keepAlive" />

          <!-- <keep-alive>
            <router-view
              v-if="$route.meta.$keepAlive"
              class="outer-bg"
              :style="{ backgroundImage: 'url(' + backgroundImageUrl + ')' }"
            />
          </keep-alive>
          <router-view
            v-if="!$route.meta.$keepAlive"
            class="outer-bg"
            :style="{ backgroundImage: 'url(' + backgroundImageUrl + ')' }"
          /> -->
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>
<script>
import top from "@/page/index/top/";
import catalogue from "@/page/index/catalogues/index.vue";
import sidebar from "./compontents/sidebar/index.vue";
import TopMenu from "./compontents/topMenu/index.vue";
import tags from "./compontents/tags.vue";
import { getMenusById } from "@/api/admin/sys/menu";
import { findItem, findItemObject } from "@/util/util";
export default {
  components: {
    top,
    catalogue,
    sidebar,
    TopMenu,
    tags,
  },

  data() {
    return {
      topFlag: true,
      sonId: "",
      homeFlag: false,
      mainPageFlag: false,
      currentMenu: {},
      backgroundImageUrl: require("@/assets/image/themesImgs/blueThem.png"),
    };
  },
  created() {
    this.$root.$on("dealTopShow2", this.dealTopShowFn);
    // console.log("logs=route==", this.$route);
    if (this.$route.path == "/main/home") {
      this.homeFlag = true;
    }
    // console.log(" this.homeFlag==", this.homeFlag);
    this.$root.$on("handleSonMenu", this.handleSonMenuFn);
    if (this.$route.query && this.$route.query.id) {
      this.sonId = this.$route.query.id;
      this.getSonMenuFn();
    }
  },
  mounted() {},
  watch: {
    $route(to, from) {
      // console.log("from==", from); //从哪来
      // console.log("to-----", to); //到哪去

      if (from.path == to.path) {
        if (
          (to.query && to.query.routeType == 1) ||
          (from.query && from.query.routeType == 1)
        ) {
          this.homeFlag = true;
        }
        // console.log(" this.homeFlag-----", this.homeFlag); //

        return;
      }
      let toPath = to.path;
      let fromPath = from.path;

      //到哪去
      // console.log("当前页面路由地址：" + toPath);
      // console.log("上一个地址：" + fromPath);
      if (toPath == "/main/home") {
        localStorage.setItem("previousPath", fromPath);
        let queryObj = from.query;
        if (queryObj && Object.keys(queryObj).length > 0) {
          localStorage.setItem("previousPathQuery", JSON.stringify(queryObj));
        } else {
          localStorage.setItem("previousPathQuery", "");
        }
        this.homeFlag = true;
      } else {
        // this.homeFlag = false;
        if (to.query && to.query.routeType == 1) {
          this.homeFlag = true;
          this.$refs.topMenuRef.dealSelectItem(toPath);
        } else {
          this.homeFlag = false;
        }
        // if (to.path == "/noticeCenter/noticeIndex/index") {
        //   this.homeFlag = true;
        // }
        // console.log("this.currentMenu==", this.currentMenu);
        if (toPath == "/main/index") {
          this.setMenuFn();
        }
      }
    },
    // 监听，当路由发生变化的时候执行
    // "$route.path"(toPath, fromPath) {

    //   console.log("当前页面路由地址：" + toPath);
    //   console.log("上一个地址：" + fromPath);
    //   if (toPath == "/main/home") {
    //     localStorage.setItem("previousPath", fromPath);
    //     this.homeFlag = true;
    //   } else {
    //     this.homeFlag = false;
    //     // console.log("this.currentMenu==", this.currentMenu);
    //     if (toPath == "/main/index") {
    //       this.setMenuFn();
    //     }
    //   }
    // },
  },
  methods: {
    changeThemesImg() {
      let imgType = localStorage.getItem("themeSelected");
      switch (imgType) {
        case "0":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/blueThem.png");
          break;
        case "1":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/greenThem.png");
          break;
        case "2":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/redThem.png");
          break;
        case "3":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/skyThem.png");
          break;
        case "4":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/yelThem.png");
          break;
        default:
          this.backgroundImageUrl = require("@/assets/image/themesImgs/blueThem.png");
          break;
      }
    },
    handleInitMenu() {
      this.$nextTick(() => {
        this.$refs.sidebarRef.refreshMenu();
      });
    },
    handleDate() {
      return new Date().getTime();
    },
    dealTopShowFn(flag) {
      this.topFlag = flag;
    },
    handleSonMenuFn(item) {
      console.log("id==lzs---==", item);
      this.currentMenu = item;
      this.sonId = item.id;
      if (item.menuName == "工作台") {
        this.sonId = "guozuotai";
      }

      if (this.sonId == "guozuotai") {
        this.$store.dispatch("GetMenu", { type: true, id: -1 }).then((data) => {
          if (data.length === 0) return;

          this.$refs.sidebarRef.handleSetMenu(data);
          this.$router.$avueRouter.formatMainRoutes(data, true);
        });
      } else if (item.path == "/noticeCenter/noticeIndex/index") {
        //     item.routeType == 1
        this.homeFlag = true;
        let arr = [item];
        this.$router.$avueRouter.formatRoutes(arr, true);

        setTimeout(() => {
          this.$router.push({ path: item.path, query: { routeType: 1 } });
        }, 500);
      } else {
        if (this.homeFlag) {
          return;
        }
        this.setMenuFn();
        // console.log("homeFlag2==", this.homeFlag);
      }
    },
    setMenuFn() {
      console.log("最后");
      // this.$router.$avueRouter.formatRoutes(this.currentMenu, true);
      // this.$refs.sidebarRef.handleSetMenu([]);
      // console.log("this.currentMenu==", this.currentMenu);
      if (this.currentMenu.children && this.currentMenu.children.length > 0) {
        // console.log("lzs--路径1");

        let menuArr = this.currentMenu.children;
        console.log("menuArr=lzs=", menuArr);
        this.$nextTick(() => {
          // console.log("menuArr=lzs--出错前=");
          this.$router.$avueRouter.formatRoutes(this.currentMenu, true);
          // this.$router.$avueRouter.formatMainRoutes(menuArr, true);
          // console.log("menuArr=lzs--出错后=");
          if (this.$refs.sidebarRef) {
            // this.$refs.sidebarRef.handleSetMenu(menuArr);
            setTimeout(() => {
              this.$refs.sidebarRef.handleSetMenu(menuArr);
            }, 350);
            // this.$router.$avueRouter.formatMainRoutes(menuArr, true);
          }
        });
        let previousPath = localStorage.getItem("previousPath"); //获取上一个路由地址
        let previousPathQuery = localStorage.getItem("previousPathQuery"); //获取上一个路由地址
        //查看menuArr 是否包含 previousPath，如果包含就跳转，如果不包含跳转到第一个页面
        // let flag = this.checkPathInArr(menuArr, previousPath);
        // console.log("previousPath==", previousPath);
        // console.log("previousPathQuery==", previousPathQuery);
        let arr = findItem(menuArr, "path", previousPath);
        // console.log("flag=ll=", arr);
        if (arr && arr.length > 0) {
          // console.log("路径4");
          // console.log("previousPath3333333333333==", previousPath);
          setTimeout(() => {
            let queryObj = "";
            if (previousPathQuery) {
              queryObj = JSON.parse(previousPathQuery);
            }
            if (queryObj) {
              // console.log("lzs-路径10", previousPath);
              this.$router.push({ path: previousPath, query: queryObj });
            } else {
              // console.log("lzs-路径9", previousPath);
              let newPArr = findItemObject(menuArr, "path", previousPath);
              // console.log("newPArr", newPArr);
              if (newPArr && newPArr.length > 0) {
                let nItem = newPArr[0];
                if (
                  previousPath == nItem.path &&
                  nItem.sysSub &&
                  nItem.sysSub.id
                ) {
                  let queryObj = {
                    path: nItem.sysSub.sysPath + nItem.query,
                  };
                  this.$router.push({ path: previousPath, query: queryObj });
                } else if (previousPath == nItem.path && nItem.query) {
                  let queryObj = {
                    path: nItem.query,
                  };
                  this.$router.push({ path: previousPath, query: queryObj });
                } else {
                  this.$router.push(previousPath);
                }
              } else {
                this.$router.push(previousPath);
              }
            }
          }, 300);
        } else {
          if (menuArr && menuArr.length > 0) {
            // console.log("----111---");
            // console.log("路径3");
            if (menuArr[0].children && menuArr[0].children.length > 0) {
              if (
                menuArr[0].children[0].children &&
                menuArr[0].children[0].children.length > 0
              ) {
                setTimeout(() => {
                  if (menuArr[0].children[0].children[0].query) {
                    let iframePath = menuArr[0].children[0].children[0].query;
                    if (
                      menuArr[0].children[0].children[0].sysSub &&
                      menuArr[0].children[0].children[0].sysSub.sysPath
                    ) {
                      iframePath =
                        menuArr[0].children[0].children[0].sysSub.sysPath +
                        menuArr[0].children[0].children[0].query;
                    }

                    // let queryObj = JSON.parse(
                    //   menuArr[0].children[0].children[0].query
                    // );
                    let queryObj = {
                      path: iframePath,
                    };
                    this.$router.push({
                      path: menuArr[0].children[0].children[0].path,
                      query: queryObj,
                    });
                  } else {
                    this.$router.push(menuArr[0].children[0].children[0].path);
                  }
                }, 400);
              } else {
                // console.log("路径1");
                // 左侧菜单有两层时
                setTimeout(() => {
                  if (menuArr[0].children[0].query) {
                    let iframePath = menuArr[0].query;
                    if (
                      menuArr[0].children[0].sysSub &&
                      menuArr[0].children[0].sysSub.sysPath
                    ) {
                      iframePath =
                        menuArr[0].children[0].sysSub.sysPath +
                        menuArr[0].children[0].query;
                    }
                    // let queryObj = JSON.parse(menuArr[0].children[0].query);
                    let queryObj = {
                      path: iframePath,
                    };
                    this.$router.push({
                      path: menuArr[0].children[0].path,
                      query: queryObj,
                    });
                  } else {
                    this.$router.push(menuArr[0].children[0].path);
                  }
                }, 400);
              }
            } else {
              // console.log("路径2==", menuArr[0]);
              setTimeout(() => {
                if (menuArr[0].query) {
                  // let queryObj = JSON.parse(menuArr[0].query);
                  let iframePath = menuArr[0].query;
                  if (menuArr[0].sysSub && menuArr[0].sysSub.sysPath) {
                    iframePath = menuArr[0].sysSub.sysPath + menuArr[0].query;
                  }
                  let queryObj = {
                    path: iframePath,
                  };
                  // console.log("queryObj==", queryObj);
                  this.$router.push({ path: menuArr[0].path, query: queryObj });
                } else {
                  this.$router.push(menuArr[0].path);
                }
              }, 400);
            }
          }
        }
      } else {
        // console.log("this.currentMenu=没有下级菜单=", this.currentMenu);
        if (this.currentMenu.id) {
          setTimeout(() => {
            this.$refs.sidebarRef.handleSetMenu([]);
          }, 300);
        }
      }
    },

    checkPathInArr(arr, previousPath) {
      if (arr && arr.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          // console.log("arr[i].path===", arr[i].path);
          // console.log("2222===", previousPath);
          if (arr[i].path == previousPath) {
            return true;
          } else if (arr[i].children && arr[i].children.length > 0) {
            return this.checkPathInArr(arr[i].children, previousPath);
          }
        }
      }
    },
    getSonMenuFn() {
      getMenusById(this.sonId).then((res) => {
        console.log("sonid menu==", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.echart-container {
  width: 100%;
  height: 100%;
  overflow-y: scroll;

  .outer-bg {
    background-size: 100% 100%;
    background-attachment: fixed;
    height: 100%;
  }

  .echart-content {
    width: 1548px;
    margin: 0 auto;
    height: calc(100vh - 200px);
    border: 1px solid red;
  }
  .main-left {
    width: 220px;
    position: fixed;
    height: 100%;
  }
  .main-container {
    display: flex;
    margin-top: 120px;
  }
  .main-right {
    background: #f0f0f0;
    min-height: calc(100vh - 120px);
  }
  .main-high {
    min-height: calc(100vh - 60px);
  }
  .outer-bg-top1 {
    margin-top: 120px;
  }
  .outer-bg-top2 {
    margin-top: 60px;
  }
}
.top-content {
  top: 0px !important;
}
</style>