<!--
 * @Description: 
 * @Author: liuzhengshuai
 * @Date: 2022-09-06 14:28:22
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-05-25 18:24:29
-->
<template>
  <div class="console-tab mainBG">
    <ul>
      <div
        class="tab-item"
        :class="[curIndex === index ? 'mainBGColor' : 'mainColor']"
        v-for="(item, index) in consoleTabData"
        :key="index"
        @click="handleClickTab(index, item)"
      >
        <!-- <i :class="item.icon"></i> -->
        <span>
          {{ item.name }}
          <el-badge
            v-if="index != 2 && item.count"
            class="mark"
            :value="item.count"
          >
          </el-badge>
        </span>
      </div>
    </ul>
  </div>
</template>
<script>
export default {
  name: "mySelfTab",
  props: {
    consoleTabData: {
      type: Array,
      default: () => [],
    },
    curIndex: {
      type: Number,
      default: 1,
    },
  },
  methods: {
    handleGet(data) {
      // console.log("dd", data);
    },
    handleClickTab(index, item) {
      this.$emit("change", index, item);
    },
  },
};
</script>
<style lang="scss" scoped>
.console-tab {
  margin-top: 0px;
  position: relative;
  z-index: 1;
  // height: 50px;
  line-height: normal;
  background-color: #dae3f0;
  ul {
    display: flex;
  }
  .tab-item {
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 8px 16px 8px 16px;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    span {
      color: #ffffff;
    }
    i {
      color: #ffffff;
    }
  }
  .active {
    span {
      color: #ffffff;
    }
    i {
      color: #ffffff;
    }
  }
}
</style>
