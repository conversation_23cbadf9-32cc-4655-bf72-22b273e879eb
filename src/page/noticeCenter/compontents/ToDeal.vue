<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        :before-open="handleBeforeOpen"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="rowDel"
        v-model="form"
        :search.sync="search"
      >
        <!-- <template slot="menuLeft">
          <el-button
            v-if="tabIndex == 1"
            type="primary"
            @click="handleReadAll"
            size="small"
            >全部已读</el-button
          >
        </template> -->
        <template slot="sendTimeSearch">
          <el-date-picker
            v-model="search.searchTime"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            :default-time="['00:00:00', '23:59:59']"
            @change="handleChangeDate(search.searchTime)"
          >
          </el-date-picker>
        </template>
        <!--  HH:mm:ss -->

        <template slot="templateName" slot-scope="{ row }">
          <!-- {{ row.status }} -->
          <template v-if="tabIndex == 3">
            <span class="title-content mainColor" @click="openDetail(row)">{{
              row.templateName
            }}</span>
          </template>
          <template v-else>
            <span class="title-content mainColor" @click="toDetail(row)">{{
              row.templateName
            }}</span>
          </template>

          <!-- <el-switch
              v-model="row.status"
              @change="hanleSwitchChange(row)"
              open-label="是"
              close-label="否"
            ></el-switch> -->
        </template>

        <template slot="mutualStatus" slot-scope="scope">
          <div v-if="tabIndex == 3">
            <el-tag type="success" v-if="scope.row.mutualStatus == '已读'"
              >已读</el-tag
            >
            <el-tag type="danger" v-else-if="scope.row.mutualStatus == '未读'"
              >未读</el-tag
            >
            <el-tag type="info" v-else>未知</el-tag>
          </div>
          <div v-else>
            {{ scope.row.mutualStatus == "未读" ? "待办" : "已办" }}
          </div>
          <!-- mutualStatus，交互状态 0 未知 1已读 2未读 -->
          <!-- <el-tag type="info" v-if="scope.row.mutualStatus == '未读'"
            >待办</el-tag
          >
          <el-tag type="success" v-else-if="scope.row.mutualStatus == '已读'"
            >已办</el-tag
          > -->
        </template>
        <!-- <template slot="menu" slot-scope="{ row, index }">
            <el-button
              type="text"
              icon="el-icon-document"
              @click="handleView(row)"
            >
              预览
            </el-button>
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              @click="handleEdit(row, index)"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              size="small"
              @click="$refs.crud.rowDel(row, index)"
              >删除</el-button
            >
          </template> -->
      </avue-crud>
    </basic-container>
    <notice-message-view-dialog
      v-if="viewFlag"
      @closeDialog="closeViewDialog"
      :viewObj="viewObj"
    ></notice-message-view-dialog>
  </div>
</template>

<script>
import { fetchList, allRead } from "@/api/noticeCenter/noticeCenter";
import { tableOption } from "@/const/crud/noticeCenter/noticeCenter";
import { mapGetters } from "vuex";
import RichText from "@/components/rich-text/index.vue";
import NoticeMessageViewDialog from "@/views/noticePortal/compontents/NoticeMessageViewDialog.vue";

export default {
  name: "noticePortal",
  components: {
    RichText,
    NoticeMessageViewDialog,
  },
  data() {
    return {
      searchForm: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      form: {
        attachmentList: [],
      },
      headerObj: {},
      fileList: [],
      fileArr: [],
      fileObj: "",
      viewFlag: false,
      viewObj: {},
      search: {},
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  props: {
    tabIndex: {
      type: Number,
      default: function () {
        return 0;
      },
    },
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.noticePortal_add, false),
        delBtn: this.vaildData(this.permissions.noticePortal_delete, false),
        editBtn: this.vaildData(this.permissions.noticePortale_edit, false),
      };
    },
    token() {
      return this.$store.getters.access_token;
    },
    cloudPivotToken() {
      return localStorage.getItem("cloudPivotToken");
    },
  },
  watch: {
    tabIndex: {
      handler(val, oldVal) {
        console.log(val);

        this.page.currentPage = 1;
        this.page.pageSize = 10;
        this.page.total = 0;
        this.getList(this.page);
      },
    },
  },
  created() {
    console.log("created--==", this.tabIndex);
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  methods: {
    toDetail(row) {
      var jumpUrl = row.messageJumpUrl;
      if (!jumpUrl.startsWith("http")) {
        jumpUrl = "http://" + jumpUrl;
      }
      window.open(jumpUrl);
    },
    openDetail(row) {
      // console.log("打开页面--", row);
      this.viewObj = row;
      this.viewFlag = true;

      // console.log("row==", row);
      let params = {
        // messageType: this.tabIndex,
        ids: [row.id],
      };
      allRead(params).then((res) => {
        console.log("re已读s==", res);
        if (res.data.code == 200) {
          this.getList(this.page);
        }
      });
    },
    handleClickAUrl(row) {
      // console.log("row==", row);
      // let params = {
      //   // messageType: this.tabIndex,
      //   ids: [row.id],
      // };
      // allRead(params).then((res) => {
      //   console.log("re已读s==", res);
      //   if (res.data.code == 200) {
      //     this.getList(this.page);
      //   }
      // });
    },
    handleChangeDate(date) {
      console.log("date", date);
      if (date == null || date == []) {
        this.search.startTime = "";
        this.search.endTime = "";
      } else {
        this.search.startTime = date[0] + " 00:00:00";
        this.search.endTime = date[1] + " 23:59:59";
      }
      console.log("date", this.page);
    },
    handleReadAll() {
      // console.log("全部已读==");
      // let params = {
      //   messageType: this.tabIndex,
      //   // ids: [],
      // };
      // allRead(params).then((res) => {
      //   // console.log("re已读s==", res);
      //   if (res.data.code == 200) {
      //     this.getList(this.page);
      //   }
      // });
    },
    clickRead(row) {
      // let params = {
      //   ids: [row.id],
      // };
      // allRead(params).then((res) => {
      //   console.log("re已读s==", res);
      //   if (res.data.code == 200) {
      //     this.getList(this.page);
      //   }
      // });
    },
    handleEdit(row, index) {
      // this.$refs.crud.visible = true;
      this.$refs.crud.rowEdit(row, index);
    },
    getList(page, params) {
      this.tableLoading = true;
      var messageType = this.tabIndex;
      var mutualStatus;
      if (this.tabIndex == 0) {
        mutualStatus = 2;
        messageType = 0;
      } else if (this.tabIndex == 1) {
        mutualStatus = 1;
        messageType = null;
      } else if (this.tabIndex == 3) {
        messageType = 1;
        // mutualStatus = 2;
      }
      var params = {
        current: page.currentPage,
        size: page.pageSize,
        messageType: messageType,
        mutualStatus: mutualStatus,
        status: 1,
      };

      params = Object.assign(params, this.searchForm);
      fetchList(params)
        .then((response) => {
          // console.log("-----fetch---", response);
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
          // this.tableData.forEach((item) => {
          //   item.status = +item.status == 1;
          // });
          //
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel: function (row, index) {
      this.$confirm("是否确认删除" + row.title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
    handleUpdate: function (row, index, done, loading) {
      row.status = +row.status;
      console.log("this.fileArr=handleUpdate=", this.fileArr);
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }
      putObj(row)
        .then((data) => {
          this.$message.success("修改成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleSave: function (row, done, loading) {
      row.status = +row.status;
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }

      addObj(row)
        .then((data) => {
          this.$message.success("添加成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    searchChange(form, done) {
      this.searchForm = form;
      console.log("======", this.searchForm);

      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
    beforeAvatarUpload(file) {
      console.log("file==", file);
      let lastType = file.name.substr(file.name.lastIndexOf(".") + 1);
      const isJPG =
        file.type === "image/jpeg" ||
        file.type == "image/jpg" ||
        file.type == "image/png" ||
        file.type == "application/pdf" ||
        lastType == "doc" ||
        lastType == "docx" ||
        lastType == "ppt" ||
        lastType == "pptx" ||
        lastType == "xlsx" ||
        lastType == "xls";
      const isLt2M = file.size / 1024 / 1024 < 100;

      if (!isJPG) {
        this.$message.error("文件类型错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 100MB!");
      }
      return isJPG && isLt2M;
    },
    // 文件上传成功后
    handleUploadDocSuccess(res, file, fileList) {
      // console.log("file==", file);
      console.log("res==", res);
      // res.data.fileType
      if (res.code == 200) {
        this.fileObj = res.data;
        let obj = {
          name: file.name,
          url:
            window.location.protocol +
            "//" +
            window.location.host +
            res.data.url,
        };
        this.fileList.push(obj);

        // 保存到后端的数据不需要加domain
        let urlObj = {
          fileName: file.name,
          url: res.data.url,
          fileType: res.data.fileType,
        };
        this.fileArr.push(urlObj);
        console.log("this.fileArr==", this.fileArr);
      } else {
        console.log("fileList==", fileList);
        fileList.splice(fileList.length - 1, 1);
        this.$message({
          showClose: true,
          message: res.msg,
          type: "warning",
        });
      }
    },
    handleUploadDocError(res, file) {
      console.log("res==", res);
    },
    handlePreview(file) {
      console.log("file==", file);
      // let url = window.location.protocol + "//" + window.location.host + file.response.data.url;
      window.open(file.url, "target");
      // window.location.href = url;
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      let arr = [];
      this.fileArr.forEach((item) => {
        // 在文件列表中查找是否有被删除的文件，有的话则踢出，没有的话则重新放入新数组
        if (file.url.indexOf(item.url) === -1) {
          arr.push(item);
        }
      });
      this.fileArr = arr;
    },
    hanleSwitchChange(row) {
      row.status = +row.status;
      let params = {
        status: row.status,
        id: row.id,
        title: row.title,
      };
      updateStatus(params).then((res) => {
        // console.log("res=修改状态=", res);
        if (res.data.code == 200) {
          this.getList(this.page);
        }
      });

      // if (row.enableStatus) {
      //   this.$message.success("启用成功!");
      // } else {
      //   this.$message.success("禁用成功!");
      // }
    },
    handleBeforeOpen(done) {
      this.fileList = [];
      this.fileArr = [];
      if (this.form.attachmentList && this.form.attachmentList.length > 0) {
        this.form.attachmentList.forEach((file) => {
          if (file.url) {
            let obj = {
              name: file.fileName,
              url:
                window.location.protocol +
                "//" +
                window.location.host +
                file.url,
            };
            this.fileList.push(obj);
            let urlObj = {
              fileName: file.fileName,
              url: file.url,
              fileType: file.fileType,
            };
            this.fileArr.push(urlObj);
          }
        });
      }

      done();
    },
    handleView(row) {
      console.log("--row=预览=", row);
      this.viewFlag = true;
      this.viewObj = row;
    },
    closeViewDialog() {
      this.viewFlag = false;
    },
    setSinfo(val) {
      // console.log("val", val);
      this.form.content = val;
      // console.log("form", this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
// /deep/ .el-table th.el-table__cell {
//   text-align: center;
// }
.title-content {
  cursor: pointer;
}
</style>
