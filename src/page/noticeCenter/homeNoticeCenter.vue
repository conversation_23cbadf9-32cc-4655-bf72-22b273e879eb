<template>
  <div style="height: 100%; padding: 10px 20px; border-radius: 15px">
    <div style="position: relative; background-color: green; width: 100%">
      <span
        style="
          font-size: 14px;
          position: absolute;
          right: 0px;
          top: 5px;
          z-index: 1;
          cursor: pointer;
          padding: 3px 10px;
          border-radius: 4px;
          color: white;
        "
        class="mainBGColor"
        @click="showMore()"
        >查看更多</span
      >
    </div>

    <div v-if="todoListNum > 0" style="position: relative">
      <el-badge style="position: absolute; left: 65px" :value="todoListNum">
      </el-badge>
    </div>

    <!-- <el-badge class="mark" :value="total"> </el-badge> -->
    <el-tabs
      class="my-tabs tabsTheme"
      v-model="activeName"
      @tab-click="handleClick"
    >
      <el-tab-pane label="待办" name="first" class="title-value"> </el-tab-pane>
      <el-tab-pane label="已办" name="second" class="title-value">
      </el-tab-pane>
    </el-tabs>
    <el-table
      v-if="tableData.length"
      :data="tableData"
      style="
        width: 100%;
        height: 83%;
        margin-top: -15px;
        background-color: transparent;
      "
    >
      <el-table-column
        class-name="column"
        prop=""
        label="序号"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          {{ scope.$index + 1 + (currentPage - 1) * pageSize }}
        </template>
      </el-table-column>
      <el-table-column class-name="column" label="标题" min-width="140">
        <template slot-scope="scope">
          <span class="blue-font-color mainColor" @click="toDetail(scope.row)">
            {{ scope.row.templateName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        class-name="column"
        prop="content"
        label="内容"
        min-width="350"
      >
      </el-table-column>
      <el-table-column class-name="column" label="状态" width="80">{{
        activeName == "first" ? "待办" : "已办"
      }}</el-table-column>
      <el-table-column
        class-name="column"
        prop="sendTime"
        label="接收时间"
        width="150"
      >
      </el-table-column>
      <el-table-column
        class-name="column"
        prop="clientName"
        label="来源"
        width="150"
      >
      </el-table-column>
    </el-table>
    <div
      v-if="tableData.length"
      style="background-color: transparent; padding: 6px 0"
    >
      <el-pagination
        background
        layout="->, total,prev,pager,next"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
    <DataEmpty
      style="margin-top: 100px; height: 325px"
      v-if="tableData.length == 0"
      desc="暂无内容"
    ></DataEmpty>
  </div>
</template>
<script>
import { getMessageList } from "@/api/message/message.js";
import DataEmpty from "@/components/data-empty/index.vue";

export default {
  components: {
    DataEmpty,
  },
  props: {
    todoListNum: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      currentPage: 1, // 当前页码默认值为1
      pageSize: 5, // 每页显示条数默认值为10
      total: 0,
      tabIndex: 0,
      tableData: [],
      activeName: "first",
    };
  },
  created() {
    this.getMsgList();
  },
  computed: {
    originUrl() {
      return window.location.origin;
    },
    token() {
      return this.$store.getters.access_token;
    },
  },
  methods: {
    toDetail(row) {
      if (row.messageJumpUrl) {
        var jumpUrl = row.messageJumpUrl;
        if (!jumpUrl.startsWith("http")) {
          jumpUrl = "http://" + jumpUrl;
        }
        //activeName
        let query1 = "";
        if (this.activeName == "first") {
          query1 = "readType=0";
        } else {
          query1 = "readType=1";
        }
        if (jumpUrl.includes("?")) {
          jumpUrl = jumpUrl + "&" + query1;
        } else {
          jumpUrl = jumpUrl + "?" + query1;
        }

        window.open(jumpUrl);
      } else {
        this.$root.$emit("showHomeInShow", true, 0);
      }
    },
    handleClick(tab, event) {
      this.tabIndex = parseInt(tab.index);
      this.getMsgList();
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getMsgList();
      // 根据 this.currentPage 获取对应页面数据
      //...
    },
    getMsgList() {
      var messageType = 0;
      var mutualStatus = 2;
      if (this.tabIndex == 0) {
        messageType = 0;
        mutualStatus = 2;
      } else if (this.tabIndex == 1) {
        mutualStatus = 1;
        messageType = null;
      }
      getMessageList({
        current: this.currentPage,
        size: this.pageSize,
        status: 1,
        messageType: messageType,
        mutualStatus: mutualStatus,
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.tableData = res.data.data.records || [];
            this.total = res.data.data.total;
          }
          setTimeout(() => {
            this.tableData.length > 0
              ? (this.todoFlag = false)
              : (this.todoFlag = true);
          }, 300);
        })
        .catch((err) => {
          this.todoFlag = true;
        });
    },
    showMore() {
      var type = 0;
      if (this.tabIndex == 1) {
        type = 1;
      }
      this.$root.$emit("showHomeInShow", true, type);
    },
  },
};
</script>
<style lang="scss" scoped>
.title-value {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
}

.my-tabs {
}

.my-label {
  font-size: 30px;
}

.blue-font-color {
  cursor: pointer;
}

::v-deep .column {
  .cell {
    color: #333333;
    font-size: 14px;
    display: flex;
    align-items: center;
    height: 48px;
    justify-content: center;
    text-align: center;
    background-color: transparent;
  }
}

::v-deep .el-tabs__content {
  background-color: transparent;
}

::v-deep .el-table th.el-table__cell > .cell {
  display: flex;
  align-items: center;
}

::v-deep .el-table::before {
  height: 0px;
}

::v-deep .el-table--small .el-table__cell {
  background-color: white;
}

::v-deep .el-table__body tr:hover > td {
  background-color: white !important;
}
</style>
