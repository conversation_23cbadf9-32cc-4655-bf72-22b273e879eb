<!--
 * @Description: 
 * @Author: liuzhengshuai
 * @Date: 2023-04-20 17:15:14
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-05-24 15:36:08
-->
<template>
  <div class="container">
    <top :isPortal="true"></top>
    <!-- <top-menu></top-menu> -->
    <div class="notice-center">
      <!-- <div class="back" @click="backAction">/工作台/消息中心</div> -->
      <MyTab
        :cur-index="curIndex"
        :console-tab-data="consoleTabData"
        @change="handleChangeTab"
        class="tab-content"
      />
      <!-- <div class="tabs-content">
        <el-tabs v-model="activeName2" @tab-click="handleClick">
          <el-tab-pane label="待办" name="0"></el-tab-pane>
          <el-tab-pane label="已办" name="1"></el-tab-pane>
          <el-tab-pane label="通知公告" name="2"></el-tab-pane>
          <el-tab-pane label="临界预警" name="3"></el-tab-pane>
        </el-tabs>
      </div> -->
      <div class="table">
        <component :is="currentTabComponent" :tabIndex="curIndex"></component>
      </div>
    </div>
  </div>
</template>
<script>
import MyTab from "./compontents/MySelfTab.vue";
import ToDeal from "./compontents/ToDeal.vue";
import NoticePortalView from "./../noticePortalView/index.vue";
import { getMsgCount } from "@/api/noticeCenter/noticeCenter";
import top from "@/page/index/top/";
import TopMenu from "@/page/main/compontents/topMenu";
export default {
  components: {
    MyTab,
    ToDeal,
    NoticePortalView,
    top,
    TopMenu,
  },
  data() {
    return {
      activeName: "待办",
      currentTabComponent: "ToDeal",
      curIndex: 0,
      activeName2: "",
      consoleTabData: [
        {
          name: "待办",
          component: "ToDeal",
          count: 0,
        },
        {
          name: "已办",
          component: "ToDeal",
          count: 0,
        },
        {
          name: "通知公告",
          component: "NoticePortalView",
          count: 0,
        },

        {
          name: "临界预警",
          component: "ToDeal",
          count: 0,
        },
      ],
      msgCountObj: {
        notifyCount: 0, // 	通知数量
        otherCount: 0, // 	其他数量
        todoCount: 0, // 	待办消息数量
        warnCount: 0, //告警数量
      },
    };
  },
  created() {
    this.currentTabComponent = "ToDeal";
    // if (this.$route.query && this.$route.query.type) {
    //   this.curIndex = parseInt(this.$route.query.type);

    //   this.activeName2 = this.$route.query.type;
    //   if (this.$route.query.type == 2) {
    //     this.currentTabComponent = "NoticePortalView";
    //   }
    // } else {
    //   this.curIndex = 0;
    // }

    // this.$route.query.type;

    this.handleGetMsgCount();
  },
  mounted() {
    // this.currentTabComponent = "ToDeal";
    if (this.$route.query && this.$route.query.type) {
      this.curIndex = parseInt(this.$route.query.type);

      this.activeName2 = this.$route.query.type;
      if (this.$route.query.type == 2) {
        this.currentTabComponent = "NoticePortalView";
      }
    } else {
      this.curIndex = 0;
    }
  },
  methods: {
    backAction() {
      this.$router.back();
    },
    handleGetMsgCount() {
      getMsgCount().then((res) => {
        console.log("res消息==", res);
        if (res.data.code == 200) {
          this.msgCountObj.notifyCount =
            parseInt(res.data.data.notifyCount) || 0;
          this.msgCountObj.otherCount = res.data.data.otherCount || 0;
          this.msgCountObj.todoCount = res.data.data.todoCount || 0;
          this.msgCountObj.warnCount = res.data.data.warnCount || 0;

          this.consoleTabData[0].count = this.msgCountObj.todoCount;
          this.consoleTabData[1].count = this.msgCountObj.otherCount;
          // this.consoleTabData[2].count = this.msgCountObj.notifyCount;
          this.consoleTabData[3].count = this.msgCountObj.warnCount;
          let unreadedNumber = 0;
          if (this.msgCountObj.todoCount) {
            unreadedNumber = unreadedNumber + this.msgCountObj.todoCount;
          }
          if (this.msgCountObj.warnCount) {
            unreadedNumber = unreadedNumber + this.msgCountObj.warnCount;
          }
          if (this.msgCountObj.otherCount) {
            unreadedNumber = unreadedNumber + this.msgCountObj.otherCount;
          }
          this.$root.$emit("allGetNewMsg", unreadedNumber);
          this.consoleTabData.forEach((cObj, i) => {
            if (cObj.count > 99) {
              cObj.count = "99+";
            }
          });
        }
      });
    },
    handleChangeTab(index, item) {
      console.log("index==", index);
      this.curIndex = index;
      this.currentTabComponent = item.component;
    },
    handleClick() {
      if (this.activeName2 == "0") {
        this.curIndex = 0;
        this.currentTabComponent = "ToDeal";
      } else if (this.activeName2 == "1") {
        this.curIndex = 1;
        this.currentTabComponent = "ToDeal";
      } else if (this.activeName2 == "2") {
        this.curIndex = 2;
        this.currentTabComponent = "NoticePortalView";
      } else if (this.activeName2 == "3") {
        this.curIndex = 3;
        this.currentTabComponent = "ToDeal";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
}
.notice-center {
  margin-top: 60px;
  // .back {
  //   padding: 20px 0;
  //   margin-top: 60px;
  //   margin-left: 20px;
  //   margin-right: 20px;
  //   font-size: 16px;
  //   font-weight: 700;
  //   color: #3489ff;
  //   cursor: pointer;
  // }
  .table {
    margin-top: 0px;
    height: calc(100vh - 120px);
    overflow: auto;
  }
}
.tabs-content {
  // margin: 10px;
  // margin-top: 120px;
  margin: 120px 24px 10px 24px;
}
</style>