<!--
 * @Description: 
 * @Author: liuzhengshuai
 * @Date: 2023-04-20 17:15:14
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-05-24 15:36:08
-->
<template>
  <div class="container">
    <top :isPortal="true"></top>
    <!-- <top-menu></top-menu> -->
    <div class="notice-center">
      <div style="width: 100%; padding: 20px 0">
        <div
          class="title-main"
          style="justify-content: space-between; padding-right: 40px"
        >
          <div class="title-main">
            <span class="mainBGColor icon-tzgg"> </span>
            <span class="title-value">更多消息</span>
          </div>
          <!-- <el-button plain @click="backAction">返回首页</el-button> -->
        </div>
      </div>
      <!-- <div class="back" @click="backAction">/工作台/消息中心</div> -->
      <!-- <MyTab
        :cur-index="curIndex"
        :console-tab-data="consoleTabData"
        @change="handleChangeTab"
        class="tab-content"
      /> -->
      <div class="tabs-content">
        <el-tabs
          class="tabsTheme"
          v-model="activeName2"
          @tab-click="handleClick"
        >
          <el-tab-pane
            v-for="item in consoleTabData"
            :key="item.index"
            :name="item.status"
            :count="item.count"
          >
            <span slot="label"
              >{{ item.name }}
              <el-badge
                :value="item.count"
                v-show="item.count > 0 ? true : false"
                size="mini"
                class="item"
              ></el-badge>
            </span>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="table">
        <component :is="currentTabComponent" :tabIndex="curIndex"></component>
      </div>
    </div>
  </div>
</template>
<script>
import MyTab from "./compontents/MySelfTab.vue";
import ToDeal from "./compontents/ToDeal.vue";
import NoticePortalView from "./../noticePortalView/index.vue";
import { getMsgCount } from "@/api/noticeCenter/noticeCenter";
import top from "@/page/index/top/";
import TopMenu from "@/page/main/compontents/topMenu";
export default {
  props: {
    messageType: {
      type: Number,
      default: 0,
    },
  },
  components: {
    MyTab,
    ToDeal,
    NoticePortalView,
    top,
    TopMenu,
  },
  data() {
    return {
      activeName: "待办",
      currentTabComponent: "ToDeal",
      curIndex: 0,
      activeName2: "",
      consoleTabData: [
        {
          name: "待办",
          component: "ToDeal",
          count: 0,
        },
        {
          name: "已办",
          component: "ToDeal",
          count: 0,
        },
        {
          name: "通知公告",
          component: "NoticePortalView",
          count: 0,
        },

        {
          name: "临界预警",
          component: "ToDeal",
          count: 0,
        },
      ],
      msgCountObj: {
        notifyCount: 0, // 	通知数量
        otherCount: 0, // 	其他数量
        todoCount: 0, // 	待办消息数量
        warnCount: 0, //告警数量
      },
    };
  },
  created() {
    this.currentTabComponent = "ToDeal";
    // if (this.messageType) {
    //   this.curIndex = this.messageType;
    //   this.activeName2 = this.messageType.toString();
    //   if (this.messageType == 2) {
    //     this.currentTabComponent = "NoticePortalView";
    //   }
    // } else {
    //   this.curIndex = 0;
    // }

    this.handleGetMsgCount();
  },
  mounted() {
    if (this.$route.query && this.$route.query.type) {
      this.curIndex = parseInt(this.$route.query.type);

      this.activeName2 = this.$route.query.type;
      if (this.$route.query.type == 2) {
        this.currentTabComponent = "NoticePortalView";
      }
    } else {
      this.curIndex = 0;
    }
  },
  methods: {
    backAction() {
      this.$root.$emit("showHomeInShow");
    },
    handleGetMsgCount() {
      getMsgCount().then((res) => {
        console.log("res消息==", res);
        if (res.data.code == 200) {
          this.msgCountObj.notifyCount =
            parseInt(res.data.data.notifyCount) || 0;
          this.msgCountObj.otherCount = res.data.data.otherCount || 0;
          this.msgCountObj.todoCount = res.data.data.todoCount || 0;
          this.msgCountObj.warnCount = res.data.data.warnCount || 0;

          this.consoleTabData[0].count = this.msgCountObj.todoCount;
          this.consoleTabData[1].count = this.msgCountObj.otherCount;
          // this.consoleTabData[2].count = this.msgCountObj.notifyCount;
          this.consoleTabData[3].count = this.msgCountObj.warnCount;
          let unreadedNumber = 0;
          if (this.msgCountObj.todoCount) {
            unreadedNumber = unreadedNumber + this.msgCountObj.todoCount;
          }
          if (this.msgCountObj.warnCount) {
            unreadedNumber = unreadedNumber + this.msgCountObj.warnCount;
          }
          if (this.msgCountObj.otherCount) {
            unreadedNumber = unreadedNumber + this.msgCountObj.otherCount;
          }
          this.$root.$emit("allGetNewMsg", unreadedNumber);
          this.consoleTabData.forEach((cObj, i) => {
            if (cObj.count > 99) {
              cObj.count = "99+";
            }
          });
        }
      });
    },
    handleChangeTab(index, item) {
      console.log("index==", index);
      this.curIndex = index;
      this.currentTabComponent = item.component;
    },
    handleClick() {
      if (this.activeName2 == "0") {
        this.curIndex = 0;
        this.currentTabComponent = "ToDeal";
      } else if (this.activeName2 == "1") {
        this.curIndex = 1;
        this.currentTabComponent = "ToDeal";
      } else if (this.activeName2 == "2") {
        this.curIndex = 2;
        this.currentTabComponent = "NoticePortalView";
      } else if (this.activeName2 == "3") {
        this.curIndex = 3;
        this.currentTabComponent = "ToDeal";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  min-height: 100%;
  height: auto;
  width: 100%;
}

.notice-center {
  height: auto;
  width: 100%;
  padding: 0 60px;
  background: white;

  .table {
    margin-top: 0px;
    height: 100%;
    overflow: auto;
  }
}

.tabs-content {
  padding: 0 40px;
}

.title-main {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-self: center;

  .icon-tzgg {
    margin-left: 20px;
    margin-right: 10px;
    width: 4px;
    height: 24px;
  }

  .title-value {
    font-weight: bold;
    font-size: 18px;
    color: #333333;
  }
}
</style>