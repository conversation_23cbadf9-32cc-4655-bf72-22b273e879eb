<template>
  <div style="height: auto">
    <top :isPortal="true" ref="topContent"> </top>

    <div
      ref="scrollTopDiv"
      class="portal-outer"
      :style="{ backgroundImage: 'url(' + backgroundImageUrl + ')' }"
    >
      <!-- self-bg2 -->
      <more-info v-if="showMoreInfoView" :messageType="messageType"></more-info>

      <div v-if="!showMoreInfoView" class="portal-container">
        <div class="btn-content" v-if="isDraggable">
          <div class="save-btn mainColor" @click="handleSave">
            <!-- <span class="icon-btn"></span> -->
            保存
          </div>
          <div class="save-btn mainColor" @click="handleCancle">取消</div>
          <div class="save-btn mainColor" @click="handleReset">重置</div>
        </div>
        <grid-layout
          :class="isDraggable ? 'mainBGrid' : ''"
          :layout="layout"
          :col-num="12"
          :row-height="120"
          :is-draggable="isDraggable"
          :is-resizable="isDraggable"
          :is-mirrored="false"
          :vertical-compact="true"
          :preventCollision="false"
          :margin="[10, 10]"
          :use-css-transforms="true"
        >
          <!-- 背景框 -->
          <!-- <myGridItemPreview :itemData="backgroundData" v-if="isDraggable" /> -->
          <grid-item
            gridItem
            v-for="item in layout"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
            :key="item.i"
            :minW="item.minW"
            :minH="item.minH"
            :maxW="item.maxW"
            :maxH="item.maxH"
            class="grid-item"
            @moved="movedEvent"
          >
            <!-- <div
              class="grid-item-close"
              @click="closeItem(item)"
              v-if="isDraggable"
            >
              &times;
            </div> -->
            <!-- {{ item.i }} -->
            <!-- self-bg -->
            <div
              v-if="item.i == 'self1'"
              class="portal-app"
              :class="isDraggable ? 'mainBGColor' : ''"
            >
              <div class="app-abs" v-if="isDraggable"></div>
              <div class="app-title">
                <el-tabs
                  class="my-tabs tabsTheme"
                  v-model="activeName"
                  @tab-click="handleClickTab"
                >
                  <el-tab-pane
                    v-for="(item, i) in groupAppList"
                    :key="i"
                    :name="item.groupId"
                    :label="item.groupName"
                  ></el-tab-pane>
                  <!-- <el-tab-pane label="配置管理" name="second"
                      >配置管理</el-tab-pane
                    > -->
                </el-tabs>
              </div>

              <div class="app-content">
                <div class="app-mycontent">
                  <div
                    class="block-content"
                    v-for="(item, index) in usualAppList"
                    :key="item.id"
                  >
                    <div
                      class="app-block"
                      :title="item.name"
                      @click="usualAppFn(item)"
                    >
                      <div
                        style="
                          width: 62px;
                          height: 62px;
                          border-radius: 5px;
                          padding: 0px;
                        "
                      >
                        <img
                          class="app-icon"
                          v-if="
                            item.logo &&
                            (item.logo.includes('http://') ||
                              item.logo.includes('https://'))
                          "
                          :src="item.logo"
                        />
                        <img
                          class="app-icon"
                          v-else
                          :src="picUrl + item.logo"
                        />
                      </div>
                      <div class="app-cliName">{{ item.name }}</div>
                    </div>
                  </div>
                  <div
                    class="block-content add-content"
                    @click="addUseApp"
                    v-if="activeName == '-1'"
                  >
                    <div class="img-add"></div>
                    <div class="add-title">新增常用应用</div>
                  </div>
                </div>
                <!-- <a
                  v-for="item in usualAppList"
                  :key="item.clientId"
                  :href="item.clientIndexUrl"
                  target="_blank"
                >
                  <div class="app-block" :title="item.clientName">
                    <img class="app-icon" :src="picUrl + item.clientLogo" />
                    <div class="app-cliName">{{ item.clientName }}</div>
                  </div>
                </a> -->
                <!-- <div
                  class="app-block"
                  @click="showMoreApps"
                  v-if="usualAppList.length"
                >
                  <img
                    class="app-icon"
                    src="@/assets/image/common/moreApp.png"
                  />
                  <div class="app-cliName">查看更多</div>
                </div> -->
                <DataEmpty
                  style="margin: auto"
                  v-if="usualAppList.length == 0"
                  desc="暂无应用"
                ></DataEmpty>
              </div>
            </div>
            <div
              v-if="item.i == 'self2'"
              class="self2"
              :class="isDraggable ? 'self-bg' : ''"
            >
              <div v-if="isDraggable"></div>
              <div class="self2-content">
                <div class="item-size" @click="showMore(0)">
                  <div style="color: #ff6c33" class="title">待办</div>
                  <div class="bottom-content">
                    <img class="img" src="@/assets/image/common/p_daiban.png" />
                    <span class="value" style="color: #ff6c33">{{
                      todoListNum
                    }}</span>
                  </div>
                </div>

                <div class="item-size" @click="showMore(3)">
                  <div style="color: #eb3e42" class="title">预警</div>
                  <div class="bottom-content">
                    <img class="img" src="@/assets/image/common/p_yujing.png" />
                    <span class="value" style="color: #eb3e42">{{
                      alertListNum
                    }}</span>
                  </div>
                </div>

                <div class="item-size">
                  <div style="color: #6b57fe" class="title">应用</div>
                  <div class="bottom-content">
                    <img
                      class="img"
                      src="@/assets/image/common/p_yingyong.png"
                    />
                    <span class="value" style="color: #6b57fe">{{
                      usualAppListNum
                    }}</span>
                  </div>
                </div>

                <div class="item-size" @click="showMore(2)">
                  <div style="color: #54ad1c" class="title">公告</div>
                  <div class="bottom-content">
                    <img
                      class="img"
                      src="@/assets/image/common/p_gonggao.png"
                    />
                    <span class="value" style="color: #54ad1c">{{
                      msgListNum
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="item.i == 'self3'"
              class="self3"
              :class="isDraggable ? 'self-bg' : ''"
            >
              <div v-if="isDraggable"></div>
              <HomeNoticeCenter :todoListNum="todoListNum"></HomeNoticeCenter>
            </div>
            <div
              v-if="item.i == 'self4'"
              class="info-right"
              :class="isDraggable ? 'self-bg' : ''"
            >
              <div v-if="isDraggable"></div>
              <div class="title-main">
                <span class="title-icon icon-tzgg mainBGColor"> </span>
                <span class="title-value">临界预警</span>
              </div>
              <div class="more-info">
                <span
                  style="
                    color: white;
                    font-size: 14px;
                    padding: 3px 10px;
                    border-radius: 4px;
                  "
                  class="mainBGColor"
                  @click="showMore(3)"
                  >查看更多</span
                >
                <!-- <span><i class="el-icon-arrow-right"></i></span> -->
              </div>
              <div class="titleLine"></div>
              <div class="block-content-height">
                <div v-for="msgItem in alertList" :key="msgItem.id">
                  <div
                    @click="handleReadTodoList(msgItem)"
                    style="
                      cursor: pointer;
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      column-gap: 10px;
                      margin-bottom: 0px;
                      padding: 11.5px 0;
                      border-bottom: 1px solid #f5f8fa;
                      margin-right: 20px;
                      height: 52px;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                      "
                    >
                      <div
                        style="
                          width: 8px;
                          height: 8px;
                          border-radius: 4px;
                          margin-right: 18px;
                          background: #333333;
                        "
                      ></div>
                      <div
                        style="font-size: 14px; color: #333333"
                        class="tit-content"
                      >
                        {{ msgItem.templateName }}
                      </div>
                      <div class="status-content">
                        <el-tag
                          type="success"
                          v-if="msgItem.mutualStatus == '已读'"
                          >已读</el-tag
                        >
                        <el-tag
                          type="danger"
                          v-else-if="msgItem.mutualStatus == '未读'"
                          >未读</el-tag
                        >
                        <el-tag type="info" v-else>未知</el-tag>
                      </div>

                      <!-- <div
                        v-if="!msgItem.readStatus"
                        style="
                          text-align: center;
                          background-color: red;
                          border-radius: 5px;
                          margin-left: 10px;
                          color: white;
                          font-size: 12px;
                          padding: 3px, 5px;
                          width: 35px;
                        "
                      >
                        new
                      </div> -->
                    </div>
                    <div style="font-size: 14px; color: #666666">
                      来源:{{ msgItem.clientName }}
                      <span
                        style="
                          font-size: 14px;
                          margin-left: 30px;
                          color: #999999;
                        "
                      >
                        {{ getYearMonthDay(msgItem.sendTime) }}
                      </span>
                    </div>
                  </div>
                </div>
                <DataEmpty
                  style="padding-top: 60px"
                  v-if="alertListFlag"
                  desc="暂无内容"
                ></DataEmpty>
              </div>
            </div>
            <!-- <div v-if="item.i == 'self6'" class="index-right">
              <index-right></index-right>
              <portal-home-data></portal-home-data>
            </div> -->
            <div
              v-if="item.i == 'self5'"
              class="note-right"
              :class="isDraggable ? 'self-bg' : ''"
            >
              <div v-if="isDraggable"></div>
              <div class="title-main">
                <span class="title-icon icon-tzgg mainBGColor"> </span>
                <span class="title-value">通知公告</span>
              </div>
              <div class="more-info">
                <span
                  style="
                    color: white;
                    font-size: 14px;
                    padding: 3px 10px;
                    border-radius: 4px;
                  "
                  class="mainBGColor"
                  @click="showMore(2)"
                  >查看更多</span
                >
                <!-- <span><i class="el-icon-arrow-right"></i></span> -->
              </div>
              <div class="titleLine"></div>
              <div class="block-content-height">
                <div v-for="msgItem in msgList" :key="msgItem.id">
                  <div
                    @click="handleReadNotice(msgItem)"
                    style="
                      cursor: pointer;
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      column-gap: 10px;
                      margin-bottom: 0px;
                      padding: 11.5px 0;
                      border-bottom: 1px solid #f5f8fa;
                      margin-right: 20px;
                      height: 52px;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                      "
                    >
                      <div
                        style="
                          width: 8px;
                          height: 8px;
                          border-radius: 4px;
                          margin-right: 18px;
                          background: #333333;
                        "
                      ></div>

                      <div
                        style="font-size: 14px; color: #333333"
                        class="tit-content"
                      >
                        {{ msgItem.title }}
                      </div>
                      <div
                        v-if="!msgItem.readStatus"
                        style="
                          text-align: center;
                          background-color: red;
                          border-radius: 5px;
                          margin-left: 10px;
                          color: white;
                          font-size: 12px;
                          padding: 3px, 5px;
                          width: 35px;
                        "
                      >
                        new
                      </div>
                    </div>
                    <div style="font-size: 14px; color: #999999">
                      {{ getYearMonthDay(msgItem.updateTime) }}
                    </div>
                  </div>
                </div>
                <DataEmpty
                  style="padding-top: 60px"
                  v-if="msgListFlag"
                  desc="暂无内容"
                ></DataEmpty>
              </div>
            </div>
          </grid-item>
          <!-- -->
          <!-- <div class="info-container" style="display: none">
            <div style="width: 20px"></div>
          </div> -->
        </grid-layout>
      </div>
    </div>

    <notice-portal-view-dialog
      v-if="viewFlag"
      @closeDialog="closeViewDialog"
      :viewObj="viewObj"
    ></notice-portal-view-dialog>
    <notice-message-view-dialog
      v-if="viewFlag3"
      @closeDialog="closeViewDialog3"
      :viewObj="viewObj"
    ></notice-message-view-dialog>
    <DialogForm
      :dialogFlag="dialogFlag"
      :dialogType="dialogType"
      :refreshAppList="getAppData"
      :closeDialog="closeDialog"
      :jumpApp="jumpApp"
    />
    <add-use-app
      ref="addUseAppRef"
      :dialogFlag="appDialogFlag"
      :closeDialog="closeAppDialog"
    ></add-use-app>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getAppList,
  saveLayoutData,
  getClientGroupTree,
} from "@/api/portal/portal.js";
import NoticeMessageViewDialog from "@/views/noticePortal/compontents/NoticeMessageViewDialog.vue";

import { getMessageList, getPersonCount } from "@/api/message/message.js";
import { fetchList, addNoticeRecord } from "@/api/noticePortal/noticePortal";
import { allRead } from "@/api/noticeCenter/noticeCenter";
import axios from "axios";
import DataEmpty from "@/components/data-empty/index.vue";
import top from "@/page/index/top/";
import catalogue from "@/page/index/catalogues/index.vue";
import NoticePortalViewDialog from "@/views/noticePortal/compontents/NoticePortalViewDialog.vue";
import DialogForm from "./components/index.vue";
import { GridLayout, GridItem } from "vue-grid-layout";
import myGridItemPreview from "./components/itemPreview.vue";
import { randomLenNum } from "@/util/util";
import { getUserInfo, getTicket } from "@/api/login";
import PortalHomeData from "./PortalHomeData.vue";
import { getFetchList } from "@/api/admin/sys/system";
import HomeNoticeCenter from "../noticeCenter/homeNoticeCenter.vue";
import moreInfo from "../noticeCenter/moreInfo.vue";
import AddUseApp from "@/page/portalHome/components/index.vue";
export default {
  name: "home",
  components: {
    DataEmpty,
    NoticePortalViewDialog,
    NoticeMessageViewDialog,
    top,
    DialogForm,
    catalogue,
    GridLayout,
    GridItem,
    myGridItemPreview,
    PortalHomeData,
    HomeNoticeCenter,
    moreInfo,
    AddUseApp,
  },
  data() {
    return {
      fileUrl:
        window.location.protocol +
        "//" +
        window.location.host +
        window.location.port,
      testBg: require("@/assets/image/yirenyidang/icon-bg0.png"),
      usualAppList: [],
      usualAppListNum: 0,
      todoList: [],
      todoListNum: 0,
      alertList: [],
      alertListNum: 0,
      instanceUrl: "",
      instanceFlag: false,
      todoFlag: false,
      alertListFlag: false,
      msgListFlag: false,
      hallRouthPath: "",
      msgList: [],
      msgListNum: 0,
      viewFlag: false,
      viewFlag3: false,
      viewObj: {},
      dialogFlag: false,
      dialogType: 0,
      backgroundImageUrl: require("@/assets/image/themesImgs/blueThem.png"),
      isDraggable: false,
      isResizable: false,
      useable: false,
      layout: [],
      layout1: [
        { x: 0, y: 0, w: 8, h: 3, moved: false, i: "self1", minW: 3, minH: 3 },
        { x: 8, y: 0, w: 4, h: 3, moved: false, i: "self2", minW: 3, minH: 3 },
        {
          x: 0,
          y: 31,
          w: 12,
          h: 4,
          moved: false,
          i: "self3",
          minW: 3,
          minH: 4,
        },
        { x: 6, y: 78, w: 6, h: 3, moved: false, i: "self4", minW: 3, minH: 3 },
        { x: 0, y: 78, w: 6, h: 3, moved: false, i: "self5", minW: 3, minH: 3 },
      ],
      showPreview: false,
      backgroundData: [],
      groupAppList: [],
      activeName: "",
      logoObj: {},
      showMoreInfoView: false,
      messageType: 0,
      randomColors: [],
      appDialogFlag: false,
      usualAppListBak: [],
    };
  },
  watch: {
    "usualAppList.length": {
      handler(val, oldVal) {
        // console.log("val---", val);
        // setTimeout(() => {
        //   this.handleDoInitLayout();
        // }, 100);
      },
    },
  },
  computed: {
    ...mapGetters(["website", "userInfo", "menu"]),
    picUrl() {
      // console.log("--pic--", process.env);
      if (process.env.NODE_ENV == "development") {
        if (window.location.host.includes("localhost:")) {
          return process.env.VUE_APP_FILE_URL;
        }
      }
      return window.location.protocol + "//" + window.location.host;
    },
    originUrl() {
      return window.location.origin;
    },
    token() {
      return this.$store.getters.access_token;
    },
    cloudPivotToken() {
      return localStorage.getItem("cloudPivotToken");
    },
  },
  created() {
    // for (let index = 0; index < 300; index++) {
    //   this.randomColors.push(this.randomColor(index))
    // }
    // let layoutArrStr = localStorage.getItem("layoutArr");
    // if (layoutArrStr) {
    //   let layArr = JSON.parse(layoutArrStr);
    //   if (layArr && layArr.length > 0) {
    //     this.layout = layArr;
    //   }
    // }
    this.getLayOutData(); //调试
    this.$root.$on("handleSetGrid", this.handleSetGridFn);
    this.$root.$on("showHomeInShow", this.showHome);

    // document.title = "河南省市场监督管理局";
    this.getAppData();
    this.getUsualAppList(); // 获取常用应用
    this.getPersonCount();
    this.getMsgList();
    this.getAlertList();
    this.getNoticeList();
    this.initPreviewData();
  },
  mounted() {
    // console.log("user=userInfo==", this.userInfo);
    // console.log("user=menu==", this.menu);
    if (this.logoObj && !this.logoObj.sysIcon) {
      this.getLogoFn();
    }
    //
    setTimeout(() => {
      this.handleInitDomHeight();
    }, 550);
  },

  methods: {
    addUseApp() {
      //addUseAppRef
      this.appDialogFlag = true;
      this.$nextTick(() => {
        this.$refs.addUseAppRef.handleInitAppData();
      });
    },
    closeAppDialog() {
      this.appDialogFlag = false;

      this.getUsualAppList(); // 获取常用应用
    },
    randomColor(index) {
      const r = Math.floor(Math.random() * 256);
      const g = Math.floor(Math.random() * 256);
      const b = Math.floor(Math.random() * 256);
      return `rgb(${r},${g},${b})`;
    },
    change_icon(iconUrl) {
      const changeFavicon = (link) => {
        let $favicon = document.querySelector('link[rel="icon"]');
        if ($favicon !== null) {
          $favicon.href = link;
        } else {
          $favicon = document.createElement("link");
          $favicon.rel = "icon";
          $favicon.href = link;
          document.head.appendChild($favicon);
        }
      };
      // 设置图标地址
      // let iconUrl = `./icoImg/1.ico`;
      // 动态修改网站图标
      changeFavicon(iconUrl);
    },
    getLogoFn() {
      // console.log("getListgetListgetList==");

      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        if (response.data.code == 200) {
          this.logoObj = response.data.data || null;
          if (this.logoObj && this.logoObj.sysIcon) {
            let path = this.$getUrlByProcess(this.logoObj.sysIcon);
            this.change_icon(path);

            this.$store.commit("SET_LOGO_OBJ", this.logoObj);
          }
          if (this.logoObj && this.logoObj.sysTitle) {
            document.title = this.logoObj.sysTitle;
          }
        }
      });
    },
    // 点击跳转
    usualAppFn(item) {
      console.log("item==", item);
      var newOpen = window.open();
      newOpen.opener = null;
      newOpen.location = item.portalUrl;
      // var params = {
      //   clientId: item.clientId,
      // };
      // getTicket(params)
      //   .then((res) => {
      //     if (res.data.code == 200) {
      //       let params = res.data.data;
      //       if (item.clientIndexUrl.includes("?")) {
      //         var jumpUrl = `${item.clientIndexUrl}&ticket=${params.ticket}`;
      //       } else {
      //         var jumpUrl = `${item.clientIndexUrl}?ticket=${params.ticket}`;
      //       }
      //       window.open(jumpUrl, "_blank");
      //     }
      //   })
      //   .catch((err) => {});
    },
    handleClickTab(tab, event) {
      this.usualAppList = [];
      console.log("this.groupAppList==", this.groupAppList);
      if (this.activeName == "-1") {
        this.usualAppList = this.usualAppListBak;
      } else {
        let fObj = this.groupAppList.find((item) => {
          return item.groupId == this.activeName;
        });
        if (fObj) {
          this.usualAppList = fObj.appList;
        }
      }
    },
    handleInitDomHeight() {
      let dom = document.querySelector(".vue-grid-layout");
      if (dom) {
        let clientHeight = dom.clientHeight;

        if (clientHeight > 0) {
          let dom2 = document.querySelector(".portal-container");

          if (dom2) {
            let myHeight = clientHeight + 130;
            dom2.style.height = myHeight + "px";
          }
        }
      }
    },
    getLayOutData() {
      getUserInfo().then((res) => {
        // console.log("获取用户信息--HomeIndex", res);
        if (res.data.code == 200) {
          if (
            res.data &&
            res.data.data &&
            res.data.data.sysUser &&
            res.data.data.sysUser.config
          ) {
            let layout = res.data.data.sysUser.config.layout;
            if (layout && layout.length == 5) {
              this.layout = layout;
              var isNew = false;
              for (let index = 0; index < layout.length; index++) {
                layout[index].minW = 3;
                layout[index].minH = 3;
                if (layout[index].i == "self3") {
                  layout[index].minH = 4;
                }
                if (layout[index].w > 12) {
                  isNew = true;
                }
              }
              if (isNew) {
                this.layout = this.layout1;
              }
              this.handleLayoutSave(0);
              localStorage.setItem("layoutArr", JSON.stringify(this.layout));
            } else {
              this.layout = this.layout1;
              setTimeout(() => {
                this.handleLayoutSave(0);
              }, 600);
            }
          } else {
            this.layout = this.layout1;
          }
        }
      });
    },
    //保存layLout
    handleLayoutSave(type) {
      saveLayoutData(this.layout).then((res) => {
        // console.log("保存layout---", res);
        if (res.data.code == 200) {
          this.handleInitDomHeight();
          if (type == 1) {
            this.$message({
              showClose: true,
              message: "保存成功",
              type: "success",
            });
          } else if (type == 2) {
            this.$message({
              showClose: true,
              message: "重置成功",
              type: "success",
            });
          }
        }
      });
    },
    handleSave() {
      this.isDraggable = false;
      // console.log("this.layout==", this.layout);
      localStorage.setItem("layoutArr", JSON.stringify(this.layout));
      this.handleLayoutSave(1);
    },
    handleCancle() {
      this.isDraggable = false;
      let layoutArrStr = localStorage.getItem("layoutArr");
      if (layoutArrStr) {
        let layArr = JSON.parse(layoutArrStr);
        if (layArr && layArr.length > 0) {
          this.layout = layArr;
        }
      }
    },
    handleReset() {
      this.isDraggable = false;
      this.layout = [
        { x: 0, y: 0, w: 8, h: 3, moved: false, i: "self1", minW: 3, minH: 3 },
        { x: 8, y: 0, w: 4, h: 3, moved: false, i: "self2", minW: 3, minH: 3 },
        {
          x: 0,
          y: 31,
          w: 12,
          h: 4,
          moved: false,
          i: "self3",
          minW: 3,
          minH: 4,
        },
        { x: 6, y: 78, w: 6, h: 3, moved: false, i: "self4", minW: 3, minH: 3 },
        { x: 0, y: 78, w: 6, h: 3, moved: false, i: "self5", minW: 3, minH: 3 },
      ];
      localStorage.setItem("layoutArr", JSON.stringify(this.layout));
      this.handleLayoutSave(2);
    },
    handleSetGridFn(flag) {
      // console.log("设置可用--");
      this.isDraggable = flag;
    },
    movedEvent: function (i, newX, newY) {
      // console.log("MOVED i=" + i + ", X=" + newX + ", Y=" + newY);
      // console.log("this.layout==", this.layout);
      this.handleInitDomHeight();
    },
    initPreviewData() {
      this.showPreview = true;
      this.backgroundData = [];
      for (let i = 0; i < 12; i++) {
        for (let j = 0; j < 50; j++) {
          this.backgroundData.push({
            x: i,
            y: j,
            w: 1,
            h: 1,
            i: randomLenNum() + i + randomLenNum(), // 防止 key 重复
          });
        }
      }
      // console.log("backgroundData==", this.backgroundData);
    },
    changeThemesImg() {
      let imgType = localStorage.getItem("themeSelected");
      switch (imgType) {
        case "0":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/blueThem.png");
          break;
        case "1":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/greenThem.png");
          break;
        case "2":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/redThem.png");
          break;
        case "3":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/skyThem.png");
          break;
        case "4":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/yelThem.png");
          break;
        default:
          this.backgroundImageUrl = require("@/assets/image/themesImgs/blueThem.png");
          break;
      }
    },
    //获取常用应用
    getUsualAppList() {
      getAppList().then((res) => {
        if (res.data.code == 200) {
          this.usualAppList = res.data.data;
          this.usualAppListBak = JSON.parse(JSON.stringify(this.usualAppList));
          // this.usualAppList = this.usualAppList.splice(0, 5);
        }
      });
    },
    getAppData() {
      //getClientGroupTree  //getAppList
      getClientGroupTree().then((res) => {
        if (res.data.code == 200) {
          this.groupAppList = res.data.data;
          let obj = {
            groupId: "-1",
            groupName: "常用应用",
            appList: [],
          };
          // if (this.groupAppList && this.groupAppList.length > 0) {
          let arr = [];
          this.groupAppList.forEach((mItem) => {
            if (mItem.appList && mItem.appList.length > 0) {
              arr = [...arr, ...mItem.appList];
            }
          });
          obj.appList = arr;
          this.groupAppList.unshift(obj);
          this.activeName = this.groupAppList[0].groupId;
          // this.usualAppList = this.groupAppList[0].appList;//获取常用应用

          this.usualAppListNum = this.usualAppList && this.usualAppList.length;
          //
          // this.usualAppList = [...this.usualAppList, ...this.usualAppList];
          // this.usualAppList = [...this.usualAppList, ...this.usualAppList];
          // }
          // this.usualAppList = res.data.data;
          // this.usualAppList = this.usualAppList.splice(0, 11);

          // setTimeout(() => {
          //   this.handleDoInitLayout();
          // }, 100);
        }
      });
    },
    handleDoInitLayout() {
      let dom = document.querySelector(".portal-app");
      let height = 0;
      if (dom) {
        height = dom.offsetHeight;
      }
      if (this.usualAppList.length == 0) {
        this.layout[0].h = 12;
        if (
          this.layout[0].y == 0 ||
          (this.layout[0].y < this.layout[1].y &&
            this.layout[0].y < this.layout[2].y)
        ) {
          this.layout[1].y = 12;
          this.layout[2].y = 12;
        }
      } else if (this.usualAppList.length <= 4) {
        this.layout[0].h = 10;
        if (
          this.layout[0].y == 0 ||
          (this.layout[0].y < this.layout[1].y &&
            this.layout[0].y < this.layout[2].y)
        ) {
          this.layout[1].y = 10;
          this.layout[2].y = 10;
        }
      } else if (this.usualAppList.length <= 8) {
        if (height > 340) {
          this.layout[0].h = 17;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            this.layout[1].y = 17;
            this.layout[2].y = 17;
          }
        } else {
          this.layout[0].h = 16;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            this.layout[1].y = 16;
            this.layout[2].y = 16;
          }
        }
      } else {
        if (height > 480) {
          let num1 = height / 20;
          // this.layout[0].h = 24;
          this.layout[0].h = num1;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            // this.layout[1].y = 24;
            // this.layout[2].y = 24;
            this.layout[1].y = num1;
            this.layout[2].y = num1;
          }
        } else {
          this.layout[0].h = 22;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            this.layout[1].y = 22;
            this.layout[2].y = 22;
          }
        }
      }

      let layoutArrStr = localStorage.getItem("layoutArr");
      if (layoutArrStr) {
        localStorage.setItem("layoutArr", JSON.stringify(this.layout));
      }
    },
    getPersonCount() {
      getPersonCount().then((res) => {
        if (res.data.code == 200) {
          this.todoListNum = res.data.data.todoCount;
          this.alertListNum = res.data.data.warnCount;
          this.msgListNum = parseInt(res.data.data.notifyCount || 0);
        }
      });
    },
    getMsgList() {
      getMessageList({
        current: 1,
        size: 6,
        status: 1,
        messageType: 0,
        mutualStatus: 2,
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.todoList = res.data.data.records || [];
            if (this.todoList && this.todoList.length > 0) {
              this.todoList = this.todoList.splice(0, 6);
            }
          }
          setTimeout(() => {
            this.todoList.length > 0
              ? (this.todoFlag = false)
              : (this.todoFlag = true);
          }, 300);
        })
        .catch((err) => {
          this.todoFlag = true;
        });
    },
    getAlertList() {
      getMessageList({
        current: 1,
        size: 6,
        status: 1,
        messageType: 1,
        // mutualStatus: 2,
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.alertList = res.data.data.records || [];
            if (this.alertList && this.alertList.length > 0) {
              this.alertList = this.alertList.splice(0, 6);
            }
          }
          setTimeout(() => {
            this.alertList.length > 0
              ? (this.alertListFlag = false)
              : (this.alertListFlag = true);
          }, 300);
        })
        .catch((err) => {
          this.alertListFlag = true;
        });
    },
    getNoticeList() {
      let param = {
        current: 1,
        size: 8,
        status: 1,
      };
      fetchList(param)
        .then((res) => {
          if (res.data.code == 200) {
            this.msgList = res.data.data.records || [];
            // let msgArr = res.data.data.records || [];

            // let msgArr2 = msgArr.filter((item) => {
            //   return item.status == 1;
            // });
            this.msgList = this.msgList.splice(0, 6);
            this.msgList.length > 0
              ? (this.msgListFlag = false)
              : (this.msgListFlag = true);
          }
        })
        .catch((err) => {
          this.msgListFlag = true;
        });
    },
    handleGoNewAddress() {},
    scrollToTop() {
      // 使用 Vue 的 $refs 访问DOM元素
      this.$refs.scrollTopDiv.scrollTop = 0;
      // 或者使用原生JavaScript的scrollTo方法
      // window.scrollTo({
      //   top: 0,
      //   behavior: 'smooth'
      // });
    },
    showHome(isShow, type) {
      if (isShow) {
        this.showMore(type);
      } else {
        this.showMoreInfoView = false;
      }
    },
    showMore(type) {
      if (this.isDraggable) return;
      this.messageType = type;
      // this.showMoreInfoView = !this.showMoreInfoView
      // this.scrollToTop();
      setTimeout(() => {
        this.$router.push({
          path: "/noticeCenter/noticeIndex",
          query: {
            routeType: 1,
            type: type,
          },
        });
      }, 500);
    },
    getDate(dateStr) {
      return new Date(dateStr).getDate();
    },
    getYearMonth(dateStr) {
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${date.getMonth() + 1}`;
    },
    getYearMonthDay(dateStr) {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    handleReadNotice(row) {
      this.viewFlag = true;
      this.viewObj = row;
      addNoticeRecord({
        noticeId: row.id,
        type: 2,
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.getNoticeList();
          }
        })
        .catch((err) => {});
    },
    handleReadTodoList(row) {
      this.viewObj = row;
      this.viewFlag3 = true;

      if (row.mutualStatus != "已读") {
        let params = {
          ids: [row.id],
        };
        allRead(params).then((res) => {
          if (res.data.code == 200) {
            this.getAlertList();
          }
        });
      }

      // this.viewFlag = true;
      // this.viewObj = row;
      // allRead({
      //   ids: [row.id],
      //   messageType: 1,
      //   bizId: "",
      // })
      //   .then((res) => {
      //     if (res.data.code == 200) {
      //       this.getAlertList();
      //     }
      //   })
      //   .catch((err) => { });
      // this.handleAlertAction(row);
    },
    closeViewDialog() {
      this.viewFlag = false;
    },
    closeViewDialog3() {
      this.viewFlag3 = false;
    },
    handleAlertAction(row) {
      if (row.messageJumpUrl) {
        var jumpUrl = row.messageJumpUrl;
        if (!jumpUrl.startsWith("http")) {
          jumpUrl = "http://" + jumpUrl;
        }
        window.open(jumpUrl);
      } else {
        this.showMore(3);
      }
    },
    handleToAction(row) {
      if (row.messageJumpUrl) {
        var jumpUrl = row.messageJumpUrl + "?token=" + this.token;
        var newOpen = window.open();
        newOpen.opener = null;
        newOpen.location = jumpUrl;
      } else {
        this.$root.$emit("showHomeInShow", true, 0);
      }
    },
    handleEditApps() {
      this.dialogFlag = true;
      this.dialogType = 0;
    },
    showMoreApps() {
      this.dialogFlag = true;
      this.dialogType = 1;
    },
    closeDialog() {
      this.dialogFlag = false;
    },
    jumpApp(item) {
      var newOpen = window.open();
      newOpen.opener = null;
      newOpen.location = item.clientIndexUrl;
    },
  },
};
</script>

<style lang="scss" scoped>
.portal-outer {
  width: 100%;
  overflow-y: auto;
  // height: auto;
  background: #eef2fb;
  display: flex;
  justify-content: center;
  background: require("/assets/image/themesImgs/blueThem.png") no-repeat;
  background-size: 100% 100%;
  background-attachment: fixed;
}

.portal-container {
  min-width: 1572px;
  max-width: 1572px;
  // height: 1500px;
  margin: 20px;
  margin-top: 40px;
  // background: #fff;
  border: 1px solid transparent;
  position: relative;
  // overflow: hidden;
  // min-height: 100%;
  min-height: 90vh;

  .self2 {
    height: calc(100%);
    width: 100%;

    .self2-content {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-content: space-between;
      flex-wrap: wrap;
      height: 100%;
      width: 100%;
    }

    .item-size {
      // width: 185px;
      height: 48.5%;
      width: 49%;
      background: white;
      border-radius: 15px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      cursor: pointer;
      .title {
        margin-left: 25px;
        font-size: 24px;
        font-weight: 600;
        height: 45px;
      }

      .bottom-content {
        margin-left: 25px;
        display: flex;
        flex-direction: row;
        align-items: center;

        .img {
          width: 50px;
          height: 50px;
        }

        .value {
          width: 60%;
          margin-right: 10px;
          font-size: 60px;
          text-align: right;
        }
      }
    }
  }

  .btn-content {
    margin-left: 12px;
    margin-top: 4px;
    display: flex;
    justify-content: right;

    .save-btn {
      height: 40px;
      line-height: 40px;
      width: 80px;
      text-align: center;
      // color: #fff;
      background: #fff;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
      margin-right: 10px;
    }

    .icon-btn {
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url("../../assets/image/save.png") no-repeat;
      background-size: 100%;
    }
  }

  .portal-app {
    position: relative;
    border: 1px solid transparent;
    background: white;
    border-radius: 15px;
    height: calc(100%);
    padding-left: 0px;
    padding-right: 0px;

    // overflow-y: scroll;
    // padding: 20px;
    .app-abs {
      position: absolute;
      // border: 1px solid red;
      top: 0;
      width: 100%;
      width: 640px;
      z-index: 2;
    }

    .app-title {
      margin: 10px 0 0px 20px;
      height: 38px;
    }

    .app-content {
      position: relative;
      height: calc(100% - 58px);
      overflow-y: scroll;

      padding: 40px 0px 0px 0px;

      a:focus {
        outline: none;
      }

      .app-mycontent {
        display: flex;
        flex-wrap: wrap;
      }
    }

    // .block-content {
    //   height: 120px;
    // }
    .app-block {
      width: 110px;
      height: 110px;
      // border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      flex-direction: column;
      border-radius: 8px;
      margin: 12px 10px;
      // &:hover {
      //   border: 1px solid #0357CA;
      //   // box-shadow: 0 0.63px 5.04px 0 #ebeef5;
      // }

      .app-icon {
        width: 100%;
        height: 100%;
        border-radius: 5px;
        background-color: white;
        // margin-right: 20px;
        // margin-bottom: 18px;
      }

      .app-cliName {
        margin-top: 10px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: center;
        display: -webkit-box;
        /* 将 div 视为弹性容器 */
        -webkit-line-clamp: 2;
        /* 显示两行内容 */
        -webkit-box-orient: vertical;
        /* 设置纵向排列内容 */
        overflow: hidden;
        /* 隐藏超出部分 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
        cursor: pointer;
      }
    }
  }

  .self-bg {
    // background: #333 !important;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    // border: 1px solid rgba(0, 0, 0, 0.2);
    // background: rgba(255, 255, 255, 0.3) !important;
  }

  .title-main {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .title-icon {
      margin-right: 10px;
      margin-top: -16px;
    }

    .icon-ywyy {
      display: inline-block;
      // background: url("../../assets/image/common/p_yewuyingyong.png") no-repeat;
      background-size: 100%;
      width: 18px;
      height: 18px;
      min-width: 18px;
    }

    .icon-wddb {
      margin-left: 20px;
      display: inline-block;
      // background: url("../../assets/image/common/p_wodedaiban.png") no-repeat;
      background-size: 100%;
      width: 18px;
      height: 18px;
      min-width: 18px;
      margin-top: 0px;
    }

    .icon-tzgg {
      margin-left: 20px;
      display: inline-block;
      // background: url("../../assets/image/common/p_tongzhigonggao.png") no-repeat;
      background-size: 100%;
      width: 4px;
      height: 18px;
      margin-top: 0px;
    }

    .title-value {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
  }

  .info-container {
    margin-top: 20px;
    position: relative;
    display: flex;
    width: 100%;
  }

  .more-info {
    position: absolute;
    right: 20px;
    top: 15px;
    cursor: pointer;
  }

  .self3 {
    // width: 50%;
    // border: 1px solid red;
    // width: 566px;
    position: relative;
    background: #f9f9f9;
    border-radius: 15px;
    height: calc(100%);

    .left-abs {
      position: absolute;
      top: 0;
      height: 100%;
      // border: 1px solid red;
      width: 100%;
      z-index: 2;
    }
  }

  .info-right {
    position: relative;
    background: white;
    border-radius: 15px;
    padding-top: 10px;
    padding-bottom: 17px;
    height: calc(100%);

    .right-abs {
      position: absolute;
      top: 0;
      height: 100%;
      // border: 1px solid red;
      width: 100%;
      z-index: 2;
    }
  }

  .note-right {
    position: relative;
    background: white;
    border-radius: 15px;
    padding-top: 10px;
    padding-bottom: 17px;
    height: calc(100%);

    .right-abs {
      position: absolute;
      top: 0;
      height: 100%;
      // border: 1px solid red;
      width: 100%;
      z-index: 2;
    }
  }

  .index-right {
    // width: 640px;
    width: 770px;
    height: 910px;
    background: #ffffff;
    border-radius: 5px;
  }

  .index-right1 {
    width: 100%;
    height: 585px;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .msg-container {
    padding: 20px;
    position: relative;

    .msg-content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      margin-top: 20px;
      margin-left: 30px;
      position: relative;

      .msg-more {
        position: absolute;
        right: 60px;
        top: 0;

        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .block-content {
    padding-left: 20px;
  }

  .block-content-height {
    padding-left: 20px;
    background-color: transparent;
    border-radius: 10px;
    margin: 10px 20px;
    height: calc(100% - 40px);
    overflow: auto;
  }

  .msg-width1 {
    width: 50%;
  }

  .msg-block {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 20px;
    margin-top: 15px;
    display: flex;
    align-items: center;
    position: relative;
    color: #2c3e50;

    // cursor: pointer;
    // &:hover {
    //   // text-decoration: underline;
    //   // background: #f0f2f5;
    //   color: #0357CA;
    //   cursor: pointer;

    //   .msg-dot {
    //     background: #0357CA;
    //   }

    //   .msg-link,
    //   .msg-time {
    //     color: #0357CA;
    //     font-weight: 600;
    //   }
    // }

    .msg-main {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      color: white;
      margin: 5px 0;
    }

    .msg-time {
      margin-left: 18px;
      color: #999999;
      font-size: 14px;
    }

    .msg-dot1 {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #ccc;
      background: transparent;
      margin-right: 10px;
    }

    .msg-dot2 {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #0357ca;
      background: transparent;
      margin-right: 10px;
    }

    .msg-link {
      width: calc(80% - 20px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 95%;
      display: inline-block;
      margin-right: 10px;
      font-size: 16px;
      color: #333333;
      // &:hover {
      //   text-decoration: underline; text-decoration: underline;
      //   cursor: pointer;
      // }
    }

    .msg-new {
      display: inline-block;
      background: #f56c6c;
      color: #fff;
      margin: 0 20px;
      padding: 0 2px;
      font-size: 12px;
    }
  }
}

.tit-content {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 设置最大显示行数 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.self-bg2 {
  background: rgba(0, 0, 0, 0.5);
}

.self-iframe {
  width: 100%;
  height: 70vh;
  // height: 87vh;
}

.high-cust {
  // /deep/ .el-dialog__header {
  //   padding-bottom: 0;
  // }
}

.titleLine {
  height: 1px;
  width: 100%;
  background-color: #e2e5ed;
}

.line-self {
  margin-top: -26px;
  height: 2px;
}

.contentLine {
  margin-top: 10px;
  height: 1px;
  width: 100%;
  background-color: #f5f8fa;
}

.my-tabs {
}
.status-content {
  margin-left: 10px;
}
.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}
.img-add {
  background: url("./../../assets/image/appGroup/addAppGroup.png") no-repeat;
  background-size: 100%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  margin-bottom: 10px;
}

// ::v-deep .el-carousel__container {
//   height: 910px;
// }
</style>
