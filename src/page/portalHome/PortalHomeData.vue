<template>
  <div class="container" v-loading="loading" element-loading-text="数据加载中" element-loading-spinner="el-icon-loading">
    <div style="
        width: 100%;
        display: flex;
        height: 50px;
        justify-content: center;
        align-self: center;
      ">
      <div class="title-icon"></div>
      <!-- <div class="title-icon icon-ywyy icon-ywyy-theme"></div> -->
      <el-tabs class="my-tabs" ref="tabs" v-model="activeTab" @tab-click="handleClickTab">
        <el-tab-pane v-for="(item, i) in tabsData" :key="i" :name="item.groupId" :label="item.groupName"></el-tab-pane>
      </el-tabs>
      <div class="suo-content">
        <span class="sc-icon" v-if="lockFlag">
          <i class="zs-iconfont icon-suoding" @click="handleLockTab(0)" title="锁定"></i>
        </span>
        <span class="sc-icon" v-else>
          <i class="zs-iconfont icon-jiesuo" @click="handleLockTab(1)" title="轮播"></i>
        </span>
      </div>
    </div>
    <div class="titleLine"></div>

    <index-right v-if="activeTab === '0'"></index-right>
    <index-echarts v-if="activeTab === '1'"></index-echarts>
    <!-- :foodObj="foodObj" -->
    <index-echarts-food ref="foodRef" v-if="activeTab === '2'"></index-echarts-food>
    <index-echarts-device ref="deviceRef" v-if="activeTab === '3'"></index-echarts-device>
    <index-echarts-industry ref="industryRef" v-if="activeTab === '4'"></index-echarts-industry>
    <index-echarts-infrastructure ref="infrastructureRef" v-if="activeTab === '5'"></index-echarts-infrastructure>

    <!-- <el-carousel
      indicator-position="outside"
      height="910px"
      :indicator-position="'none'"
      arrow="always"
      :autoplay="true"
      :interval="10000"
    >
      <el-carousel-item>
        <index-right></index-right>
      </el-carousel-item>
      <el-carousel-item>
        <index-echarts></index-echarts>
      </el-carousel-item>
      <el-carousel-item>
        <index-echarts-food></index-echarts-food>
      </el-carousel-item>
      <el-carousel-item>
        <index-echarts-device></index-echarts-device>
      </el-carousel-item>
      <el-carousel-item>
        <index-echarts-industry></index-echarts-industry>
      </el-carousel-item>
      <el-carousel-item>
        <index-echarts-infrastructure></index-echarts-infrastructure>
      </el-carousel-item>
    </el-carousel> -->
  </div>
</template>

<script>
import IndexRight from "./components/IndexRight.vue";
import IndexEcharts from "./components/IndexEcharts.vue";
import IndexEchartsFood from "./components/IndexEchartsFood";
import IndexEchartsDevice from "./components/IndexEchartsDevice";
import IndexEchartsIndustry from "./components/IndexEchartsIndustry";
import IndexEchartsInfrastructure from "./components/IndexEchartsInfrastructure";
import { getShipinShebeiDengData } from "@/api/portal/portal.js";
import { getCurrentDate } from "@/util/date";
export default {
  components: {
    IndexRight,
    IndexEcharts,
    IndexEchartsFood,
    IndexEchartsDevice,
    IndexEchartsIndustry,
    IndexEchartsInfrastructure,
  },
  data() {
    return {
      tabsData: [
        // { groupId: "0", groupName: "主体" },
        // { groupId: "1", groupName: "企业" },
        // { groupId: "2", groupName: "食品" },
        // { groupId: "3", groupName: "设备" },
        // { groupId: "4", groupName: "工业品" },
        // { groupId: "5", groupName: "基础设施" },
        { groupId: "0", groupName: "市场主体" },
        { groupId: "1", groupName: "信用风险" },
        { groupId: "2", groupName: "食品" },
        { groupId: "4", groupName: "工业品" },
        { groupId: "3", groupName: "特种设备" },
        { groupId: "5", groupName: "质量基础设施" },
      ],
      activeTab: "0",
      intervalId: null,
      lockFlag: false,
      allData: {},
      foodObj: {},
      loading: false,
    };
  },
  watch: {
    activeTab: {
      handler(val, oldVal) {
        if (val == 2 || val == 3 || val == 4 || val == 5) {
          // this.getShipinShebeiAllDataFn();
          this.loading = true;
          setTimeout(() => {
            this.loading = false;
          }, 500);
          this.dealTableData();
        }
      },
    },
  },
  created() {
    // this.getShipinShebeiAllDataFn();
  },
  mounted() {
    this.activeTab = this.tabsData[0].groupId;
    this.startTimer();
  },
  methods: {
    getShipinShebeiAllDataFn() {
      this.loading = true;

      let params = {
        year: getCurrentDate("-"),
      };
      getShipinShebeiDengData(params)
        .then((res) => {
          setTimeout(() => {
            this.loading = false;
          }, 100);
          if (res.data.code == 200) {
            this.allData = res.data.data;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    dealTableData() {
      let cityKeyArr = [
        "zzs",
        "kfs",
        "lys",
        "pdss",
        "ays",
        "hbs",
        "xxs",
        "jzs",
        "pys",
        "xcs",
        "lhs",
        "smxs",
        "nys",
        "sqs",
        "xys",
        "zks",
        "zmds",
        "jys",
      ];
      if (this.activeTab == 2) {
        if (this.allData.countInfo) {
          this.foodObj.spscCount = this.allData.countInfo.spscCount || 0; //生产企业
          this.foodObj.ltCount = this.allData.countInfo.ltCount || 0; //流通企业数量
          this.foodObj.cyCount = this.allData.countInfo.cyCount || 0; //餐饮企业数量
          this.foodObj.xzfCount = this.allData.countInfo.xzfCount || 0; //小作坊数量
          this.foodObj.xjydCount = this.allData.countInfo.xjydCount || 0; //小经营店数量
          this.foodObj.xtdCount = this.allData.countInfo.xtdCount || 0; //小摊点
          this.foodObj.sjUnitCount = this.allData.countInfo.sjUnitCount || 0; //设计单位数量
          this.foodObj.alevelCount = this.allData.countInfo.alevelCount || 0; //风险等级A
          this.foodObj.blevelCount = this.allData.countInfo.blevelCount || 0; //风险等级b
          this.foodObj.clevelCount = this.allData.countInfo.clevelCount || 0; //风险等级c
          this.foodObj.dlevelCount = this.allData.countInfo.dlevelCount || 0; //风险等级d
        }
        this.foodObj.superviseQyInfoList = this.allData.superviseQyInfoList; //监督检查列表
        let cityInfo = this.allData.cityInfo;
        let qiyuFenbuArr = [];
        if (cityInfo) {
          cityKeyArr.forEach((key) => {
            qiyuFenbuArr.push(cityInfo[key].qyCount);
          });
        }
        this.foodObj.qiyuFenbuArr = qiyuFenbuArr;
        this.$nextTick(() => {
          this.$refs.foodRef.initData(this.foodObj);
        });
      } else if (this.activeTab == 3) {
        let shebeiObj = {};
        let cityInfo = this.allData.cityInfo;
        shebeiObj.cnjdcUnitCount = this.allData.countInfo.cnjdcUnitCount; //车辆
        shebeiObj.dtUnitCount = this.allData.countInfo.dtUnitCount; //电梯
        shebeiObj.glUnitCount = this.allData.countInfo.glUnitCount; //锅炉
        shebeiObj.qzjxUnitCount = this.allData.countInfo.qzjxUnitCount; //起重机械
        shebeiObj.kysdUnitCount = this.allData.countInfo.kysdUnitCount; //客运索道
        shebeiObj.ylgdUnitCount = this.allData.countInfo.ylgdUnitCount; //压力管道
        shebeiObj.ylrqUnitCount = this.allData.countInfo.ylrqUnitCount; //压力容器
        shebeiObj.dxylssUnitCount = this.allData.countInfo.dxylssUnitCount; //大型游乐设施

        //安装维修
        let anzhuangArr = [
          { value: 0, name: "检验检测" },
          { value: 0, name: "气瓶充装" },
          { value: 0, name: "移动式压力容器" },
          { value: 0, name: "安装改造维修" },
          { value: 0, name: "制造" },
          { value: 0, name: "设计" },
        ];
        anzhuangArr[0].value = this.allData.countInfo.jyjcdwUnitCount;
        anzhuangArr[1].value = this.allData.countInfo.qpczUnitCount;
        anzhuangArr[2].value = this.allData.countInfo.ydsylrqUnitCount;
        anzhuangArr[3].value = this.allData.countInfo.azgzwxUnitCount;
        anzhuangArr[4].value = this.allData.countInfo.zzUnitCount;
        anzhuangArr[5].value = this.allData.countInfo.sjUnitCount;

        shebeiObj.anzhuangArr = anzhuangArr;

        let shebeiFenbuArr = [];
        if (cityInfo) {
          cityKeyArr.forEach((key) => {
            shebeiFenbuArr.push(cityInfo[key].sydwCount);
          });
        }
        shebeiObj.shebeiFenbuArr = shebeiFenbuArr;

        this.$nextTick(() => {
          this.$refs.deviceRef.initDataFn(shebeiObj);
        });
      } else if (this.activeTab == 4) {
        let gongyeObj = {};
        let cityInfo = this.allData.cityInfo;
        // gongyeObj.cnjdcUnitCount = this.allData.countInfo.cnjdcUnitCount; //
        let fazhengArr = [];
        fazhengArr.push(this.allData.countInfo.dxdlPermitCount); //电线电缆
        fazhengArr.push(this.allData.countInfo.hfPermitCount); //化肥
        fazhengArr.push(this.allData.countInfo.jzygjPermitCount); //建筑用钢筋
        fazhengArr.push(this.allData.countInfo.snPermitCount); //水泥
        fazhengArr.push(this.allData.countInfo.gbdscssbPermitCount); //广播电视\n传输设备
        fazhengArr.push(this.allData.countInfo.rmbjdyPermitCount); //人民币\n鉴别仪
        fazhengArr.push(this.allData.countInfo.wxhxpPermitCount); //危险化学\n品
        fazhengArr.push(this.allData.countInfo.hnttlqjzlPermitCount); //预应力混\n凝土铁路\n桥简支梁
        fazhengArr.push(this.allData.countInfo.spxgcpPermitCount); //食品\n相关产品
        gongyeObj.fazhengArr = fazhengArr;

        let spotCheckProductInfoList = this.allData.spotCheckProductInfoList;
        let choujianArr = [];
        spotCheckProductInfoList.forEach((item, i) => {
          let nObj = {};
          if (i < 7) {
            nObj.name = item.name;
            nObj.value = item.count;
            choujianArr.push(nObj);
          }
        });
        gongyeObj.choujianArr = choujianArr;

        let luoshizhutiArr = [];
        if (cityInfo) {
          cityKeyArr.forEach((key) => {
            luoshizhutiArr.push(cityInfo[key].lsZtzrCount);
          });
        }
        gongyeObj.luoshizhutiArr = luoshizhutiArr;

        this.$nextTick(() => {
          this.$refs.industryRef.initDataFn(gongyeObj);
        });
        //
      } else if (this.activeTab == 5) {
        let baseObj = {};
        baseObj.jlbzqjkhUnitCount = this.allData.countInfo.jlbzqjkhUnitCount; //计量标准器具考核
        baseObj.fdjlsqUnitCount = this.allData.countInfo.fdjlsqUnitCount; //法定计量授权
        baseObj.jlqjxspzUnitCount = this.allData.countInfo.jlqjxspzUnitCount; //计量器具形式批准
        baseObj.zzjlsUnitCount = this.allData.countInfo.zzjlsUnitCount; //注册计量师
        //检验检测统计
        baseObj.jianyanCount = this.allData.jyjcList;
        //地方标准
        baseObj.dfbzList = this.allData.dfbzList;
        this.$nextTick(() => {
          this.$refs.infrastructureRef.initDataFn(baseObj);
        });
      }
    },

    startTimer() {
      this.intervalId = setInterval(() => {
        // // 切换到下一个标签
        var currentTabIndex = this.getIndexFromeData();
        const nextTabIndex = (currentTabIndex + 1) % this.tabsData.length;
        this.activeTab = this.tabsData[nextTabIndex].groupId;
      }, 10000); // 切换时间间隔为 3 秒
    },
    getIndexFromeData() {
      var array = this.tabsData;
      var currentTabIndex = 0;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (element.groupId === this.activeTab) {
          currentTabIndex = index;
        }
      }
      return currentTabIndex;
    },
    handleClick(tab, event) {
    },
    handleClickTab(tab, event) {

      if (this.intervalId && !this.lockFlag) {
        clearInterval(this.intervalId);
        this.startTimer();
      }
    },
    handleLockTab(type) {
      if (type == 1) {
        this.lockFlag = true;
        //锁定
        if (this.intervalId) {
          clearInterval(this.intervalId);
        }
      } else {
        this.lockFlag = false;
        //解锁
        clearInterval(this.intervalId);
        this.startTimer();
      }
    },
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;

  .title-icon {
    margin-left: 20px;
    margin-top: 22px;
    margin-right: 20px;
  }

  .icon-ywyy {
    display: inline-block;
    // background: url("../../assets/image/common/p_yewuyingyong.png") no-repeat;
    background-size: 100%;
    width: 18px;
    height: 18px;
    min-width: 18px;
  }

  .my-tabs {
    margin-top: 12px;
    width: 100%;

    ::v-deep .el-tabs__item {
      font-size: 18px !important;
      // color: $mainBg;$mainBg;
      // color: $mainBg;
    }
  }

  .titleLine {
    height: 2px;
    width: 100%;
    background-color: #e2e5ed;
  }

  .suo-content {
    position: absolute;
    right: 10px;
    width: 30px;
    height: 30px;
    top: 5px;

    // border: 1px solid red;
    .sc-icon {
      font-size: 32px;
      color: #3272ce;
      cursor: pointer;
    }
  }
}
</style>