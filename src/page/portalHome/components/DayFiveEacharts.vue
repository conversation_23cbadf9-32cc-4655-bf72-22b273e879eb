<template>
  <div class="person-view echart-panels-container">
    <!-- <comp-welcome sys-name="人员管理" /> -->

    <div class="echart-main">
      <div class="echarts-row">
        <!-- 办事群众人流量统计 -->
        <div class="echarts-people-flow">
          <div id="dayFiveId" class="flow-echarts">
            <!-- <data-empty title="办事群众人流量统计"></data-empty> -->
          </div>
          <!-- <div class="date-pick">
            <div
              class="pick-day"
              @click="handlePicking((type = 1))"
              :class="active == 1 ? 'pick-active' : ''"
            >
              <span class="day-color">本日</span>
            </div>
            <div
              class="pick-month"
              @click="handlePicking((type = 2))"
              :class="active == 2 ? 'pick-active' : ''"
            >
              <span class="day-color">本月</span>
            </div>
            <div
              class="pick-year"
              @click="handlePicking((type = 3))"
              :class="active == 3 ? 'pick-active' : ''"
            >
              <span class="day-color">本年</span>
            </div>
          </div> -->
        </div>
      </div>

      <!-- 特殊人群组成分类，  -->
      <!-- <specil-man></specil-man> -->
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "index",
  components: {},
  data() {
    let _this = this;
    return {
      active: 0,
      liActive: 0,
      peopleCountApiData: {},
      // peopleCountNameArr: [],
      dateArr: [],
      peopleCountNameArr3: [
        {
          count: 150,
          month: "1月",
        },
        {
          count: 100,
          month: "2月",
        },
        {
          count: 180,
          month: "3月",
        },
        {
          count: 200,
          month: "4月",
        },
        {
          count: 120,
          month: "5月",
        },
        {
          count: 150,
          month: "6月",
        },
        {
          count: 200,
          month: "7月",
        },
        {
          count: 220,
          month: "8月",
        },
        {
          count: 130,
          month: "9月",
        },
        {
          count: 130,
          month: "10月",
        },
        {
          count: 150,
          month: "11月",
        },
        {
          count: 80,
          month: "12月",
        },
      ],
      peopleCountNameArr: [],
      peopleCountNowValArr: [],
      peopleCountLastValArr: [],
      // peopleCountNowValArr: [],
      optionData: {
        span: 6,
        data: [
          // {
          //   title: "部门数量",
          //   count: "0",
          //   icon: "cust-iconfont icon-bumenshuliang",
          //   color: "#71D5CF",
          //   click: function () {
          //     _this.$router.push("/person/personmngdeptinfo/index");
          //   },
          // },
          {
            title: "工作人员数量",
            count: "0",
            icon: "cust-iconfont icon-renyuanshuliang",
            color: "#FEC268",
            click: function () {
              _this.$router.push("/person/personmngstaffinfo/index");
            },
          },
          {
            title: "办事群众数量",
            count: "0",
            icon: "cust-iconfont icon-banshiqunzhongshuliang",
            color: "#3272CE",
            click: function () {
              _this.$router.push("/person/personmngcitizeninfo/index");
            },
          },
          // {
          //   title: "特殊人群数量",
          //   count: "0",
          //   icon: "cust-iconfont icon-teshurenqunshuliang",
          //   color: "#FB7254",
          //   click: function () {
          //     _this.$router.push("/person/personmngcitizeninfo/index");
          //   },
          // },
        ],
      },
      currentEchartsObj: {
        value: 62,
        id: "echarts1",
        color: "#8878F7",
        name: "窗口在线",
      }, //当前窗口Echarts配置
    };
  },
  props: {
    fiveData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  created() {
    // this.handleGetPersonOverviewData();
  },
  mounted() {
    // this.handleGetEchartData();

    setTimeout(() => {
      this.initFiveData();
    }, 300);
    let that = this;
    window.addEventListener("resize", function () {
      if (that.peopleFlowEcharts) {
        that.peopleFlowEcharts.resize();
      }
    });
  },
  methods: {
    handleRefInit(data) {
      // console.log("handleRefInit");
      // console.log("fiveData=1111=", data);
      if (data && data.length > 0) {
        this.peopleCountNameArr = [];
        this.peopleCountNowValArr = [];
        data.forEach((item) => {
          this.peopleCountNameArr.push(item.indName);
          this.peopleCountNowValArr.push(item.indValue);
        });
      }

      this.initPeopleFlowEchartsFn();
    },
    initFiveData() {
      if (this.fiveData && this.fiveData.length > 0) {
        this.peopleCountNameArr = [];
        this.peopleCountNowValArr = [];
        this.fiveData.forEach((item) => {
          this.peopleCountNameArr.push(item.indName);
          this.peopleCountNowValArr.push(item.indValue);
        });
      }
      // console.log(" this.peopleCountNameArr==", this.peopleCountNameArr);
      this.initPeopleFlowEchartsFn();
    },

    handleSetPeopleRecordData(resData) {
      if (
        resData.length === 0 ||
        resData.every((item) => {
          return +item.zoneMatterCount === 0;
        })
      ) {
        return;
      }
      this.peopleCountNameArr = [];
      this.peopleCountLastValArr = [];
      this.peopleCountNowValArr = [];
      resData.forEach((item) => {
        this.peopleCountNameArr.push(item.month + "月");
        this.peopleCountLastValArr.push(item.lastYearCount);
        this.peopleCountNowValArr.push(item.currentYearCount);
      });
      // this.initPeopleFlowEchartsFn(); //
    },

    // 办事群众人流量统计
    initPeopleFlowEchartsFn() {
      // console.log("initPeopleFlowEchartsFn==");
      let chartDom = document.getElementById("dayFiveId");
      // console.log("chartDom===", chartDom);
      let myChart = echarts.init(chartDom);
      this.peopleFlowEcharts = myChart;
      let option;
      option = {
        title: {
          text: "近5日新登记主体变化趋势",
          subtext: "单位：户",
          textStyle: {
            fontSize: 14,
            color: "#22242C",
            align: "center",
          },
          padding: [20, 20],
          show: false,
        },
        // legend: {
        //   data: [
        //     {
        //       name: "今年人流量",
        //       textStyle: {
        //         color: "#606266",
        //         fontWeight: "bold",
        //       },
        //     },
        //   ],
        //   icon: "circle",
        //   padding: [20, 40],
        //   right: "1%",
        // },
        color: ["#5B8FF9", "#71D5CF"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#5B8FF9",
            },
          },
          backgroundColor: "#46484f",
          borderWidth: 0,
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
        },

        grid: {
          left: "5%",
          right: "4%",
          top: "5%",
          bottom: "20",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.peopleCountNameArr,
          axisLine: {
            lineStyle: {
              color: "rgba(97,96,220,0.10)",
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#8e8ea1",
              fontSize: "12",
            },
            interval: 0,
            /* rotate: 40*/
          },
          textStyle: {
            padding: [0, 0, 0, 0],
            fontSize: 12,
            fontWeight: "700",
            color: "#5B8FF9",
          },
        },
        yAxis: {
          type: "value",
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: true },
        },
        // dataZoom: [
        //   {
        //     type: "inside"
        //   }
        // ],
        series: [
          {
            data: this.peopleCountNowValArr,
            type: "line",
            smooth: false,
            name: "",
            // symbol: "circle",

            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#5B8FF9", // 0% 处的颜色
                  // color: "#8878F7", // 0% 处的颜色
                },
                {
                  offset: 0.5,
                  color: "#5B8FF9", // 100% 处的颜色
                  // color: "#eefcfd", // 100% 处的颜色
                },
                {
                  offset: 1,
                  color: "#fff", // 100% 处的颜色
                },
              ]), //背景渐变色
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  // 系列级个性化折线样式
                  width: 3,
                  type: "solid",
                  color: "#5B8FF9",
                },
              },
              emphasis: {
                color: "#5B8FF9",
                lineStyle: {
                  // 系列级个性化折线样式
                  width: 2,
                  type: "solid",
                  color: "#5B8FF9", //折线的颜色
                },
              },
            }, //线条样式
          },
          // {
          //   data: this.peopleCountNowValArr,
          //   type: "line",
          //   smooth: false, // 直线或曲线
          //   name: "往年人流量",
          //   // symbol: "circle",
          //   areaStyle: {
          //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //       {
          //         offset: 0,
          //         color: "#71D5CF", // 0% 处的颜色
          //       },
          //       {
          //         offset: 0.4,
          //         color: "#e4f2ff", // 100% 处的颜色
          //       },
          //       {
          //         offset: 1,
          //         color: "#fff", // 100% 处的颜色
          //       },
          //     ]), //背景渐变色
          //   },
          //   itemStyle: {
          //     normal: {
          //       lineStyle: {
          //         // 系列级个性化折线样式
          //         width: 3,
          //         type: "solid",
          //         color: "#71D5CF", //折线的颜色
          //       },
          //     },
          //     emphasis: {
          //       color: "#71D5CF",
          //       lineStyle: {
          //         // 系列级个性化折线样式
          //         width: 2,
          //         type: "solid",
          //         color: "#71D5CF",
          //       },
          //     },
          //   }, //线条样式
          // },
        ],
      };

      option && myChart.setOption(option);
    },

    // 专区窗口 点击切换 active
    handleShowLi(val) {
      this.liActive = val;
    },
    // 当前窗口状态 点击切换切换
    handleSetChangeCharts(item) {
      this.currentEchartsObj.value = item.press;
      this.currentEchartsObj.name = item.title;
      this.currentEchartsObj.color = item.color;

      this.initCurrentMainEchartsFn();
    },
  },
};
</script>

<style lang="scss" scoped>
.person-view {
  ul li {
    list-style-type: none;
  }
  .view-icon {
    ::deep .cust-iconfont {
      font-size: 64px !important;
    }
  }

  ::deep .avue-data-panel .el-col:nth-child(1) {
    .item-title {
      color: #71d5cf;
    }
  }
  ::deep .avue-data-panel .el-col:nth-child(2) {
    .item-title {
      color: #fec268;
    }
  }
  ::deep .avue-data-panel .el-col:nth-child(3) {
    .item-title {
      color: #3b95e8;
    }
  }
  ::deep .avue-data-panel .el-col:nth-child(4) {
    .item-title {
      color: #fb7254;
    }
  }

  .echart-main {
    margin: 18px 10px 0 0;
    position: relative;
    // width: 100%;
    width: 600px;

    .echarts-row {
      width: 100%;
      position: relative;
      margin-top: 16px;
      .echarts-people-flow {
        position: relative;
        // width: calc(100% - 2px);
        width: 600px;
        height: 264px;
        background: #ffffff;
        border-radius: 8px;
        border: 1px solid transparent;
        .flow-echarts {
          width: 100%;
          height: 100%;
        }
        .date-pick {
          position: absolute;
          top: 18px;
          right: 5%;
          width: 140px;
          height: 30px;
          justify-content: space-between;
          // border: 1px solid red;
          display: flex;
          line-height: 30px;
          .pick-day {
            width: 45px;
            height: 30px;
            text-align: center;
            background: #ffffff;
            border: 1px solid #3272ce;
            border-radius: 4px;
            line-height: 30px;
            &:hover {
              cursor: pointer;
            }
            .day-color {
              color: #3272ce;
              font-size: 12px;
              line-height: 30px;
            }
          }
          .pick-month {
            width: 45px;
            height: 30px;
            text-align: center;
            background: #ffffff;
            border: 1px solid #3272ce;
            border-radius: 4px;
            line-height: 30px;
            &:hover {
              cursor: pointer;
            }
            .day-color {
              color: #3272ce;
              font-size: 12px;
              line-height: 30px;
            }
          }
          .pick-year {
            width: 45px;
            height: 30px;
            text-align: center;
            background: #ffffff;
            border: 1px solid #3272ce;
            border-radius: 4px;
            line-height: 30px;
            &:hover {
              cursor: pointer;
            }
            .day-color {
              color: #3272ce;
              font-size: 12px;
              line-height: 30px;
            }
          }
          .pick-active {
            background: #3272ce;
            .day-color {
              color: #ffffff;
            }
          }
        }
        .people-day {
          display: inline-block;
          position: absolute;
          right: 240px;
          top: 25px;
          &:hover {
            cursor: pointer;
          }
        }
        .people-day-text {
          display: inline-block;
          position: absolute;
          color: #4a4a57;
          font-size: 12px;
          right: 430px;
          top: 30px;
          &:hover {
            cursor: pointer;
          }
        }
        .people-month {
          display: inline-block;
          position: absolute;
          background-color: rgb(113, 213, 207);
          right: 370px;
          top: 33px;
          width: 8px;
          height: 8px;
          border-radius: 40px;
          &:hover {
            cursor: pointer;
          }
        }
        .people-month-text {
          display: inline-block;
          position: absolute;
          color: #4a4a57;
          font-size: 12px;
          right: 300px;
          top: 30px;
          line-height: 30px;
          &:hover {
            cursor: pointer;
          }
        }
        .people-year {
          display: inline-block;
          position: absolute;
          background-color: rgb(113, 213, 207);
          right: 240px;
          top: 33px;
          width: 8px;
          height: 8px;
          border-radius: 40px;
          line-height: 30px;
          &:hover {
            cursor: pointer;
          }
        }
        .people-year-text {
          display: inline-block;
          position: absolute;
          color: #4a4a57;
          font-size: 12px;
          right: 170px;
          top: 30px;
          line-height: 30px;
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
    .row-flex {
      display: flex;
      position: relative;
    }

    .echarts-bootm {
      display: flex;
      width: 100%;
      position: relative;
      margin-top: 16px;

      .echarts-current {
        width: 37%;
        height: 348px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 1px 4px 0px rgba(74, 91, 109, 0.1);
      }
      .echarts-window {
        margin-left: 16px;
        width: 59.5%;
        height: 348px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 1px 4px 0px rgba(74, 91, 109, 0.1);
      }
      .window-use-month {
        position: absolute;
        top: 10px;
        right: 5%;
        width: 8%;
        height: 32px;
        line-height: 32px;
        background: #ffffff;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        color: #22242c;
        font-size: 14px;
        padding-left: 16px;
      }
      .current-press {
        position: absolute;
        width: 33%;
        height: 60px;
        left: 2%;
        bottom: 40px;

        .current-content {
          display: flex;
          margin-bottom: 25px;
          .current-content-title {
            font-size: 14px;
            color: #22242c;
            font-weight: bold;
            min-width: 60px;
          }
          .current-content-word {
            font-size: 14px;
            color: #8e8ea1;
            font-weight: bold;
          }
          .current-content-line {
            width: 66%;
            margin: 8px 16px 0 16px;
            &:hover {
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}

::deep .data-empty .module-title {
  font-size: 14px !important;
  font-weight: 700;
}

::deep .age-title {
  font-size: 14px !important;
  font-weight: 700;
}

::deepv .catgory-title {
  font-size: 14px !important;
  font-weight: 700;
}
</style>
