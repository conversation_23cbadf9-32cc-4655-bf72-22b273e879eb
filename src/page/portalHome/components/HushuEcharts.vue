<!--
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2021-10-25 11:00:47
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-10-25 10:07:08
-->
<template>
  <div class="echarts-up">
    <!--工作人员部门分类-->
    <!-- <div id="workManDepart" class="echarts-depart">
      <data-empty title="工作人员分类"></data-empty>
    </div> -->
    <!-- <div class="depart-tooltip" v-if="departShowTipFlag"></div> -->
    <!--工作人员进驻TOP10-->
    <div class="echarts-topten">
      <div class="echarts-topten2" id="manTopTen"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
// import {
//   getPersonClassify,
//   getStaffDeptClassify,
//   getDicByType,
// } from "@/api/person/person-view.js";
// import { compare, exactDiv, exactMul } from "@/util/util.js";
export default {
  name: "HushuEcharts",
  components: {},
  data() {
    return {
      workManNameArr: [],
      workManValArr: [],
      workManTopTenData: {},
      workCateData: [],
      workCateApiData: [],
      activeType: 0,
      topFlag: true,
      personTypeData: [],
      widthStr: "",
    };
  },
  props: {
    fengxianArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  created() {
    // this.handleGetTopTenData(); // 工作人员进驻top10 请求数据
  },
  mounted() {
    setTimeout(() => {
      this.initData();
    }, 1000);

    // this.initWorkManDepartEchartsFn();
    // let that = this;
    // that.initWorkManDepartEchartsFn();
    // window.addEventListener("resize", function () {
    //   that.workManDepartEcharts.resize();
    //   that.manTopTenEcharts.resize();
    // });
  },
  methods: {
    initData() {
      if (this.fengxianArr && this.fengxianArr.length > 0) {
        this.workManNameArr = [];
        this.workManValArr = [];
        this.fengxianArr.forEach((item) => {
          // this.workManNameArr.unshift(item.riskLevelCode + "级");
          // this.workManValArr.unshift(item.value);
          this.workManNameArr.push(item.riskLevelCode + "级");
          this.workManValArr.push(item.value);
        });
      }

      this.initManTopTenEchartsFn();
    },
    handleSetDepartData(resData) {
      this.workCateData = [];
      if (
        resData.length === 0 ||
        resData.every((item) => {
          return +item.zoneMatterCount === 0;
        })
      ) {
        return;
      }
      if (resData && resData.length > 0) {
        let sum = 0;
        resData.forEach((item) => {
          if (item.count !== 0) {
            let obj = {};
            obj.value = item.count;
            obj.mName = item.classify;
            //   obj.name = item.count;

            sum = sum + item.count;
            this.workCateData.push(obj);
            //  name: "34%", mName: "外包人员"
          }
        });

        let nameArr = []; //存放name,用来判断是否有相同的值，如果存在相同的value，会影响Echarts的显示
        let wSum = 0;
        this.workCateData.forEach((workItem, i) => {
          let wNameNum = 0;
          if (sum != 0) {
            //四舍五入
            wNameNum = Math.round(exactMul(exactDiv(workItem.value, sum), 100));
          }

          //   if (i == this.workCateData - 1) {
          //     wNameNum = 100 - wSum;
          //   }
          wSum += wNameNum;
          // 最后一个用100 减去总数
          //wNameNum = Math.round(exactDiv(workItem.value, sum));
          //   wNameNum = Math.round(wNameNum);
          if (nameArr.includes(wNameNum)) {
            let sName = wNameNum + "% ";
            // workItem.name = wNameNum + "% "; //添加了空格
            this.handleSetNameRepeatFn(nameArr, sName, workItem);
          } else {
            nameArr.push(wNameNum);
            workItem.name = wNameNum + "%";
          }
          //   console.log("workItem==", workItem);
        });
        // Math.round 四舍五入
      }
      this.initWorkManDepartEchartsFn();
    },
    // 如果value一直有重复的话，那就一直添加空格,一直循环
    handleSetNameRepeatFn(nameArr, sName, workItem) {
      if (nameArr.includes(sName)) {
        sName = sName + " ";
        this.handleSetNameRepeatFn(nameArr, sName, workItem);
      } else {
        nameArr.push(sName);
        workItem.name = sName;
      }
    },
    //工作人员top10
    handleGetTopTenData() {
      getDicByType("person_type").then((res) => {
        let map = new Map();
        res.data.data.forEach((item) => {
          map.set(item.value, item.label);
        });
        this.personTypeData = map;
      });
      getStaffDeptClassify().then((res) => {
        if (res.data.code == 0) {
          this.workManTopTenData = res.data.data;
          this.handleSetTopTenApiData(this.workManTopTenData);
        }
      });
    },
    handleSetTopTenApiData(resArr) {
      this.workManNameArr = [];
      this.workManValArr = [];
      if (
        this.workManTopTenData.length === 0 ||
        this.workManTopTenData.every((item) => {
          return +item.zoneMatterCount === 0;
        })
      ) {
        this.topFlag = false;
        return;
      } else {
        this.topFlag = true;
      }
      this.workManTopTenData.forEach((item, i) => {
        this.workManNameArr.push(item.deptName);
        this.workManValArr.push(item.count);
      });
      this.widthStr = this.workManValArr.length * 120 + "px";
      console.log("this.widthStr==", this.widthStr);

      setTimeout(() => {
        this.initManTopTenEchartsFn();
      }, 500);
    },
    // top10 升序 降序
    handleUpDown(type = 1) {
      this.activeType = type;
      let resArr = this.workManTopTenData.sort(compare("count", type));
      this.handleSetTopTenApiData(resArr);
    },
    //工作人员进驻TOP10 echarts 图
    initManTopTenEchartsFn() {
      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0,0,0,0.3)",
        },
      };
      let chartDom = document.getElementById("manTopTen");
      let myChart = echarts.init(chartDom);
      this.manTopTenEcharts = myChart;
      let option;
      option = {
        title: {
          text: "户数",
          subtext: "单位：户",
          textStyle: {
            fontSize: 14,
            fontWeight: 700,
            color: "#22242C",
            align: "center",
          },
          padding: [20, 20],
          show: false,
        },
        tooltip: {
          trigger: "item",

          backgroundColor: "#46484f",
          borderWidth: 0,
          textStyle: {
            color: "#ffffff",
            fontSize: 12,
          },
        },
        // dataZoom: [
        //   {
        //     type: "slider",
        //     show: true,
        //     realtime: true,
        //     // start: 0,
        //     // end: 5,
        //     xAxisIndex: [0, 1],
        //     bottom: "2px",
        //   },
        //   // {
        //   //   type: "inside",
        //   //   realtime: true,
        //   //   // start: 30,
        //   //   // end: 70,
        //   //   xAxisIndex: [0, 1],
        //   // },
        // ],
        grid: {
          left: 20,
          right: 20,
          bottom: "9%",
          top: 20,
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: this.workManNameArr,
          /*data: ['1', '2', '3', '4', '5', '6', '7','8','58','88'],*/
          axisLine: {
            lineStyle: {
              color: "rgba(97,96,220,0.10)",
              // width:3,   //这里是坐标轴的宽度,可以去掉
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#8e8ea1",
              fontSize: "12",
            },
            interval: 0,
            /*    rotate: 60, */
            // formatter: function (params) {
            //   if (params && params.length > 22) {
            //     params =
            //       params.substring(0, 7) +
            //       "\n" +
            //       params.substring(7, 14) +
            //       "\n" +
            //       params.substring(14, 21) +
            //       "\n" +
            //       params.substring(20);
            //   } else if (params && params.length > 15) {
            //     params =
            //       params.substring(0, 7) +
            //       "\n" +
            //       params.substring(7, 14) +
            //       "\n" +
            //       params.substring(13);
            //   } else if (params && params.length > 8) {
            //     params = params.substring(0, 7) + "\n" + params.substring(6);
            //   }
            //   return params;
            // },
          },
        },
        yAxis: {
          type: "value",
          axisLine: { show: false },
          axisTick: { show: false },

          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(97,96,220,0.10)",
            },
          },
          // inverse: true
          // axisLabel: {
          //   formatter: function() {
          //     // 去掉y轴值
          //     return "";
          //   }
          // }
        },
        series: [
          {
            data: this.workManValArr,
            type: "bar",
            barWidth: 30,
            barGap: "80%",
            // barCategoryGap: "90%",
            /*showBackground: true,
                backgroundStyle: {
                  color: '#71d5cf'
                },*/
            itemStyle: {
              normal: {
                color: "#6395FA",
                label: {
                  show: true, //开启显示
                  position: "top", //在上方显示
                  textStyle: {
                    //数值样式
                    color: "black",
                    fontSize: 12,
                  },
                },
              },
            },
          },
        ],
      };
      option && myChart.setOption(option);
    },

    getSum: function (arr, key) {
      var sum = 0,
        len = arr.length;
      for (var i = 0; i < len; i++) {
        sum += arr[i][key];
      }
      return sum;
    },
  },
};
</script>

<style scoped lang="scss">
.echarts-up {
  display: flex;
  position: relative;
  .area-option {
    position: absolute;
    width: 29%;
    height: 32px;
    top: 20px;
    left: 28%;
    // border: 1px solid red;
    .area-ul {
      display: flex;
      margin-top: 0;
      // border: 1px solid #dcdfe6;
      border-radius: 4px;
      .li-item {
        list-style-type: none;
        width: 16.6%;
        height: 32px;
        border: 1px solid #dcdfe6;
        border-left: 0;
        line-height: 32px;
        text-align: center;
        font-size: 13px;
        font-weight: 500;
        &:nth-of-type(1) {
          border-radius: 4px 0px 0px 4px;
          border-left: 1px solid #dcdfe6;
        }
        &:nth-of-type(6) {
          border-radius: 0px 4px 4px 0px;
        }
        &:hover {
          cursor: pointer;
        }
      }
      .item-active {
        border: 1px solid #3272ce !important;
        color: #3272ce;
      }
    }
  }
  .echarts-depart {
    width: calc(40% - 8px);
    height: 260px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px rgba(28, 110, 230, 0.08);
  }
  .echarts-topten {
    // margin-left: 16px;
    // width: calc(60% - 10px);
    width: 100%;
    height: 260px;
    background: #ffffff;
    border-radius: 8px;
    // box-shadow: 0px 1px 4px 0px rgba(74, 91, 109, 0.1);
    // overflow-y: hidden;
    // overflow-x: scroll;
    .echarts-topten2 {
      // width: 3300px;
      height: 260px;
      min-width: 100%;
      // background: #ffffff;
      // border-radius: 8px;
      // box-shadow: 0px 1px 4px 0px rgba(74, 91, 109, 0.1);
    }
    // &:hover {
    //   overflow-x: scroll;
    // }
  }

  .topten-sort {
    position: absolute;
    top: 18px;
    right: 5%;
    width: 70px;
    height: 30px;
    // border: 1px solid red;
    display: flex;
    .top-up,
    .top-down {
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #ffffff;

      border: 1px solid #3272ce;
      border-radius: 4px;
      .self-icon-color {
        color: #3272ce;
      }
      &:hover {
        cursor: pointer;
      }
    }
    .top-down {
      margin-left: 4px;
      background: #ffffff;
      border: 1px solid #3272ce;
      .self-icon-color {
        color: #3272ce;
      }
    }
    .icon-active {
      background: #3272ce;
      .self-icon-color {
        color: #ffffff;
      }
    }
  }
}
</style>
