<template>
  <div
    class="right-container"
    v-loading="loading"
    element-loading-text="数据加载中"
    element-loading-spinner="el-icon-loading"
  >
    <div class="ri-one">
      <div class="one-block">
        <span class="title-icon icon-ywyy icon-sjcs-theme"> </span>
        <div>企业信用风险数据概览</div>
        <div class="my-more">
          <a
            href="http://10.8.1.193/bigScreenHN/index2.html?1=1"
            target="_blank"  rel="noopenner noreferrer"
            >更多 ></a
          >
        </div>
      </div>
      <div class="titleLine line-self"></div>
    </div>
    <div class="ri-two">
      <div class="rt-title">全省企业数量</div>
      <div class="rt-ncontent">
        <span class="rt-num">{{ qiyeObj.value }}</span>
        <span>{{ qiyeObj.unit }}</span>
      </div>
    </div>
    <div class="ri-three">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">户数</div>
        <div class="rh-dw">单位：万户</div>
      </div>
      <hushu-echarts :fengxianArr="fengxianArr"></hushu-echarts>
    </div>
    <div class="ri-three">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">占比</div>
      </div>
      <type-per-echarts :fengxianArr="fengxianArr"></type-per-echarts>
    </div>
    <div class="ri-four">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">环比</div>
      </div>
      <div class="four-content">
        <template v-if="fengxianFanArr.length > 0">
          <div
            class="progress-content"
            v-for="(item, i) in fengxianFanArr"
            :key="i"
          >
            <el-progress
              type="circle"
              :percentage="item.hb"
              :stroke-width="9"
              :show-text="false"
              :color="item.hbNum < 0 ? '#FEC268' : ''"
            >
            </el-progress>
            <div class="circleCenter">
              <span>{{ item.riskLevelCode }}级</span>
              <div>{{ item.valueHB }}%</div>
            </div>
          </div>
        </template>

        <!-- <el-progress
          type="circle"
          :percentage="25"
          stroke-width="9"
          :format="format"
        >
        </el-progress>
        <el-progress
          type="circle"
          :percentage="25"
          stroke-width="9"
          :format="format"
        >
        </el-progress>
        <el-progress
          type="circle"
          :percentage="25"
          stroke-width="9"
          :format="format"
        > </el-progress>-->
      </div>
    </div>
  </div>
</template>
<script>
import HushuEcharts from "./HushuEcharts.vue";
import TypePerEcharts from "./TypePerEcharts.vue";
import { getCompanyFengxianData } from "@/api/portal/portal.js";
export default {
  components: { HushuEcharts, TypePerEcharts },
  data() {
    return {
      allData: [],
      qiyeObj: {},
      fengxianArr: [],
      fengxianFanArr: [],
      loading: false,
    };
  },
  created() {
    this.handleGetCompanyDataFn();
  },
  methods: {
    handleGetCompanyDataFn() {
      this.loading = true;
      let params = {
        domDistrict: "410000",
      };
      getCompanyFengxianData(params).then((res) => {
        this.loading = false;
        // console.log("企业风险==", res);
        if (res.data.code == 200) {
          this.allData = res.data.data;
          if (this.allData && this.allData.length == 5) {
            this.qiyeObj = this.allData[4];
            this.fengxianArr = this.allData.splice(0, 4);
            this.fengxianFanArr = this.fengxianArr.reverse();
            // console.log("this.fengxianFanArr==", this.fengxianFanArr);
            this.fengxianFanArr.forEach((cItem) => {
              // console.log("cItem==", cItem.valueHB);
              // console.log("cItem=+=", +cItem.valueHB);
              let hb = +cItem.valueHB;
              cItem.hbNum = hb;
              if (hb < 0) {
                cItem.hb = hb * -1;
              } else {
                cItem.hb = hb;
              }
            });
            // console.log("fengxianArr==", this.fengxianArr);
            // console.log("qiyeObj==", this.qiyeObj);
          }
        }
      });
    },
    format2(val) {
      let tex = "A级";
      let str = `${tex} \n ${val}%`;
      return str;
    },
    format(hb, riskLevelCode) {
      // console.log("hb==", hb);
      // console.log("riskLevelCode==", riskLevelCode);
      let tex = riskLevelCode + "级";
      let str = `${tex} \n ${hb}%`;
      console.log("str====", str);
      return str;
      // return tex + "\n" + hb + "%";
    },
  },
};
</script>
<style lang="scss" scoped>
.right-container {
  position: relative;
  .ri-one {
    height: 48px;
    line-height: 48px;

    .title-icon {
      margin-right: 10px;
      //   margin-top: -16px;
    }

    .icon-ywyy {
      display: inline-block;
      // background: url("../../assets/image/common/p_yewuyingyong.png") no-repeat;
      background-size: 100%;
      width: 20px;
      height: 20px;
    }
    .one-block {
      height: 48px;
      line-height: 48px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      .my-more {
        position: absolute;
        right: 10px;
        z-index: 9;
        cursor: pointer;
        color: #437fff;
        a {
          color: #437fff;
        }
      }
    }

    .titleLine {
      height: 2px;
      width: 100%;
      background-color: #e2e5ed;
    }
  }
  .ri-two {
    display: flex;
    align-items: center;

    height: 70px;
    background: #f4f8ff;
    .rt-title {
      margin-left: 40px;
      margin-right: 80px;
      color: #333333;
      font-size: 16px;
    }
    .rt-ncontent {
      display: flex;
      align-items: center;
      .rt-num {
        font-weight: 600;
        color: #002b82;
        font-size: 24px;
      }
    }
    .lo-title {
      font-size: 16px;

      font-weight: 600;
      color: #0357ca;
    }
    .lo-title-new {
      color: #994500;
    }
    .two-right {
      margin-left: 10px;
      width: 295px;
      height: 180px;
      background: linear-gradient(180deg, #ffe0ad 0%, #fff2d4 100%);
    }
    .title-ys {
      //   margin: 20px 13px 0 7px;
      margin-right: 13px;
    }
  }
  .ri-three {
    margin: 10px 0 0 20px;
    height: 230px;
    // border: 1px solid red;
  }
  .rh-top {
    position: relative;
    display: flex;
    align-items: center;
    .ti-left {
      margin-left: 10px;
      color: #0357ca;
    }
    .rh-dw {
      position: absolute;
      right: 10px;
      top: 0px;
      color: #666666;
      font-size: 12px;
    }
  }
  .ri-four {
    margin: 20px 0 0 20px;
    .four-content {
      margin-top: 20px;
      display: flex;
      justify-content: space-around;
      position: relative;
    }
    .progress-content {
      position: relative;
      .circleCenter {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        div {
          font-size: 22px;
          color: #333333;
          font-weight: 600;
          margin-bottom: 5px;
          width: 80px;
          text-align: center;
        }
        span {
          font-size: 14px;
          color: #333333;
          display: inline-block;
          width: 80px;
          text-align: center;
        }
      }
    }
  }
  .title-zi {
    width: 3px;
    height: 17px;
    background: #0357ca;
  }
  .title-zi-bg {
    background: #994500;
  }
}
::v-deep .el-progress__text {
  white-space: pre;
}
</style>