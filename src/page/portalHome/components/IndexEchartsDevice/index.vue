<template>
  <div class="right-container">
    <div class="ri-one">
      <div class="one-block">
        <span class="title-icon icon-ywyy icon-sjcs-theme"> </span>
        <div>特种设备数据概览</div>
      </div>
      <!-- <div class="titleLine line-self"></div> -->
    </div>
    <div style="margin-left: 20px; margin-top: 10px">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">特种设备数量统计</div>
      </div>
    </div>
    <div class="ri-two">
      <div class="two-top-left">
        <div class="block-three">
          <div class="bl-panel" v-for="(item, i) in topLeftData" :key="i">
            <div class="bp-color lo-title-top-left">
              {{ item.indValue }}<span style="font-size: 12px"></span>
            </div>
            <div>{{ item.indName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="ri-two">
      <div class="two-bottom-left">
        <div class="block-three">
          <div class="bl-panel" v-for="(item, i) in bottomLeftData" :key="i">
            <div class="bp-color lo-title-bottom-left">
              {{ item.indValue }}<span style="font-size: 12px"></span>
            </div>
            <div>{{ item.indName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="ri-three">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">特种设备企业区域分布</div>
      </div>
      <index-echarts-bar-middle ref="barMiddleRef"></index-echarts-bar-middle>
    </div>
    <div class="ri-three">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">安装维修</div>
      </div>
      <index-echarts-pie-bottom ref="pieBottomRef"></index-echarts-pie-bottom>
    </div>
  </div>
</template>
<script>
import IndexEchartsBarMiddle from "./IndexEchartsBarMiddle.vue";
import IndexEchartsPieBottom from "./IndexEchartsPieBottom.vue";

import { getZhutiData } from "@/api/portal/portal.js";
export default {
  components: { IndexEchartsBarMiddle, IndexEchartsPieBottom },
  data() {
    return {
      defaultOption: {
        step: 8, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 46, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
      },
      scrollHeight: "180px",
      areaData: [],
      diaoxiaoObj: {},
      areaConpanyArr: [
        { indName: "焦作军田建...", indValue: "优", date: "2023-10-11" },
        { indName: "桐柏县武五...", indValue: "良", date: "2023-09-28" },
        { indName: "临颍县岚萝...", indValue: "合格", date: "2023-11-04" },
        { indName: "永城市景顺...", indValue: "不合格", date: "2023-11-08" },
      ],
      topLeftData: [
        { indName: "车辆", indValue: "0" },
        { indName: "电梯", indValue: "0" },
        { indName: "锅炉", indValue: "0" },
        { indName: "起重机械", indValue: "0" },
      ],
      bottomLeftData: [
        { indName: "客运索道", indValue: "0" },
        { indName: "压力管道", indValue: "0" },
        { indName: "压力容器", indValue: "0" },
        { indName: "大型游乐设施", indValue: "0" },
      ],
      barData: [],
      funnelData: [
        { value: 20, name: "A(2851家）" },
        { value: 40, name: "B(5606家）" },
        { value: 60, name: "C(9999家）" },
        { value: 80, name: "D(254家）" },
      ],
    };
  },
  mounted() {
    this.handleGetZhutiDataFn();
  },
  methods: {
    initDataFn(data) {
      console.log("data===", data);

      this.topLeftData[0].indValue = data.cnjdcUnitCount;
      this.topLeftData[1].indValue = data.dtUnitCount;
      this.topLeftData[2].indValue = data.glUnitCount;
      this.topLeftData[3].indValue = data.glUnitCount;
      this.bottomLeftData[0].indValue = data.kysdUnitCount;
      this.bottomLeftData[1].indValue = data.ylgdUnitCount;
      this.bottomLeftData[2].indValue = data.ylrqUnitCount;
      this.bottomLeftData[3].indValue = data.dxylssUnitCount;

      let shebeiFenbuArr = data.shebeiFenbuArr;
      let anzhuangArr = data.anzhuangArr;
      this.$nextTick(() => {
        this.$refs.barMiddleRef.handleInit(shebeiFenbuArr);
        this.$refs.pieBottomRef.handleInit(anzhuangArr);
      });
    },
    handleGetZhutiDataFn() {
      // if (this.$refs.barEchartRef) {
      //   this.$refs.barEchartRef.handleInit(this.barData);
      // }
      // if (this.$refs.funnelEchartRef) {
      //   this.$refs.funnelEchartRef.handleInit(this.funnelData);
      // }
      //   getZhutiData().then((res) => {
      //     if (res.data.code == 200) {
      //       this.allData = res.data.data;
      //       let diaoxiaoArr = res.data.data.regCanRevData; //吊销
      //       if (diaoxiaoArr && diaoxiaoArr.length > 0) {
      //         this.diaoxiaoObj = diaoxiaoArr[0];
      //       }
      //       this.areaData = res.data.data.regEntDistrictData || [];
      //       this.areaConpanyArr = res.data.data.regEntListData || [];
      //       this.topLeftData = res.data.data.topLeftData || [];
      //       this.barData = res.data.data.regEntChange5Data || [];
      //       console.log("this.barData==", this.barData);
      //     }
      //   });
    },
  },
};
</script>
<style lang="scss" scoped>
.right-container {
  position: relative;
  .ri-one {
    height: 48px;
    line-height: 48px;

    .title-icon {
      margin-right: 10px;
      //   margin-top: -16px;
    }

    .icon-ywyy {
      display: inline-block;
      // background: url("../../assets/image/common/p_yewuyingyong.png") no-repeat;
      background-size: 100%;
      width: 20px;
      height: 20px;
    }
    .one-block {
      height: 48px;
      line-height: 48px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      .my-more {
        position: absolute;
        right: 10px;
        z-index: 9;
        cursor: pointer;
        color: #437fff;
        a {
          color: #437fff;
        }
      }
    }

    .titleLine {
      height: 2px;
      width: 100%;
      background-color: #e2e5ed;
    }
  }
  .ri-two {
    display: flex;
    height: 90px;
    padding-top: 20px;
    .two-top-left {
      width: 100%;
      height: 80px;
    }
    .two-bottom-left {
      width: 100%;
      height: 80px;
    }
    .bolck-two {
      text-align: center;
      margin-top: 15px;
      .two-num {
        font-size: 30px;

        font-weight: 600;
        color: #002b82;
      }
      .two-num-color {
        color: #692300;
        font-size: 30px;

        font-weight: 600;
      }
    }
    .block-three {
      display: flex;
      justify-content: space-around;
      margin-top: 19px;
      .bp-color {
        margin-top: 10px;
        text-align: center;
      }
    }

    .two-sty {
      margin-top: 27px;
    }
    .left-one {
      display: flex;
      align-items: center;
      margin: 20px 0 0 7px;
    }
    .lo-title-top-left {
      font-size: 16px;
      font-weight: 600;
      color: #0357ca;
    }
    .lo-title-top-right {
      font-size: 16px;
      font-weight: 600;
      color: #994500;
    }
    .lo-title-bottom-left {
      font-size: 16px;
      font-weight: 600;
      color: #6520d6;
    }
    .lo-title-bottom-right {
      font-size: 16px;
      font-weight: 600;
      color: #2ca302;
    }
    .title-ys {
      //   margin: 20px 13px 0 7px;
      margin-right: 13px;
    }
  }
  .ri-three {
    margin: 30px 0 0 20px;
    height: 266px;
    // border: 1px solid red;
  }
  .rh-top {
    display: flex;
    align-items: center;
    .ti-left {
      margin-left: 10px;
      color: #0357ca;
    }
  }
  .ri-four {
    margin: 20px 0 0 20px;
    display: flex;
    .rf-tab {
      width: 295px;
      height: 220px;
      background: #ffffff;
      border: 1px solid #e6eaf4;
      position: relative;
      margin-top: 16px;
      overflow: hidden;
      .t-row {
        position: relative;
        display: flex;
        .r-head {
          width: 50%;
          text-align: center;
          font-size: 14px;
        }
      }
      .max-col {
        width: 97px;
        max-width: 97px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .z-row {
        height: 46px;
        line-height: 46px;
        border-bottom: 1px solid #e6eaf4;
      }
      .last-row {
        height: 45px;
        line-height: 45px;
      }
      .t-row-head {
        background: #f4f8ff;
        height: 40px;
        line-height: 40px;
        font-weight: 600;
        color: #333333;
        border-bottom: 1px solid #e6eaf4;
        z-index: 2;
      }
      .r-column {
        color: #333333;
        text-align: center;
        width: 50%;
      }
    }
    .rf-right {
      margin-left: 12px;
    }
  }
  .title-zi {
    width: 3px;
    height: 17px;
    background: #0357ca;
  }
  .title-zi-bg {
    background: #994500;
  }
}
</style>