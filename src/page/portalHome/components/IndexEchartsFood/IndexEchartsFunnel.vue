<template>
  <div class="typeper">
    <template>
      <div class="chart" ref="typeperRef"></div>
    </template>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "typeper",
  // mixins: [resize],
  components: {},
  props: {
    chartData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {},
  data() {
    return {
      valueData: [],
    };
  },
  mounted() {
    // setTimeout(() => {
    //   this.initData();
    // }, 1000);
  },
  methods: {
    initData() {
      this.valueData = [];
      this.handleInitChart();
    },
    handleInit(data) {
      this.valueData = data;
      this.$nextTick(() => {
        this.handleInitChart();
      });
    },
    handleInitChart() {
      const option = {
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            name: "",
            type: "funnel",
            width: "70%",
            height: "80%",
            left: "10%",
            top: "10%",
            sort: "ascending",
            label: {
              position: "right",
            },
            data: this.chartData,
          },
        ],
      };
      const myChart = echarts.init(this.$refs.typeperRef);
      myChart.setOption(option);
      this.myChart = myChart;
      // window.addEventListener("resize", this.handleResizeFun);
    },
  },
};
</script>
<style scoped lang="scss">
.typeper {
  width: 100%;
  height: 100%;
  margin: 0 auto;

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
