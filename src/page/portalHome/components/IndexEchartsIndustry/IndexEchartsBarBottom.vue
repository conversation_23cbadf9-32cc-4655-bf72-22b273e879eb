<template>
  <div class="typeper">
    <template>
      <div class="chart" ref="typeperRef"></div>
    </template>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "typeper",
  // mixins: [resize],
  components: {},
  props: {
    chartData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {},
  data() {
    return {
      valueData: [],
    };
  },
  mounted() {},
  methods: {
    initData() {
      this.valueData = [];
      this.handleInitChart();
    },
    handleInit(data) {
      this.valueData = data;
      this.$nextTick(() => {
        this.handleInitChart();
      });
    },
    getNameArray() {
      var array = [];
      for (let index = 0; index < this.valueData.length; index++) {
        array.push(this.valueData[index].name);
      }
      return array;
    },
    getValueArray() {
      var array = [];
      for (let index = 0; index < this.valueData.length; index++) {
        array.push(this.valueData[index].count);
      }
      return array;
    },
    handleInitChart() {
      const option = {
        tooltip: {
          trigger: "item",
        },
        xAxis: {
          axisLabel: {
            interval: 0,
          },
          type: "category",
          data: [
            "郑州",
            "开封",
            "洛阳",
            "平顶山",
            "安阳",
            "鹤壁",
            "新乡",
            "焦作",
            "濮阳",
            "许昌",
            "漯河",
            "三门峡",
            "南阳",
            "商丘",
            "信阳",
            "周口",
            "驻马店",
            "济源",
          ],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.valueData,
            type: "bar",
            barWidth: "15px",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "#25BC3E",
                  },
                  {
                    offset: 1,
                    color: "#25BC3E",
                  },
                ]),
              },
            },
          },
        ],
      };
      const myChart = echarts.init(this.$refs.typeperRef);
      myChart.setOption(option);
      this.myChart = myChart;
      // window.addEventListener("resize", this.handleResizeFun);
    },
  },
};
</script>
<style scoped lang="scss">
.typeper {
  width: 100%;
  height: 100%;
  margin: 0 auto;

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
