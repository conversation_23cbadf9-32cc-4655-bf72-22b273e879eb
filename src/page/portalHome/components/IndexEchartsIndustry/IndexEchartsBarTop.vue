<template>
  <div class="typeper">
    <template>
      <div class="chart" ref="typeperRef"></div>
    </template>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "typeper",
  // mixins: [resize],
  components: {},
  props: {
    chartData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {},
  data() {
    return {
      valueData: [],
    };
  },
  mounted() {},
  methods: {
    initData() {
      this.valueData = [];
      this.handleInitChart();
    },
    handleInit(data) {
      this.valueData = data;
      this.$nextTick(() => {
        this.handleInitChart();
      });
    },
    getNameArray() {
      var array = [];
      for (let index = 0; index < this.valueData.length; index++) {
        array.push(this.valueData[index].name);
      }
      return array;
    },
    getValueArray() {
      var array = [];
      for (let index = 0; index < this.valueData.length; index++) {
        array.push(this.valueData[index].count);
      }
      return array;
    },
    handleInitChart() {
      const option = {
        tooltip: {
          trigger: "item",
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            fontSize: 11,
          },
          type: "category",
          data: [
            "电线电缆",
            "化肥",
            "建筑用钢筋",
            "水泥",
            "广播电视\n传输设备",
            "人民币\n鉴别仪",
            "危险化学\n品",
            "预应力混\n凝土铁路\n桥简支梁",
            "食品\n相关产品",
          ],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.valueData,
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                borderRadius: [15, 15, 15, 15],
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "#5470C6",
                  },
                  {
                    offset: 1,
                    color: "#A5BAFB",
                  },
                ]),
              },
            },
          },
        ],
      };
      const myChart = echarts.init(this.$refs.typeperRef);
      myChart.setOption(option);
      this.myChart = myChart;
      // window.addEventListener("resize", this.handleResizeFun);
    },
  },
};
</script>
<style scoped lang="scss">
.typeper {
  width: 100%;
  height: 100%;
  margin: 0 auto;

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
