<template>
  <div class="typeper">
    <template>
      <div class="chart" ref="middleBingRef"></div>
    </template>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "typeper",
  // mixins: [resize],
  components: {},
  props: {
    chartData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {},
  data() {
    return {
      valueData: [],
    };
  },
  mounted() {
    // setTimeout(() => {
    //   this.initData();
    // }, 1000);
  },
  methods: {
    initData() {
      this.valueData = [];
      this.handleInitChart();
    },
    handleInit(data) {
      this.valueData = data;
      // console.log("抽检==222=", data);

      this.$nextTick(() => {
        this.handleInitChart();
      });
    },
    handleInitChart() {
      const option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "left",
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: "50%",
            data: this.valueData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      const myChart = echarts.init(this.$refs.middleBingRef);
      myChart.setOption(option);
      this.myChart = myChart;
      // window.addEventListener("resize", this.handleResizeFun);
    },
  },
};
</script>
<style scoped lang="scss">
.typeper {
  width: 100%;
  height: 100%;
  margin: 0 auto;

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
