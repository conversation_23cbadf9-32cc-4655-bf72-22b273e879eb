<template>
  <div class="typeper">
    <template>
      <div class="chart" ref="typeperRef"></div>
    </template>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "typeper",
  // mixins: [resize],
  components: {},
  props: {
    chartData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {},
  data() {
    return {
      valueData: [],
    };
  },
  mounted() {},
  methods: {
    initData() {
      this.valueData = [];
      this.handleInitChart();
    },
    handleInit(data) {
      this.valueData = data;
      this.getNameArray();
      this.getValueArray();
      this.$nextTick(() => {
        this.handleInitChart();
      });
    },
    getNameArray() {
      var array = [];
      for (let index = 0; index < this.valueData.length; index++) {
        array.push(this.valueData[index].name);
      }
      return array;
    },
    getValueArray() {
      var array = [];
      for (let index = 0; index < this.valueData.length; index++) {
        array.push(this.valueData[index].count);
      }
      return array;
    },
    handleInitChart() {
      const option = {
        tooltip: {
          trigger: "item",
        },
        xAxis: {
          axisLabel: {
            interval: 0,
          },
          type: "category",
          data: this.getNameArray(),
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: this.getValueArray(),
            type: "bar",
            barWidth: "15px",
            itemStyle: {
              normal: {
                borderRadius: [15, 15, 15, 15],
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "#8928F6",
                  },
                  {
                    offset: 1,
                    color: "#DFC2FC",
                  },
                ]),
              },
            },
          },
        ],
      };
      const myChart = echarts.init(this.$refs.typeperRef);
      myChart.setOption(option);
      this.myChart = myChart;
      // window.addEventListener("resize", this.handleResizeFun);
    },
  },
};
</script>
<style scoped lang="scss">
.typeper {
  width: 100%;
  height: 100%;
  margin: 0 auto;

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
