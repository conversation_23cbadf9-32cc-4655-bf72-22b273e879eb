<template>
  <div class="right-container">
    <div class="ri-one">
      <div class="one-block">
        <span class="title-icon icon-ywyy icon-sjcs-theme"> </span>
        <div>质量基础设施数据概览</div>
      </div>
      <!-- <div class="titleLine line-self"></div> -->
    </div>
    <div style="margin-left: 20px; margin-top: 10px">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">计量统计</div>
      </div>
    </div>
    <div class="ri-two">
      <div class="two-top-left">
        <div class="block-three">
          <div class="bl-panel" v-for="(item, i) in topLeftData" :key="i">
            <div>{{ item.indName }}</div>
            <div class="bp-color lo-title-top-left">
              {{ item.indValue
              }}<span style="font-size: 12px">/{{ item.indUnit }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="two-top-right">
        <div class="block-three">
          <div class="bl-panel" v-for="(item, i) in topRightData" :key="i">
            <div>{{ item.indName }}</div>
            <div class="bp-color lo-title-top-right">
              {{ item.indValue
              }}<span style="font-size: 12px">/{{ item.indUnit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="ri-two">
      <div class="two-bottom-left">
        <div class="block-three">
          <div class="bl-panel" v-for="(item, i) in bottomLeftData" :key="i">
            <div>{{ item.indName }}</div>
            <div class="bp-color lo-title-bottom-left">
              {{ item.indValue
              }}<span style="font-size: 12px">/{{ item.indUnit }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="two-bottom-right">
        <div class="block-three">
          <div class="bl-panel" v-for="(item, i) in bottomRightData" :key="i">
            <div>{{ item.indName }}</div>
            <div class="bp-color lo-title-bottom-right">
              {{ item.indValue
              }}<span style="font-size: 12px">/{{ item.indUnit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="ri-three">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">检验检测统计</div>
      </div>
      <index-echarts-bar-middle
        :chartData="barData"
        ref="middleBarEchartRef"
      ></index-echarts-bar-middle>
    </div>
    <div class="ri-three">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">地方标准</div>
      </div>
      <index-echarts-bar-bottom
        :chartData="barData"
        ref="barEchartRef"
      ></index-echarts-bar-bottom>
    </div>
  </div>
</template>
<script>
import IndexEchartsBarMiddle from "./IndexEchartsBarMiddle.vue";
import IndexEchartsBarBottom from "./IndexEchartsBarBottom.vue";

// import { getZhutiData } from "@/api/portal/portal.js";
export default {
  components: { IndexEchartsBarMiddle, IndexEchartsBarBottom },
  data() {
    return {
      defaultOption: {
        step: 8, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 46, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
      },
      scrollHeight: "180px",
      areaData: [],
      diaoxiaoObj: {},

      topLeftData: [
        { indName: "计量标准器具考核", indValue: "0", indUnit: "户" },
      ],
      topRightData: [{ indName: "法定计量授权", indValue: "0", indUnit: "户" }],
      bottomLeftData: [
        { indName: "计量器具形式批准", indValue: "0", indUnit: "户" },
      ],
      bottomRightData: [
        { indName: "注册计量师", indValue: "0", indUnit: "户" },
      ],
      barData: [],
      funnelData: [],
    };
  },
  mounted() {
    this.handleGetZhutiDataFn();
  },
  methods: {
    initDataFn(data) {
      // console.log("dddd===", data);
      if (data) {
        this.topLeftData[0].indValue = data.jlbzqjkhUnitCount; //计量标准器具考核
        this.topRightData[0].indValue = data.fdjlsqUnitCount; //法定计量授权
        this.bottomLeftData[0].indValue = data.jlqjxspzUnitCount; //计量器具形式批准
        this.bottomRightData[0].indValue = data.zzjlsUnitCount; //注册计量师

        let jianyanCount = data.jianyanCount;

        let dfbzList = data.dfbzList;
        this.$nextTick(() => {
          if (this.$refs.middleBarEchartRef) {
            this.$refs.middleBarEchartRef.handleInit(jianyanCount);
          }
          if (this.$refs.barEchartRef) {
            this.$refs.barEchartRef.handleInit(dfbzList);
          }
        });
      }
    },
    handleGetZhutiDataFn() {
      // if (this.$refs.middleBarEchartRef) {
      //   this.$refs.middleBarEchartRef.handleInit(jianyanCount);
      // }
      if (this.$refs.barEchartRef) {
        this.$refs.barEchartRef.handleInit(this.barData);
      }

      //   getZhutiData().then((res) => {
      //     if (res.data.code == 200) {
      //       this.allData = res.data.data;
      //       let diaoxiaoArr = res.data.data.regCanRevData; //吊销
      //       if (diaoxiaoArr && diaoxiaoArr.length > 0) {
      //         this.diaoxiaoObj = diaoxiaoArr[0];
      //       }
      //       this.areaData = res.data.data.regEntDistrictData || [];
      //       this.areaConpanyArr = res.data.data.regEntListData || [];
      //       this.topLeftData = res.data.data.topLeftData || [];
      //       this.barData = res.data.data.regEntChange5Data || [];
      //       console.log("this.barData==", this.barData);

      //     }
      //   });
    },
  },
};
</script>
<style lang="scss" scoped>
.right-container {
  position: relative;
  .ri-one {
    height: 48px;
    line-height: 48px;

    .title-icon {
      margin-right: 10px;
      //   margin-top: -16px;
    }

    .icon-ywyy {
      display: inline-block;
      // background: url("../../assets/image/common/p_yewuyingyong.png") no-repeat;
      background-size: 100%;
      width: 20px;
      height: 20px;
    }
    .one-block {
      height: 48px;
      line-height: 48px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      .my-more {
        position: absolute;
        right: 10px;
        z-index: 9;
        cursor: pointer;
        color: #437fff;
        a {
          color: #437fff;
        }
      }
    }

    .titleLine {
      height: 2px;
      width: 100%;
      background-color: #e2e5ed;
    }
  }
  .ri-two {
    display: flex;
    height: 100px;
    padding-top: 20px;
    .two-top-left {
      width: 363px;
      height: 90px;
      background: linear-gradient(180deg, #d0e4ff 0%, #e6f4ff 100%);
      margin-left: 20px;
    }
    .two-top-right {
      margin-left: 10px;
      width: 363px;
      height: 90px;
      background: linear-gradient(180deg, #ffe0ad 0%, #fff2d4 100%);
    }
    .two-bottom-left {
      width: 363px;
      height: 90px;
      background: linear-gradient(180deg, #e0cdff, #f3ecfe 100%);
      margin-left: 20px;
    }
    .two-bottom-right {
      margin-left: 10px;
      width: 363px;
      height: 90px;
      background: linear-gradient(180deg, #cafdb8 0%, #ebfde4 100%);
    }
    .bolck-two {
      text-align: center;
      margin-top: 15px;
      .two-num {
        font-size: 30px;

        font-weight: 600;
        color: #002b82;
      }
      .two-num-color {
        color: #692300;
        font-size: 30px;

        font-weight: 600;
      }
    }
    .block-three {
      display: flex;
      justify-content: space-around;
      margin-top: 19px;
      .bp-color {
        margin-top: 10px;
        text-align: center;
      }
    }

    .two-sty {
      margin-top: 27px;
    }
    .left-one {
      display: flex;
      align-items: center;
      margin: 20px 0 0 7px;
    }
    .lo-title-top-left {
      font-size: 16px;
      font-weight: 600;
      color: #0357ca;
    }
    .lo-title-top-right {
      font-size: 16px;
      font-weight: 600;
      color: #994500;
    }
    .lo-title-bottom-left {
      font-size: 16px;
      font-weight: 600;
      color: #6520d6;
    }
    .lo-title-bottom-right {
      font-size: 16px;
      font-weight: 600;
      color: #2ca302;
    }
    .title-ys {
      //   margin: 20px 13px 0 7px;
      margin-right: 13px;
    }
  }
  .ri-three {
    margin: 30px 0 0 20px;
    height: 266px;
    // border: 1px solid red;
  }
  .rh-top {
    display: flex;
    align-items: center;
    .ti-left {
      margin-left: 10px;
      color: #0357ca;
    }
  }
  .ri-four {
    margin: 20px 0 0 20px;
    display: flex;
    .rf-tab {
      width: 295px;
      height: 220px;
      background: #ffffff;
      border: 1px solid #e6eaf4;
      position: relative;
      margin-top: 16px;
      overflow: hidden;
      .t-row {
        position: relative;
        display: flex;
        .r-head {
          width: 50%;
          text-align: center;
          font-size: 14px;
        }
      }
      .max-col {
        width: 97px;
        max-width: 97px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .z-row {
        height: 46px;
        line-height: 46px;
        border-bottom: 1px solid #e6eaf4;
      }
      .last-row {
        height: 45px;
        line-height: 45px;
      }
      .t-row-head {
        background: #f4f8ff;
        height: 40px;
        line-height: 40px;
        font-weight: 600;
        color: #333333;
        border-bottom: 1px solid #e6eaf4;
        z-index: 2;
      }
      .r-column {
        color: #333333;
        text-align: center;
        width: 50%;
      }
    }
    .rf-right {
      margin-left: 12px;
    }
  }
  .title-zi {
    width: 3px;
    height: 17px;
    background: #0357ca;
  }
  .title-zi-bg {
    background: #994500;
  }
}
</style>