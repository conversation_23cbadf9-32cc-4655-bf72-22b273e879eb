<template>
  <div
    class="right-container"
    v-loading="loading"
    element-loading-text="数据加载中"
    element-loading-spinner="el-icon-loading"
  >
    <div class="ri-one">
      <div class="one-block">
        <span class="title-icon icon-ywyy icon-sjcs-theme"> </span>
        <div>市场主体发展数据概览</div>
        <div class="my-more">
          <a
            href="http://10.8.1.193/bigScreenHN/index2.html?1=1"
            target="_blank" rel="noopenner noreferrer"
            >更多 ></a
          >
        </div>
      </div>
      <div class="titleLine line-self"></div>
    </div>
    <div class="ri-two">
      <div class="two-left">
        <div class="left-one">
          <div class="title-zi title-ys"></div>
          <div class="lo-title">新登记主体</div>
        </div>
        <div class="bolck-two">
          <span class="two-num">{{ dengjiObj.indValue }}</span>
          <span>{{ dengjiObj.indUnit }}</span>
        </div>
        <div class="block-three">
          <div class="bl-panel" v-for="(item, i) in regEntTypeData" :key="i">
            <div>{{ item.indName }}</div>
            <div class="bp-color">{{ item.indValue }}</div>
          </div>
          <!-- <div class="bl-panel">
            <div>个体</div>
            <div class="bp-color">468</div>
          </div>
          <div class="bl-panel">
            <div>农专</div>
            <div class="bp-color">899</div>
          </div> -->
        </div>
      </div>
      <div class="two-right">
        <div class="left-one">
          <div class="title-zi title-ys title-zi-bg"></div>
          <div class="lo-title lo-title-new">注吊销主体</div>
        </div>
        <div class="bolck-two two-sty">
          <span class="two-num-color">{{ diaoxiaoObj.indValue }}</span>
          <span>{{ diaoxiaoObj.indUnit }}</span>
        </div>
      </div>
    </div>
    <div class="ri-three">
      <div class="rh-top">
        <div class="title-zi title-ys"></div>
        <div class="lo-title ti-left">近5日新登记主体变化趋势</div>
      </div>
      <day-five-eacharts
        :fiveData="fiveData"
        ref="fiveEchartRef"
      ></day-five-eacharts>
    </div>
    <div class="ri-four">
      <div class="rf-left">
        <div class="rh-top">
          <div class="title-zi title-ys"></div>
          <div class="lo-title ti-left">新登记主体区域分布</div>
        </div>

        <div class="rf-tab">
          <div class="t-row t-row-head">
            <div class="r-head">地区</div>
            <div class="r-head">数量（户）</div>
          </div>
          <vue-seamless-scroll
            class="scroll"
            :data="areaData"
            :class-option="defaultOption"
            :style="{ height: scrollHeight }"
            ref="scrollRef"
          >
            <!--   v-if="i == areaData.length - 1" -->
            <template v-for="(item, i) in areaData">
              <!-- <div class="t-row last-row" :key="i">
                <div class="r-column">{{ item.name }}</div>
                <div class="r-column">{{ item.num }}</div>
              </div> -->
              <div class="t-row z-row" :key="i">
                <div class="r-column">{{ item.indName }}</div>
                <div class="r-column">{{ item.indValue }}</div>
              </div>
            </template>
          </vue-seamless-scroll>
        </div>
      </div>
      <div class="rf-right">
        <div class="rh-top">
          <div class="title-zi title-ys"></div>
          <div class="lo-title ti-left">新登记主体区域分布企业名单</div>
        </div>
        <div class="rf-tab">
          <div class="t-row t-row-head">
            <div class="r-head">名称</div>
            <div class="r-head">地区</div>
            <div class="r-head">注册资本(万元)</div>
          </div>
          <vue-seamless-scroll
            class="scroll"
            :data="areaConpanyArr"
            :class-option="defaultOption"
            :style="{ height: scrollHeight }"
            ref="scrollRef"
          >
            <template v-for="(item, i) in areaConpanyArr">
              <div class="t-row z-row" :key="i">
                <div class="r-column max-col">{{ item.indName }}</div>
                <div class="r-column">{{ item.indUnit }}</div>
                <div class="r-column">{{ item.indValue }}</div>
              </div>
            </template>
          </vue-seamless-scroll>

          <!-- <div class="t-row last-row">
            <div class="r-column">***有限公司</div>
            <div class="r-column">新乡</div>
            <div class="r-column">546458</div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import DayFiveEacharts from "./DayFiveEacharts.vue";
import { getZhutiData } from "@/api/portal/portal.js";
export default {
  components: { DayFiveEacharts },
  data() {
    return {
      defaultOption: {
        step: 8, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 46, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
      },
      scrollHeight: "180px",
      areaData: [],
      diaoxiaoObj: {},
      dengjiObj: {},
      areaConpanyArr: [],
      regEntTypeData: [],
      fiveData: [],
      loading: false,
    };
  },
  created() {
    this.handleGetZhutiDataFn();
  },
  methods: {
    handleGetZhutiDataFn() {
      this.false = true;
      getZhutiData().then((res) => {
        if (res.data.code == 200) {
          this.false = false;
          this.allData = res.data.data;
          let diaoxiaoArr = res.data.data.regCanRevData; //吊销
          if (diaoxiaoArr && diaoxiaoArr.length > 0) {
            this.diaoxiaoObj = diaoxiaoArr[0];
          }
          let dengjiArr = res.data.data.regEntNumData; //新登记
          if (dengjiArr && dengjiArr.length > 0) {
            this.dengjiObj = dengjiArr[0];
          }
          this.areaData = res.data.data.regEntDistrictData || [];
          this.areaConpanyArr = res.data.data.regEntListData || [];
          this.regEntTypeData = res.data.data.regEntTypeData || [];
          this.fiveData = res.data.data.regEntChange5Data || [];
          // console.log("this.fiveData==", this.fiveData);
          if (this.$refs.fiveEchartRef) {
            this.$refs.fiveEchartRef.handleRefInit(this.fiveData);
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.right-container {
  position: relative;
  .ri-one {
    height: 48px;
    line-height: 48px;

    .title-icon {
      margin-right: 10px;
      //   margin-top: -16px;
    }

    .icon-ywyy {
      display: inline-block;
      // background: url("../../assets/image/common/p_yewuyingyong.png") no-repeat;
      background-size: 100%;
      width: 20px;
      height: 20px;
    }
    .one-block {
      height: 48px;
      line-height: 48px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      .my-more {
        position: absolute;
        right: 10px;
        z-index: 9;
        cursor: pointer;
        color: #437fff;
        a {
          color: #437fff;
        }
      }
    }

    .titleLine {
      height: 2px;
      width: 100%;
      background-color: #e2e5ed;
    }
  }
  .ri-two {
    display: flex;
    height: 170px;
    padding-top: 20px;
    .two-left {
      width: 363px;
      height: 160px;
      background: linear-gradient(180deg, #d0e4ff 0%, #e6f4ff 100%);
      margin-left: 20px;
    }
    .bolck-two {
      text-align: center;
      margin-top: 15px;
      .two-num {
        font-size: 30px;

        font-weight: 600;
        color: #002b82;
      }
      .two-num-color {
        color: #692300;
        font-size: 30px;

        font-weight: 600;
      }
    }
    .block-three {
      display: flex;
      justify-content: space-around;
      margin-top: 19px;
      .bp-color {
        color: #0357ca;
        margin-top: 10px;
      }
    }

    .two-sty {
      margin-top: 27px;
    }
    .left-one {
      display: flex;
      align-items: center;
      margin: 20px 0 0 7px;
    }
    .lo-title {
      font-size: 16px;

      font-weight: 600;
      color: #0357ca;
    }
    .lo-title-new {
      color: #994500;
    }
    .two-right {
      margin-left: 10px;
      width: 363px;
      height: 160px;
      background: linear-gradient(180deg, #ffe0ad 0%, #fff2d4 100%);
    }
    .title-ys {
      //   margin: 20px 13px 0 7px;
      margin-right: 13px;
    }
  }
  .ri-three {
    margin: 30px 0 0 20px;
    height: 296px;
    // border: 1px solid red;
  }
  .rh-top {
    display: flex;
    align-items: center;
    .ti-left {
      margin-left: 10px;
      color: #0357ca;
    }
  }
  .ri-four {
    margin: 20px 0 0 20px;
    display: flex;
    .rf-tab {
      width: 363px;
      height: 220px;
      background: #ffffff;
      border: 1px solid #e6eaf4;
      position: relative;
      margin-top: 16px;
      overflow: hidden;
      .t-row {
        position: relative;
        display: flex;
        .r-head {
          width: 50%;
          text-align: center;
          font-size: 14px;
        }
      }
      .max-col {
        width: 115px;
        max-width: 115px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .z-row {
        height: 46px;
        line-height: 46px;
        border-bottom: 1px solid #e6eaf4;
      }
      .last-row {
        height: 45px;
        line-height: 45px;
      }
      .t-row-head {
        background: #f4f8ff;
        height: 40px;
        line-height: 40px;
        font-weight: 600;
        color: #333333;
        border-bottom: 1px solid #e6eaf4;
        z-index: 2;
      }
      .r-column {
        color: #333333;
        text-align: center;
        width: 50%;
      }
    }
    .rf-right {
      margin-left: 12px;
    }
  }
  .title-zi {
    width: 3px;
    height: 17px;
    background: #0357ca;
  }
  .title-zi-bg {
    background: #994500;
  }
}
</style>