<template>
  <div class="typeper">
    <template>
      <div class="chart" ref="typeperRef"></div>
    </template>
    <!-- <template v-else>
      <DataEmpty
        style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        "
      />
    </template> -->
  </div>
</template>
<script>
import * as echarts from "echarts";
import {
  exactAdd,
  exactSub,
  exactMul,
  exactDiv,
  formatDecimal,
} from "@/util/util.js";
// import DataEmpty from "@/components/data-empty/index.vue";
// import resize from "./mixin.js";
export default {
  name: "typeper",
  // mixins: [resize],
  components: {},

  props: {
    fengxianArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {
    showData(newVal) {
      if (newVal.length > 0) this.handleInit(newVal);
    },
  },
  data() {
    return {
      valueData: [],
    };
  },
  mounted() {
    setTimeout(() => {
      // console.log("handleInitChar2222t==");
      this.initData();
    }, 1000);
  },
  methods: {
    initData() {
      if (this.fengxianArr && this.fengxianArr.length > 0) {
        this.valueData = [];
        let sum = 0;
        this.fengxianArr.forEach((item) => {
          // this.workManNameArr.unshift(item.riskLevelCode + "级");
          // this.workManValArr.unshift(item.value);
          sum = exactAdd(sum, item.value);
        });
        // console.log("sum===", sum);
        this.fengxianArr.forEach((item) => {
          let zhanbi = exactDiv(item.value, sum);
          let zhanbi2 = exactMul(zhanbi, 100);

          let zhanbi3 = formatDecimal(zhanbi2, 2);

          let obj = {
            value: zhanbi3,
            name: item.riskLevelCode + "级",
          };
          this.valueData.push(obj);
        });
      }

      this.handleInitChart();
    },
    handleInit(data) {
      data.forEach((item) => {
        item.value = item.count;
      });
      this.valueData = data;
      this.$nextTick(() => {
        this.handleInitChart();
      });
    },
    handleInitChart() {
      // console.log("handleInitChart==");
      const option = {
        // tooltip: {
        //   trigger: "item",
        // },
        legend: {
          show: false,
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: true,
            labelLine: {
              show: true,
              // lineStyle: {
              //   color: "rgba(0, 0, 255, 0.3)",
              // },
              length: 8,
              length2: 8,
              // maxSurfaceAngle: 60,
            },
            label: {
              alignTo: "edge",
              formatter: "{name|{b}}\n{time|{c}%}",
              // formatter: ({ value, color, name }) => {
              //   // console.log("val1", val1, val2);
              //   return `<div style="color:red">s</div>`;
              // },
              minMargin: 5,
              edgeDistance: 1,
              // lineHeight: 15,
              rich: {
                time: {
                  fontSize: 10,
                  color: "#999",
                },
              },
            },

            data: this.valueData,
            labelLine: {
              // lineStyle: {
              //   color: 'rgba(0, 0, 255, 0.3)'
              // },
              // smooth: 1,
              length: 30,
              length2: 30,
            },
          },
        ],
      };
      const myChart = echarts.init(this.$refs.typeperRef);
      myChart.setOption(option);
      this.myChart = myChart;
      // window.addEventListener("resize", this.handleResizeFun);
    },
  },
};
</script>
<style scoped lang="scss">
.typeper {
  width: 80%;
  height: 100%;
  margin: 0 auto;
  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
