<template>
  <div class="container" style="height: 100%">
    <el-drawer
      :before-close="handleClose"
      size="1050px"
      :visible.sync="dialogFlag"
      direction="rtl"
      ref="drawer"
      custom-class="drawer"
    >
      <!--头部-->
      <div class="drawer__header">
        {{ type ? "常用应用" : "编辑常用应用" }}
        <div
          @click="handleEditApps"
          style="
            float: right;
            position: absolute;
            top: 20px;
            right: 45px;
            width: 80px;
            height: 34px;
            font-size: 14px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
          "
          class="mainBGColor"
        >
          {{ type ? "编辑" : "取消编辑" }}
        </div>
      </div>
      <!--内容-->
      <div class="drawer__content">
        <div
          style="
            width: 100%;
            text-align: center;
            flex: 0;
            box-shadow: inset 0px -3px 8px 0px rgba(0, 0, 0, 0.09);
          "
        >
          <div>
            <draggable
              v-model="usualAppList"
              :disabled="type"
              @end="handleEndFn"
              class="app-content"
            >
              <div
                v-for="(item, index) in usualAppList"
                :key="index"
                class="app-block"
                @click="jumpAppAction(item)"
                :title="item.name"
              >
                <img class="app-icon" :src="picUrl + item.logo" />
                <div class="app-cliName">{{ item.name }}</div>
                <div
                  class="app-del"
                  @click.stop="removeAppFromGroup(item)"
                  v-if="!type"
                >
                  <img class="del-app-icon" src="@/assets/image/badNote.png" />
                </div>
              </div>
            </draggable>
          </div>
          <div
            v-if="allUsualAppList.length > 5 && type"
            style="
              height: 40px;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              color: #0357ca;
              font-size: 14px;
              background-color: #efeff4;
              margin-left: 20px;
              margin-right: 20px;
              margin-bottom: 20px;
            "
            @click="isShowAll = !isShowAll"
          >
            {{ !isShowAll ? "展开" : "收起" }}
            <img
              style="width: 12px; height: 12px; margin-left: 5px"
              v-if="isShowAll"
              src="@/assets/image/appGroup/showAll.png"
            />
            <img
              style="width: 12px; height: 12px; margin-left: 5px"
              v-else
              src="@/assets/image/appGroup/closeAll.png"
            />
          </div>
        </div>
        <div style="width: 100%; flex: 1; overflow: auto; padding-top: 10px">
          <div class="drawer__header">
            {{ type ? "全部应用" : "全部应用" }}
          </div>
          <div
            v-for="(item, index) in clientGroupTree"
            :key="index"
            style="background: #ffffff"
          >
            <div
              style="
                display: flex;
                align-items: center;
                margin-top: 3px;
                padding-top: 40px;
              "
            >
              <div
                style="
                  width: 4px;
                  height: 18px;
                  background: #0357ca;
                  margin-left: 50px;
                  margin-right: 10px;
                "
              ></div>
              <div style="font-size: 18px; font-weight: 500; color: #333333">
                {{ item.groupName }}
              </div>
            </div>

            <div class="app-content">
              <div
                v-for="(appItem, appIndex) in item.appList"
                :key="appIndex"
                class="app-block"
                @click="jumpAppAction(appItem)"
              >
                <img class="app-icon" :src="picUrl + appItem.logo" />
                <div class="app-cliName">{{ appItem.name }}</div>
                <div
                  class="app-del"
                  @click.stop="addAppToGroup(appItem)"
                  v-if="!type && isInUsualList(appItem.id)"
                >
                  <img
                    class="del-app-icon"
                    src="@/assets/image/appGroup/addApp.png"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--底部-->
      <div class="drawer__footer">
        <el-button @click="handleClose" v-if="!type">取 消</el-button>
        <el-button
          type="primary"
          class="mainBGColor"
          @click="handleConfirmAction"
          v-if="!type"
          >{{ "保 存" }}</el-button
        >
        <el-button @click="handleClose" v-if="type">返 回</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getClientGroupTree,
  addAppToGroup,
  getAppList,
} from "@/api/portal/portal.js";
import { mapGetters } from "vuex";
import items from "@/components/rich-text/config/items";
import { done } from "nprogress";
import draggable from "vuedraggable";
export default {
  name: "DialogForm",
  components: {
    draggable,
  },
  props: {
    dialogFlag: {
      default: false,
    },
    dialogType: {},
    refreshAppList: {
      type: Function,
      default: null,
    },
    closeDialog: {
      type: Function,
      default: null,
    },
    jumpApp: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      // 开关
      type: 1,
      clientGroupTree: [],
      usualAppList: [],
      allUsualAppList: [],
      isShowAll: false,
    };
  },
  computed: {
    picUrl() {
      // return "http://*************:9000";
      if (process.env.NODE_ENV == "development") {
        if (window.location.host.includes("localhost:")) {
          return process.env.VUE_APP_FILE_URL;
        }
      }
      return window.location.protocol + "//" + window.location.host;
    },
    ...mapGetters(["userInfo"]),
  },
  watch: {
    isShowAll() {
      this.usualAppList = JSON.parse(JSON.stringify(this.allUsualAppList));
      // console.log("this.usualAppList==", this.usualAppList);
      if (!this.isShowAll) {
        this.usualAppList = this.usualAppList.splice(0, 5);
      }
    },
    dialogType() {
      this.type = this.dialogType;
    },
  },
  created() {
    // this.getUsualAppList();
  },
  methods: {
    handleEndFn(obj) {
      // console.log("obj===", obj);
      // console.log("obj==usualAppList=", this.usualAppList);
      this.usualAppList.forEach((item, i) => {
        item.sort = i;
      });
    },
    // 表单关闭事件-通知父组件关闭（.syanc）
    // 不通知父组件可能会报错，导致只能打开一次
    handleClose() {
      this.type = 1;
      this.closeDialog();
    },
    handleInitAppData() {
      this.getAllAppList();
      this.getUsualAppList();
    },
    getUsualAppList() {
      getAppList().then((res) => {
        if (res.data.code == 200) {
          this.allUsualAppList = res.data.data;
          this.usualAppList = JSON.parse(JSON.stringify(this.allUsualAppList));

          this.usualAppList = this.usualAppList.splice(0, 5);
        }
      });
    },
    getAllAppList() {
      let params = {
        scope: 0,
      };
      getClientGroupTree(params).then((res) => {
        if (res.data.code == 200) {
          this.clientGroupTree = res.data.data;
        }
      });
    },
    addAppToGroup(item) {
      if (this.usualAppList.length > 10) {
        this.$message.error(
          "常用应用数量上限为11个，超出部分请先移除后再添加。"
        );
        return;
      }
      //已经在里面的不需要再次添加
      for (let i in this.usualAppList) {
        if (this.usualAppList[i].id == item.id) {
          this.$message.error("已存在!");
          return;
        }
      }
      this.usualAppList.push(item);
      this.allUsualAppList = JSON.parse(JSON.stringify(this.usualAppList));
    },
    removeAppFromGroup(app) {
      // console.log("=======", this.usualAppList);
      this.usualAppList = this.usualAppList.filter(
        (item) => item.id !== app.id
      );
      this.allUsualAppList = JSON.parse(JSON.stringify(this.usualAppList));

      // console.log("=======usualAppList", this.usualAppList);
    },

    updateAppInGroup() {
      let param = {
        clientInfoList: this.clientInfoList(),
        userId: this.userInfo.id,
      };
      // console.log("=====", param);
      addAppToGroup(param).then((res) => {
        if (res.data.code == 200) {
          this.$message({
            showClose: true,
            message: "保存成功！",
            type: "success",
          });
          this.$refs.drawer.closeDrawer();
          //通知父级刷新
          this.refreshAppList();
          this.$message.success("保存成功");
          this.type = 1;
          this.allUsualAppList = JSON.parse(JSON.stringify(this.usualAppList));
        }
      });
    },
    clientInfoList() {
      let clientInfoListArr = [];
      for (let i in this.usualAppList) {
        let param = {
          clientId: this.usualAppList[i].id,
          sort: i,
        };
        clientInfoListArr.push(param);
      }
      return clientInfoListArr;
    },
    handleConfirmAction() {
      this.updateAppInGroup();
    },
    jumpAppAction(item) {
      if (this.type) {
        this.jumpApp(item);
        this.closeDialog();
      }
    },
    handleEditApps() {
      this.type = !this.type;
      this.isShowAll = !this.type;
    },
    isInUsualList(id) {
      for (let i in this.usualAppList) {
        if (id === this.usualAppList[i].id) {
          return false;
        }
      }
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.container ::v-deep .el-drawer__header {
  margin: 0;
  padding: 0;

  .drawer {
    display: flex;
    height: 100%;
  }
}

.drawer__header {
  width: 100%;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #333333;
  height: 20px;
  flex: 0;
  margin-bottom: 10px;
}

.drawer__content {
  padding-top: 0;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  flex: 1;
}

.drawer__footer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  background-color: re;
  flex: 0;
  box-shadow: inset 0px 3px 8px 0px rgba(0, 0, 0, 0.09);
}

.app-content {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  // padding: 0 20px;
  margin-left: 60px;

  a:focus {
    outline: none;
  }

  .app-del {
    float: right;
    position: absolute;
    top: 0px;
    right: 45px;
    cursor: pointer;
  }

  .app-block {
    width: 176px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    margin: 20px 5px;
    position: relative;
    padding: 10px 30px;

    // &:hover {
    //   border: 1px solid #0357CA;
    //   // box-shadow: 0 0.63px 5.04px 0 #ebeef5;
    // }

    .app-icon {
      width: 56px;
      height: 56px;

      // margin-right: 20px;
      // margin-bottom: 18px;
    }

    .del-app-icon {
      width: 24px;
      height: 24px;
    }

    .app-cliName {
      font-size: 14px;
      color: #333333;
      margin-top: 8px;
      text-align: center;
      display: -webkit-box;
      /* 将 div 视为弹性容器 */
      -webkit-line-clamp: 2;
      /* 显示两行内容 */
      -webkit-box-orient: vertical;
      /* 设置纵向排列内容 */
      overflow: hidden;
      /* 隐藏超出部分 */
      text-overflow: ellipsis;
      /* 超出部分显示省略号 */
      cursor: pointer;
    }
  }

  .add-app-block {
    width: 266px;
    height: 150px;
    background-color: #f4f6f7;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin: 20px 30px;

    // &:hover {
    //   border: 1px solid #0357CA;
    //   // box-shadow: 0 0.63px 5.04px 0 #ebeef5;
    // }

    .app-icon {
      width: 56px;
      height: 56px;

      // margin-right: 20px;
      // margin-bottom: 18px;
    }

    .app-cliName {
      font-size: 14px;
      color: #333333;
      margin-top: 8px;
      text-align: center;
      font-weight: 500;
    }
  }
}
</style>
