<template>
  <div style="height: 100%">
    <top :isPortal="true" ref="topContent" v-if="topFlag"> </top>
    <!-- <catalogue></catalogue> -->
    <top-menu></top-menu>
    <!-- <div class="my-top">
      <i
        v-if="topFlag"
        class="el-icon-arrow-up"
        @click="handleShowFlag(false)"
      ></i>
      <i v-else class="el-icon-arrow-down" @click="handleShowFlag(true)"></i>
    </div> -->
    <keep-alive>
      <router-view
        v-if="$route.meta.$keepAlive"
        class="outer-bg"
        :style="{ backgroundImage: 'url(' + backgroundImageUrl + ')' }"
      />
    </keep-alive>
    <router-view
      v-if="!$route.meta.$keepAlive"
      class="outer-bg"
      :style="{ backgroundImage: 'url(' + backgroundImageUrl + ')' }"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  getAppList,
  saveLayoutData,
  getClientGroupTree,
} from "@/api/portal/portal.js";
import { getMessageList } from "@/api/message/message.js";
import { fetchList } from "@/api/noticePortal/noticePortal";
import { allRead } from "@/api/noticeCenter/noticeCenter";
import axios from "axios";
import DataEmpty from "@/components/data-empty/index.vue";
import top from "@/page/index/top/";
import catalogue from "@/page/index/catalogues/index.vue";
import NoticePortalViewDialog from "@/views/noticePortal/compontents/NoticePortalViewDialog.vue";
import DialogForm from "./components/index.vue";
import { GridLayout, GridItem } from "vue-grid-layout";
import myGridItemPreview from "./components/itemPreview.vue";
import { randomLenNum } from "@/util/util";
import { getUserInfo, getTicket } from "@/api/login";
import PortalHomeData from "./PortalHomeData.vue";
import { getFetchList } from "@/api/admin/sys/system";
import TopMenu from "@/page/main/compontents/topMenu";
export default {
  name: "home",
  components: {
    DataEmpty,
    NoticePortalViewDialog,
    top,
    DialogForm,
    catalogue,
    GridLayout,
    GridItem,
    myGridItemPreview,
    PortalHomeData,
    TopMenu,
  },
  data() {
    return {
      fileUrl:
        window.location.protocol +
        "//" +
        window.location.host +
        window.location.port,
      testBg: require("@/assets/image/yirenyidang/icon-bg0.png"),
      usualAppList: [],
      todoList: [],
      fiveTodoList: [],
      instanceUrl: "",
      instanceFlag: false,
      todoFlag: false,
      msgListFlag: false,
      hallRouthPath: "",
      msgList: [],
      viewFlag: false,
      viewObj: {},
      dialogFlag: false,
      dialogType: 0,
      backgroundImageUrl: require("@/assets/image/themesImgs/blueThem.png"),
      isDraggable: false,
      isResizable: false,
      useable: false,
      layout: [],
      topFlag: true,
      layout1: [
        { x: 6, y: 0, w: 6, h: 22, moved: false, i: "self1" },
        { x: 6, y: 22, w: 3, h: 22, moved: false, i: "self2" },
        { x: 9, y: 22, w: 3, h: 22, moved: false, i: "self3" },
        { x: 0, y: 0, w: 6, h: 44, moved: false, i: "self4" },
      ],

      showPreview: false,
      backgroundData: [],
      groupAppList: [],
      activeName: "",
      logoObj: {},
    };
  },
  watch: {
    "usualAppList.length": {
      handler(val, oldVal) {
        // console.log("val---", val);
        // setTimeout(() => {
        //   this.handleDoInitLayout();
        // }, 100);
      },
    },
  },
  computed: {
    ...mapGetters(["website", "userInfo", "menu"]),
    picUrl() {
      // console.log("--pic--", process.env);
      if (process.env.NODE_ENV == "development") {
        if (window.location.host.includes("localhost:")) {
          return process.env.VUE_APP_FILE_URL;
        }
      }
      return window.location.protocol + "//" + window.location.host;
    },
    originUrl() {
      return window.location.origin;
    },
    token() {
      return this.$store.getters.access_token;
    },
    cloudPivotToken() {
      return localStorage.getItem("cloudPivotToken");
    },
  },
  created() {
    // let layoutArrStr = localStorage.getItem("layoutArr");
    // if (layoutArrStr) {
    //   let layArr = JSON.parse(layoutArrStr);
    //   if (layArr && layArr.length > 0) {
    //     this.layout = layArr;
    //   }
    // }
    // this.getLayOutData(); //调试
    // console.log("this.layout=1111=", JSON.stringify(this.layout));
    // this.$root.$on("handleSetGrid", this.handleSetGridFn);
    // document.title = "河南省市场监督管理局";
    // this.getAppData();
    // this.getMsgList();
    // this.getNoticeList();
    // this.initPreviewData();
    this.$root.$on("dealTopShow", this.dealTopShowFn);
  },
  mounted() {
    // console.log("user=userInfo==", this.userInfo);
    // console.log("user=menu==", this.menu);
    // console.log("logoObj=222=", this.logoObj);
    if (this.logoObj && !this.logoObj.sysIcon) {
      this.getLogoFn();
    }
    //
    // setTimeout(() => {
    //   this.handleInitDomHeight();
    // }, 550);
  },

  methods: {
    // handleShowFlag(flag) {
    //   this.topFlag = flag;
    //   // this.$root.$emit("dealTopShow", flag);
    // },
    dealTopShowFn(flag) {
      this.topFlag = flag;
    },
    change_icon(iconUrl) {
      const changeFavicon = (link) => {
        let $favicon = document.querySelector('link[rel="icon"]');
        if ($favicon !== null) {
          $favicon.href = link;
        } else {
          $favicon = document.createElement("link");
          $favicon.rel = "icon";
          $favicon.href = link;
          document.head.appendChild($favicon);
        }
      };
      // 设置图标地址
      // let iconUrl = `./icoImg/1.ico`;
      // 动态修改网站图标
      changeFavicon(iconUrl);
    },
    getLogoFn() {
      // console.log("getListgetListgetList==");

      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        // console.log("logoObj==", response);
        if (response.data.code == 200) {
          this.logoObj = response.data.data || null;
          if (this.logoObj && this.logoObj.sysIcon) {
            let path = this.$getUrlByProcess(this.logoObj.sysIcon);
            this.change_icon(path);

            this.$store.commit("SET_LOGO_OBJ", this.logoObj);
          }
          if (this.logoObj && this.logoObj.sysTitle) {
            document.title = this.logoObj.sysTitle;
          }
        }
      });
    },
    // 点击跳转
    usualAppFn(item) {
      var newOpen = window.open();
      newOpen.opener = null;
      newOpen.location = item.clientIndexUrl;
      // var params = {
      //   clientId: item.clientId,
      // };
      // getTicket(params)
      //   .then((res) => {
      //     if (res.data.code == 200) {
      //       let params = res.data.data;
      //       if (item.clientIndexUrl.includes("?")) {
      //         var jumpUrl = `${item.clientIndexUrl}&ticket=${params.ticket}`;
      //       } else {
      //         var jumpUrl = `${item.clientIndexUrl}?ticket=${params.ticket}`;
      //       }
      //       window.open(jumpUrl, "_blank");
      //     }
      //   })
      //   .catch((err) => {});
    },
    handleClickTab(tab, event) {
      // console.log("tab---", tab);
      // console.log("tab-name--", tab.name);
      // console.log("event---", event);
      // console.log("this.active---", this.activeName);
      this.usualAppList = [];
      let fObj = this.groupAppList.find((item) => {
        return item.groupId == this.activeName;
      });
      if (fObj) {
        this.usualAppList = fObj.clientResList;
      }
    },
    handleInitDomHeight() {
      let dom = document.querySelector(".vue-grid-layout");
      if (dom) {
        let clientHeight = dom.clientHeight;

        if (clientHeight > 0) {
          let dom2 = document.querySelector(".portal-container");

          if (dom2) {
            let myHeight = clientHeight + 130;
            dom2.style.height = myHeight + "px";
          }
        }
      }
    },
    getLayOutData() {
      getUserInfo().then((res) => {
        // console.log("res=获取用户信息=portalHome=", res);
        if (res.data.code == 200) {
          let layout = res.data.data.sysUser.config.layout;
          // layout[3].w = 8
          if (layout && layout.length == 4) {
            this.layout = layout;
            this.layout.forEach((nItem) => {
              if (nItem.i == "self4") {
                nItem.h = 44;
              }
              if (nItem.i == "self3" || nItem.i == "self2") {
                nItem.h = 22;
              }
            });
            localStorage.setItem("layoutArr", JSON.stringify(this.layout));
          } else {
            this.layout = this.layout1;
            setTimeout(() => {
              this.handleLayoutSave(0);
            }, 600);
          }
        }
      });
    },
    //保存layLout
    handleLayoutSave(type) {
      // let params = {
      //   layouts: this.layout,
      // };
      saveLayoutData(this.layout).then((res) => {
        // console.log("保存layout---", res);
        if (res.data.code == 200) {
          this.handleInitDomHeight();
          if (type == 1) {
            this.$message({
              showClose: true,
              message: "保存成功！",
              type: "success",
            });
          }
        }
      });
    },
    handleSave() {
      this.isDraggable = false;
      // console.log("this.layout==", this.layout);
      localStorage.setItem("layoutArr", JSON.stringify(this.layout));
      this.handleLayoutSave(1);
    },
    handleCancle() {
      this.isDraggable = false;
      let layoutArrStr = localStorage.getItem("layoutArr");
      if (layoutArrStr) {
        let layArr = JSON.parse(layoutArrStr);
        if (layArr && layArr.length > 0) {
          this.layout = layArr;
        }
      }
    },
    handleSetGridFn(flag) {
      // console.log("设置可用--");
      this.isDraggable = flag;
    },
    movedEvent: function (i, newX, newY) {
      console.log("MOVED i=" + i + ", X=" + newX + ", Y=" + newY);
      console.log("this.layout==", this.layout);
      this.handleInitDomHeight();
    },
    initPreviewData() {
      this.showPreview = true;
      this.backgroundData = [];
      for (let i = 0; i < 12; i++) {
        for (let j = 0; j < 50; j++) {
          this.backgroundData.push({
            x: i,
            y: j,
            w: 1,
            h: 1,
            i: randomLenNum() + i + randomLenNum(), // 防止 key 重复
          });
        }
      }
      // console.log("backgroundData==", this.backgroundData);
    },
    changeThemesImg() {
      let imgType = localStorage.getItem("themeSelected");
      switch (imgType) {
        case "0":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/blueThem.png");
          break;
        case "1":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/greenThem.png");
          break;
        case "2":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/redThem.png");
          break;
        case "3":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/skyThem.png");
          break;
        case "4":
          this.backgroundImageUrl = require("@/assets/image/themesImgs/yelThem.png");
          break;
        default:
          this.backgroundImageUrl = require("@/assets/image/themesImgs/blueThem.png");
          break;
      }
    },
    getAppData() {
      //getClientGroupTree  //getAppList
      getClientGroupTree().then((res) => {
        console.log("获取分组的app--", res);
        if (res.data.code == 200) {
          this.groupAppList = res.data.data;
          let obj = {
            groupId: "-1",
            groupName: "全部",
            clientResList: [],
          };
          if (this.groupAppList && this.groupAppList.length > 0) {
            let arr = [];
            this.groupAppList.forEach((mItem) => {
              if (mItem.clientResList && mItem.clientResList.length > 0) {
                arr = [...arr, ...mItem.clientResList];
              }
            });
            console.log("arr-----", arr);
            obj.clientResList = arr;
            this.groupAppList.unshift(obj);
            this.activeName = this.groupAppList[0].groupId;
            this.usualAppList = this.groupAppList[0].clientResList;
          }
          // this.usualAppList = res.data.data;
          // this.usualAppList = this.usualAppList.splice(0, 11);

          // setTimeout(() => {
          //   this.handleDoInitLayout();
          // }, 100);
        }
      });
    },
    handleDoInitLayout() {
      let dom = document.querySelector(".portal-app");
      let height = 0;
      if (dom) {
        // console.log("dom---", dom.offsetHeight);
        height = dom.offsetHeight;
      }
      if (this.usualAppList.length == 0) {
        this.layout[0].h = 12;
        if (
          this.layout[0].y == 0 ||
          (this.layout[0].y < this.layout[1].y &&
            this.layout[0].y < this.layout[2].y)
        ) {
          this.layout[1].y = 12;
          this.layout[2].y = 12;
        }
      } else if (this.usualAppList.length <= 4) {
        this.layout[0].h = 10;
        if (
          this.layout[0].y == 0 ||
          (this.layout[0].y < this.layout[1].y &&
            this.layout[0].y < this.layout[2].y)
        ) {
          this.layout[1].y = 10;
          this.layout[2].y = 10;
        }
      } else if (this.usualAppList.length <= 8) {
        if (height > 340) {
          this.layout[0].h = 17;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            this.layout[1].y = 17;
            this.layout[2].y = 17;
          }
        } else {
          this.layout[0].h = 16;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            this.layout[1].y = 16;
            this.layout[2].y = 16;
          }
        }
      } else {
        if (height > 480) {
          let num1 = height / 20;
          // this.layout[0].h = 24;
          this.layout[0].h = num1;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            // this.layout[1].y = 24;
            // this.layout[2].y = 24;
            this.layout[1].y = num1;
            this.layout[2].y = num1;
          }
        } else {
          this.layout[0].h = 22;
          if (
            this.layout[0].y == 0 ||
            (this.layout[0].y < this.layout[1].y &&
              this.layout[0].y < this.layout[2].y)
          ) {
            this.layout[1].y = 22;
            this.layout[2].y = 22;
          }
        }
      }

      let layoutArrStr = localStorage.getItem("layoutArr");
      if (layoutArrStr) {
        localStorage.setItem("layoutArr", JSON.stringify(this.layout));
      }
    },
    getMsgList() {
      getMessageList({
        current: 1,
        size: 6,
        status: 1,
        messageType: 0,
        mutualStatus: 2,
      })
        .then((res) => {
          if (res.data.code == 200) {
            this.todoList = res.data.data.records || [];
            if (this.todoList && this.todoList.length > 0) {
              this.fiveTodoList = this.todoList.splice(0, 4);
            }
          }
          setTimeout(() => {
            this.fiveTodoList.length > 0
              ? (this.todoFlag = false)
              : (this.todoFlag = true);
          }, 300);
        })
        .catch((err) => {
          this.todoFlag = true;
        });
    },
    getNoticeList() {
      let param = {
        current: 1,
        size: 8,
        status: 1,
      };
      fetchList(param)
        .then((res) => {
          if (res.data.code == 200) {
            this.msgList = res.data.data.records || [];
            // let msgArr = res.data.data.records || [];

            // let msgArr2 = msgArr.filter((item) => {
            //   return item.status == 1;
            // });
            this.msgList = this.msgList.splice(0, 4);
            this.msgList.length > 0
              ? (this.msgListFlag = false)
              : (this.msgListFlag = true);
          }
        })
        .catch((err) => {
          this.msgListFlag = true;
        });
    },
    handleGoNewAddress() {},
    showMore() {
      this.$router.push({ path: "/noticeCenter/index" });
    },
    getDate(dateStr) {
      return new Date(dateStr).getDate();
    },
    getYearMonth(dateStr) {
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${date.getMonth() + 1}`;
    },
    handleView(row) {
      this.viewFlag = true;
      this.viewObj = row;
    },
    closeViewDialog() {
      this.viewFlag = false;
    },
    handleToAction(row) {
      if (row.messageJumpUrl) {
        var jumpUrl = row.messageJumpUrl + "?token=" + this.token;
        var newOpen = window.open();
        newOpen.opener = null;
        newOpen.location = jumpUrl;
      } else {
        this.$root.$emit("showHomeInShow", true, 0);
      }
    },
    handleEditApps() {
      this.dialogFlag = true;
      this.dialogType = 0;
    },
    showMoreApps() {
      this.dialogFlag = true;
      this.dialogType = 1;
    },
    closeDialog() {
      this.dialogFlag = false;
    },
    jumpApp(item) {
      var newOpen = window.open();
      newOpen.opener = null;
      newOpen.location = item.clientIndexUrl;
    },
  },
};
</script>

<style lang="scss" scoped>
.outer-bg {
  background-size: 100% 100%;
  background-attachment: fixed;
  height: 100%;
}
.portal-outer {
  width: 100%;
  height: 100%;
  overflow-y: auto;

  // height: auto;
  background: #eef2fb;
  display: flex;
  justify-content: center;
  background: require("/assets/image/themesImgs/blueThem.png") no-repeat;
  background-size: 100% 100%;
  background-attachment: fixed;
}

.portal-container {
  min-width: 1572px;
  max-width: 1572px;
  // height: 1500px;
  margin: 20px;
  // background: #fff;
  border: 1px solid transparent;
  position: relative;
  // overflow: hidden;

  .btn-content {
    margin-left: 12px;
    margin-top: 4px;
    display: flex;
    justify-content: right;
    .save-btn {
      height: 40px;
      line-height: 40px;
      width: 80px;
      text-align: center;
      // color: #fff;
      background: #fff;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
      margin-right: 10px;
    }
    .icon-btn {
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url("../../assets/image/save.png") no-repeat;
      background-size: 100%;
    }
  }
  .portal-app {
    position: relative;
    border: 1px solid transparent;
    background: #fff;
    border-radius: 5px;
    // overflow-y: scroll;
    // padding: 20px;
    .app-abs {
      position: absolute;
      // border: 1px solid red;
      top: 0;
      width: 100%;
      width: 640px;
      height: 190px;
      z-index: 2;
    }
    .app-title {
      margin: 10px 0 10px 20px;
    }

    .app-content {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      height: 390px;
      overflow-y: scroll;
      // padding: 0 20px;

      a:focus {
        outline: none;
      }
    }

    .app-block {
      width: 85px;
      // height: 120px;
      // border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      flex-direction: column;
      border-radius: 8px;
      margin: 10px 20px;

      // &:hover {
      //   border: 1px solid #0357CA;
      //   // box-shadow: 0 0.63px 5.04px 0 #ebeef5;
      // }

      .app-icon {
        width: 64px;
        height: 64px;

        // margin-right: 20px;
        // margin-bottom: 18px;
      }

      .app-cliName {
        font-size: 14px;
        color: #333333;
        margin-top: 8px;
        text-align: center;
        display: -webkit-box;
        /* 将 div 视为弹性容器 */
        -webkit-line-clamp: 2;
        /* 显示两行内容 */
        -webkit-box-orient: vertical;
        /* 设置纵向排列内容 */
        overflow: hidden;
        /* 隐藏超出部分 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
        cursor: pointer;
      }
    }
  }
  .self-bg {
    // background: #333 !important;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    // border: 1px solid rgba(0, 0, 0, 0.2);
    // background: rgba(255, 255, 255, 0.3) !important;
  }

  .title-main {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .title-icon {
      margin-right: 10px;
      margin-top: -16px;
    }

    .icon-ywyy {
      display: inline-block;
      // background: url("../../assets/image/common/p_yewuyingyong.png") no-repeat;
      background-size: 100%;
      width: 18px;
      height: 18px;
      min-width: 18px;
    }

    .icon-wddb {
      margin-left: 20px;
      display: inline-block;
      // background: url("../../assets/image/common/p_wodedaiban.png") no-repeat;
      background-size: 100%;
      width: 18px;
      height: 18px;
      min-width: 18px;
      margin-top: 0px;
    }

    .icon-tzgg {
      margin-left: 20px;
      display: inline-block;
      // background: url("../../assets/image/common/p_tongzhigonggao.png") no-repeat;
      background-size: 100%;
      width: 18px;
      height: 18px;
      min-width: 18px;
      margin-top: 0px;
    }

    .title-value {
      font-size: 18px;
      font-weight: 500;
      color: #333333;
    }
  }

  .info-container {
    margin-top: 20px;
    position: relative;
    display: flex;
    width: 100%;
  }
  .more-info {
    position: absolute;
    right: 20px;
    top: 15px;
    cursor: pointer;
  }
  .info-left {
    // width: 50%;
    // border: 1px solid red;
    // width: 566px;
    position: relative;
    background: #fff;
    padding-top: 10px;
    padding-bottom: 17px;
    border-radius: 5px;
    height: 448px;
    .left-abs {
      position: absolute;
      top: 0;
      height: 518px;
      // border: 1px solid red;
      width: 100%;
      z-index: 2;
    }
  }

  .info-right {
    // margin-left: 6px;
    // width: 566px;
    // width: 50%;
    // border: 1px solid green;
    position: relative;
    background: #fff;
    padding-top: 10px;
    padding-bottom: 17px;
    border-radius: 5px;
    height: 448px;
    .right-abs {
      position: absolute;
      top: 0;
      height: 518px;
      // border: 1px solid red;
      width: 100%;
      z-index: 2;
    }
  }
  .index-right {
    // width: 640px;
    width: 770px;
    height: 910px;
    background: #ffffff;
    border-radius: 5px;
  }
  .msg-container {
    padding: 20px;
    position: relative;

    .msg-content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      margin-top: 20px;
      margin-left: 30px;
      position: relative;

      .msg-more {
        position: absolute;
        right: 60px;
        top: 0;

        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .block-content {
    padding-left: 20px;
  }

  .msg-width1 {
    width: 50%;
  }

  .msg-block {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 20px;
    margin-top: 15px;
    display: flex;
    align-items: center;
    position: relative;
    color: #2c3e50;

    // cursor: pointer;
    // &:hover {
    //   // text-decoration: underline;
    //   // background: #f0f2f5;
    //   color: #0357CA;
    //   cursor: pointer;

    //   .msg-dot {
    //     background: #0357CA;
    //   }

    //   .msg-link,
    //   .msg-time {
    //     color: #0357CA;
    //     font-weight: 600;
    //   }
    // }

    .msg-main {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      color: white;
      margin: 5px 0;
    }

    .msg-time {
      margin-left: 18px;
      color: #999999;
      font-size: 14px;
    }

    .msg-dot1 {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #ccc;
      background: transparent;
      margin-right: 10px;
    }

    .msg-dot2 {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #0357ca;
      background: transparent;
      margin-right: 10px;
    }

    .msg-link {
      width: calc(80% - 20px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 95%;
      display: inline-block;
      margin-right: 10px;
      font-size: 16px;
      color: #333333;
      // &:hover {
      //   text-decoration: underline; text-decoration: underline;
      //   cursor: pointer;
      // }
    }

    .msg-new {
      display: inline-block;
      background: #f56c6c;
      color: #fff;
      margin: 0 20px;
      padding: 0 2px;
      font-size: 12px;
    }
  }
}
.tit-content {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 设置最大显示行数 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}
.self-bg2 {
  background: rgba(0, 0, 0, 0.5);
}

.self-iframe {
  width: 100%;
  height: 70vh;
  // height: 87vh;
}

.high-cust {
  // /deep/ .el-dialog__header {
  //   padding-bottom: 0;
  // }
}

.titleLine {
  height: 1px;
  width: 100%;
  background-color: #e2e5ed;
}
.line-self {
  margin-top: -26px;
  height: 2px;
}

.contentLine {
  margin-top: 10px;
  height: 1px;
  width: 100%;
  background-color: #f5f8fa;
}
.my-tabs {
  width: 578px;
  ::v-deep .el-tabs__item {
    font-size: 18px !important;
    // color: $mainBg;$mainBg;
    // color: $mainBg;
  }
}
.my-top {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #fff;
  font-size: 32px;
}
// ::v-deep .el-carousel__container {
//   height: 910px;
// }
</style>

