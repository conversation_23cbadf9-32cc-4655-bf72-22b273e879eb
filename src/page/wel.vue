<template>
  <div>
    <basic-container>
      <div class="banner-text">
        <br />
        <span>
          <el-collapse v-model="activeNames">
            <el-collapse-item name="1">
              <div></div>
            </el-collapse-item>
          </el-collapse>
        </span>
      </div>
    </basic-container>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import { getFetchList } from "@/api/admin/sys/system";
export default {
  name: "wel",
  data() {
    return {
      activeNames: ["1", "2", "3", "4"],
      DATA: [],
      text: "",
      actor: "",
      count: 0,
      isText: false,
      logoObject: {},
    };
  },
  computed: {
    ...mapGetters(["website"]),
    ...mapState({
      logoObj: (state) => state.user.logoObj,
    }),
  },
  mounted() {
    // console.log("mapgetters===");
    setTimeout(() => {
      console.log("mapgetters==3355=", this.logoObj);
      if (this.logoObj && this.logoObj.sysTitle) {
        this.logoObject = Object.assign({}, this.logoObj);
      }
      if (!this.logoObj || !this.logoObj.sysTitle) {
        this.getLogoFn();
      }
    }, 600);
  },
  methods: {
    getLogoFn() {
      // console.log("getListgetListgetList==");

      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        // console.log("logoObj==", response);
        if (response.data.code == 200) {
          this.logoObject = response.data.data || null;
          if (this.logoObject && this.logoObject.sysIcon) {
            let path = this.$getUrlByProcess(this.logoObject.sysIcon);
            this.change_icon(path);

            this.$store.commit("SET_LOGO_OBJ", this.logoObject);
          }
          if (this.logoObject && this.logoObject.sysTitle) {
            document.title = this.logoObject.sysTitle;
          }
        }
      });
    },
    change_icon(iconUrl) {
      const changeFavicon = (link) => {
        let $favicon = document.querySelector('link[rel="icon"]');
        if ($favicon !== null) {
          $favicon.href = link;
        } else {
          $favicon = document.createElement("link");
          $favicon.rel = "icon";
          $favicon.href = link;
          document.head.appendChild($favicon);
        }
      };
      // 设置图标地址
      // let iconUrl = `./icoImg/1.ico`;
      // 动态修改网站图标
      changeFavicon(iconUrl);
    },
    getData() {
      if (this.count < this.DATA.length - 1) {
        this.count++;
      } else {
        this.count = 0;
      }
      this.isText = true;
      this.actor = this.DATA[this.count];
    },
    setData() {
      let num = 0;
      let count = 0;
      let active = false;
      let timeoutstart = 5000;
      let timeoutend = 1000;
      let timespeed = 10;
      setInterval(() => {
        if (this.isText) {
          if (count == this.actor.length) {
            active = true;
          } else {
            active = false;
          }
          if (active) {
            num--;
            this.text = this.actor.substr(0, num);
            if (num == 0) {
              this.isText = false;
              setTimeout(() => {
                count = 0;
                this.getData();
              }, timeoutend);
            }
          } else {
            num++;
            this.text = this.actor.substr(0, num);
            if (num == this.actor.length) {
              this.isText = false;
              setTimeout(() => {
                this.isText = true;
                count = this.actor.length;
              }, timeoutstart);
            }
          }
        }
      }, timespeed);
    },
  },
};
</script>

<style scoped="scoped" lang="scss">
.wel-contailer {
  position: relative;
}

.banner-text {
  position: relative;
  padding: 0 20px;
  font-size: 20px;
  text-align: center;
  color: #333;
}

.banner-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
  display: none;
}

.actor {
  height: 250px;
  overflow: hidden;
  font-size: 18px;
  color: #333;
}

.actor:after {
  content: "";
  width: 3px;
  height: 25px;
  vertical-align: -5px;
  margin-left: 5px;
  background-color: #333;
  display: inline-block;
  animation: blink 0.4s infinite alternate;
}

.typeing:after {
  animation: none;
}

@keyframes blink {
  to {
    opacity: 0;
  }
}
</style>
