/**
 * 全站权限配置
 *
 */
import router from "./router/router";
import store from "@/store";
import { validatenull } from "@/util/validate";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
NProgress.configure({ showSpinner: false });

const whiteList = ['/gateway/home', '/gateway/news', '/gateway/news/detail', '/gateway/publicity','/gateway/subject/more','/gateway/subject', '/gateway/publicity/detail','/gateway/highSearch'];
/**
 * 导航守卫，相关内容可以参考:
 * https://router.vuejs.org/zh/guide/advanced/navigation-guards.html
 */
router.beforeEach((to, from, next) => {
  // 缓冲设置
  if (
    to.meta.keepAlive === true &&
    store.state.tags.tagList.some(ele => {
      return ele.value === to.fullPath;
    })
  ) {
    to.meta.$keepAlive = true;
  } else {
    NProgress.start();
    if (whiteList.indexOf(to.path) !== -1) {
      next()
      return
    }
    if (to.meta.keepAlive === true && validatenull(to.meta.$keepAlive)) {
      to.meta.$keepAlive = true;
    } else {
      to.meta.$keepAlive = false;
    }
  }
  const meta = to.meta || {};
  if (store.getters.access_token) {
    if (to.path === "/login") {
      next({ path: "/" });
    } else {
      // NOTE: 页面刷新时重新请求
      let spath = JSON.parse(sessionStorage.getItem('beforeunload-path'))
      if (spath === to.path) {
        store.dispatch("GetUserInfo").then(() => {
            // 获取菜单
            store.dispatch("GetMenu", { type: true}).then(data => {
              if (data.length === 0) return;
              router.$avueRouter.formatRoutes(data, true);
            });
            next();
          })
          .catch(() => {
            store.dispatch("FedLogOut").then(() => {
              // next({ path: "/login" });
              next({ path:"/sso"});
              // next({ path:"/"});
            });
          });
        // 如果对应的js把每个函数分别export则使用带module的方法
        // store.dispatch("dict/loadDict");
        store.dispatch("loadDict");
        sessionStorage.removeItem('beforeunload-path') // 判断完之后，移除刷新时缓存的路由，以免造成对其他页面的路由影响
      } else {
      
        const value = to.query.src || to.fullPath;
        const label = to.query.name || to.name;

        // 针对外链跳转
        if (value.substring(0,4) === 'http' || value.substring(0,5) === 'https') {
          NProgress.done();
          // 如果是链接，则打开新窗口
          window.open(value, "_blank");
          return;
        }

        if (
          meta.isTab !== false &&
          !validatenull(value) &&
          !validatenull(label)
        ) {
          store.commit("ADD_TAG", {
            label: label,
            value: value,
            params: to.params,
            query: to.query,
            group: router.$avueRouter.group || []
          });
        }
        next();
      }
    }
  } else {
    if (meta.isAuth === false) {
      next();
    } else {
      // next("/login");
      next("/sso");
      // next("/");
      console.log("没有token")
    }
  }
});

router.afterEach(() => {
  NProgress.done();
  const title = store.getters.tag.label;
  router.$avueRouter.setTitle(title);
});
