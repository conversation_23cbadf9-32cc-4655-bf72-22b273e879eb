/**
 * 全站权限配置
 *
 */
import router from "./router/router";
import store from "@/store";
import {validatenull} from "@/util/validate";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import {getParameters} from "@/util/util";

NProgress.configure({showSpinner: false});

const whiteList = ['/gateway/home', '/gateway/news', '/gateway/news/detail', '/gateway/publicity','/gateway/subject/more','/gateway/subject', '/gateway/publicity/detail'];


/**
 * 导航守卫，相关内容可以参考:
 * https://router.vuejs.org/zh/guide/advanced/navigation-guards.html
 */
router.beforeEach((to, from, next) => {
  // 缓冲设置
  if (
    to.meta.keepAlive === true &&
    store.state.tags.tagList.some(ele => {
      return ele.value === to.fullPath;
    })
  ) {
    to.meta.$keepAlive = true;
  } else {
    NProgress.start();
    if (whiteList.indexOf(to.path) !== -1) {
      next()
      return
    }
    if (to.meta.keepAlive === true && validatenull(to.meta.$keepAlive)) {
      to.meta.$keepAlive = true;
    } else {
      to.meta.$keepAlive = false;
    }
  }
  const meta = to.meta || {};
  // console.log("to.from=11==", to);
  // console.log("to.from=22==",from)
  var parameters = getParameters();
  if (store.getters.access_token) {
    if (parameters) {
      // console.log("isLogin--parameters-48")
      if (parameters.ticket && parameters.client_id) {
        loginByTicket()
      } else {
        // console.log("isLogin--parameters--else-52")
        isLogin()
      }
    } else {
      // console.log("isLogin---55")
      isLogin()
    }
  } else if (!store.getters.access_token && parameters) {
    if (parameters.ticket && parameters.client_id) {
      loginByTicket()
    } else {
      // console.log("noLoin1")
      noLogin()
    }
  } else {
    // console.log("noLoin22222")
    noLogin()
  }

  function loginByTicket() {
    let params = {
      ticket: parameters.ticket,
      clientId: parameters.client_id
    }
    store.dispatch('loginByTicket', params).then(async res => {
      await getUserInfo();
      isLogin()
    })
  }
  function noLogin() {
    if (meta.isAuth === false) {
      const redirect = encodeURIComponent(to.fullPath) // 编码 URI，保证参数跳转回去后，可以继续带上
      console.log("没有token isAuth == false重定向地址", redirect)
      // next(`/login?redirect=${redirect}`);
      next();
    } else {
      const redirect = encodeURIComponent(to.fullPath) // 编码 URI，保证参数跳转回去后，可以继续带上
      console.log("没有token 重定向地址", redirect)
      // next(`/login?redirect=${redirect}`);
      next(`/login`);
    }
  }

  function isLogin() {

    if (to.path === "/login" && from.path == '/reset') {
      next();
    }
    else if (to.path === "/login") {
      let token = localStorage.getItem("tiny-access_token");
      if (!token) {
        next();
      } else {
        //
        // console.log("已登录")
        ///main/home
        next({path: '/main/home'});
        // next({path: process.env.VUE_APP_BASE_SERVER_PATH});
      }

    }
    else if (to.path === '/reset') {
      next();
    } else {
      // console.log("isLogin----------else")
      // NOTE: 当用户角色不存在时，会存在无限请求用户信息接口的问题
      let menu = localStorage.getItem("tiny-menu")
      // if (store.getters.roles.length === 0) {
      if (!menu) {
        // console.log("没有角色----------permission.js")
        getUserInfo();//
        // 如果对应的js把每个函数分别export则使用带module的方法
        // store.dispatch("dict/loadDict");
        store.dispatch("loadDict");



        //  2024年4月8日 添加以下代码
        const value = to.query.src || to.fullPath;
        const label = to.query.name || to.name;

        // 针对外链跳转
        if (value.substring(0, 4) === 'http' || value.substring(0, 5) === 'https') {
          NProgress.done();
          // 如果是链接，则打开新窗口
          var newOpen = window.open();
          newOpen.opener = null;
          newOpen.location = value;
          return;
        }
        // console.log("isLogin----------116")
        if (
          meta.isTab !== false &&
          !validatenull(value) &&
          !validatenull(label)
        ) {
          store.commit("ADD_TAG", {
            label: label,
            value: value,
            params: to.params,
            query: to.query,
            group: router.$avueRouter.group || []
          });
        }
        next();
        ///// 2024年4月8日 添加以上代码


      } else {
        // console.log("isLogin----------107", to)
        //如果是刷新的情况下 请求用户新接口
        let spath = JSON.parse(sessionStorage.getItem('beforeunload-path'))
      if (spath === to.path) {
    
        getUserInfo();//
        sessionStorage.removeItem('beforeunload-path') // 判断完之后，移除刷新时缓存的路由，以免造成对其他页面的路由影响
      }


        const value = to.query.src || to.fullPath;
        const label = to.query.name || to.name;

        // 针对外链跳转
        if (value.substring(0, 4) === 'http' || value.substring(0, 5) === 'https') {
          NProgress.done();
          // 如果是链接，则打开新窗口
          var newOpen = window.open();
          newOpen.opener = null;
          newOpen.location = value;
          return;
        }
        // console.log("isLogin----------116")
        if (
          meta.isTab !== false &&
          !validatenull(value) &&
          !validatenull(label)
        ) {
          store.commit("ADD_TAG", {
            label: label,
            value: value,
            params: to.params,
            query: to.query,
            group: router.$avueRouter.group || []
          });
        }
        next();
      }
    }
  }

  /**
   * 获取用户信息
   */
  function getUserInfo(f) {
    store.dispatch("GetUserInfo").then((res) => {
/*
      // 2024年6月25日 90天强制修改密码
      // if (res.sysUser && res.sysUser.firstLogin == 1) {
      if ((res.sysUser && res.sysUser.firstLogin == 1)) {
        // console.log("第一次登录，跳转/reset")
        router.push({path: "/reset"}).catch(() => {
        });

      } else if (res.longTimeNotModifyPwd) {
        router.push({path: "/reset",query:{type:2}}).catch(() => {
        });
       } else {
        // console.log("获取菜单153")
        // 获取菜单
        store.dispatch("GetMenu", {type: true}).then(data => {
          if (data.length === 0) return;
          router.$avueRouter.formatRoutes(data, true);
        });
        next();
      }
*/

      // console.log("获取菜单153")
      // 获取菜单
      store.dispatch("GetMenu", {type: true}).then(data => {
        if (data.length === 0) return;
        router.$avueRouter.formatRoutes(data, true);
      });
      next();

    })
      .catch(() => {
        store.dispatch("FedLogOut").then(() => {
          // next({ path: "/login" });
          const redirect = encodeURIComponent(to.fullPath) // 编码 URI，保证参数跳转回去后，可以继续带上
          console.log("登录失效 重定向地址", redirect)
          // next(`/login?redirect=${redirect}`);
          next(`/login`);
        });
      });
  }
});

router.afterEach(() => {
  NProgress.done();
  // const title = store.getters.tag.label;
  // router.$avueRouter.setTitle(title);
});
