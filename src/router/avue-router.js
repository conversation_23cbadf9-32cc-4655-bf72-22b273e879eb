const RouterPlugin = function() {
  this.$router = null
  this.$store = null
}
RouterPlugin.install = function(router, store) {
  this.$router = router
  this.$store = store
  function isURL(s) {
    return /^http[s]?:\/\/.*/.test(s)
  }
  function objToform(obj) {
    const result = []
    Object.keys(obj).forEach(ele => {
      result.push(`${ele}=${obj[ele]}`)
    })
    return result.join('&')
  }
  this.$router.$avueRouter = {
    // 全局配置
    $website: this.$store.getters.website,
    group: '',
    safe: this,
    // 设置标题
    setTitle: function(title) {
      title = title ? `${title}——${this.$website.title}` : `${this.$website.title}`
      document.title = title
    },
    closeTag: (value) => {
      const tag = value || this.$store.getters.tag
      this.$store.commit('DEL_TAG', tag)
    },
    // 处理路由
    getPath: function(params) {
      const { src } = params
      let result = src || '/'
      // 判断是否为超链接，即以http｜https开头
      if (src.substring(0,4) === 'http' || src.substring(0,5) === 'https') {
        result = `/myiframe/urlPath?${objToform(params)}`
      }
      return result
    },
    // 正则处理路由
    vaildPath: function(list, path) {
      let result = false
      list.forEach(ele => {
        if (new RegExp('^' + ele + '.*', 'g').test(path)) {
          result = true
        }
      })
      return result
    },
    // 设置路由值
    getValue: function(route) {
      let value = ''
      if (route.query.src) {
        value = route.query.src
      } else {
        value = route.path
      }
      return value
    },
    // 动态路由
    formatRoutes: function(aMenu = [], first) {
      const aRouter = []
      const propsConfig = this.$website.menu.props
      const propsDefault = {
        label: propsConfig.label || 'label',
        name:propsConfig.name || 'menuName',
        path: propsConfig.path || 'path',
        component: propsConfig.component || 'component',
        icon: propsConfig.icon || 'icon',
        children: propsConfig.children || 'children',
        meta: propsConfig.meta || 'meta'
      }
      if (aMenu.length === 0) return
      for (let i = 0; i < aMenu.length; i++) {
        const oMenu = aMenu[i]
        const path = (() => {
          if (!oMenu[propsDefault.path]) {
            return
          } else if (first) {
            return oMenu[propsDefault.path].replace('/index', '')
          } else {
            return oMenu[propsDefault.path]
          }
        })()

        //特殊处理组件 这里对应菜单配置的组件地址 达到浏览器路径和组件地址分离的目的
        const component = 'views/' + oMenu.component

        const name = oMenu[propsDefault.name]

        const icon = oMenu[propsDefault.icon]

        const children = oMenu[propsDefault.children]

        const meta = {
          keepAlive: Number(oMenu['keepAlive']) === 1
        }
        const isChild = children && children.length !== 0
        // console.log("路由地址",path)
        // console.log("组件地址",component)
        const oRouter = {
          path: path,
          component(resolve) {
            // 判断是否为首路由
            if (first) {
              // require(['../page/index'], resolve)
              require(['../page/main'], resolve)

              // 判断是否为多层路由
            } else if (isChild && !first) {
              require(['../page/index/layout'], resolve)

              // 判断是否为最终的页面视图
            } else {
              require([`../${component}.vue`], resolve)
            }
          },
          name: name,
          icon: icon,
          meta: meta,
          redirect: (() => {
            if (!isChild && first && !isURL(path)) return `${path}/index`
            else return ''
          })(),
          // 处理是否为一级路由
          children: !isChild ? (() => {
            if (first) {
              if (!isURL(path)) oMenu[propsDefault.path] = `${path}/index`
              return [{
                component(resolve) { require([`../${component}.vue`], resolve) },
                icon: icon,
                name: name,
                meta: meta,
                path: 'index'
              }]
            }
            return []
          })() : (() => {
            return this.formatRoutes(children, false)
          })()
        }
        // console.log("oRouter==lzs-000--",oRouter)
        aRouter.push(oRouter)
      }
      if (first) {
        this.safe.$router.addRoutes(aRouter)
      } else {
        return aRouter
      }
    },

    // 动态路由2
    formatMainRoutes: function(aMenu = [], first) {
      const aRouter = []
      const propsConfig = this.$website.menu.props
      const propsDefault = {
        label: propsConfig.label || 'label',
        path: propsConfig.path || 'path',
        component: propsConfig.component || 'component',
        icon: propsConfig.icon || 'icon',
        children: propsConfig.children || 'children',
        meta: propsConfig.meta || 'meta'
      }
      if (aMenu.length === 0) return
      for (let i = 0; i < aMenu.length; i++) {
        const oMenu = aMenu[i]
        const path = (() => {
          if (!oMenu[propsDefault.path]) {
            return
          } else if (first) {
            // return oMenu[propsDefault.path].replace('/index', '')
            return oMenu[propsDefault.path]
          } else {
            return oMenu[propsDefault.path]
          }
        })()

        //特殊处理组件 这里对应菜单配置的组件地址 达到浏览器路径和组件地址分离的目的
        const component = 'views' + oMenu.component

        const name = oMenu[propsDefault.label]

        const icon = oMenu[propsDefault.icon]

        const children = oMenu[propsDefault.children]

        const meta = {
          keepAlive: Number(oMenu['keepAlive']) === 1
        }
        const isChild = children&& children.length !== 0
        // console.log("路由地址",path)
        // console.log("组件地址",component)
        const oRouter = {
          path: path,
          component(resolve) {
            // 判断是否为首路由
            if (first) {
              require(['../page/main'], resolve)

              // 判断是否为多层路由
            } else if (isChild && !first) {
              require(['../page/index/layout'], resolve)

              // 判断是否为最终的页面视图
            } else {
              require([`../${component}.vue`], resolve)
            }
          },
          name: name,
          icon: icon,
          meta: meta,
          redirect: (() => {
            if (!isChild && first && !isURL(path)) return `${path}`
            else return ''
          })(),
          // 处理是否为一级路由
          children: !isChild ? (() => {
            if (first) {
              if (!isURL(path)) oMenu[propsDefault.path] = `${path}`
              return [{
                component(resolve) { require([`../${component}.vue`], resolve) },
                icon: icon,
                name: name,
                meta: meta,
                path: 'index'
              }]
            }
            return []
          })() : (() => {
            return this.formatMainRoutes(children, false)
          })()
        }
        // console.log("oRouter==lzs---",oRouter)
        aRouter.push(oRouter)
      }
      // console.log("添加了路由0");
      if (first) {
        // console.log("添加了路由1");
        this.safe.$router.addRoutes(aRouter)
        // console.log("添加了路由2==",this.safe.$router);
      } else {
        return aRouter
      }
    },
     // 动态路由3
     formatMainHomeRoutes: function(aMenu = [], first) {
      const aRouter = []
      const propsConfig = this.$website.menu.props
      const propsDefault = {
        label: propsConfig.label || 'label',
        path: propsConfig.path || 'path',
        component: propsConfig.component || 'component',
        icon: propsConfig.icon || 'icon',
        children: propsConfig.children || 'children',
        meta: propsConfig.meta || 'meta'
      }
      if (aMenu.length === 0) return
      for (let i = 0; i < aMenu.length; i++) {
        const oMenu = aMenu[i]
        const path = (() => {
          if (!oMenu[propsDefault.path]) {
            return
          } else if (first) {
            return oMenu[propsDefault.path].replace('/index', '')
          } else {
            return oMenu[propsDefault.path]
          }
        })()

        //特殊处理组件 这里对应菜单配置的组件地址 达到浏览器路径和组件地址分离的目的
        const component = 'views' + oMenu.component

        const name = oMenu[propsDefault.label]

        const icon = oMenu[propsDefault.icon]

        const children = oMenu[propsDefault.children]

        const meta = {
          keepAlive: Number(oMenu['keepAlive']) === 1
        }
        const isChild = children&& children.length !== 0
        // console.log("路由地址",path)
        // console.log("组件地址",component)
        const oRouter = {
          path: path,
          component(resolve) {
            // 判断是否为首路由
            // require(['../page/main/index'], resolve)
            // if (first) {
            //   require(['../page/main'], resolve)

            //   // 判断是否为多层路由
            // } else if (isChild && !first) {
            //   require(['../page/index/layout'], resolve)

            //   // 判断是否为最终的页面视图
            // } else {
              require([`../${component}.vue`], resolve)
            // }
          },
          name: name,
          icon: icon,
          meta: meta,
          // redirect: (() => {
          //   if (!isChild && first && !isURL(path)) return `${path}/index`
          //   else return ''
          // })(),
          // 处理是否为一级路由
          // children: !isChild ? (() => {
          //   if (first) {
          //     if (!isURL(path)) oMenu[propsDefault.path] = `${path}/index`
          //     return [{
          //       component(resolve) { require([`../${component}.vue`], resolve) },
          //       icon: icon,
          //       name: name,
          //       meta: meta,
          //       path: 'index'
          //     }]
          //   }
          //   return []
          // }
          // )() : (() => {
          //   return this.formatMainRoutes(children, false)
          // })()
        }
        // console.log("oRouter=22==",oRouter)
        aRouter.push(oRouter)
       }
      //  console.log("oRouter=33==",aRouter)
     
      if (first) {
        
        this.safe.$router.addRoutes(aRouter)
       
      } else {
        return aRouter
      }
    }
  }
}
export default RouterPlugin
