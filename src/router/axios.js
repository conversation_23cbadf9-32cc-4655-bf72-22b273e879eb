import axios from "axios";
import {serialize} from "@/util/util";
import NProgress from "nprogress"; // progress bar
import errorCode from "@/const/errorCode";
import {Message, MessageBox} from "element-ui";
import "nprogress/nprogress.css";
import qs from "qs";
import store from "@/store"; // progress bar style
axios.defaults.timeout = 30000;
import router from './router'

let showMsg = true;

let url = process.env.VUE_APP_BASE_SERVER_PATH + '/#/login'
axios.defaults.baseURL = process.env.VUE_APP_BASE_API_PRE;
// 返回其他状态吗
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500; // 默认的
};
// 跨域请求，允许保存cookie
axios.defaults.withCredentials = true;
// NProgress Configuration
NProgress.configure({
  showSpinner: false
});

// HTTPrequest拦截
axios.interceptors.request.use(
  config => {

    NProgress.start(); // start progress bar
    const isToken = (config.headers || {}).isToken === false;
    let token = store.getters.access_token;
    // 如果header中已经有 authorization 则不进行token赋值
    let authorization = config.headers["Authorization"];
    if (token && !isToken && !authorization) {
      config.headers["Authorization"] = "Bearer " + token; // token
    }

    // const url = config.url;
    // if (url) {
    //   config.url = `api${url}`;
    // }
    // console.log(" config.url=111==", config)
    // if (config.headers &&config.headers.apiFlag) {
    //   config.url = `${url}`;
    // }
    // console.log(" config.url==222=", config.url)


    // headers中配置serialize为true开启序列化
    if (config.methods === "post" && config.headers.serialize) {
      config.data = serialize(config.data);
      delete config.data.serialize;
    }

    // 处理get 请求的数组 springmvc 可以处理
    if (config.method === "get") {
      config.paramsSerializer = function (params) {
        return qs.stringify(params, {arrayFormat: "repeat", skipNulls: true});
      };
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// HTTPresponse拦截
axios.interceptors.response.use(
  res => {
    NProgress.done();
    const status = Number(res.data.code) || 200;
    const message = res.data.msg || errorCode[status] || errorCode["default"];
    // console.log("status==",status)
    // 后台定义 424 针对令牌过去的特殊响应码
    // console.log("res-axios-",res)
    if (status === 401) {
      // 刷新登录页面，避免多次弹框
      // window.location.reload();
      // MessageBox.confirm("登录状态已过期，请点击重新登录", "系统提示", {
      //   confirmButtonText: "重新登录",
      //   cancelButtonText: "取消",
      //   type: "warning"
      // })
      //   .then(() => {
      //     store.dispatch("LogOut").then(() => {
      //       // 刷新登录页面，避免多次弹框
      //       window.location.reload();
      //     });
      //   }).catch(() => {});
      localStorage.clear();
      return;
    }

    if (status !== 200) {
      Message({
        message: message,
        type: "error"
      });
      return Promise.reject(new Error(message));
    }
    if (res.data.code && res.data.code !== 200) {
      Message({
        message: message,
        type: "error"
      });
      return Promise.reject(new Error(message));
    }

    return res;
  },
  error => {
    NProgress.done();
    return Promise.reject(new Error(error));
  }
);

export default axios;
