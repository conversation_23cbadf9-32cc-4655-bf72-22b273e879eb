import Layout2 from '@/page/index/'
import Layout from '@/page/portalHome/'
import Layout3 from '@/page/main/'
export default [
  {
    path: '/wel',
    component: Layout2,
    // redirect: '/gateway/home',
    children: [{
      path: 'index',
      name: 'wel首页',
      component: () =>
        import( /* webpackChunkName: "views" */ '@/page/wel')
    }]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/gateway/home',
    // redirect: '/login',
    children: [{
      path: 'index',
      name: '首页',
      component: () => import ( /* webpackChunkName: "page" */ '@/page/login/index')
        // import( /* webpackChunkName: "views" */ '@/views/gateway/home')
    }]
  },
  {
    path: '/sso',
    name: '单点登录',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/login/sso'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },
  {
    path: '/sso-callback',
    name: '单点登录回调',
    component: () => import( /* webpackChunkName: "page" */ '@/page/login/ssoCallback'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },

  {
        path: '/login',
        name: '登录页',
        component: () =>
            import ( /* webpackChunkName: "page" */ '@/page/login/index'),
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
},
{
    path: "/reset",
    name: "密码修改页",
    component: () =>
      import(/* webpackChunkName: "page" */ "@/page/login/ResetPassword.vue"),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
    {
      path: '/authorization',
      component: () =>
        import ( /* webpackChunkName: "page" */ '@/views/sso/index'),
      name: 'authorization',
      meta: {
        keepAlive: true,
        isTab: false,
        isAuth: true
      }
    },
    {
        path: '/404',
        component: () =>
            import ( /* webpackChunkName: "page" */ '@/components/error-page/404'),
        name: '404',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: true
        }

    },
    {
        path: '/403',
        component: () =>
            import ( /* webpackChunkName: "page" */ '@/components/error-page/403'),
        name: '403',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/500',
        component: () =>
            import ( /* webpackChunkName: "page" */ '@/components/error-page/500'),
        name: '500',
        meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
        }
    },
    {
        path: '/',
        name: '主页',
        redirect: '/wel'
  }, {
      path: '/main',
      component: () =>
        import( /* webpackChunkName: "page" */ '@/page/main/index'),
      name: 'main',
      children: [
        {
          // path: '/main/index/:id',
          path: '/main/index',
          component: () =>
          import( /* webpackChunkName: "page" */ '@/page/main/TabIndex'),
        name: '首页',
        meta: {
          keepAlive: true,
          isTab: false,
          isAuth: false
          },

        },
        {
          path: '/main/home',
          component: () =>
            import( /* webpackChunkName: "page" */ '@/page/portalHome/HomeIndex'),
          name: '500',
          meta: {
            keepAlive: true,
            isTab: false,
            isAuth: false
          }
        },
        // {
        //   path: '/main/moreInfo',
        //   component: () =>
        //     import( /* webpackChunkName: "page" */ '@/page/noticeCenter/moreInfo'),
        //   name: '500',
        //   meta: {
        //     keepAlive: true,
        //     isTab: false,
        //     isAuth: false
        //   }
        // },
      ]
  },

  {
    path: '/home',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/portalHome/index'),
    name: '500',
    // meta: {
    //   keepAlive: true,
    //   isTab: false,
    //   isAuth: false
    // }
    children: [
      {
        path: '/home/<USER>',
        component: () =>
          import( /* webpackChunkName: "page" */ '@/page/portalHome/HomeIndex'),
        name: '500',
        meta: {
          keepAlive: true,
          isTab: false,
          isAuth: false
        }
      },
      // {
      //   path: '/home/<USER>',
      //   component: () =>
      //     import( /* webpackChunkName: "page" */ '@/page/iframe/index'),
      //   name: 'echart',
      //   meta: {
      //     keepAlive: false,
      //     isTab: false,
      //     isAuth: false
      //   }
      // },
      // {
      //   path: '/home/<USER>',
      //   component: () =>
      //     import( /* webpackChunkName: "page" */ '@/page/echart/index'),
      //   name: 'echart',
      //   meta: {
      //     keepAlive: false,
      //     isTab: false,
      //     isAuth: false
      //   }
      // },
      {
        path: '/home/<USER>',
        component: () =>
          import( /* webpackChunkName: "page" */ '@/page/portalHome/GridIndex'),
        name: 'GridIndex',
        meta: {
          keepAlive: true,
          isTab: false,
          isAuth: false
        }
      }
    ]
  },

  {
    path: '/noticeCenter/index',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/page/noticeCenter/index'),
    name: '',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },
  // {
  //   path: '/noticeCenter/moreInfo',
  //   component: () =>
  //     import( /* webpackChunkName: "page" */ '@/page/noticeCenter/moreInfo'),
  //   name: '',
  //   meta: {
  //     keepAlive: true,
  //     isTab: false,
  //     isAuth: false
  //   }
  // },

  {
    path: '/info/index',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/views/admin/user/info'),
    name: '500',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },
  {
    path: '/gateway',
    component: () => import('@/views/gateway'),
    name: 'gateway',
    redirect: '/gateway/home',
    children: [
      {
        path: '/gateway/home',
        name: '首页',
        component: () => import('@/views/gateway/home')
      },
      {
        path: '/gateway/highSearch',
        name: '高级检索',
        component: () => import('@/views/gateway/highSearch/index.vue')
      },
      {
        path: '/gateway/news',
        meta: {
          keepAlive: true,
          $keepAlive:true,
          isTab: false,
          isAuth: false
        },
        name: '新闻动态',
        component: () => import('@/views/gateway/news'),
      },
      {
        path: '/gateway/news/detail',
        name: '新闻详情',
        component: () => import('@/views/gateway/news/detail.vue')
      },
      {
        path: '/gateway/publicity',
        meta: {
          keepAlive: true,
          $keepAlive:true,
          isTab: false,
          isAuth: false
        },
        name: '公示公告',
        component: () => import('@/views/gateway/publicity')
      },
      {
        path: '/gateway/publicity/detail',
        name: '公告详情',
        component: () => import('@/views/gateway/publicity/detail.vue')
      },
       {
        path: '/gateway/subject',
        name: '专题专栏',
        component: () => import('@/views/gateway/subject')
      },
      {
        path: '/gateway/subject/more',
        name: '专题专栏详情',
        component: () => import('@/views/gateway/subject/more.vue')
      },
      // {
      //   path: '/message',
      //   component: Layout2,

      //   children: [{
      //     path: '/message/newsForm',
      //     name: 'newsForm',
      //     component: () =>
      //       import( /* webpackChunkName: "views" */ '@/views/message/publish/compoents/NewsForm')

      //   }]
      // },
      // {
      //   path: '/gateway/policy',
      //   name: '政策法规',
      //   component: () => import('@/views/gateway/policy')
      // }
    ]
  }
  // 此处影响token失效之后的回调地址
    // {
    //   path: '*',
    //   redirect: '/404',
    // }
]
