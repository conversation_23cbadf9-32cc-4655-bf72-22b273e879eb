import Vue from 'vue'
import VueRouter from 'vue-router'
import PageRouter from './page/'
import ViewsRouter from './views/'
import AvueRouter from './avue-router'
import Store from '../store/'
Vue.use(VueRouter)
let Router = new VueRouter({
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      if (from.meta.keepAlive) {
        from.meta.savedPosition = document.body.scrollTop
      }
      return {
        x: 0,
        y: to.meta.savedPosition || 0
      }
    }
  },
  routes: [].concat([])
})


AvueRouter.install(Router, Store)
Router.$avueRouter.formatRoutes(Store.state.user.menu, true)
Router.addRoutes([...PageRouter, ...ViewsRouter])
// console.log("动态路由router1==",[...PageRouter, ...ViewsRouter])

// 解决路由相同，参数不同的前端bug
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
	return originalPush.call(this, location).catch(err => err);
}



// let arouteObj = {
//   name: "测试",
//   component: "@/page/main/TabIndex",
//   label: "测试1",
//   id: "1001",
//   parentId: "-1",
//   path: "/main/index",
// };
// let arouteObj2 = {
//   name: "测试2",
//   component: "/test/index",
//   label: "测试2",
//   id: "1002",
//   parentId: "-1",
//   path: "/test/index",
// };
// let menu2 = [];
// menu2.push(arouteObj);
// menu2.push(arouteObj2);
// console.log("Store.state.user===",Store.state.user)
// Router.$avueRouter.formatMainRoutes(menu2, true);  //解决页面刷新空白的问题

export default Router
