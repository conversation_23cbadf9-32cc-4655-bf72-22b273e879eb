import Layout from '@/page/index/'
import Layout3 from '@/page/main/'
export default [{
  path: '/wel',
  component: Layout,
  redirect: '/wel/index',
  children: [{
    path: 'index',
    name: '首页',
    component: () =>
      import( /* webpackChunkName: "views" */ '@/page/wel')
  }]
}, {
  path: '/info',
  component: Layout,
  redirect: '/info/index',
  children: [{
    path: 'index',
    name: '个人信息',
    component: () =>
      import( /* webpackChunkName: "page" */ '@/views/admin/user/info'),
  }]
}, {
  path: '/message',
  component: Layout3,

  children: [{
    path: '/message/newsForm',
    name: '内容发布',
    meta: {
      isTab: false
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/message/publish/compoents/NewsForm')

  },
  {
    path: '/message/shenhe/IndexShenhe',
    name: '内容审核',
    meta: {
      isTab: false
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/message/shenhe/IndexShenhe')


  },
  {
    path: '/message/deptPublishs/index',
    name: '详情页面',
    meta: {
      isTab: false
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/message/deptPublishs/index')


  }
  ]
},
]
