/*
 * @Author: liuzhengshuai <EMAIL>
 * @Date: 2024-09-11 09:16:41
 * @LastEditors: liuzhengshuai <EMAIL>
 * @LastEditTime: 2024-11-07 16:42:30
 * @FilePath: \uc-portal\src\store\getters.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getStore } from '@/util/store'
const getters = {
  tag: state => state.tags.tag,
  website: state => state.common.website,
  userInfo: state => state.user.userInfo,
  logoObj: state => state.user.logoObj,
  userRole:state => state.user.userRole,
  theme: state => state.common.theme,
  themeName: state => state.common.themeName,
  isShade: state => state.common.isShade,
  isCollapse: state => state.common.isCollapse,
  keyCollapse: (state, getters) => getters.screen > 1 ? getters.isCollapse : false,
  screen: state => state.common.screen,
  isLock: state => state.common.isLock,
  isFullScreen: state => state.common.isFullScreen,
  lockPasswd: state => state.common.lockPasswd,
  tagList: state => state.tags.tagList,
  tagWel: state => state.tags.tagWel,
  access_token: state => state.user.access_token,
  refresh_token: state => state.user.refresh_token,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  menu: state => state.user.menu,
  menuAll: state => state.user.menuAll,
  // 数据字典
  dict_data: state => state.dict.dictData,
  userObj:state => state.user,
  dept:state => state.user.dept,
  curAreaCode:state => state.common.curAreaCode || getStore({name: "curAreaCode"}),
}
export default getters
