import {getStore, setStore} from '@/util/store'
import {isURL, validatenull} from '@/util/validate'
import {getUserInfo, loginByUsername, loginByMobile, logout, refreshToken,loginByTicket} from '@/api/login'
import {deepClone,encryption} from '@/util/util'
import webiste from '@/const/website'
import {getMenu} from '@/api/admin/sys/menu'

function addPath(ele, first) {
  const menu = webiste.menu
  const propsConfig = menu.props
  const propsDefault = {
    label: propsConfig.label || 'name',
    path: propsConfig.path || 'path',
    icon: propsConfig.icon || 'icon',
    children: propsConfig.children || 'children'
  }
  const icon = ele[propsDefault.icon]
  ele[propsDefault.icon] = validatenull(icon) ? menu.iconDefault : icon
  const isChild = ele[propsDefault.children] && ele[propsDefault.children].length !== 0
  if (!isChild) ele[propsDefault.children] = []
  if (!isChild && first && !isURL(ele[propsDefault.path])) {
    ele[propsDefault.path] = ele[propsDefault.path] + '/index'
  } else {
    ele[propsDefault.children].forEach(child => {
      addPath(child)
    })
  }
}

const user = {
  state: {
    logoObj:{},
    userInfo: {},
    permissions: {},
    roles: [],
    posts: [],
    dept: {},
    userRole:{},
    menu: getStore({
      name: 'menu'
    }) || [],
    menuAll: [],
    mainMenu: getStore({
      name: 'mainMenu'
    }) || [],
    access_token: getStore({
      name: 'access_token'
    }) || '',
    refresh_token: getStore({
      name: 'refresh_token'
    }) || ''
  },
  actions: {
    // 根据用户名登录
    LoginByUsername({commit}, userInfo) {
      // let login = {
      //   username: userInfo.username,
      //   password: userInfo.password,
      //   uuid: userInfo.uuid,
      //   captcha: userInfo.captcha
      // }
      const myUserInfo = {
        username: userInfo.username,
        password: userInfo.password,
      };
      const user = encryption({
        data: myUserInfo,
        key: "user_center_2023",
        param: ["password"],
      });
      // console.log("user==", user);
      const newParams = {
        username: user.username,
        password: user.password,
        captcha: userInfo.captcha,
        uuid: userInfo.uuid,
      };
      return new Promise((resolve, reject) => {
        loginByUsername(newParams).then(response => {
          const data = response.data.data;
          // console.log("data==",data)
          // if (data.firstLogin == 1) {
          //   this.$router.push({ path: "/reset" }).catch(() => { });
          // }
          //debugger
          commit('SET_ACCESS_TOKEN', data.accessToken)
          commit('CLEAR_LOCK')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 根据手机号登录
    LoginByPhone({commit}, userInfo) {
      return new Promise((resolve, reject) => {
        loginByMobile(userInfo.phone, userInfo.code).then(response => {
          const data = response.data.data
          // debugger
          commit('SET_ACCESS_TOKEN', data.accessToken)
          commit('CLEAR_LOCK')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    loginByTicket({commit}, ticketInfo) {
      return new Promise((resolve, reject) => {
        loginByTicket(ticketInfo).then(response => {
          const data = response.data.data
          commit('SET_ACCESS_TOKEN', data.accessToken)
          commit('CLEAR_LOCK')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    GetUserInfo({commit}) {
      return new Promise((resolve, reject) => {
        getUserInfo().then((res) => {
          console.log("获取用户信息==",res)
          const data = res.data.data || {}
          commit('SET_USER_INFO', data.sysUser)
          commit('SET_ROLES', data.roleList || [])
          commit('SET_PERMISSIONS', data.permissions || [])
          commit('SET_POSTS', data.postList || [])
          commit('SET_DEPT', data.dept || [])
          commit('SET_USER_ROLE', data.sysSecureRoleResVO || {})
          resolve(data)
        }).catch((err) => {
          reject()
        })
      })
    },
    // 登出
    LogOut({commit}) {
      let params = {
        "logoutScope": 1
      }
      return new Promise((resolve, reject) => {
        logout(params).then(() => {
          commit('SET_MENU', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_USER_INFO', {})
          commit('SET_ACCESS_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_USER_ROLE', null)
          commit('DEL_ALL_TAG')
          commit('CLEAR_LOCK')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 注销session
    FedLogOut({commit}) {
      return new Promise(resolve => {
        commit('SET_MENU', [])
        commit('SET_PERMISSIONS', [])
        commit('SET_USER_INFO', {})
        commit('SET_ACCESS_TOKEN', '')
        commit('SET_ROLES', [])
        commit('DEL_ALL_TAG')
        commit('CLEAR_LOCK')
        resolve()
      })
    },
    // 获取系统菜单
    GetMenu({commit}, obj) {
      return new Promise(resolve => {
        getMenu(obj.id).then((res) => {
          console.log("user.js---getMenu-",res)
          const data = res.data.data
          let menu = deepClone(data)
          menu.forEach(ele => {
            addPath(ele)
          })
          let type = obj.type
          commit('SET_MENU', {type, menu})
          resolve(menu)
        })
      })
    },
    StoreAccessToken({commit}, accessToken) {
      return new Promise(resolve => {
        commit('SET_ACCESS_TOKEN', accessToken)
        commit('CLEAR_LOCK')
        resolve()
      })
    }

  },
  mutations: {
    SET_ACCESS_TOKEN: (state, access_token) => {
      state.access_token = access_token
      setStore({
        name: 'access_token',
        content: state.access_token,
        // type: 'session'
      })
    },
    SET_LOGO_OBJ: (state, data) => {
      state.logoObj = data
    },
    SET_USER_INFO: (state, userInfo) => {
      state.userInfo = userInfo
    },
    SET_MENU: (state, params = {}) => {
      let {menu, type} = params;
      if (type !== false) state.menu = menu
      setStore({
        name: 'menu',
        content: menu,
        // type: 'session'
      })
    },
    SET_MENU_ALL: (state, menuAll) => {
      state.menuAll = menuAll
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_POSTS: (state, posts) => {
      state.posts = posts
    },
    SET_DEPT: (state, dept) => {
      state.dept = dept
    },
    SET_USER_ROLE: (state, role) => {
      state.userRole = role
    },
    SET_PERMISSIONS: (state, permissions) => {
      const list = {}
      for (let i = 0; i < permissions.length; i++) {
        list[permissions[i]] = true
      }
      state.permissions = list
    }
  }

}
export default user
