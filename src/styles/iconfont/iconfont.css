@font-face {
  font-family: "zs-iconfont"; /* Project id 4379176 */
  src: url('iconfont.woff2?t=1702950272442') format('woff2'),
       url('iconfont.woff?t=1702950272442') format('woff'),
       url('iconfont.ttf?t=1702950272442') format('truetype');
}

.zs-iconfont {
  font-family: "zs-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-suoding:before {
  content: "\e600";
}

.icon-jiesuo:before {
  content: "\e601";
}

