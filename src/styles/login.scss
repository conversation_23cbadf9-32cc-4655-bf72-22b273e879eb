.login-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  // background-color: #fff;
  overflow: hidden;
  // border: 1px solid red;
  background: url("../assets/image/login_bg.png") no-repeat;
  background-size: 100%;
  // &::before{
  //   content:' ';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 100%;
  //   margin-left: -48%;
  //   background-image: url(/svg/login-bg.svg);
  //   background-position: 100%;
  //   background-repeat: no-repeat;
  //   background-size: auto 100%;
  // }

  .title-content{
    position: absolute;
    left: 100px;
    top:6vh;
    display: flex;
    align-items: center;
    .logo-img{
     margin-right: 4px;
     width: 60px;
     height: 60px;
    }
    .lg-img{
      width: 60px;
     height: 60px;
    }
  }
  .title-word{
    font-size: 36px;
    text-align: center;
    font-weight: 500;
    color: #0357CA;
  }
}
.login-weaper {
  margin: 0 auto;
  height: 737px;
  width:100%;
  // border: 1px solid red;
  .new-title{
    font-size: 36px;
    text-align: center;
    font-weight: 500;
    color: #0357CA;
    margin-top: 16px;
    // margin-left: 44%;
  }
  .login-mains{
    width: 570px;
    height: 737px;
    margin-left: 57%;
    margin-top: 15vh;
    // margin-top: -40px;
    // border: 1px solid red;
    // display: flex;
    // margin: 0 auto;
    // .m-left{
    //   width: 600px;
    //   height: 700px;
    //   background:rgba(3, 87, 202, 0.7);
    //   box-shadow: 4px 4px 10px 0px rgba(0,0,0,0.1);

    //   .le-title{
    //     font-size: 42px;
    //     font-weight: 500;
    //     color: #FFFFFF;
    //     text-shadow: 4px 4px 10px rgba(0,0,0,0.1);
    //     margin-top: 250px;
    //     width: 100%;
    //     text-align: center;
    //   }
    //   .le-fu{
    //     font-size: 30px;
    //     font-weight: 500;
    //     color: #FFFFFF;
    //     text-shadow: 4px 4px 10px rgba(0,0,0,0.1);
    //     margin-top: 20px;
    //     width: 100%;
    //     text-align: center;
    //   }
    // }
    .m-top{
      .le-title{
        font-size: 44px;
        text-align: center;
        font-weight: 500;
        color: #0357CA;
      }
      .le-fu{
        font-size: 36px;
        margin-top: 10px;
        font-weight: 500;
        color: #0357CA;
        text-align: center;
      }
    }
    .m-right{
      width: 560px;
      height: 480px;
      background: #FFFFFF;
      box-shadow: 0px 2px 10px 0px rgba(161,190,245,0.7);
      border-radius: 10px;
      margin-top: 40px;
      border: 1px solid transparent;

      .ri-tips{
        // font-size: 30px;
        // font-weight: 500;
        // color: #333333;
        // text-shadow: 4px 4px 10px rgba(0,0,0,0.1);
        // // padding-left: 26px;
        // padding-left: 62px;
        // // margin-top: 120px;

        width: 419px;
        height: 48px;
         line-height: 48px;
        border-bottom: 1px solid #E2E5ED;
        margin: 60px auto 40px;
        // display: flex;
        // justify-content: space-between;

      }
      .dl-title{
        font-size: 22px;
        color: #666666;
        cursor: pointer;
        margin-left: 16px;

      }
      .dl-active{
        font-weight: 600;
        color: #0357CA;
        border-bottom: 3px solid #0357CA;
      }
    }
  }
}

.first-row {
  display: flex;
  justify-content: space-between;
  height: 40px;
  line-height: 40px;

  .item-one {
    height: 65px;
    font-size: 18px;
    font-weight: 500;
    padding: 4px;
    line-height: 65px;

    .text-active {
      font-weight: 700;
    }
  }

  .item-two {
    height: 65px;
    font-size: 18px;
    font-weight: 500;
    padding: 4px;
    margin-left: 30px;
    line-height: 65px;

    .text-active {
      font-weight: 700;
    }
  }

  .row-title {
    min-width: 112px;
    height: 40px;
    line-height: 40px;
    opacity: 1;
    color: #22242c;
    font-weight: 500;
    font-size: 18px;
  }

}
.row-line {
  width: 81%;
  padding-left: 5px;
  border-bottom: 1px solid #dcdfe6;
  height: 1px;
  padding-top: 24px;
  margin-left: 55px;
}

.login-left,
.login-border {
  position: relative;
  min-height: 500px;
  align-items: center;
  display: flex;
}
.login-left {
  justify-content: center;
  flex-direction: column;
  color: #fff;
  float: left;
  width: 50%;
  position: relative;
  box-sizing: border-box;
}
.login-left .img {
  width: 200px;
}
.login-time {
  position: absolute;
  left: 25px;
  top: 25px;
  width: 100%;
  color: #fff;
  font-weight: 200;
  opacity: 0.9;
  font-size: 18px;
  overflow: hidden;
  font-weight: bold;
}
.login-left .title {
  margin-top: 60px;
  text-align: center;
  color: #fff;
  font-weight: 300;
  letter-spacing: 2px;
  font-size: 28px;
  font-weight: bold;
}

.login-border {
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  color: #fff;
  width: 50%;
  float: left;
  box-sizing: border-box;
}
.login-main {
  margin: 0 auto;
  padding: 30px 50px;
  width: 70%;
  box-shadow: -4px 5px 10px rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
}
.login-main > h3 {
  margin-bottom: 20px;
}
.login-title {
  color: #000;
  margin-bottom: 50px;
  font-weight: bold;
  font-size: 28px;
  text-align: center;
  letter-spacing: 4px;
}
.login-menu {
  margin-top: 40px;
  width: 100%;
  text-align: center;
  a {
    color: #999;
    font-size: 12px;
    margin: 0px 8px;
  }
}
.login-submit {
  width: 100%;
  height: 48px;
  // line-height: 48px;
  // border: 1px solid #409EFF;
  background: none;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 300;
  // color: #409EFF;
  cursor: pointer;
  margin-top: 30px;
  font-family: "neo";
  transition: 0.25s;
  background: #0357CA;
  box-shadow: 4px 4px 10px 0px rgba(0,0,0,0.1);
  border-radius: 5px;
  color:#fff;
  &:hover{
    background: #3272CE;
  }
}
.login-form {
  // margin: 10px 0;
    margin: 42px auto;
    // width: 80%;
    width: 420px;
  i {
    color: #333;
  }
  .el-form-item__content {
    width: 100%;
  }
  .el-form-item {
    margin-bottom: 12px;
  }
  .el-input {
    input {
      padding:20px 0 20px 40px;
      text-indent: 5px;
      border-radius: 0;
    }
    .el-input__suffix,.el-input__prefix{
      display: flex;
      align-items: center;
      text-align: center;
    }
    .el-input__prefix{
      margin-left: 8px;
    }
  }
}
.login-code {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 0 0 0 10px;
}
.login-code-img {
  //margin-top: 2px;
  width: 130px;
  height: 44px;
  background-color: #fdfdfd;
  border: 1px solid #f0f0f0;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 5px;
  line-height: 38px;
  text-indent: 5px;
  text-align: center;
}
