::v-deep .switch-style {
    //开关小盒子
    .el-switch__core {
      width: 52px !important;
      height: 25px !important;
      border-radius: 60px;
      background: #ffffff; //圆球在左时的开关背景色 只改变内部色，不改变边框色
      //圆球在右时的开关背景色在html结构中active-color="#67c23a"设置即可
    }
    //开关内区域
    .el-switch__label {
      position: absolute;
      padding-top: 1px;
      display: none;
      color: #fff;
      font-size: 10px !important;
      //圆球在左的 文字设置
      &--left {
        color: #fff !important;
        // color: #606266 !important; 
        z-index: 1;
        right: 1px;
      }
      //圆球在右的 文字设置
      &--right {
        color: #ffffff !important;
        z-index: 1;
        left: 1px;
        font-size: 10px;
      }
      &.is-active {
        display: block;
      }
    }
    //圆球靠左的 圆球样式
    .el-switch__core:after {
      top: 15%;
      left: 4%;
      background-color: #d2d2d2;
    }
    //圆球靠右的 圆球样式
    &.el-switch.is-checked .el-switch__core::after {
      top: 15%;
      left: 97%;
      margin-left: -1.063rem;
      background-color: #fff;
    }
  }