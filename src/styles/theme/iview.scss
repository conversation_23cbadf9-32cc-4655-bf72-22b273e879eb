.theme-iview {
  $mainBg: #3478FF;

  $mainColor: #0456CD;

  .icon-sjcs-theme {
    background: url("./../assets/image/common/skyThem/p_sjcs.png") no-repeat;
  }

  .icon-ywyy-theme {
    background: url("./../assets/image/common/skyThem/p_yewuyingyong.png") no-repeat;
  }

  .icon-wddb-theme {
    background: url("./../assets/image/common/skyThem/p_wodedaiban.png") no-repeat;
  }

  .icon-tzgg-theme {
    background: url("./../assets/image/common/skyThem/p_tongzhigonggao.png") no-repeat;
  }

  .mainBG {
    background: linear-gradient(90deg, #0357CA 0%, #4398FF 100%);
  }

  .mainBGImg {
    background: url("./../assets/image/themesImgs/blueThem.png") no-repeat;
  }

  .infoBGColor {
    background-color: #eef2fb;
  }

  .infoBtnBGColor {
    background-color: #F5F9FA;
  }

  .mainBGColor {
    background-color: $mainBg;
  }

  .my-top-menu0 {
    .el-menu-item.is-active {
      background-color: $mainBg !important;
    }
  }

  .my-top-menu {
    .el-menu-item.is-active {
      background-color: $mainBg !important;
    }
  }

  .mainBGrid {
    background-color: rgba(29, 146, 207, 0.3);
  }

  .mainColor {
    // color: #409EFF;
    color: $mainColor;
  }

  .avue-logo {
    // background: #1D92CF;
    // background: #2942b2;
    box-shadow: none;
    text-align: center;

    .avue-logo_title {
      font-size: 16px;
      font-weight: 700;
      transition: all .5s;
    }
  }

  .avue-tags {
    padding: 3px 5px 5px 0;
    background: #f0f0f0;
    box-shadow: inset 0 0 3px 2px hsla(0, 0%, 39.2%, .1);



    .el-tabs__item {
      padding: 0 15px !important;
      position: relative;
      height: 32px !important;
      line-height: 32px !important;
      border: 1px solid #e8eaec !important;
      // color: #515a6e !important;
      background: #fff !important;
      border-radius: 3px;

      &:before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 10px;
        border-radius: 50%;
        background: #e8eaec;
      }
    }
  }

  .avue-sidebar {

    background: #0357CA;

    .el-menu-item {
      &.is-active {
        background: linear-gradient(90deg, #0357CA 0%, #4398FF 100%);

        &:before {
          display: none;
        }

        i,
        span {
          color: white;
        }
      }
    }

    .el-submenu {
      .el-menu-item {
        &.is-active {
          background: linear-gradient(90deg, #0357CA 0%, #4398FF 100%);

          &:before {
            display: none;
          }

          i,
          span {
            color: white;
          }
        }
      }
    }
  }

  .tabsTheme {
    .el-tabs__item.is-active {
      color: $mainColor;
      font-weight: 600;
    }

    .el-tabs__item {
      min-width: 100px;
      font-size: 18px !important;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
      text-align: center;
    }

    .el-tabs__active-bar {
      background-color: $mainColor;
      height: 4px;
    }
  }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: $mainColor;
      color: #FFF;
  }


}

.theme-iview1 {
  $mainBg: #50C49C;
  $mainColor: #399777;


  .icon-sjcs-theme {
    background: url("./../assets/image/common/skyThem/p_sjcs.png") no-repeat;
  }

  .icon-ywyy-theme {
    background: url("./../assets/image/common/greenTheme/p_yewuyingyong.png") no-repeat;
  }

  .icon-wddb-theme {
    background: url("./../assets/image/common/greenTheme/p_wodedaiban.png") no-repeat;
  }

  .icon-tzgg-theme {
    background: url("./../assets/image/common/greenTheme/p_tongzhigonggao.png") no-repeat;
  }

  .mainBG {
    background: linear-gradient(90deg, #399777 0%, #4FC49C 100%);
  }

  .mainBGImg {
    background: url("./../assets/image/themesImgs/greenThem.png") no-repeat;
  }

  .infoBGColor {
    background-color: #F6FFFB;
  }

  .infoBtnBGColor {
    background-color: #F5FAF7;
  }

  .mainBGColor {
    background-color: $mainBg;
  }

  .my-top-menu1 {
    .el-menu-item.is-active {
      background-color: $mainBg !important;
    }
  }

  .mainBGrid {
    background-color: rgba(57, 151, 119, 0.3);

  }

  .mainColor {
    color: $mainColor;
  }

  .avue-logo {
    background: $mainBg;
    box-shadow: none;
    text-align: center;

    .avue-logo_title {
      font-size: 16px;
      font-weight: 700;
      transition: all .5s;
    }
  }

  .avue-tags {
    padding: 3px 5px 5px 0;
    background: #f0f0f0;
    box-shadow: inset 0 0 3px 2px hsla(0, 0%, 39.2%, .1);



    .el-tabs__item {
      padding: 0 15px !important;
      position: relative;
      height: 32px !important;
      line-height: 32px !important;
      border: 1px solid #e8eaec !important;
      // color: #515a6e !important;
      background: #fff !important;
      border-radius: 3px;

      &:before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 10px;
        border-radius: 50%;
        background: #e8eaec;
      }
    }
  }

  .avue-sidebar {

    background: #399777;

    .el-menu-item {
      &.is-active {
        background: linear-gradient(90deg, #399777 0%, #4FC49C 100%);

        &:before {
          display: none;
        }

        i,
        span {
          color: white;
        }
      }
    }

    .el-submenu {
      .el-menu-item {
        &.is-active {
          background: linear-gradient(90deg, #399777 0%, #4FC49C 100%);

          &:before {
            display: none;
          }

          i,
          span {
            color: white;
          }
        }
      }
    }
  }

  .tabsTheme {
    .el-tabs__item.is-active {
      color: $mainColor;
      font-weight: 600;
    }

    .el-tabs__item {
      min-width: 100px;
      font-size: 18px !important;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
      text-align: center;
    }

    .el-tabs__active-bar {
      background-color: $mainColor;
      height: 4px;
    }
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $mainColor;
    color: #FFF;
}
}

.theme-iview2 {
  $mainBg: #CA1B1B;
  $mainColor: #B00208;


  .icon-sjcs-theme {
    background: url("./../assets/image/common/skyThem/p_sjcs.png") no-repeat;
  }

  .icon-ywyy-theme {
    background: url("./../assets/image/common/redTheme/p_yewuyingyong.png") no-repeat;
  }

  .icon-wddb-theme {
    background: url("./../assets/image/common/redTheme/p_wodedaiban.png") no-repeat;
  }

  .icon-tzgg-theme {
    background: url("./../assets/image/common/redTheme/p_tongzhigonggao.png") no-repeat;
  }

  .mainBG {
    background: linear-gradient(90deg, #8B191D 0%, #B00208 100%);
  }

  .mainBGImg {
    background: url("./../assets/image/themesImgs/redThem.png") no-repeat;
  }

  .infoBGColor {
    background-color: #FBEEEE;
  }

  .infoBtnBGColor {
    background-color: #FAF5F5;
  }

  .mainBGColor {
    background-color: $mainBg;
  }

  .my-top-menu2 {
    .el-menu-item.is-active {
      background-color: $mainBg !important;
    }
  }

  .mainBGrid {
    background-color: rgba(176, 2, 8, 0.3);
  }

  .mainColor {
    color: $mainColor;
  }

  .avue-logo {
    background: $mainBg;
    box-shadow: none;
    text-align: center;

    .avue-logo_title {
      font-size: 16px;
      font-weight: 700;
      transition: all .5s;
    }
  }

  .avue-tags {
    padding: 3px 5px 5px 0;
    background: #f0f0f0;
    box-shadow: inset 0 0 3px 2px hsla(0, 0%, 39.2%, .1);



    .el-tabs__item {
      padding: 0 15px !important;
      position: relative;
      height: 32px !important;
      line-height: 32px !important;
      border: 1px solid #e8eaec !important;
      // color: #515a6e !important;
      background: #fff !important;
      border-radius: 3px;

      &:before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 10px;
        border-radius: 50%;
        background: #e8eaec;
      }
    }
  }

  .avue-sidebar {

    // background: #c86a6d;
    background: #8B191D;

    .el-menu-item {
      &.is-active {
        background: linear-gradient(90deg, #8B191D 0%, #B00208 100%);

        &:before {
          display: none;
        }

        i,
        span {
          color: white;
        }
      }
    }

    .el-submenu {
      .el-menu-item {
        &.is-active {
          background: linear-gradient(90deg, #8B191D 0%, #B00208 100%);

          &:before {
            display: none;
          }

          i,
          span {
            color: white;
          }
        }
      }
    }
  }

  .tabsTheme {
    .el-tabs__item.is-active {
      color: $mainColor;
      font-weight: 600;
    }

    .el-tabs__item {
      min-width: 100px;
      font-size: 18px !important;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
      text-align: center;
    }

    .el-tabs__active-bar {
      background-color: $mainColor;
      height: 4px;
    }
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $mainColor;
    color: #FFF;
}
}

.theme-iview3 {
  $mainBg: #41C5F4;
  $mainColor: #1D92CF;


  .icon-sjcs-theme {
    background: url("./../assets/image/common/skyThem/p_sjcs.png") no-repeat;
  }

  .icon-ywyy-theme {
    background: url("./../assets/image/common/blueTheme/p_yewuyingyong.png") no-repeat;
  }

  .icon-wddb-theme {
    background: url("./../assets/image/common/blueTheme/p_wodedaiban.png") no-repeat;
  }

  .icon-tzgg-theme {
    background: url("./../assets/image/common/blueTheme/p_tongzhigonggao.png") no-repeat;
  }

  .mainBG {
    background: linear-gradient(90deg, #1D92CF 0%, #3AB5E0 100%);
  }

  .mainBGImg {
    background: url("./../assets/image/themesImgs/skyThem.png") no-repeat;
  }

  .infoBGColor {
    background-color: #EEF7FB;
  }

  .infoBtnBGColor {
    background-color: #F5F9FA;
  }

  .mainBGColor {
    background-color: $mainBg;
  }

  .my-top-menu3 {
    .el-menu-item.is-active {
      background-color: $mainBg !important;
    }
  }

  .mainBGrid {
    background-color: rgba(29, 146, 207, 0.3);
  }

  .mainColor {
    color: $mainColor;
  }

  .avue-logo {
    background: $mainBg;
    box-shadow: none;
    text-align: center;

    .avue-logo_title {
      font-size: 16px;
      font-weight: 700;
      transition: all .5s;
    }
  }

  .avue-tags {
    padding: 3px 5px 5px 0;
    background: #f0f0f0;
    box-shadow: inset 0 0 3px 2px hsla(0, 0%, 39.2%, .1);



    .el-tabs__item {
      padding: 0 15px !important;
      position: relative;
      height: 32px !important;
      line-height: 32px !important;
      border: 1px solid #e8eaec !important;
      // color: #515a6e !important;
      background: #fff !important;
      border-radius: 3px;

      &:before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 10px;
        border-radius: 50%;
        background: #e8eaec;
      }
    }
  }

  .avue-sidebar {

    background: #1D92CF;

    .el-menu-item {
      &.is-active {
        background: linear-gradient(90deg, #1D92CF 0%, #3AB5E0 100%);

        &:before {
          display: none;
        }

        i,
        span {
          color: white;
        }
      }
    }

    .el-submenu {
      .el-menu-item {
        &.is-active {
          background: linear-gradient(90deg, #1D92CF 0%, #3AB5E0 100%);

          &:before {
            display: none;
          }

          i,
          span {
            color: white;
          }
        }
      }
    }
  }

  .tabsTheme {
    .el-tabs__item.is-active {
      color: $mainColor;
      font-weight: 600;
    }

    .el-tabs__item {
      min-width: 100px;
      font-size: 18px !important;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
      text-align: center;
    }

    .el-tabs__active-bar {
      background-color: $mainColor;
      height: 4px;
    }
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $mainColor;
    color: #FFF;
}
}

.theme-iview4 {
  $mainBg: #EEB300;
  $mainColor: #F0A001;


  .icon-sjcs-theme {
    background: url("./../assets/image/common/skyThem/p_sjcs.png") no-repeat;
  }

  .icon-ywyy-theme {
    background: url("./../assets/image/common/yelTheme/p_yewuyingyong.png") no-repeat;
  }

  .icon-wddb-theme {
    background: url("./../assets/image/common/yelTheme/p_wodedaiban.png") no-repeat;
  }

  .icon-tzgg-theme {
    background: url("./../assets/image/common/yelTheme/p_tongzhigonggao.png") no-repeat;
  }

  .mainBG {
    background: linear-gradient(90deg, #CC7E00 0%, #F0A001 100%);
  }

  .mainBGImg {
    background: url("./../assets/image/themesImgs/yelThem.png") no-repeat;
  }

  .infoBGColor {
    background-color: #FFF6E8;
  }

  .infoBtnBGColor {
    background-color: #FEFDF3;
  }

  .mainBGColor {
    background-color: $mainBg;
  }

  .my-top-menu4 {
    .el-menu-item.is-active {
      background-color: $mainBg !important;
    }
  }

  .mainBGrid {
    background-color: rgba(240, 160, 1, 0.3);
  }

  .mainColor {
    color: $mainColor;
  }

  .avue-logo {
    background: $mainBg;
    box-shadow: none;
    text-align: center;

    .avue-logo_title {
      font-size: 16px;
      font-weight: 700;
      transition: all .5s;
    }
  }

  .avue-tags {
    padding: 3px 5px 5px 0;
    background: #f0f0f0;
    box-shadow: inset 0 0 3px 2px hsla(0, 0%, 39.2%, .1);



    .el-tabs__item {
      padding: 0 15px !important;
      position: relative;
      height: 32px !important;
      line-height: 32px !important;
      border: 1px solid #e8eaec !important;
      // color: #515a6e !important;
      background: #fff !important;
      border-radius: 3px;

      &:before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 10px;
        border-radius: 50%;
        background: #e8eaec;
      }
    }
  }

  .avue-sidebar {

    background: #CC7E00;

    .el-menu-item {
      &.is-active {
        background: linear-gradient(90deg, #CC7E00 0%, #F0A001 100%);

        &:before {
          display: none;
        }

        i,
        span {
          color: white;
        }
      }
    }

    .el-submenu {
      .el-menu-item {
        &.is-active {
          background: linear-gradient(90deg, #CC7E00 0%, #F0A001 100%);

          &:before {
            display: none;
          }

          i,
          span {
            color: white;
          }
        }
      }
    }
  }

  .tabsTheme {
    .el-tabs__item.is-active {
      color: $mainColor;
      font-weight: 600;
    }

    .el-tabs__item {
      min-width: 100px;
      font-size: 18px !important;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 25px;
      text-align: center;
    }

    .el-tabs__active-bar {
      background-color: $mainColor;
      height: 4px;
    }
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $mainColor;
    color: #FFF;
}
}