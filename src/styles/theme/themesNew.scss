$white: #ffffff;
$black: #000000;
:root[data-theme="dark"] {
  --text-color: $white;
  --bg-color: $black;
}
:root[data-theme="light"] {
  --text-color:$black;
  --bg-color: $white;
}
.textColor {
  color: var(--text-color);
}
.bg {
  background-color: var(--bg-color);
}

// // 1.主题定义
// $theme-default: (
//   background:linear-gradient(90deg, #0357CA 0%, #4398FF 100%),
// );
// $theme-red: (
//   font-color:linear-gradient(90deg, #8B191D 0%, #B00208 100%)
// );
// $theme-green: (
//   font-color:linear-gradient(90deg, #399777 0%, #4FC49C 100%),
// );
// $theme-skyblue: (
//   background:linear-gradient(90deg, #1D92CF 0%, #3AB5E0 100%),
// );
// $theme-yellow: (
//   background:linear-gradient(90deg, #CC7E00 0%, #F0A001 100%),
// );
// //  2.将定义好到主题添加到map中
// $themes:(
//   default:$theme-default,
//   red:$theme-red,
//   green:$theme-green,
//   skyblue:$theme-skyblue,
//   yellow:$theme-yellow
// );

// // 4. 调用混合指令themify() ,定义规则,此处到规则会替换@content
// @mixin mytheme{
//   @include themify() {
//     // color: themed("font-color");
//     // font-size: themed("font-size");
//     .test-theme {
//       background: themed("background");
//     }
//   }
// };

// // 3.定义混合指令, 切换主题,并将主题中到所有规则添加到theme-map中
// @mixin themify() {
//   @each $theme-name, $map in $themes {
//     // & 表示父级元素
//     // !global 表示覆盖原来的
//     .theme-#{$theme-name} & {
//       $theme-map: () !global;
//       // 循环合并键值对
//       @each $key, $value in $map {
//         $theme-map: map-merge($theme-map, ($key: $value)) !global;
//       }
//       // 表示包含 下面函数 themed()
//       @content;
//       $theme-map: null !global;
//     }
//   }
// }

// // 通过key获取map中到值
// @function themed($key) {
//   @return map-get($theme-map, $key);
// }