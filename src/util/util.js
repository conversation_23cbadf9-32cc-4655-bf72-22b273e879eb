import { validatenull } from "./validate";
import request from "@/router/axios";
import * as CryptoJS from "crypto-js";

// 数据合并
export function mergeRecursive(source, target) {
  for (let index in target) {
    try {
      if (target[index].constructor === Object) {
        source[index] = mergeRecursive(source[index], target[index]);
      } else {
        source[index] = target[index];
      }
    } catch (e) {
      source[index] = target[index];
    }
  }
  return source;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return "";
  }
  let actions = [];
  Object.keys(datas).some((key) => {
    if (datas[key].value === "" + value) {
      actions.push(datas[key].label);
      return true;
    }
  });
  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join("");
}

// 表单序列化
export const serialize = (data) => {
  let list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join("&");
};
export const getObjType = (obj) => {
  var toString = Object.prototype.toString;
  var map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Date]": "date",
    "[object RegExp]": "regExp",
    "[object Undefined]": "undefined",
    "[object Null]": "null",
    "[object Object]": "object",
  };
  if (obj instanceof Element) {
    return "element";
  }
  return map[toString.call(obj)];
};
/**
 * 对象深拷贝
 */
export const deepClone = (data) => {
  var type = getObjType(data);
  var obj;
  if (type === "array") {
    obj = [];
  } else if (type === "object") {
    obj = {};
  } else {
    // 不再具有下一层次
    return data;
  }
  if (type === "array") {
    for (var i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]));
    }
  } else if (type === "object") {
    for (var key in data) {
      obj[key] = deepClone(data[key]);
    }
  }
  return obj;
};
/**
 * 判断路由是否相等
 */
export const diff = (obj1, obj2) => {
  delete obj1.close;
  var o1 = obj1 instanceof Object;
  var o2 = obj2 instanceof Object;
  if (!o1 || !o2) {
    /*  判断不是对象  */
    return obj1 === obj2;
  }

  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false;
    // Object.keys() 返回一个由对象的自身可枚举属性(key值)组成的数组,例如：数组返回下表：let arr = ["a", "b", "c"];console.log(Object.keys(arr))->0,1,2;
  }

  for (var attr in obj1) {
    var t1 = obj1[attr] instanceof Object;
    var t2 = obj2[attr] instanceof Object;
    if (t1 && t2) {
      return diff(obj1[attr], obj2[attr]);
    } else if (obj1[attr] !== obj2[attr]) {
      return false;
    }
  }
  return true;
};
/**
 * 设置灰度模式
 */
export const toggleGrayMode = (status) => {
  if (status) {
    document.body.className = document.body.className + " grayMode";
  } else {
    document.body.className = document.body.className.replace(" grayMode", "");
  }
};
/**
 * 设置主题
 */
export const setTheme = (name) => {
  document.body.className = name;
};

/**
 *加密处理
 */
export const encryption = (params) => {
  let { data, type, param, key } = params;
  const result = JSON.parse(JSON.stringify(data));
  if (type === "Base64") {
    param.forEach((ele) => {
      result[ele] = btoa(result[ele]);
    });
  } else {
    param.forEach((ele) => {
      var data = result[ele];
      key = CryptoJS.enc.Latin1.parse(key);
      var iv = key;
      // 加密
      var encrypted = CryptoJS.AES.encrypt(data, key, {
        iv: iv,
        mode: CryptoJS.mode.CFB,
        padding: CryptoJS.pad.NoPadding,
      });
      result[ele] = encrypted.toString();
    });
  }
  return result;
};
/**
 * 浏览器判断是否全屏
 */
export const fullscreenToggel = () => {
  if (fullscreenEnable()) {
    exitFullScreen();
  } else {
    reqFullScreen();
  }
};
/**
 * esc监听全屏
 */
export const listenfullscreen = (callback) => {
  function listen() {
    callback();
  }

  document.addEventListener("fullscreenchange", function () {
    listen();
  });
  document.addEventListener("mozfullscreenchange", function () {
    listen();
  });
  document.addEventListener("webkitfullscreenchange", function () {
    listen();
  });
  document.addEventListener("msfullscreenchange", function () {
    listen();
  });
};
/**
 * 浏览器判断是否全屏
 */
export const fullscreenEnable = () => {
  return (
    document.isFullScreen ||
    document.mozIsFullScreen ||
    document.webkitIsFullScreen
  );
};

/**
 * 浏览器全屏
 */
export const reqFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.documentElement.requestFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.documentElement.webkitRequestFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.documentElement.mozRequestFullScreen();
  }
};
/**
 * 浏览器退出全屏
 */
export const exitFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.exitFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.webkitCancelFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.mozCancelFullScreen();
  }
};
/**
 * 递归寻找子类的父类
 */

export const findParent = (menu, id) => {
  for (let i = 0; i < menu.length; i++) {
    if (menu[i].children.length != 0) {
      for (let j = 0; j < menu[i].children.length; j++) {
        if (menu[i].children[j].id == id) {
          return menu[i];
        } else {
          if (menu[i].children[j].children.length != 0) {
            return findParent(menu[i].children[j].children, id);
          }
        }
      }
    }
  }
};

/**
 * 动态插入css
 */

export const loadStyle = (url) => {
  const link = document.createElement("link");
  link.type = "text/css";
  link.rel = "stylesheet";
  link.href = url;
  const head = document.getElementsByTagName("head")[0];
  head.appendChild(link);
};
/**
 * 判断路由是否相等
 */
export const isObjectValueEqual = (a, b) => {
  let result = true;
  Object.keys(a).forEach((ele) => {
    const type = typeof a[ele];
    if (type === "string" && a[ele] !== b[ele]) result = false;
    else if (
      type === "object" &&
      JSON.stringify(a[ele]) !== JSON.stringify(b[ele])
    )
      result = false;
  });
  return result;
};
/**
 * 根据字典的value显示label
 */
export const findByvalue = (dic, value) => {
  let result = "";
  if (validatenull(dic)) return value;
  if (
    typeof value === "string" ||
    typeof value === "number" ||
    typeof value === "boolean"
  ) {
    let index = 0;
    index = findArray(dic, value);
    if (index != -1) {
      result = dic[index].label;
    } else {
      result = value;
    }
  } else if (value instanceof Array) {
    result = [];
    let index = 0;
    value.forEach((ele) => {
      index = findArray(dic, ele);
      if (index != -1) {
        result.push(dic[index].label);
      } else {
        result.push(value);
      }
    });
    result = result.toString();
  }
  return result;
};
/**
 * 根据字典的value查找对应的index
 */
export const findArray = (dic, value) => {
  for (let i = 0; i < dic.length; i++) {
    if (dic[i].value == value) {
      return i;
    }
  }
  return -1;
};
/**
 * 生成随机len位数字
 */
export const randomLenNum = (len, date) => {
  let random = "";
  random = Math.ceil(Math.random() * 100000000000000)
    .toString()
    .substr(0, len || 4);
  if (date) random = random + Date.now();
  return random;
};

/**
 *
 * @param url 目标下载接口
 * @param query 查询参数
 * @param fileName 文件名称
 * @returns {*}
 */
export function downBlobFile(url, query, fileName) {
  return request({
    url: url,
    method: "get",
    responseType: "blob",
    params: query,
  }).then((response) => {
    // 处理返回的文件流
    const blob = response?.data;
    if (!(blob instanceof Blob)) {
      throw new Error("Invalid response data type");
    }
    if (blob && blob.size === 0) {
      this.$message.error("内容为空，无法下载");
      return;
    }
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    window.setTimeout(function () {
      URL.revokeObjectURL(blob);
      document.body.removeChild(link);
    }, 0);
  });
}

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === "string") {
      time = time
        .replace(new RegExp(/-/gm), "/")
        .replace("T", " ")
        .replace(new RegExp(/\.[\d]{3}/gm), "");
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

/**
 * 获取uuid
 */
export function getUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    return (c === "x" ? (Math.random() * 16) | 0 : "r&0x3" | "0x8").toString(
      16
    );
  });
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

export function getParameters(param) {
  let currentUrl = window.location.href; //获取当前链接
  let arr = currentUrl.split("?"); //分割域名和参数界限
  if (arr.length > 1) {
    // let res = new Map();

    let res = new Object();
    arr = arr[1].split("&"); //分割参数
    for (let i = 0; i < arr.length; i++) {
      let tem = arr[i].split("="); //分割参数名和参数内容
      let key = tem[0];
      res[key] = tem[1];
    }
    return res;
  } else {
    return null;
  }
}

// js保留两位小数
export function formatDecimal(num, decimal) {
  num = num.toString();
  let index = num.indexOf(".");
  if (index !== -1) {
    num = num.substring(0, decimal + index + 1);
  } else {
    num = num.substring(0);
  }
  return parseFloat(parseFloat(num).toFixed(decimal));
}

/**
 * 不丢失精度相加
 * @param {number} num1
 * @param {number} num2
 */
export const exactAdd = (num1 = 0, num2 = 0) => {
  return Decimal.add(+num1, +num2).toNumber();
};

/**
 * 不丢失精度相减
 * @param {number} num1
 * @param {number} num2
 */
export const exactSub = (num1 = 0, num2 = 0) => {
  return Decimal.sub(+num1, +num2).toNumber();
};

/**
 * 不丢失精度相乘
 * @param {number} num1
 * @param {number} num2
 */
export const exactMul = (num1 = 0, num2 = 0) => {
  return Decimal.mul(+num1, +num2).toNumber();
};

/**
 * 不丢失精度相除
 * @param {number} num1
 * @param {number} num2
 */
export const exactDiv = (num1 = 0, num2 = 0) => {
  return Decimal.div(+num1, +num2).toNumber();
};

/**
 * 数字小数点格式化
 * @param {string, number} digit 待处理数字
 * @param {number} minDecimals 最小小数点位数
 * @param {number} maxDecimals 最大小数点位数
 * @param {string, number} defaultVal 参数非法默认返回值
 */
export const decimalsFormat = (
  digit,
  minDecimals = 2,
  maxDecimals = 2,
  defaultVal = 0
) => {
  // 非数值或者数值小于0.000000001
  if (isNaN(digit) || digit === "") return defaultVal;
  if (
    !Number(digit) ||
    (digit > 0 && digit * 100000000 < 1) ||
    (digit < 0 && digit * 100000000 > -1)
  ) {
    return 0;
  }
  const flVal = parseFloat(digit);
  // 数值为整数
  if (Number.isInteger(flVal)) return flVal.toFixed(minDecimals);
  // 如果数值为科学计数法，则小数位已经达到8位
  if (/e/.test(flVal)) {
    return numberRound(flVal, maxDecimals);
  }
  const digitArr = (flVal + "").split(".");
  const decimalPoint = digitArr[1];

  if (decimalPoint.length < minDecimals) {
    return numberRound(flVal, minDecimals);
  } else if (decimalPoint.length > maxDecimals) {
    // eslint-disable-next-line prefer-const
    let [newDigit, point] = (numberRound(flVal, maxDecimals) + "").split(".");
    // 保留8位后，出现多余的填充0，进一步处理
    point = point.replace(/(0+)$/, "").padEnd(minDecimals, "0");
    return `${newDigit}.${point}`;
  } else {
    return flVal;
  }
};

/**
 * 根据树结构查找对象
 * @param {Array} tree 要遍历的树
 * @param {ziduan} ziduan 对象中比较的哪个字段
 * @param {id} id 查找的节点id
 * @returns 查找到的路径 path为空数组，表示没找到
 */
export function findItem(tree, ziduan, id) {
  // 是否找到,flag 要定义在外面，循环里面每次循环会重新赋值，是不对的
  let flag = false;
  // 查找的路径，当前塞的id,也可以往里面塞对象
  let path = [];
  // 定义一个查找函数。递归使用
  /**
   * 内部真正查找的函数，递归使用
   * @param {Array} tree 要遍历的树

   * @param {id} id 查找的节点id
   * @param {Number} level 上级树级别，默认传的0(看不到的最外层不循环)，每循环一层执行加1，
   */
  const find = (tree, id, level = 0) => {
    level += 1; // level 进行加1操作
    // 循环操作
    for (let index = 0; index < tree.length; index++) {
      // 如果找到了，不再循环，flag 外层定义，会终止所有的循环
      if (flag) break;
      // 当前枝干
      const branch = tree[index];
      // 操作前，根据当前的level 清空一下，不需要的level,只保留当前level的父级以上的level
      path = path.slice(0, level - 1);
      // 将当前id加到路径里面，也可以添加对象
      path.push(branch[ziduan]);
      // eslint-disable-next-line
      if (branch[ziduan] == id) {
        // 隐式转换，利用一下
        flag = true;
        break; // 找到了，就返回
      }
      // 没找到，如果有children的话，就继续遍历children
      const { children = [] } = branch;
      if (children && children.length) {
        find(children, id, level);
      }
    }
  };
  find(tree, id);
  if (!flag) path = [];
  return path;
}

/**
 * 1、根据节点id,获取其所有父节点
 * @param {*} list 完整的树结构数组
 * @param {*} id 当前点击的id
 * @param {*} name 需要对比的id 的属性节点
 * @param {*} child 子节点名称
 * @returns
 */
export function getAllParentArr(list, id, name, child) {
  for (let i in list) {
    if (list[i][name] == id) {
      return [list[i]];
    }
    if (list[i][child]) {
      let node = this.getAllParentArr(list[i][child], id, name, child);
      if (!!node) {
        return node.concat(list[i]);
      }
    }
  }
}
/**
 * 根据树结构查找对象
 * @param {Array} tree 要遍历的树
 * @param {ziduan} ziduan 对象中比较的哪个字段
 * @param {id} id 查找的节点id
 * @returns 查找到的路径 path为空数组，表示没找到
 */
export function findItemObject(tree, ziduan, id) {
  // 是否找到,flag 要定义在外面，循环里面每次循环会重新赋值，是不对的
  let flag = false;
  // 查找的路径，当前塞的id,也可以往里面塞对象
  let path = [];
  // 定义一个查找函数。递归使用
  /**
   * 内部真正查找的函数，递归使用
   * @param {Array} tree 要遍历的树

   * @param {id} id 查找的节点id
   * @param {Number} level 上级树级别，默认传的0(看不到的最外层不循环)，每循环一层执行加1，
   */
  const find = (tree, id, level = 0) => {
    level += 1; // level 进行加1操作
    // 循环操作
    for (let index = 0; index < tree.length; index++) {
      // 如果找到了，不再循环，flag 外层定义，会终止所有的循环
      if (flag) break;
      // 当前枝干
      const branch = tree[index];
      // 操作前，根据当前的level 清空一下，不需要的level,只保留当前level的父级以上的level
      path = path.slice(0, level - 1);
      // 将当前id加到路径里面，也可以添加对象
      path.push(branch);
      // eslint-disable-next-line
      if (branch[ziduan] == id) {
        // 隐式转换，利用一下
        flag = true;
        break; // 找到了，就返回
      }
      // 没找到，如果有children的话，就继续遍历children
      const { children = [] } = branch;
      if (children.length) {
        find(children, id, level);
      }
    }
  };
  find(tree, id);
  if (!flag) path = [];
  return path;
}
