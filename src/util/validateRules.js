/*
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2023-05-19 09:16:37
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2023-06-06 09:40:41
 */
/**
 * @desc  [自定义校验规则]
 * @example
 *  import { validateRule } from "@/utils/validateRules";
 *  rules: [
 *     { validator: validateRule.emailValue, trigger: 'blur'}
 *  ]
 */
import { validateIdCard } from "./validate.js";
export const rule = {
  /**
   * 校验 请输入中文、英文、数字包括下划线
   * 名称校验
   */
  validatorNameCn(rule, value, callback) {
    let acount = /^[\u4E00-\u9FA5A-Za-z0-9_]+$/;
    if (value && !acount.test(value)) {
      callback(new Error("请输入中文、英文、数字包括下划线"));
    } else {
      callback();
    }
  },
  /**
   * 校验 请输入中文、英文、数字包括下划线
   * 名称校验
   */
  validatorKey(rule, value, callback) {
    let acount = /^[A-Z_]+$/;
    if (value && !acount.test(value)) {
      callback(new Error("请输入大写英文、下划线"));
    } else {
      callback();
    }
  },

  /**
   * 校验首尾空白字符的正则表达式
   *
   */
  checkSpace(rule, value, callback) {
    let longrg = /[^\s]+$/;
    if (!longrg.test(value)) {
      callback(new Error("请输入非空格信息"));
    } else {
      callback();
    }
  },
  /** 密码可为空的校验，如果为空不校验，如果不为则 校验密码 ：密码必须包含大写字母、小写字母、数字、特殊字符且12-20位*/
  //  !/^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,20}$/.test(value)
  validatePasswordCanEmpty2(rule, value, callback) {
    if (value) {
      if (
        !/^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,20}$/.test(
          value
        )
      ) {
        callback(
          new Error("密码必须包含大写字母、小写字母、数字、特殊字符且8-20位")
        );
      } else {
        callback();
      }
    } else {
      callback();
    }
  },
  validatePasswordCanEmpty(rule, value, callback) {
    if (value) {
      if (!/^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,}$/.test(value)) {
        callback(new Error("密码必须包含大写字母、小写字母、数字、不低于8位"));
      } else {
        callback();
      }
    } else {
      callback();
    }
  },

  /** 校验密码 ：密码必须包含大写字母、小写字母、数字、特殊字符且8-20位*/
  validatePassword(rule, value, callback) {
    // if (!/^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,}$/.test(value)) {
    //   callback(new Error("密码必须包含大写字母、小写字母、数字,不低于8位"));
    // } else {
    //   callback();
    // }
    if (
      !/^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/.test(
        value
      )
    ) {
      callback(
        new Error("密码必须包含大写字母、小写字母、数字、特殊字符且不低于8位")
      );
    } else {
      callback();
    }
  },
  
  //验证身份证号
  validateFormCardIdCanEmpty(rule, value, callback) {
    console.log(value);
    if (!value) {
      callback();
    } else {
      if (validateIdCard(value)) {
        callback();
      } else {
        callback("请输入正确的身份证号");
      }
    }
  },
};
