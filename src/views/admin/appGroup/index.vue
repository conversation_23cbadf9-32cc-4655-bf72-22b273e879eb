<template>
  <div class="container">
    <div class="dept-content">
      <div class="dept-left">
        <div class="appGroupTitle" @click="showAddGroup">
          应用分类
          <img
            src="@/assets/image/appGroup/addAppGroup.png"
            class="appGroupTitleImg"
            alt=""
          />
        </div>
        <div style="width: 100%; height: 1px; background: #e3e4e6"></div>
        <div
          class="app-group-list"
          v-for="(item, index) in groupList"
          :style="
            selectedGroupId === item.id
              ? 'backgroundColor:#F4F6F7'
              : 'backgroundColor:white'
          "
          :key="index"
          @click="clickGroupPage(item)"
        >
          {{ item.groupName }}
          <el-dropdown>
            <img
              src="@/assets/image/appGroup/editGroup.png"
              class="edit-group"
              alt=""
            />
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                @click.native="showEditGroup(item)"
                style="border-top: none"
                divided
                >编辑
              </el-dropdown-item>
              <el-dropdown-item @click.native="delGroup(item)" divided
                >删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div class="dept-right">
        <div class="app-content">
          <div class="add-app-block" @click="showAddAppToGroup">
            <img class="app-icon" src="@/assets/image/appGroup/addApp.png" />
            <div class="app-cliName">{{ "添加应用" }}</div>
          </div>
          <div
            v-for="item in appsList"
            :key="item.id"
            class="app-block"
            :title="item.name"
          >
            <img class="app-icon" :src="picUrl + item.clientLogo" />
            <div class="app-cliName">{{ item.name }}</div>
            <div class="app-del">
              <el-dropdown>
                <img
                  class="del-app-icon"
                  src="@/assets/image/appGroup/editGroup.png"
                />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    @click.native="showEditApp(item)"
                    style="border-top: none"
                    divided
                    >应用排序
                  </el-dropdown-item>
                  <el-dropdown-item
                    @click.native="removeAppInGroup(item)"
                    divided
                    >移出分组
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
        <div class="pagination-content" style="">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="pageSize"
            layout="total, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
          <!-- <el-pagination background layout="prev, pager, next" :total=total>
          </el-pagination> -->
        </div>
      </div>
    </div>
    <el-dialog
      :title="form.title"
      :visible.sync="visible"
      :close-on-click-modal="false"
    >
      <el-form
        label-width="80px"
        :model="form"
        v-if="form.title === '新增分类' || form.title === '编辑分类'"
      >
        <el-form-item label="分类名称">
          <el-input v-model="form.groupName"></el-input>
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input v-model="form.groupDesc" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input-number
            v-model="form.sortNum"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
      </el-form>
      <el-form
        label-width="80px"
        :model="form"
        v-else-if="form.title === '新增应用'"
      >
        <el-form-item label="应用名称">
          <el-select
            v-model="form.appsIdsNotInGroupList"
            placeholder="请选择"
            multiple
            clearable
            filterable
          >
            <el-option
              v-for="dict in appsNotInGroupList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-form
        label-width="80px"
        :model="form"
        v-else-if="form.title === '编辑应用'"
      >
        <el-form-item label="应用名称">
          {{ form.item.name }}
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sortNum"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConformAction">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  fetchAllGroup,
  addGroup,
  delGroup,
  putGroup,
  fetchAppsByGroup,
  fetchAllAppNotInGroup,
  addAppToGroup,
  editAppToGroup,
  removeAppInGroup,
} from "@/api/admin/sys/appGroup";
import { handleDown } from "@/api/gen/gen";

export default {
  name: "table_area",
  components: {},
  data() {
    return {
      groupList: [],
      appsList: [],
      appsNotInGroupList: [],
      selectedGroupId: "",
      visible: false,
      form: {
        title: "",
        groupName: "",
        groupDesc: "",
        sortNum: 0,
        appsIdsNotInGroupList: "",
      },
      total: 0,
      currentPage: 1, // 当前页面
      pageSize: 15, // 当前页面显示条数
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    picUrl() {
      return window.location.protocol + "//" + window.location.host;
    },
  },
  created() {
    this.loadGroups();
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.clickGroup();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.clickGroup();
    },
    loadGroups() {
      fetchAllGroup().then((res) => {
        if (res.data.code === 200) {
          this.groupList = res.data.data;
          this.selectedGroupId = this.groupList[0].id;
          this.clickGroup();
        }
      });
    },

    handleConformAction() {
      if (this.form.title === "新增分类") {
        this.addGroup();
      } else if (this.form.title === "编辑分类") {
        this.editGroup();
      } else if (this.form.title === "新增应用") {
        this.addAppToGroup();
      } else if (this.form.title === "编辑应用") {
        this.changeAppInGroup();
      }
    },

    showAddGroup() {
      this.form.title = "新增分类";
      this.visible = true;
    },

    addGroup() {
      let param = {
        groupDesc: this.form.groupDesc,
        groupName: this.form.groupName,
        sortNum: this.form.sortNum,
        status: 0,
      };
      addGroup(param).then((res) => {
        if (res.data.code === 200) {
          this.loadGroups();
        }
        this.visible = false;
        this.resetForm();
      });
    },

    resetForm() {
      this.form = {
        title: "",
        groupName: "",
        groupDesc: "",
        sortNum: 0,
        appsIdsNotInGroupList: "",
      };
    },
    clickGroupPage(item) {
      this.currentPage = 1;
      this.clickGroup(item);
    },
    clickGroup(item) {
      if (!item) {
        item = { id: this.selectedGroupId };
      }
      this.selectedGroupId = item.id;
      let param = {
        current: this.currentPage,
        size: this.pageSize,
        groupId: item.id,
      };
      fetchAppsByGroup(param).then((res) => {
        if (res.data.code === 200) {
          this.appsList = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },

    showEditGroup(item) {
      this.form.title = "编辑分类";
      this.form.groupDesc = item.groupDesc;
      this.form.groupName = item.groupName;
      this.form.sortNum = item.sortNum;
      this.form.item = item;
      this.visible = true;
    },
    showEditApp(item) {
      this.form.title = "编辑应用";
      this.form.groupDesc = item.groupDesc;
      this.form.groupName = item.groupName;
      this.form.sortNum = item.sortNum;
      this.form.item = item;
      this.visible = true;
    },

    editGroup() {
      let param = {
        groupDesc: this.form.groupDesc,
        groupName: this.form.groupName,
        sortNum: this.form.sortNum,
        status: 0,
        id: this.form.item.id,
      };
      putGroup(param).then((res) => {
        if (res.data.code === 200) {
          this.loadGroups();
          this.visible = false;
          this.resetForm();
        }
      });
    },

    delGroup(item) {
      this.$confirm("你确定要删除该分类吗?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delGroup(item.id).then((res) => {
            if (res.data.code === 200) {
              this.loadGroups();
            }
          });
        })
        .catch(() => {});
    },

    showAddAppToGroup() {
      fetchAllAppNotInGroup().then((res) => {
        if (res.data.code === 200) {
          this.appsNotInGroupList = res.data.data;
          this.form.title = "新增应用";
          this.visible = true;
        }
      });
    },
    addAppToGroup() {
      let listArr = [];
      for (let i in this.form.appsIdsNotInGroupList) {
        listArr.push({
          appId: this.form.appsIdsNotInGroupList[i],
          sortNum: 1,
        });
      }
      let param = {
        appInfoList: listArr,
        groupId: this.selectedGroupId,
      };
      addAppToGroup(param).then((res) => {
        this.visible = false;
        this.resetForm();
        this.clickGroup();
      });
    },

    changeAppInGroup() {
      let param = {
        groupId: this.selectedGroupId,
        sortNum: this.form.sortNum,
        appId: this.form.item.id,
      };
      editAppToGroup(param).then((res) => {
        if (res.data.code === 200) {
          this.loadGroups();
        }
        this.visible = false;
        this.resetForm();
      });
    },

    removeAppInGroup(item) {
      this.$confirm("确定将应用移出当前分组?", "移出应用", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = {
            appIds: [item.id],
            groupId: this.selectedGroupId,
          };
          removeAppInGroup(param).then((res) => {
            if (res.data.code === 200) {
              this.clickGroup();
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>
<style scoped lang="scss">
.container {
  height: calc(100vh - 124px);
  background-color: white;

  .dept-content {
    width: 100%;
    height: 100%;
    display: flex;
    border: 1px solid transparent;

    .dept-left {
      width: 254px;
      min-width: 254px;
      margin: 0 0 5px 20px;
      font-size: 14px;
      height: 100%;
      overflow: auto;

      border-right: 1px solid #e3e4e6;

      .appGroupTitle {
        width: 100%;
        padding: 15px 10px;
        display: flex;
        font-size: 16px;
        font-weight: 500;
        justify-content: space-between;
        align-items: center;

        .appGroupTitleImg {
          width: 18px;
          width: 18px;
          cursor: pointer;
        }
      }

      .app-group-list {
        margin-left: 10px;
        // width: calc(90vh -40px);
        padding: 15px 10px 15px 25px;
        display: flex;
        font-size: 16px;
        font-weight: 500;
        justify-content: space-between;
        align-items: center;

        .edit-group {
          width: 18px;
          width: 18px;
          cursor: pointer;
        }
      }
    }

    .dept-right {
      // display: flex;
      height: 100%;
      // flex-direction: column;
      width: 100%;
      // overflow: auto;

      .app-content {
        // flex: 1;
        overflow: auto;

        position: relative;
        display: flex;
        flex-wrap: wrap;

        // padding: 0 20px;
        // background-color: yellow;
        a:focus {
          outline: none;
        }

        .app-del {
          float: right;
          position: absolute;
          top: 10px;
          right: 10px;
          cursor: pointer;
        }

        .app-block {
          width: 266px;
          height: 150px;
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;

          margin: 10px 15px;
          position: relative;
          padding: 10px 30px;
          border-width: 1px;
          border-style: solid;
          border-color: #e3e4e6;

          // &:hover {
          //   border: 1px solid #0357CA;
          //   // box-shadow: 0 0.63px 5.04px 0 #ebeef5;
          // }

          .app-icon {
            width: 56px;
            height: 56px;

            // margin-right: 20px;
            // margin-bottom: 18px;
          }

          .del-app-icon {
            width: 24px;
            height: 24px;
          }

          .app-cliName {
            font-size: 14px;
            color: #333333;
            margin-top: 8px;
            text-align: center;
            display: -webkit-box;
            /* 将 div 视为弹性容器 */
            -webkit-line-clamp: 2;
            /* 显示两行内容 */
            -webkit-box-orient: vertical;
            /* 设置纵向排列内容 */
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            cursor: pointer;
          }
        }

        .add-app-block {
          width: 266px;
          height: 150px;
          background-color: #f4f6f7;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          margin: 10px 15px;
          border-width: 1px;
          border-style: solid;
          border-color: #e3e4e6;

          // &:hover {
          //   border: 1px solid #0357CA;
          //   // box-shadow: 0 0.63px 5.04px 0 #ebeef5;
          // }

          .app-icon {
            width: 56px;
            height: 56px;

            // margin-right: 20px;
            // margin-bottom: 18px;
          }

          .app-cliName {
            font-size: 14px;
            color: #333333;
            margin-top: 8px;
            text-align: center;
            display: -webkit-box;
            /* 将 div 视为弹性容器 */
            -webkit-line-clamp: 2;
            /* 显示两行内容 */
            -webkit-box-orient: vertical;
            /* 设置纵向排列内容 */
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
            cursor: pointer;
          }
        }
      }
    }
  }
}
.pagination-content {
  height: 60px;
  width: 100%;
  display: flex;
  // justify-content: flex-end;
  align-items: center;
  margin-left: 32px;
}
</style>
