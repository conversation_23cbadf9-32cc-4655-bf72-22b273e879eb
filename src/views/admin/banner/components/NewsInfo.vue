<template>
  <div class="wrapper">
    <div class="main">
      <div class="main-left">
        <div class="floor-nav">
          <div class="floor-nav-head">
            <div class="option">
              <span class="option-title">栏目列表</span>
              <!-- <el-button @click="handleOpenCategory">栏目管理</el-button> -->
            </div>
            <div class="banner">
              <avue-input placeholder="请输入内容查询" style="width: 100%" v-model="filterText"></avue-input>
            </div>
          </div>
          <div class="floor-nav-body tree-container">
            <el-tree :props="defaultProps" :data="treeData" :default-expanded-keys="groupExpandedKeys"
              @node-click="handleSelectCategory" :filter-node-method="filterNode" node-key="id"
              :expand-on-click-node="false" ref="tree">
            </el-tree>
          </div>
        </div>
      </div>
      <div class="main-right">
        <avue-crud ref="crud" :page.sync="page" :data="tableData" :permission="permissionList"
          :table-loading="tableLoading" :option="tableOption" @on-load="getList" @search-change="searchChange"
          @refresh-change="refreshChange" @size-change="sizeChange" @search-reset="searchReset"
          @current-change="currentChange" @row-update="handleUpdate" @row-save="handleSave" @row-del="rowDel"
          v-model="form" :search.sync="searchForm">
          <template slot-scope="{ disabled, size }" slot="pubStatusSearch">
            <el-select v-model="searchForm.pubStatus" placeholder="请选择">
              <el-option v-for="item in pubStatusOptions" :key="item.value" :label="item.label" :value="item.value"
                :disabled="item.disabled">
              </el-option>
            </el-select>

            <!-- <el-slider  :disabled="disabled" :size="size" v-model="search.age"></el-slider> -->
          </template>
          <template slot-scope="scope" slot="menu">
            <el-button icon="el-icon-tickets" class="none-border" type="text" size="small"
              @click="handleSubimt(scope.row)" style="color: #327fff">选择</el-button>
          </template>
        </avue-crud>
      </div>
    </div>
    <category-manage v-if="groupDialogFlag" @handleAddCategroy="handleAddCategroy"
      @closeDialogFn="closeDialogFn"></category-manage>
    <cust-dialog title="发布公告" v-if="visible" :dialog-status.sync="visible" width="90%">
      <avue-form :defaults.sync="defaults" v-model="publishForm" :option="publishOption" class="spform">
        <template slot="content">
          <rich-text ref="skindeditor" :id="'s_kind_editor'" :content.sync="publishForm.content" @input="setSinfo" />
        </template>
        <template slot="videoUrl">
          <el-upload class="video-uploader" ref="videoRef" :multiple="false" :limit="1" :action="uploadVideoPath"
            :before-upload="beforeVideoUpload" :on-remove="handleVideoRemove" :on-success="handleVideoSuccess"
            :on-progress="progressVideo" :file-list="videoList" :show-file-list="false" :headers="headerObj">
            <div slot="trigger" class="upload-btn" v-if="videoPercent == 0">
              <i class="el-icon-plus"></i>
              <div>只能上传mp4格式文件</div>
            </div>
          </el-upload>

          <!-- <video
            style="width: 400px; height: 300px"
            src="http://.154.55:9000/hall-file/2022/03/04/32okfxy9qzehij8yzn08/shipin.mp4"
            controls="controls"
          ></video> -->
          <div class="vid-show" v-if="videoPercent > 0">
            <div class="upload-pic" v-if="videoPercent > 0 && videoPercent < 100">
              <div class="lic-press">
                <el-progress type="circle" :percentage="videoPercent"></el-progress>
              </div>
            </div>
            <div class="" v-else-if="videoObj.videoPath">
              <span class="my-icon-close" @click.stop="handleRemoveVideo">
                <i class="el-icon-close"></i>
              </span>
              <video-player style="width: 400px; height: 225px" class="vjs-custom-skin"
                :options="playerOptions"></video-player>
            </div>
          </div>
        </template>
      </avue-form>
      <div slot="bottom-footer">
        <el-button type="success" v-if="publishForm.contentType === '2'"
          @click="handlePreview(publishForm)">预览</el-button>
        <el-button type="primary" @click="handleAdd">确定</el-button>
        <el-button @click="handleCloseAdd">取消</el-button>
      </div>
    </cust-dialog>
    <cust-dialog title="编辑" v-if="editVisible" :dialog-status.sync="editVisible" width="90%">
      <avue-form :defaults.sync="editDefaults" v-model="editPublishForm" :option="publishOption" class="spform">
        <template slot="content">
          <rich-text ref="skindeditor" :id="'s_kind_editor'" :content.sync="editPublishForm.content"
            @input="setEidtSinfo" />
        </template>
        <template slot="videoUrl">
          <el-upload class="video-uploader" ref="videoRef" :multiple="false" :limit="1" :action="uploadVideoPath"
            :before-upload="beforeVideoUpload" :on-remove="handleVideoRemove" :on-success="handleVideoSuccess"
            :on-progress="progressVideo" :file-list="videoList" :show-file-list="false" :headers="headerObj">
            <div slot="trigger" class="upload-btn" v-if="videoPercent == 0">
              <i class="el-icon-plus"></i>
              <div>只能上传mp4格式文件</div>
            </div>
          </el-upload>

          <!-- <video
            style="width: 400px; height: 300px"
            src="http://7.154.55:9000/hall-file/2022/03/04/32okfxy9qzehij8yzn08/shipin.mp4"
            controls="controls"
          ></video> -->
          <div class="vid-show" v-if="videoPercent > 0">
            <div class="upload-pic" v-if="videoPercent > 0 && videoPercent < 100">
              <div class="lic-press">
                <el-progress type="circle" :percentage="videoPercent"></el-progress>
              </div>
            </div>
            <div class="" v-else-if="videoObj.videoPath">
              <span class="my-icon-close" @click.stop="handleRemoveVideo">
                <i class="el-icon-close"></i>
              </span>
              <video-player style="width: 400px; height: 225px" class="vjs-custom-skin"
                :options="playerOptions"></video-player>
            </div>
          </div>
        </template>
      </avue-form>
      <div slot="bottom-footer">
        <el-button type="success" v-if="editPublishForm.contentType === '2'"
          @click="handlePreview(editPublishForm)">预览</el-button>
        <el-button type="primary" @click="handleEdit">确定</el-button>
        <el-button @click="handleCloseEdit">取消</el-button>
      </div>
    </cust-dialog>
    <cust-dialog class="mydialogsp" title="预览" v-if="previewVisible" :dialog-status.sync="previewVisible" width="70%">
      <div class="previewbox">
        <div class="previewbox-container">
          <!--  <div class="previewbox-head">
            当前:
          </div> -->
          <div class="previewbox-content" v-html="previewboxContent"></div>
        </div>
      </div>
      <div slot="bottom-footer">
        <el-button @click="handleClosePreview">取消</el-button>
      </div>
    </cust-dialog>

    <cust-dialog class="mydialog" title="选择视频素材" v-if="videoListFlag" :dialog-status.sync="videoListFlag" width="70%">
      <div class="video-list"></div>
    </cust-dialog>
    <dialog-form :addType="addType" v-if="picFlag" @closeDialog="closeDialog" :contentTypeDialog="contentTypeDialog"
      :treeData="treeData" :formObj="formObj" :editForm="editForm"></dialog-form>
    <view-dialog :treeData="treeData" v-if="viewDialogFn" @backDialog="backDialog" :viewObj="viewObj"></view-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  getObj,
  getArticleObj,
  addObj,
  putObj,
  delObj,
  deleteObj,
  getPublishCategory,
  updateStatus,
  updateStatusQiyong,
  submitNewsObj,
} from "@/api/message/publish";
import dialogForm from "./compoents/dialog.vue";
import { tableOption } from "@/const/crud/message/publishBanner";
import { mapGetters } from "vuex";
import RichText from "@/components/rich-text/index.vue";
import CategoryManage from "@/views/message/components/CategoryManage.vue";
import { formatDecimal } from "@/util/util";

import viewDialog from "./compoents/viewDialog.vue";
import "video.js/dist/video-js.css";
import "vue-video-player/src/custom-theme.css";
import { videoPlayer } from "vue-video-player";

//引入hls.js
// import "videojs-contrib-hls.js/src/videojs.hlsjs";

export default {
  name: "publish",
  components: {
    RichText,
    CategoryManage,
    // VideoPanel,
    videoPlayer,
    dialogForm,
    viewDialog,
  },
  data() {
    return {
      search: {
        pubStatus: "2",
      },
      pubStatusOptions: [
        {
          label: "待提交",
          value: "0",
          disabled: true,
        },
        {
          label: "待审核",
          value: "1",
          disabled: true,
        },
        {
          label: "已发布",
          value: "2",
        },
        {
          label: "已驳回",
          value: "-1",
          disabled: true,
        },
        {
          label: "已下线",
          value: "-2",
          disabled: true,
        },
      ],
      editForm: {},
      formObj: {},
      viewObj: {},
      addType: "",
      viewDialogFn: false,
      levelVal: "",
      viewDialogData: [],
      viewFileList: [],
      groupDialogFlag: false,
      contentTypeDialog: "",
      fileList: [],
      formType: "",
      picFlag: false,
      videoFlag: "",
      videoListFlag: "",
      viewContentType: "",
      groupExpandedKeys: [],
      playerOptions: {
        playbackRates: [0.7, 1.0, 1.25, 1.5, 2.0], //播放速度
        autoplay: false, //如果true,浏览器准备好时开始回放。
        controls: true, //控制条
        preload: "auto", //视频预加载
        muted: false, //默认情况下将会消除任何音频。
        loop: false, //导致视频一结束就重新开始。
        language: "zh-CN",
        aspectRatio: "16:9", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [
          {
            // type: "application/x-mpegURL",
            type: "video/mp4",
            // type: "rtmp/flv",
            src: "",
          },
        ],
        poster: "", //你的封面地址
        width: document.documentElement.clientWidth,
        notSupportedMessage: "此视频暂无法播放，请稍后再试", //允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: true,
          durationDisplay: true,
          remainingTimeDisplay: false,
          fullscreenToggle: true, //全屏按钮
        },
      },
      searchForm: {},
      headerObj: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      form: {},
      filterText: "",
      treeData: [],
      videoList: [],
      defaultProps: {
        children: "children",
        label: "name",
        key: "id",
      },
      selectCategory: {},
      categoryName: "",
      addVisible: false,
      visible: false,
      publishForm: {},
      videoUrl: "",
      // uploadVideoPath: process.env.VUE_APP_PIC_URL + "/api/file/storage/upload",
      uploadVideoPath: "/api/file/storage/upload",
      videoObj: {
        videoPath: "",
        videoSrcPath: "",
        videoName: "",
      },
      videoPercent: 0,
      publishOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            type: "input",
            label: "文章标题",
            prop: "articleTitle",
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入文章标题",
                trigger: "blur",
              },
            ],
          },
          {
            type: "input",
            label: "关键字",
            prop: "keyWord",
            span: 12,
          },
          {
            type: "tree",
            label: "所属栏目",
            prop: "categoryId",
            dicUrl: "/business-cms-article-category/treeList",
            props: {
              label: "name",
              value: "id",
              children: "children",
            },
            defaultExpandAll: true,
            span: 12,
            rules: [
              {
                required: true,
                message: "请选择所属栏目",
                trigger: "change",
              },
            ],
          },
          {
            type: "input",
            label: "作者",
            prop: "writer",
            span: 12,
          },
          {
            type: "radio",
            label: "内容类别",
            prop: "contentType",
            span: 12,
            dataType: "string",
            dicUrl: "/admin/sys_dict/type/article_contenttype",
            props: {
              label: "dictLabel",
              value: "dictValue",
            },
          },

          {
            type: "input-number",
            label: "排序号",
            prop: "sort",
            span: 12,
          },

          {
            label: "缩略图",
            prop: "picUrl",
            type: "upload",
            span: 12,
            display: true,
            showFileList: true,
            multiple: true,
            limit: 6,
            propsHttp: {
              res: "data",
            },
            dataType: "string",
            canvasOption: {},
            action: "/file/storage/upload",
            accept: "image/*,application/pdf",
            headers: {},
            listType: "picture-card",
            tip: "（文件格式支持jpg、png、svg、pdf格式，文件容量100M以内。)",
          },
          {
            label: "附件列表",
            prop: "filePath",
            type: "upload",
            span: 12,
            display: true,
            showFileList: true,
            multiple: true,
            limit: 6,
            dataType: "string",
            propsHttp: {
              res: "data",
            },
            hide: true,
            accept: "application/pdf",
            action: "/file/storage/upload",
            headers: {},
            listType: "text",
            // tip: "（文件格式支持jpg、png、svg、pdf格式，文件容量100M以内。)",
          },
          {
            label: "视频",
            prop: "videoUrl",
            slot: true,
            type: "upload",
            span: 24,
            align: "center",
            display: false,
            showFileList: true,
            multiple: false,
            limit: 6,
            dataType: "string",
            propsHttp: {
              res: "data",
            },
            hide: true,
            accept: ["video/mp4"],
            action: "/file/storage/upload",
            headers: {},
            listType: "picture-img",
            // tip: "（文件格式支持jpg、png、svg、pdf格式，文件容量100M以内。)",
          },
          {
            type: "textarea",
            label: "内容摘要",
            prop: "overview",
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入内容摘要",
                trigger: "blur",
              },
            ],
          },
          {
            type: "input",
            label: "内容",
            prop: "content",
            span: 24,
            hide: true,
            slot: true,
            rules: [
              {
                required: true,
                message: "请输入内容",
                trigger: "blur",
              },
            ],
          },
        ],
      },
      editVisible: false,
      editPublishForm: {},
      defaults: {},
      editDefaults: {},
      previewVisible: false,
      previewboxContent: "",
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(false),
        delBtn: this.vaildData(false),
        editBtn: this.vaildData(false),
      };
    },
    permissionListDiy() {
      return {
        delBtn: this.vaildData(this.permissions.common_cmsarticle_del, false),
        editBtn: this.vaildData(this.permissions.common_cmsarticle_edit, false),
      };
    },
    token() {
      return this.$store.getters.access_token;
    },
  },
  mounted() {
    console.log("publish=====");
    let _this = this;
    _this.handleGetTreeData();
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  methods: {
    handleSubmiting() { },
    backDialog() {
      this.viewDialogFn = false;
    },
    handleOpenView(row) {
      console.log("view==", row);
      this.addType = "view";
      this.editForm = row;
      this.$router.push({
        path: "/message/newsForm",
        query: {
          addType: "view",
          id: row.id,
        },
      });
      // this.picFlag = true;
      // getArticleObj(row.id).then((res) => {
      //   if (res.data.code == 200) {
      //     if (res.data.data.picUrl && res.data.data.contentType == 3) {
      //       res.data.data.picUrl = res.data.data.picUrl.split(",");
      //       res.data.data.picUrl.forEach((item, i) => {
      //         // res.data.data.picUrl[i] = process.env.VUE_APP_PIC_URL + item;
      //         if (item.includes("http://") || item.includes("https://")) {
      //           res.data.data.picUrl[i] = item;
      //         } else {
      //           res.data.data.picUrl[i] = process.env.VUE_APP_BASE_FILE + item;
      //         }
      //       });
      //     } else if (res.data.data.videoUrl && res.data.data.contentType == 1) {
      //       res.data.data.videoUrl = res.data.data.videoUrl.split(",");
      //       res.data.data.videoUrl.forEach((item, i) => {
      //         // res.data.data.videoUrl[i] = process.env.VUE_APP_PIC_URL + item;
      //         if (item.includes("http://") || item.includes("https://")) {
      //           res.data.data.videoUrl[i] = item;
      //         } else {
      //           res.data.data.videoUrl[i] =
      //             process.env.VUE_APP_BASE_FILE + item;
      //         }
      //       });
      //     }
      //     this.contentTypeDialog = res.data.data.contentType;
      //     this.viewObj = res.data.data;
      //     this.viewDialogFn = true;
      //     console.log("查看==", this.viewObj);
      //   }
      // });
    },
    closeDialogFn() {
      this.groupDialogFlag = false;
      this.getList(this.page);
      // 刷新左侧树
      this.handleGetTreeData();
    },
    handleVideoList() {
      this.videoListFlag = true;
    },
    handleOpenPic() {
      this.picFlag = true;
      this.contentTypeDialog = "3";
      this.addType = "add";
    },
    closeDialog() {
      this.picFlag = false;
      this.getList(this.page);
    },
    handleOpenVideo() {
      this.picFlag = true;
      this.contentTypeDialog = "1";
      this.addType = "add";
    },
    handleOpenText() {
      // this.picFlag = true;
      this.$router.push({
        path: "/message/newsForm",
        query: {
          addType: "add",
        },
      });
      this.contentTypeDialog = "2";
      this.addType = "add";
    },
    beforeUploadFile(file) {
      const size = this.record.fileList.length < 5;
      const isLt500M = file.size / 1024 / 1024 < 5;
      if (!isLt500M) {
        this.$message.error("上传文件大小不能超过 5MB!");
      }
      let fileName = file.name;
      let idx = fileName.lastIndexOf(".");
      let suffix = fileName.substring(idx + 1);
      const isTypeFlag =
        suffix.toLowerCase() === "jpg" ||
        suffix.toLowerCase() === "jpeg" ||
        suffix.toLowerCase() === "png" ||
        suffix.toLowerCase() === "pdf" ||
        suffix.toLowerCase() === "doc" ||
        suffix.toLowerCase() === "docx" ||
        suffix.toLowerCase() === "xls" ||
        suffix.toLowerCase() === "xlsx"; //  ||suffix.toLowerCase()==='avi'
      if (!isTypeFlag) {
        this.$message.error(
          "上传文件只能是 jpg,png,jpeg,pdf,word,pdf,xls,xlsx格式!"
        );
      }
      if (!size) {
        this.$message.error("最多上传五个文件");
      }
      return size && isTypeFlag && isLt500M;
    },
    handleUploadDocSuccess(res, file) {
      if (res.code == 0) {
        this.softDocObj = res.data;
        this.fileArr.push(res.data);
        this.record.uploadFlag = true;
        this.record.fileList.push({
          fileName: file.name,
          fileUrl: file.response.data.url,
        });
      }
    },
    handlePreviewed(file) {
      let url = this.getUrl(file);
      window.open(url, "target");
    },
    getUrl(file) {
      return process.env.VUE_APP_PIC_URL + file.response.data.url
    },
    handleRemoveDocSuccess(removeFile, fileList) {
      this.fileList = fileList;
    },
    handleError(error, file, fileLis) {
      this.$message.error("文件上传失败，请您重新退出后再登录");
    },
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
    getList(page, params) {
      let _this = this;
      this.tableLoading = true;
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            pubStatus: 2,
          },
          params,
          this.searchForm,
          {
            categoryId: _this.selectCategory.id || -1,
          }
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
          this.tableData.forEach((item) => {
            item.articleState = +item.articleState === 1;
          });
          //解决换页以后行错位问题
          this.$nextTick(() => {
            this.$refs.crud.doLayout();
          });
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel: function (row, index) {
      console.log("row==", row);
      this.$confirm("是否确认删除" + row.articleTitle, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return deleteObj(row.id);
        })
        .then((data) => {
          // this.$message.success("删除成功");
          console.log("删除==", res);
          this.getList(this.page);
        });
    },
    handleUpdate: function (row, index, done, loading) {
      if (row.videoUrl && row.videoUrl instanceof Array) {
        row.videoUrl = row.videoUrl.join(",");
      }
      if (row.picUrl && row.picUrl instanceof Array) {
        row.picUrl = row.picUrl.join(",");
      }
      putObj(row)
        .then((data) => {
          this.$message.success("修改成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleSave: function (row, done, loading) {
      if (row.picUrl && row.picUrl instanceof Array) {
        row.picUrl = row.picUrl.join(",");
      }
      if (row.videoUrl && row.videoUrl instanceof Array) {
        row.videoUrl = row.videoUrl.join(",");
      }
      addObj(Object.assign(row, this.selectCategory))
        .then((data) => {
          this.$message.success("添加成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    searchReset() {
      this.searchForm.pubStatus = "2";
      // this.getList(this.page);
    },
    searchChange(form, done) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
    setSinfo(val) {
      this.publishForm.content = val;
    },
    setEidtSinfo(val) {
      this.editPublishForm.content = val;
    },
    handleGetTreeData() {
      let _this = this;
      getPublishCategory({ areaCode: this.$store.getters.dept.areaCode || window.localStorage.getItem('areaCodely') }).then((res) => {
        console.log("treeDate==getPublishCategory200=", res);
        let data = res.data.data;
        _this.treeData = data;
        console.log("_this.treeData=", _this.treeData);
        _this.treeData.forEach((item) => {
          this.groupExpandedKeys.push(item.id);
        });

        // groupExpandedKeys
      });
    },
    handleSelectCategory(data, node) {
      this.formObj = {
        name: data.name,
        id: data.id,
      };
      console.log("dat==========data=====", data);
      console.log("dat===============", this.formObj);
      this.levelVal = node.level;
      let _this = this;
      _this.selectCategory = data;

      _this.categoryName = data.name;
      this.page.currentPage = 1;
      _this.getList(this.page);
    },
    handleOpenCategory() {
      this.groupDialogFlag = true;
    },
    handleAddCategroy() {
      this.addVisible = false;
      this.groupDialogFlag = false;
      this.$message.success("提交成功");
      // 刷新左侧树
      this.handleGetTreeData();
    },
    AddPublishSubmit(form, done) { },
    filterNode(value, data) {
      console.log("value==", value);
      console.log("data==", data);
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleOpenAdd() {
      // 点击发布公告
      let _this = this;
      _this.publishForm = _this.handleDefaultForm();
      _this.publishForm.categoryId = _this.selectCategory.categoryId;
      _this.visible = true;
      this.videoObj.videoPath = "";
      this.videoPercent = 0;
      this.formType = "add";
    },
    handleAdd() {
      // 点击确定
      let _this = this,
        flag = true;
      // console.log("xin===", _this.publishForm);
      if (!_this.publishForm.articleTitle) {
        _this.$message.error("请填写标题");
        flag = false;
      } else if (!_this.publishForm.categoryId) {
        _this.$message.error("请选择栏目");
        flag = false;
      } else if (
        _this.publishForm.contentType === "1" &&
        !_this.publishForm.videoUrl
      ) {
        _this.$message.error("请上传视频");
        flag = false;
      } else if (
        _this.publishForm.contentType === "2" &&
        !_this.publishForm.overview
      ) {
        _this.$message.error("请填写内容摘要");
        flag = false;
      } else if (
        _this.publishForm.contentType === "2" &&
        !_this.publishForm.content
      ) {
        _this.$message.error("请填写内容");
        flag = false;
      }
      if (_this.publishForm.videoUrl) {
        _this.publishForm.videoUrl = _this.publishForm.videoUrl + "";
      }
      if (_this.publishForm.picUrl) {
        _this.publishForm.picUrl = _this.publishForm.picUrl.join(",");
      }
      if (_this.publishForm.filePath) {
        _this.publishForm.filePath = _this.publishForm.filePath.join(",");
      }
      if (flag) {
        addObj(_this.publishForm).then((data) => {
          _this.$message.success("添加成功");
          _this.getList(this.page);
          _this.visible = false;
          this.getList(this.page);
        });
      }
    },
    handleCloseAdd() {
      // 关闭新增
      this.visible = false;
    },
    handleDefaultForm() {
      return {
        articleTitle: "",
        articleType: "1",
        articleUrl: "",
        categoryId: "",
        content: "",
        contentType: "2",
        filePath: "",
        isFirst: "1",
        keyWord: "",
        /*         othersOptionList: [], */
        overview: "",
        picUrl: [],
        sort: 0,
        writer: "",
        videoUrl: "",
        pubStatus: 0,
      };
    },
    handleSwitchChange(scope) {
      console.log("scope.row==", scope.row);
      scope.row.pubStatus = +scope.row.pubStatus;
      if (scope.row.videoUrl) {
        scope.row.videoUrl = scope.row.videoUrl + "";
      }
      if (scope.row.picUrl && scope.row.picUrl instanceof Array) {
        scope.row.picUrl = scope.row.picUrl.join(",");
      }
      console.log("scope.row.pubStatus===", scope.row.pubStatus);
      if (scope.row.pubStatus == 0) {
        updateStatusQiyong(scope.row.id)
          .then((res) => {
            console.log("更新状态=", res);
            if (res.data.code == 200) {
              this.$message.success("更新状态成功");
              this.refreshChange();
            } else {
              this.$message.error("更新状态失败");
              // scope.row.articleState = 0;
            }
          })
          .catch((err) => {
            this.$message.error("更新状态失败");
          });
      } else {
        updateStatus(scope.row.id)
          .then((res) => {
            console.log("更新状态=", res);
            if (res.data.code == 200) {
              this.$message.success("更新状态成功");
              this.refreshChange();
            } else {
              this.$message.error("更新状态失败");
              // scope.row.articleState = 0;
            }
          })
          .catch((err) => {
            this.$message.error("更新状态失败");
          });
      }
    },
    handleOpenEdit(row) {
      console.log("编辑==", row);
      this.addType = "edit";
      this.editForm = row;
      // this.picFlag = true;
      this.$router.push({
        path: "/message/newsForm",
        query: {
          addType: "edit",
          id: row.id,
        },
      });
      // getArticleObj(data.id).then((res) => {
      //   if (res.data.code == 200) {
      //     if (res.data.data.picUrl && res.data.data.contentType == 3) {
      //       res.data.data.picUrl = res.data.data.picUrl.split(",");
      //       res.data.data.picUrl.forEach((item, i) => {
      //         // res.data.data.picUrl[i] = process.env.VUE_APP_PIC_URL + item;
      //         res.data.data.picUrl[i] = item;
      //       });
      //     } else if (res.data.data.videoUrl && res.data.data.contentType == 1) {
      //       res.data.data.videoUrl = res.data.data.videoUrl.split(",");
      //       res.data.data.videoUrl.forEach((item, i) => {
      //         // res.data.data.videoUrl[i] = process.env.VUE_APP_PIC_URL + item;
      //         res.data.data.videoUrl[i] = item;
      //       });
      //     }
      //     this.contentTypeDialog = res.data.data.contentType;
      //     this.addType = "edit";
      //     this.editForm = res.data.data;
      //     this.picFlag = true;
      //   }
      // });
    },
    handleEdit() {
      let _this = this;

      _this.editPublishForm.articleState = _this.editPublishForm.articleState
        ? "1"
        : "0";
      // 点击保存
      if (_this.editPublishForm.videoUrl) {
        console.log(_this.editPublishForm.videoUrl);
        _this.editPublishForm.videoUrl = _this.editPublishForm.videoUrl + "";
        if (_this.editPublishForm.videoUrl == "0") {
          _this.editPublishForm.videoUrl = null;
        }
      }

      console.log(typeof _this.editPublishForm.picUrl);
      if (
        _this.editPublishForm.picUrl &&
        typeof _this.editPublishForm.picUrl != "string"
      ) {
        _this.editPublishForm.picUrl = _this.editPublishForm.picUrl.join(",");
      }
      if (_this.editPublishForm.filePath) {
        _this.editPublishForm.filePath =
          _this.editPublishForm.filePath.join(",");
      }
      putObj(this.editPublishForm).then((data) => {
        this.$message.success("修改成功");
        this.editVisible = false;
        this.getList(this.page);
      });
    },
    handleCloseEdit() {
      this.editVisible = false;
    },
    //提交
    handleSubimt(row) {
      console.log(row, 10766666);
      this.$emit("handleChoose", {
        ...row,
      });
    },
    handlePreview(form) {
      let _this = this;
      if (form) {
        _this.previewboxContent = form.content;
      }
      this.previewVisible = true;
    },
    handleClosePreview() {
      this.previewVisible = false;
    },
    beforeVideoUpload(file) {
      let fileName = file.name;
      let idx = fileName.lastIndexOf(".");
      let suffix = fileName.substring(idx + 1);
      const isTypeFlag = suffix.toLowerCase() === "mp4"; //  ||suffix.toLowerCase()==='avi'
      if (!isTypeFlag) {
        this.$message.error("上传视频只能是 mp4格式!");
      }
      return isTypeFlag;
    },
    handleVideoRemove(file, fileList) {
      if (file.status == "success") {
        this.videoObj.videoPath = "";
        this.publishForm.videoUrl = "";
        this.editPublishForm.videoUrl = "";
      }
    },
    handleVideoSuccess(res, file) {
      if (res.code === 0) {
        this.videoPercent = 100;
        this.videoObj.videoPath = process.env.VUE_APP_PIC_URL + res.data.url;
        this.playerOptions.sources[0].src = this.videoObj.videoPath;
        if (this.formType == "edit") {
          this.editPublishForm.videoUrl =
            process.env.VUE_APP_PIC_URL + res.data.url;
        } else {
          this.publishForm.videoUrl =
            process.env.VUE_APP_PIC_URL + res.data.url;
        }
        // console.log("this.publishForm.videoUrl==", this.publishForm.videoUrl);

        // console.log("this.videoObj.videoPath==", this.videoObj.videoPath);
        // http://.154.55:9000/hall-file/2022/03/04/32okfxy9qzehij8yzn08/shipin.mp4
        this.videoObj.videoName = file.name;
        //this.getPsignFromFileid();
      } else {
        this.videoObj.videoPath = "";
        this.videoObj.videoName = "";
        this.$message.error("上传失败");
      }
    },
    progressVideo(event, file, fileList) {
      let videoPercent = formatDecimal(event.percent, 2);
      if (videoPercent == 100) {
        this.videoPercent = 99.99;
      } else {
        this.videoPercent = videoPercent;
      }
    },
    handleRemoveVideo() {
      this.$refs.videoRef.clearFiles();
      this.videoObj.videoPath = "";
      this.videoObj.videoSrcPath = "";
      this.videoObj.videoName = "";
      this.videoPercent = 0;
      this.publishForm.videoUrl = "";
    },
  },
  watch: {
    filterText(newVal) {
      this.$refs.tree.filter(newVal);
    },
    "publishForm.contentType"(val) {
      if (val === "1") {
        // 视频
        if (this.defaults.articleTitle) {
          this.defaults.picUrl.display = false;
          this.defaults.filePath.display = false;
          this.defaults.overview.display = false;
          this.defaults.content.display = false;
          this.defaults.videoUrl.display = true;
        }
      } else {
        // 文本
        if (this.defaults.articleTitle) {
          this.defaults.picUrl.display = true;
          this.defaults.filePath.display = true;
          this.defaults.overview.display = true;
          this.defaults.content.display = true;
          this.defaults.videoUrl.display = false;
        }
      }
    },
    editPublishForm: {
      handler(newVal, oldVal) {
        let _this = this;
        if (newVal.contentType === "1") {
          if (this.editDefaults.articleTitle) {
            this.editDefaults.picUrl.display = false;
            this.editDefaults.filePath.display = false;
            this.editDefaults.overview.display = false;
            this.editDefaults.content.display = false;
            this.editDefaults.videoUrl.display = true;
          }
        } else {
          if (this.editDefaults.articleTitle) {
            this.editDefaults.picUrl.display = true;
            this.editDefaults.filePath.display = true;
            this.editDefaults.overview.display = true;
            this.editDefaults.content.display = true;
            this.editDefaults.videoUrl.display = false;
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.el-table th.gutter {
  display: table-cell !important;
}

.wrapper {
  padding: 18px 18px 8px;
  height: 100%;
}

.main {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;

  &-left {
    padding-right: 10px;
    flex-shrink: 0;
    width: 302px;
    height: 100%;
  }

  &-right {
    padding-left: 10px;
    width: calc(100% - 302px);
    height: 100%;
  }
}

::v-deep .el-table th.el-table__cell {
  text-align: center;
}

.spform {
  ::v-deep .el-collapse-item__wrap {
    overflow-y: auto;
  }
}

.floor-nav {
  height: 100%;
  border-radius: 4px;
  color: #fff;
  border: 1px solid #cfc9c9;
  /*  background: rgba(33.03, 43.73, 96.69, 0.4); */

  &-head {
    padding: 10px 16px 0;

    .option {
      position: relative;
      display: flex;
      margin-bottom: 20px;
      height: 32px;
      line-height: 32px;
      justify-content: space-between;
      color: #22242c;

      &::after {
        position: absolute;
        bottom: -11px;
        left: 0;
        width: 100%;
        height: 1px;
        content: "";
        background-color: #cfc9c9;
      }

      &-title {
        font-size: 16px;
        font-weight: 700;
      }
    }

    .banner {
      /* padding: 0 15px; */
      display: flex;
      justify-content: space-between;
      flex-grow: 1;
      height: 40px;
      line-height: 40px;
      color: #333;
      border-radius: 4px;
    }

    .banner-btn {
      flex-shrink: 0;
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: rgba(50, 114, 206, 1);
      border-radius: 0 4px 4px 0;
    }
  }

  &-body {
    padding-bottom: 10px;
    height: 50px;

    &.tree-container {
      padding: 20px 15px 10px;
      height: calc(100% - 102px);
      overflow: auto;
    }

    .floor-list {
      padding-top: 10px;
    }

    .floor-item {
      padding: 0 25px;
      display: flex;
      justify-content: space-between;
      height: 30px;
      line-height: 30px;
      cursor: pointer;

      &:hover,
      &.active {
        background-color: rgba(50, 114, 206, 0.5);
      }
    }
  }
}

.previewbox {
  &-container {
    margin: 0 auto;
    padding: 10px 20px 50px;
    /*  width: 1034px; */
    /*  height: 1414px; */
    /*    background: url("../../../assets/image/hallmanage/frame-bac.png") no-repeat
      center;
    background-size: 100% 100%; */
  }

  &-head {
    padding: 0 80px 0 30px;
    height: 95px;
    color: #a6afeb;
    font-size: 32px;
    line-height: 90px;
    // background: url("../../../assets/image/hallmanage/split-line.png") center
    //   bottom no-repeat;
  }

  &-content {
    padding: 0 25px;
    /*   font-size: 26px;
    line-height: 52px;
    color: #b4c9f2;
    text-indent: 2em; */
    /*  height: calc(100% - 95px); */
    height: 100%;
    overflow: auto;
  }

  ::-webkit-scrollbar-thumb {
    background-color: transparent;
  }
}

.upload-btn {
  width: 400px;
  height: 225px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  line-height: 20px;
  padding: 0px 16px 0;
  border-radius: 4px;
  border: 1px dashed #cccccc;

  .el-icon-plus {
    margin-bottom: 8px;
    font-size: 49px;
    color: #cccccc;
  }

  div {
    font-size: 14px;
    font-weight: 400;
    color: rgba(204, 204, 204, 1);
    line-height: 20px;
  }
}

.vid-show {
  position: relative;
  margin-top: -32px;
  width: 400px;
  height: 225px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;

  border-radius: 4px;
  border: 1px dashed #cccccc;

  .upload-pic {
    width: 126px;
    height: 126px;
    margin: auto;
  }
}

.my-icon-close {
  position: absolute;
  display: inline-block;
  width: 22px;
  height: 22px;
  top: 0;
  // border: 1px solid #cccccc;
  // border-radius: 50%;
  right: 0px;
  z-index: 2;
  color: #cccccc;

  &:hover {
    cursor: pointer;
  }
}
</style>
<style></style>
