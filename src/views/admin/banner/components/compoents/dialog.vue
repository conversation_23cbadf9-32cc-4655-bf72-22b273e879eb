<template>
  <div>
    <cust-dialog
      @closed="handleBack"
      class="mydialog"
      :title="title"
      width="90%"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        :disabled="addType == 'view'"
      >
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item
              label="内容分类："
              label-width="100px"
              prop="contentType"
            >
              <el-input
                :disabled="true"
                v-model="form.contentType"
              ></el-input> </el-form-item
          ></el-col> -->
          <el-col :span="24">
            <el-form-item label="标题：" label-width="90px" prop="title">
              <el-input
                maxlength="100"
                show-word-limit
                v-model="form.title"
              ></el-input> </el-form-item
          ></el-col>
          <el-col :span="24">
            <el-form-item label="新闻摘要" label-width="90px" prop="memo">
              <el-input
                maxlength="200"
                show-word-limit
                v-model="form.memo"
              ></el-input> </el-form-item
          ></el-col>
          <el-col :span="24">
            <el-form-item label="发布部门" label-width="90px" prop="source">
              <el-input
                maxlength="100"
                show-word-limit
                v-model="form.source"
              ></el-input> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item
              label="内容类型"
              prop="articleType"
              :rules="[{ required: true, message: '内容类型不能为空' }]"
            >
              <el-select
                v-model="form.articleType"
                @change="changeTypeFn"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in articleTypeArr"
                  :key="item.id"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="分组：" label-width="90px" prop="categoryName">
              <el-select v-model="form.categoryId" ref="selectTree" clearable>
                <el-option
                  :value="form.categoryId"
                  :label="form.categoryName"
                  style="height: auto"
                  hidden
                >
                </el-option>
                <el-tree
                  ref="treeRef"
                  :data="treeData"
                  :props="defaultProps"
                  :render-after-expand="true"
                  :expand-on-click-node="false"
                  node-key="id"
                  default-expand-all
                  @node-click="handleClickNode"
                />
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item
              label="是否显示发布人"
              label-width="120px"
              prop="showUser"
            >
              <el-radio-group v-model="form.showUser">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="正文类型" label-width="90px" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio label="0">富文本</el-radio>
                <el-radio label="1">纯文件（PDF）</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="封面图片" label-width="90px" prop="pic">
              <file-upload
                v-model="form.pic"
                :fileUrls="form.pic"
              ></file-upload>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              v-if="form.type == 0"
              label="正文："
              label-width="90px"
              prop="content"
            >
              <rich-text
                ref="skindeditor"
                :id="'s_kind_editor'"
                :content.sync="form.content"
                @input="setEidtSinfo"
              /> </el-form-item
          ></el-col>
          <el-col :span="24">
            <el-form-item label="附件" label-width="90px" prop="">
              <file-upload-zujian
                v-model="fjList"
                :files="form.files"
                :addType="addType"
              ></file-upload-zujian> </el-form-item
          ></el-col>
          <el-col :span="24">
            <el-form-item
              v-if="contentTypeDialog == '3'"
              label="上传封面："
              label-width="100px"
              prop="uu"
            >
              <div class="picBtn" @click="handleSelectPic">
                <div class="pic-up">
                  <div class="pic-up-plus"></div>
                  <!-- <i class="el-icon-plus pic-up-plus"></i> -->
                </div>
                <div class="pic-down">
                  <span>选择图片</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item
              v-if="contentTypeDialog == '1'"
              label="上传视频："
              label-width="100px"
              prop="uum"
            >
              <el-button
                icon="el-icon-plus"
                type="primary"
                @click="handleSelectVideo"
                >选择视频</el-button
              >
            </el-form-item>
            <div :style="{ 'margin-left': '68px' }">
              <upload
                :fileList="fileList"
                :uploadVisible="uploadVisible"
              ></upload>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <span slot="bottom-footer" class="dialog-footer">
        <el-button @click="handleBack">关闭</el-button>
        <el-button
          type="primary"
          v-if="addType != 'view'"
          @click="debounceHandleSubmit"
          >提交</el-button
        >
      </span></cust-dialog
    >
    <pic-dialog
      :openType="openType"
      :fileList="fileArr"
      v-if="picFlag"
      @closePic="closePic"
      @handleData="picData"
    ></pic-dialog>
  </div>
</template>
<script>
import {
  addInfo,
  editObj,
  putObj,
  getArticleDetail,
} from "@/api/message/publish";
import RichText from "@/components/rich-text/index.vue";
import upload from "./upload.vue";
import picDialog from "./picDialog.vue";
import debounce from "@/util/debounce";
import FileUpload from "@/views/message/components/FileUpload.vue";

import FileUploadZujian from "@/components/upload/fileUpload.vue";
import { remote } from "@/api/admin/sys/sys-dict";
export default {
  name: "dialog",
  components: {
    RichText,
    upload,
    picDialog,
    FileUpload,
    FileUploadZujian,
  },
  props: {
    contentTypeDialog: {
      type: String,
      default: function () {
        return "";
      },
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    formObj: {
      type: Object,
      default: function () {
        return {};
      },
    },
    addType: {
      type: String,
      default: function () {
        return "";
      },
    },
    editForm: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      title: "内容新增",
      file: [],
      nameList: [],
      fileList: [],
      headerObj: {},
      fileArr: [],
      openType: "",
      defaultProps: {
        /** 唯一标识 **/
        value: "id",
        /** 标签显示 **/
        label: "name",
        /** 子级 **/
        children: "children",
      },
      form: {
        contentType: "",
        categoryName: "",
        articleTitle: "",
        content: "",
        categoryId: "",
        articleId: "",
        uu: "1",
        uum: "1",
        id: "",
        title: "",
        type: "0",
        showUser: "1",
        articleType: "",
      },
      articleTypeArr: [],
      uploadVisible: false,
      picFlag: false,
      fjList: [],
      allTreeArr: [],
      rules: {
        categoryName: [
          { required: true, message: "请选择分组", trigger: "blur" },
        ],
        contentType: [
          { required: true, message: "请选择分类", trigger: "blur" },
        ],
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        uu: [{ required: true, message: "请上传图片", trigger: "blur" }],
        uum: [{ required: true, message: "请上传视频", trigger: "blur" }],
      },
    };
  },
  computed: {
    token() {
      return this.$store.getters.access_token;
    },
  },
  created() {
    this.handleGetSysData();
    if (this.addType == "add") {
      this.form.categoryId = "";
      // setTimeout(() => {
      //   if (this.form.categoryId) {
      //     this.$nextTick(() => {
      //       this.$refs.treeRef.setCurrentKey(this.form.categoryId);
      //     });

      //     this.$forceUpdate();
      //   }
      // }, 500);
    }
    console.log("formObj===", this.formObj);
  },
  mounted() {
    console.log("editForm=treeData=", this.treeData);
    console.log("editForm==", this.editForm);
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
    this.initForAllTree(this.treeData);
    this.handleData();
  },
  methods: {
    changeTypeFn() {
      console.log("form.articleType==", this.form.articleType);
      console.log("form.treeData==", this.treeData);
      // this.treeData
    },
    handleGetSysData() {
      remote("portal_article_type").then((res) => {
        console.log("获取的数据字典==", res);
        if (res.data.code == 200) {
          this.articleTypeArr = res.data.data;
        }
      });
    },
    initForAllTree(arr) {
      if (arr && arr.length > 0) {
        arr.forEach((item) => {
          this.allTreeArr.push(item);
          if (item.children && item.children.length > 0) {
            this.initForAllTree(item.children);
          }
        });
      }
    },
    getArticleDetailFn() {
      getArticleDetail(this.editForm.id).then((res) => {
        console.log("获取详情---", res);
        //
        if (res.data.code == 200) {
          let editForm = res.data.data;
          this.form = Object.assign({}, editForm);
          this.dealGetCategory();
        }
      });
    },
    dealGetCategory() {
      let fObj = this.allTreeArr.find((item) => {
        return item.id == this.editForm.categoryId;
      });
      console.log("fObj==", fObj);
      if (fObj) {
        this.form.categoryName = fObj.name;
      }
    },
    handleClickNode(data) {
      this.form.categoryId = data.id;
      this.form.categoryName = data.name;
      this.$refs.selectTree.blur();
      // 选择器执行完成后，使其失去焦点隐藏下拉框的效果
      // this.$refs.selectTree.blur();
    },
    setEidtSinfo(val) {
      this.form.content = val;
    },
    handleBack() {
      this.$emit("closeDialog");
    },
    handleSelectPic() {
      this.fileArr = this.fileList;
      this.picFlag = true;
      this.openType = "pic";
    },
    handleSelectVideo() {
      this.fileArr = this.fileList;
      this.picFlag = true;
      this.openType = "video";
    },
    closePic() {
      this.picFlag = false;
    },
    picData(data) {
      this.fileList = data;
      this.uploadVisible = true;
    },
    handleData() {
      if (this.addType == "edit" || this.addType == "view") {
        this.form = Object.assign({}, this.editForm);
        this.getArticleDetailFn();
        this.form.id = this.editForm.id;
        this.title = "内容编辑";
        console.log("this.treeData==", this.treeData);

        // this.treeData.forEach((item) => {
        //   if (item.categoryId == this.editForm.categoryId) {
        //     this.form.categoryName = item.name;
        //   } else {
        //     if (item.child) {
        //       item.child.forEach((element) => {
        //         if (element.categoryId == this.editForm.categoryId) {
        //           this.form.categoryName = element.name;
        //         } else {
        //           if (element.child) {
        //             element.child.forEach((xqc) => {
        //               if (xqc.categoryId == this.editForm.categoryId) {
        //                 this.form.categoryName = xqc.name;
        //               }
        //             });
        //           }
        //         }
        //       });
        //     }
        //   }
        // });
        // this.form.categoryId = this.editForm.categoryId;
        // this.form.articleTitle = this.editForm.articleTitle;
        // this.form.content = this.editForm.content;
        // this.form.categoryName = this.editForm.categoryName;

        this.$nextTick(() => {
          this.$refs.treeRef.setCurrentKey(this.form.categoryId);
        });

        this.$forceUpdate();
        // if (this.editForm.contentType == 3) {
        //   this.form.contentType = "图片";
        //   this.editForm.picUrl.forEach((item) => {
        //     let index = item.lastIndexOf("/");
        //     let pig = item.substr(index + 1);
        //     this.file.push({
        //       name: pig,
        //       url: item,
        //     });
        //   });
        //   this.fileList = this.file;
        //   // console.log("图片回显==", this.fileList);
        //   // console.log("图片回显==", this.editForm);
        //   this.uploadVisible = true;
        // } else if (this.editForm.contentType == 2) {
        //   this.form.contentType = "图文";
        // } else if (this.editForm.contentType == 1) {
        //   this.form.contentType = "视频";

        //   this.editForm.videoUrl.forEach((item) => {
        //     let index = item.lastIndexOf("/");
        //     let pig = item.substr(index + 1);
        //     this.file.push({
        //       name: pig,
        //       url: item,
        //     });
        //   });
        //   this.fileList = this.file;
        //   this.uploadVisible = true;
        // }
      } else {
        setTimeout(() => {
          this.form.categoryId = this.formObj.id;
          this.form.categoryName = this.formObj.name;
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.form.categoryId);
          });
          this.$forceUpdate();
        }, 500);

        // 1-视频；2-文字；3-图文
        //内容类型:0富文本，1文件
      }
    },
    debounceHandleSubmit: debounce(function () {
      this.handleSubmit();
    }, 500),
    handleSubmit() {
      console.log("fjList==", this.fjList);
      //fileList
      if (this.fjList && this.fjList.length > 0) {
        let fileArr = [];
        this.fjList.forEach((item) => {
          if (item.response) {
            let obj = {
              fileName: item.response.fileName,
              path: item.response.uri,
              id: item.response.bizId,
            };
            fileArr.push(obj);
          }
        });
        this.form.fileList = fileArr;
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.form.type == 0 || this.form.type == 1) {
            // this.form.contentType = "2";
            this.form.videoUrl = "";
            this.form.picUrl = "";
            if (this.addType == "edit") {
              this.form.id = this.editForm.id;

              editObj(this.form).then((res) => {
                // console.log("res更新==", res);
                if (res.data.code == 200) {
                  this.$message.success("更新成功");
                  this.$emit("closeDialog");
                }
              });
            } else {
              addInfo(this.form).then((res) => {
                if (res.data.code == 200) {
                  this.$message.success("提交成功");
                  this.$emit("closeDialog");
                }
                // console.log("res");
              });
            }
          } else {
            let arr = [];
            if (this.fileList.length > 0) {
              this.fileList.forEach((item) => {
                if (item.url) {
                  // item.url = item.url.substring(num1);
                  arr.push(item.url);
                }
              });
              arr = arr.join(",");
              if (
                this.form.contentType == "视频" ||
                this.form.contentType == "1"
              ) {
                this.form.contentType = "1";
                this.form.videoUrl = arr;
                this.form.picUrl = "";
              } else if (
                this.form.contentType == "图片" ||
                this.form.contentType == "3"
              ) {
                this.form.contentType = "3";
                this.form.picUrl = arr;
                this.form.videoUrl = "";
              } else if (this.form.contentType == "图文") {
                this.form.contentType = "2";
                this.form.videoUrl = "";
                this.form.picUrl = "";
              }
              if (this.addType == "edit") {
                this.form.articleId = this.editForm.articleId;
                // this.form.articleId = this.editForm.articleId;
                putObj(this.form).then((res) => {
                  if (res.data.code == 200) {
                    this.$message.success("更新成功");
                    this.$emit("closeDialog");
                  }
                });
              } else {
                addInfo(this.form).then((res) => {
                  if (res.data.code == 200) {
                    this.$message.success("提交成功");
                    this.$emit("closeDialog");
                  }
                });
              }
            } else {
              this.$message.error("请上传文件！");
            }
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload-list__item .el-icon-close-tip {
  display: none !important;
}
::v-deep .el-upload-list__item.is-success .el-upload-list__item-status-label {
  display: none !important;
}
::v-deep .el-upload-dragger {
  width: 200px;
  height: 140px;
}
::v-deep.el-select {
  width: 240px;
}
.picBtn {
  width: 106px;
  height: 80px;
  border-radius: 4px;
  border: 1px dashed #3272ce;
  cursor: pointer;
  .pic-up {
    height: 40px;
    position: relative;
    .pic-up-plus {
      position: absolute;
      width: 30px;
      height: 30px;
      background: url("~@/assets/img/picturePic.png") no-repeat;
      background-size: 100% 100%;
      left: 35px;
      top: 10px;
    }
  }
  .pic-down {
    text-align: center;
    height: 40px;
    font-size: 12px;
    font-weight: 500;
    color: #3272ce;
  }
}
</style>
