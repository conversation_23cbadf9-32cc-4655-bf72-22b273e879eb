<template>
  <div class="attachment" v-show="uploadVisible && fileList.length > 0">
    <div
      v-for="(element, index) in fileList"
      :key="index"
      :class="getIsDocumentClass(element.name)"
    >
      <div class="doc" v-if="getIsDocument(element.name)">
        <div class="text" @click="handleOpenDocument(element)">
          <span class="icon"></span>
        </div>
        <div class="itxt-info" @click="handleOpenDocument(element)">
          <div class="info1">{{ element.name }}</div>
          <div class="info2">
            <!-- <span>{{ element.size + "b" }}</span>
            <span>{{ element.createTime }}</span> -->
          </div>
        </div>
        <div class="delete" v-if="openFlag != 'view'">
          <el-button
            @click="handleDeleteFile(fileList, index)"
            type="text"
            icon="el-icon-delete"
          ></el-button>
        </div>
      </div>
      <div class="img-view" v-else>
        <el-image
          style="width: 45px; height: 57px"
          :src="getUrl(element)"
          :preview-src-list="[getUrl(element)]"
          alt=""
        ></el-image>
        <div
          class="pic-delete"
          @click="handleDeleteFile(fileList, index)"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "upload",
  props: {
    fileList: {
      type: Array,
      default: () => {
        return {};
      },
    },
    uploadVisible: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    openFlag: {
      type: String,
      default: () => {
        return 'edit';
      },
    },
  },
  created() {},
  mounted() {
    // console.log("===",this.fileList)
  },
  data() {
    return {
      fileArr: [],
      srcList: [],
    };
  },
  methods: {
    handleOpenDocument(file) {
      window.open(file.url);
    },
    getIsDocumentClass(fileName) {
      let index = fileName.lastIndexOf(".");
      let suffix = fileName.substr(index + 1);
      const isDocument =
        suffix.toLowerCase() === "pdf" ||
        suffix.toLowerCase() === "doc" ||
        suffix.toLowerCase() === "docx" ||
        suffix.toLowerCase() === "txt" ||
        suffix.toLowerCase() === "xls" ||
        suffix.toLowerCase() === "mp4" ||
        suffix.toLowerCase() === "xlsx";
      const isPic =
        suffix.toLowerCase() === "jpg" ||
        suffix.toLowerCase() === "jpeg" ||
        suffix.toLowerCase() === "png";
      if (isDocument) {
        return "attachment-item";
      } else if (isPic) {
        return "attachment-pic";
      }
    },
    getUrl(element) {
      return element.url;
    },
    handleDeleteFile(data, index) {
      data.splice(index, 1);
    },
    getIsDocument(fileName) {
      let index = fileName.lastIndexOf(".");
      let suffix = fileName.substr(index + 1);
      const isDocument =
        suffix.toLowerCase() === "pdf" ||
        suffix.toLowerCase() === "doc" ||
        suffix.toLowerCase() === "docx" ||
        suffix.toLowerCase() === "txt" ||
        suffix.toLowerCase() === "xls" ||
        suffix.toLowerCase() === "mp4" ||
        suffix.toLowerCase() === "xlsx";
      const isPic =
        suffix.toLowerCase() === "jpg" ||
        suffix.toLowerCase() === "jpeg" ||
        suffix.toLowerCase() === "png";
      if (isDocument) {
        return true;
      } else if (isPic) {
        return false;
      } else {
        return true;
      }
    },
    beforeUploadFile(file) {
      const isLt500M = file.size / 1024 / 1024 < 5;
      if (!isLt500M) {
        this.$message.error("上传文件大小不能超过 5MB!");
      }
      let fileName = file.name;
      let idx = fileName.lastIndexOf(".");
      let suffix = fileName.substring(idx + 1);
      const isTypeFlag =
        suffix.toLowerCase() === "jpg" ||
        suffix.toLowerCase() === "jpeg" ||
        suffix.toLowerCase() === "png" ||
        suffix.toLowerCase() === "pdf" ||
        suffix.toLowerCase() === "doc" ||
        suffix.toLowerCase() === "docx"; //  ||suffix.toLowerCase()==='avi'
      if (!isTypeFlag) {
        this.$message.error("上传文件只能是 jpg,png,jpeg,pdf,word格式!");
      }
      return isTypeFlag && isLt500M;
    },
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
    handleUploadDocSuccess(res, file) {
      if (res.code == 0) {
        this.softDocObj = res.data;
        this.fileArr.push(res.data);
      }
    },
    handleError(error, file, fileLis) {
      this.$message.error("文件上传失败，请您重新退出后再登录");
    },
    handleRemoveDocSuccess(removeFile, fileList) {
      this.fileList = fileList;
    },
    handlePreview(file) {
      window.open(this.getJumpUrl(file), "target");
      // window.location.href = url;
    },
    getJumpUrl(file) {
      return file.response.data.url;
    }
  },
};
</script>
<style lang="scss" scoped>
.attachment {
  // margin-left: 65px;
  display: flex;
  flex-wrap: wrap;
  .attachment-pic {
    cursor: pointer;
    margin-left: 20px;
    margin-top: 10px;
    img {
      width: 45px;
      height: 57px;
    }
    .img-view {
      position: relative;
      .pic-delete {
        position: absolute;
        cursor: pointer;
        width: 12px;
        height: 12px;
        // background: url("../../../../assets/image/jointreview/delete-pic.png")
        //   no-repeat;
        background-size: 100% 100%;
        border-radius: 50%;
        top: -6px;
        right: -6px;
      }
    }
  }
  .attachment-item {
    position: relative;
    cursor: pointer;
    margin-left: 20px;
    margin-top: 5px;
    margin-bottom: 5px;
    // width: 20%;
    height: 60px;
    padding-left: 10px;
    width: 255px;
    // background-color: #fafafa;
    border: 1px solid #dcdfe6;
    flex-wrap: wrap;
    .doc {
      display: flex;
      //   flex-wrap:wrap;
      flex-flow: wrap;
      //   justify-content: space-between;
      .text {
        // width: 80%;
        width: 50px;
        font-size: 10px;
      }
      .itxt-info {
        width: 160px;
        font-size: 14px;
        vertical-align: middle;
        .info1 {
          margin-top: 10px;
          height: 18px;
          font-weight: 500;
          color: #606266;
        }
        .info2 {
          height: 28px;
          font-weight: 500;
          color: #8e8ea1;
        }
      }
    }
    ::v-deep .el-button {
      font-size: 16px;
      color: #95979d;
    }
    .icon {
      margin-top: 13px;
      display: inline-block;
      vertical-align: middle;
      height: 30px;
      width: 30px;
      // background: url("../../../../assets/image/jointreview/cfhd.png") no-repeat;
      background-size: 100% 100%;
    }
    span {
      line-height: 26px;
    }
    .delete {
      // width: 10%;
      margin-top: 10px;
      margin-right: 5px;
      // position: absolute;
      // right: 10px;
      // top: 5px;
    }
    .delete:hover {
      ::v-deep .el-button {
        color: red;
      }
    }
  }
}
</style>
