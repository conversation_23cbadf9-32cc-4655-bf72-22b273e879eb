<template>
  <div class="scrollTable">
     <div v-for="(item,index) in topTenData" :key="index" class="infoWrap">
      <div class="leftWrap">
        <div :class="index<3?'activeIndex':'index'">
          {{index+1}}
        </div>
        <div class="name">
          {{item.username}}
        </div>
      </div>
      <div class="rightWrap">
        {{item.cno}}
      </div>
     </div>
  </div>
</template>

<script>
export default {
  props: {
    topTenData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      tableData: [],
    };
  },
  mounted() {},
  beforeDestroy() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.scrollTable {
  .infoWrap {
    display: flex;
    justify-content: space-between;
    height: 42px;
    align-items: center;
    .leftWrap {
      display: flex;
      align-items: center;
      font-size: 16px;
      .index {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: #f0f0f0;
        text-align: center;
        line-height: 22px;
        margin-right: 10px;
        font-size: 14px;
        font-weight: bold;
      }
      .activeIndex {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: #ffa940;
        text-align: center;
        line-height: 22px;
        margin-right: 10px;
        color: #ffffff;
        font-size: 14px;
        font-weight: bold;
      }
    }
    .rightWrap {
      font-size: 15px;
      font-weight: bold;
    }
  }
}
</style>
