<template>
  <div class="line-chart">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "LineChart",
  props: {
    authData: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      chart: null,
      option: {
        backgroundColor: "#fff",
        color: [
          "#23CF9C",
          "#578FFB",
          "#6E40F2",
          "#FF61E6",
          "#8B5CFF",
          "#00CA69",
        ],
        legend: {
          top: "10%",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          top: "20%",
          left: "4%",
          right: "4%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#333",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#D9D9D9",
              },
            },
            data: [1],
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLabel: {
              textStyle: {
                color: "#666",
              },
            },
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
              lineHeight: 40,
            },
            splitLine: {
              lineStyle: {
                type: "dashed",
                color: "#E9E9E9",
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "成功",
            type: "line",
            smooth: true,
            // showSymbol: false,/
            symbolSize: 8,
            zlevel: 3,
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#6E40F230",
                    },
                    {
                      offset: 1,
                      color: "#6E40F210",
                    },
                  ],
                  false
                ),
                shadowColor: "#23CF9C10",
                shadowBlur: 10,
              },
            },
            data: [100, 138, 350, 173, 180, 150, 180, 230],
          },
          {
            name: "失败",
            type: "line",
            smooth: true,
            // showSymbol: false,
            symbolSize: 8,
            zlevel: 3,
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#FF61E630",
                    },
                    {
                      offset: 1,
                      color: "#FF61E610",
                    },
                  ],
                  false
                ),
                shadowColor: "#578FFB10",
                shadowBlur: 10,
              },
            },
            data: [233, 233, 200, 180, 199, 233, 210, 180],
          },
        ],
      },
    };
  },
  mounted() {
    console.log(this.authData, 16222)
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  watch: {
    authData(newValue,oldValue) {
      this.initChart()
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer);
      this.option.xAxis[0].data = this.authData.map(item=>{
        return item.date
      })
      this.option.series[0].data = this.authData.map(item=>{
        return item.succ
      })
      this.option.series[1].data = this.authData.map(item=>{
        return item.fail
      })
      this.chart.setOption(this.option);
    },
  },
};
</script>

<style scoped>
.line-chart {
  height: 400px;
}
.chart-container {
  height: 100%;
}
</style>
