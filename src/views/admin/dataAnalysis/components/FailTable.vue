<template>
  <div class="scrollTable">
    <div class="title">
      <el-row>
        <el-col class="center" :span="3"> 序号 </el-col>
        <el-col class="center" :span="5"> 账号 </el-col>
        <el-col class="center" :span="11"> 认证失败原因 </el-col>
        <el-col class="center" :span="5"> 认证时间 </el-col>
      </el-row>
    </div>
    <vue-seamless-scroll
      :step="0.6"
      limitMoveNum="10"
      :data="failData"
      class="scroll"
    >
      <div class="info" v-for="(item, index) in failData" :key="index">
        <el-row class="infoWrap">
          <el-col class="center" :span="3"> {{index+1}} </el-col>
          <el-col class="center" :span="5"> {{item.operateUsername}} </el-col>
          <el-col class="center" :span="11"> {{item.result}} </el-col>
          <el-col class="center" :span="5"> {{item.createTime}} </el-col>
        </el-row>
      </div>
    </vue-seamless-scroll>
  </div>
</template>

<script>
export default {
  props: {
    failData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      tableData: [],
    };
  },
  mounted() {},
  beforeDestroy() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.scrollTable {
  .title {
    border-bottom: 1px solid #f2f2f2;
    padding: 0 12px 12px;
  }
}
.scroll {
  overflow: hidden;
  height: 400px;
  .info {
    .infoWrap {
      padding: 12px;
      border-bottom: 1px solid #f2f2f2;
    }
    .infoWrap:hover {
background: #e6f7ff;
    }
  }
}
.center {
  text-align: center;
}
</style>
