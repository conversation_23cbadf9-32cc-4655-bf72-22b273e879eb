<template>
  <div class="analysis">
    <div class="topWrap">
      <div class="wrap">
        <div class="filter">
          <div class="top">
            <div class="left">
              <el-image
                style="width: 24px; height: 24px"
                class="img"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAmCAYAAACyAQkgAAAAAXNSR0IArs4c6QAAA2xJREFUWEftmEtoE1EUhv9z02RSn4siiFpfC820VCiILwQf+EJBsZOgGzeiBUGwKoKgaOsDRHzuBF2IKL4yC7sQRXyACIouhNJOsvBR6kKrBbEWM9P0Hpk2KU2bpplkTIN6d8m95/+/nDvn3jMhhNpKFdkVBng9inLQfdP6GSJFMx4CWFOUjEkowiMblIsaMgGXAsrASzAeFAU4YR0Bi/qTOjCjDDps6YGTxQDq0yKHCHwiLSiILpjhwN5iAFWCkfNgrvsP6tZuOM6oP9hyPGaJ02gMdLoFkY2OI1B/Tcs2JroG8AZTr7g/0MAXjIQEQ4npgevZGDtdkz1o7Ruv0jH2K4CJAD029cCqpJk/1DydpWi1P7PAPOuu2uQUZKT1GUGJ0BALq/W2iFITuQziHUlBFrLSulvZglCzT5EiCmBm3xx9L+nuDnQ1Vn0ZydzJvD9o1DPjaNqqJ9BtYnmhBzSbCDcGCZ8zdXW/UmM8A2FZ6hy/I8Z2AiwnMJnWMok6Bm9JfzxldKE2gF8DqHELxolOvnd9F4CxTgxzXZsHKO8W7L0nKW4/r2NyBcg2bjBolBmvBOAD0APAw4StQ8X4kqlX7LK/92rN1R6IAwB7wCSzNc60TgIWERYCmDtMMeFYTFf7K623+oPGGTD29weA78T0iv6H3A2wdBp+zWhg4MhwxdRb2SmBoeZxihTfbGYQHplhtSBNtqIZZwHsSw86TPfk04yrRFhthtWpfyqDg3Wzv5kGRI7fbJTBL6jz5lw7swUZOYEWhGyQyd8BShha9aORTdszY9UT41YPi1MlIu4fLUDbNy5LYh6SBwee4XncTIX9KTZoO4BJhbV17NZO3lB0gZDySaGaC8eIQJcUYiXZgaWbmsrZ490Jwhy2byCw0zu7ByAvwGttuQTML4AeAtxt9wzOAEkQYAIcpXj8yq97VW29oG4NRTM+ApiR0Gs1dTXxFpC/g9ugbQCmJbA+mbpanj9in4J7oMuflihlkz+kgHZ8noVnK+JuwP6DoHYfqBnvAcxKZPCDqauz3cjmkK0fszEyRfq4NidxSYKJ9wCYkIj/QUwXIRyfIL3hEhyxwhW3kiwpW+8PRZeylM9zAnU9iN6aeqA6LahXi8wXfa/Eoz8IjWZY3ZQWtDRoLJGMF6NPCdj/flu6ujjJ8hsoZYtIRHuRYQAAAABJRU5ErkJggg=="
              ></el-image>
              <span class="title"> 数据概览 </span>
            </div>
            <div class="right">
              <el-radio-group @input="handleChoose" v-model="filter.queryType">
                <el-radio-button label="today" border>今日</el-radio-button>
                <el-radio-button label="week" border>近七天</el-radio-button>
                <el-radio-button label="month" border>近一月</el-radio-button>
                <el-radio-button label="year" border>年度</el-radio-button>
              </el-radio-group>
              <div class="pickerWrap">
                <el-date-picker
                  v-model="value1"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleBlur"
                >
                </el-date-picker>
              </div>
            </div>
          </div>
        </div>
        <div class="bottomWrap">
          <div
            v-for="(item, index) in titleWrap"
            :class="
              index + 1 >= titleWrap.length ? 'content' : 'content border'
            "
            :key="index"
          >
            <div>{{ item.title }}</div>
            <div class="bottom">
              <el-image
                style="width: 35px; height: 35px"
                class="img"
                :src="item.img"
              ></el-image>
              <span>
                {{ item.number }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="lineWrap">
      <div class="echartsWrap">
        <div class="access">
          <div class="top">
            <div class="left">
              <el-image
                style="width: 24px; height: 24px"
                class="img"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAmCAYAAACyAQkgAAAAAXNSR0IArs4c6QAAA2xJREFUWEftmEtoE1EUhv9z02RSn4siiFpfC820VCiILwQf+EJBsZOgGzeiBUGwKoKgaOsDRHzuBF2IKL4yC7sQRXyACIouhNJOsvBR6kKrBbEWM9P0Hpk2KU2bpplkTIN6d8m95/+/nDvn3jMhhNpKFdkVBng9inLQfdP6GSJFMx4CWFOUjEkowiMblIsaMgGXAsrASzAeFAU4YR0Bi/qTOjCjDDps6YGTxQDq0yKHCHwiLSiILpjhwN5iAFWCkfNgrvsP6tZuOM6oP9hyPGaJ02gMdLoFkY2OI1B/Tcs2JroG8AZTr7g/0MAXjIQEQ4npgevZGDtdkz1o7Ruv0jH2K4CJAD029cCqpJk/1DydpWi1P7PAPOuu2uQUZKT1GUGJ0BALq/W2iFITuQziHUlBFrLSulvZglCzT5EiCmBm3xx9L+nuDnQ1Vn0ZydzJvD9o1DPjaNqqJ9BtYnmhBzSbCDcGCZ8zdXW/UmM8A2FZ6hy/I8Z2AiwnMJnWMok6Bm9JfzxldKE2gF8DqHELxolOvnd9F4CxTgxzXZsHKO8W7L0nKW4/r2NyBcg2bjBolBmvBOAD0APAw4StQ8X4kqlX7LK/92rN1R6IAwB7wCSzNc60TgIWERYCmDtMMeFYTFf7K623+oPGGTD29weA78T0iv6H3A2wdBp+zWhg4MhwxdRb2SmBoeZxihTfbGYQHplhtSBNtqIZZwHsSw86TPfk04yrRFhthtWpfyqDg3Wzv5kGRI7fbJTBL6jz5lw7swUZOYEWhGyQyd8BShha9aORTdszY9UT41YPi1MlIu4fLUDbNy5LYh6SBwee4XncTIX9KTZoO4BJhbV17NZO3lB0gZDySaGaC8eIQJcUYiXZgaWbmsrZ490Jwhy2byCw0zu7ByAvwGttuQTML4AeAtxt9wzOAEkQYAIcpXj8yq97VW29oG4NRTM+ApiR0Gs1dTXxFpC/g9ugbQCmJbA+mbpanj9in4J7oMuflihlkz+kgHZ8noVnK+JuwP6DoHYfqBnvAcxKZPCDqauz3cjmkK0fszEyRfq4NidxSYKJ9wCYkIj/QUwXIRyfIL3hEhyxwhW3kiwpW+8PRZeylM9zAnU9iN6aeqA6LahXi8wXfa/Eoz8IjWZY3ZQWtDRoLJGMF6NPCdj/flu6ujjJ8hsoZYtIRHuRYQAAAABJRU5ErkJggg=="
              ></el-image>
              <span class="title"> 访问趋势 </span>
            </div>
          </div>
          <Access :visitData="visitData"></Access>
        </div>
        <div class="accessTable">
          <div class="top">
            <div class="left">
              <el-image
                style="width: 24px; height: 24px"
                class="img"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAmCAYAAACyAQkgAAAAAXNSR0IArs4c6QAAA2xJREFUWEftmEtoE1EUhv9z02RSn4siiFpfC820VCiILwQf+EJBsZOgGzeiBUGwKoKgaOsDRHzuBF2IKL4yC7sQRXyACIouhNJOsvBR6kKrBbEWM9P0Hpk2KU2bpplkTIN6d8m95/+/nDvn3jMhhNpKFdkVBng9inLQfdP6GSJFMx4CWFOUjEkowiMblIsaMgGXAsrASzAeFAU4YR0Bi/qTOjCjDDps6YGTxQDq0yKHCHwiLSiILpjhwN5iAFWCkfNgrvsP6tZuOM6oP9hyPGaJ02gMdLoFkY2OI1B/Tcs2JroG8AZTr7g/0MAXjIQEQ4npgevZGDtdkz1o7Ruv0jH2K4CJAD029cCqpJk/1DydpWi1P7PAPOuu2uQUZKT1GUGJ0BALq/W2iFITuQziHUlBFrLSulvZglCzT5EiCmBm3xx9L+nuDnQ1Vn0ZydzJvD9o1DPjaNqqJ9BtYnmhBzSbCDcGCZ8zdXW/UmM8A2FZ6hy/I8Z2AiwnMJnWMok6Bm9JfzxldKE2gF8DqHELxolOvnd9F4CxTgxzXZsHKO8W7L0nKW4/r2NyBcg2bjBolBmvBOAD0APAw4StQ8X4kqlX7LK/92rN1R6IAwB7wCSzNc60TgIWERYCmDtMMeFYTFf7K623+oPGGTD29weA78T0iv6H3A2wdBp+zWhg4MhwxdRb2SmBoeZxihTfbGYQHplhtSBNtqIZZwHsSw86TPfk04yrRFhthtWpfyqDg3Wzv5kGRI7fbJTBL6jz5lw7swUZOYEWhGyQyd8BShha9aORTdszY9UT41YPi1MlIu4fLUDbNy5LYh6SBwee4XncTIX9KTZoO4BJhbV17NZO3lB0gZDySaGaC8eIQJcUYiXZgaWbmsrZ490Jwhy2byCw0zu7ByAvwGttuQTML4AeAtxt9wzOAEkQYAIcpXj8yq97VW29oG4NRTM+ApiR0Gs1dTXxFpC/g9ugbQCmJbA+mbpanj9in4J7oMuflihlkz+kgHZ8noVnK+JuwP6DoHYfqBnvAcxKZPCDqauz3cjmkK0fszEyRfq4NidxSYKJ9wCYkIj/QUwXIRyfIL3hEhyxwhW3kiwpW+8PRZeylM9zAnU9iN6aeqA6LahXi8wXfa/Eoz8IjWZY3ZQWtDRoLJGMF6NPCdj/flu6ujjJ8hsoZYtIRHuRYQAAAABJRU5ErkJggg=="
              ></el-image>
              <span class="title"> 用户访问TOP10 </span>
            </div>
          </div>
          <div class="bottom">
            <AccessTable :topTenData="topTenData"></AccessTable>
          </div>
        </div>
      </div>
    </div>

    <div class="lineWrap">
      <div class="echartsWrap">
        <div class="access">
          <div class="top">
            <div class="left">
              <el-image
                style="width: 24px; height: 24px"
                class="img"
                src="data:image/png;base64,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"
              ></el-image>
              <span class="title"> 认证趋势 </span>
            </div>
          </div>
          <Authentication :authData="authData"></Authentication>
        </div>
        <div class="accessTable">
          <div class="top">
            <div class="left">
              <el-image
                style="width: 24px; height: 24px"
                class="img"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAAAXNSR0IArs4c6QAABRZJREFUWEftmWtsFFUUx/9ndjszaDVpImpi0CCUzmxB4wONyAdBY0JApDtdg02E+gCjBDUYEY2RD0Y0UYMSrURUoCExZju7hBgxMSo+YtAQImB3trRJI8TgCwEtZe+2e4+Zbbdst7M77boUY3q/7Zxz5/zu/94599y7BABao/MwGE8CuNz9fR5aDzG1pmLGumKxSW9M3s/M758HOK+QrwrbfMrLQJrl/A2g+j8CCpFJ1WDndScLeVzQPgDBAQOdBPF+MJTxAacMwFcBmJ6Lx4qsT0frE16gfwG4aIATn4o2887xgRyIooedZiZsHYopUSfi5uHSoMAeYZvzxhNUsxKrAHrzbEyeIexQZ2lQxpciZt42vqDJxwB+YwK0UqprVoUU1ZZ0TuNg/7UABcAsywJkIiggZnT32ca+/HdUBFS12usJyo9lwRXpRMDKlG1uyZkrAnqOdq39wjZvqCioaiWvIfCBSioK0CPCNjZXFNR9mdbgzCCiK7IvVpjLh5aBTCb4R1+8btjAKzL15UONvucE6Oi1Gp3nhKKj02n0XpVRtLlbV3vOPA7GTCW7M6H0zkSgbLHI6KaA0nImWvezH7IWdlaDsOlfFSVaOLEQRB/5BfOyE/Byyjaf8eurNjrPEuPFnB9LnpWOh0bshm6Ff7ZwLijzVCt5D4E/9AvmaWdqETFjlV9f3Uo+wOD3cn5VSvDSnmjt72OrR5sO1uhCfZvB7pYXAPyKEhqYekIXZ3iNlzIjwO87cKHeq25hkEnAaynb2OE5Q6UU9VNjPO0lp348QfxiTYD6KTRW+/9I0eZuXTuVurKwzGMEAulM7y9etxp+aumR5NRMhi4JgKsRoD4p+0+k+wJHsMtwb208W0lFJ919aIoMBr8vcXl2ipgXp2Khr/zgEDk6SeOeVWA8CMAY6U8nmTmOADamo+ahMeVR3XKWM7DNB+ILYZvzS/lkkzrxBjAuy/PrBqEbjMkAZg3rT4iLar0J26amcs9LKxpxbpES35YGpbXCNl4p5qNZiRb3+JFn74CkJ0Tc+GRoNwp3zFZIPg9gUZ5fF0MuSdv17e4z349JDTtNRLhxcGcafhQhHBZtoZZikGrY2U6EZUN2wq8iTbXF1qJqOXsJuHlIReCEhDInbdclfUF9114RBz3sfMCEpflmIl6Ragu96z7TGp1NYNwF0A/i+LEI9szrd89nUNBR8MoUS559TkA1y3kHwIqCgKeFUj0Z0SlnVMtZR8BLeUp/LtqMOwBizXK+AXBrQd+fKg/adLBGE1V/enzVR4VtZNOcZiW6AJqW7yN6gzp21wrVcrYS0Dymr76saV/QqWkX9H/moUq6SlZN6YlP/01vTDzETEO3JcxoTcfM5QODcNzUNDM/NgPrhysK+ljYxsKyAAs66eHEd0x00/CA9FzaNrJFshpOrCVSFgF8QNjmaveZHumYy1J+PSJVtZlhF9TNVdqg8QgxbYfCQUgqfj1OzEykQMpjIhZ63XNgK/dVacerdwN8e569hxXM8UroiLSrmlQcAFef9aedwjYacumpfyD1lNnc63QyFiDq3sePbINfd1axwSaYeH1QorU3FjqGSHu1zrSUmZ7Ov8sHsFHY5ppcJ9IbnQ3M8D3blBqGUKSGaH26mI9qOfcS86Mgmlvg4x6DLi5Yj3sV0FuFlb57dIAWdjaDsLiMv3FOg/iFUkk/H0JvSM6XCi8j4HoAte6yBOBuIm7uPASmHSJm7PIa8D9CQOiR6SBckwAAAABJRU5ErkJggg=="
              ></el-image>
              <span class="title"> 认证失败情况统计 </span>
            </div>
          </div>
          <div class="bottom">
            <FailTable class="table" :failData="failData"></FailTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Access from "./components/Access";
import Authentication from "./components/Authentication";
import AccessTable from "./components/AccessTable";
import FailTable from "./components/FailTable";
import {
  getSummary,
  getTop10,
  getVisit,
  getAuth,
  getLoginPage,
} from "@/api/admin/dataAnalysis";
import json from "highlight.js/lib/languages/json";
export default {
  components: {
    Access,
    Authentication,
    AccessTable,
    FailTable,
  },
  data() {
    return {
      filterWrap: [
        {
          title: "今日",
        },
        {
          title: "近七天",
        },
        {
          title: "月度",
        },
        {
          title: "年度",
        },
        {
          title: "自定义",
        },
      ],
      filter: {
        queryType: "today",
        startDate: "",
        endDate: "",
      },
      titleWrap: [
        {
          title: "用户总数",
          number: 0,
          class: "",
          img: require("@/assets/one.png"),
          type: "userTotal",
        },
        {
          title: "新增用户数",
          number: 0,
          img: require("@/assets/two.png"),
          type: "userNew",
        },
        {
          title: "活跃用户数",
          number: 0,
          img: require("@/assets/three.png"),
          type: "userActive",
        },
        {
          title: "访问量",
          number: 0,
          img: require("@/assets/four.png"),
          type: "visitCno",
        },
        {
          title: "部门数量",
          number: 0,
          img: require("@/assets/five.png"),
          type: "deptCno",
        },
        {
          title: "角色数量",
          number: 0,
          img: require("@/assets/one.png"),
          type: "roleCno",
        },
      ],
      value1: "",
      authData: [],
      visitData: [],
      topTenData: [],
      failData: [],
    };
  },
  computed: {},
  mounted() {
    this.getData();
    this.getLoginPage();
  },
  methods: {
    async getLoginPage() {
      try {
        let res = await getLoginPage({
          current: 1,
          size: 20,
          operateType: 0,
          isFaild: 1,
        });

        this.failData = res.data.data.records;
      } catch (error) {}
    },
    handleChoose(value) {
      this.value1 = "";
      this.filter.queryType = value;
      this.filter.startDate = "";
      this.filter.endDate = "";
      this.getData();
    },
    handleBlur(value) {
      this.filter.queryType = "custom";
      this.filter.startDate = this.value1[0];
      this.filter.endDate = this.value1[1];
      this.getData();
    },
    getData() {
      this.getSummary();
      this.getTop10();
      this.getAuth();
      this.getVisit();
    },
    async getSummary() {
      try {
        let res = await getSummary(this.filter);

        //后台返回的数据给titleWrap对应的number进行赋值
        this.titleWrap.forEach((item) => {
          item.number = res.data.data[item.type];
        });
      } catch (error) {}
    },
    async getTop10() {
      try {
        let res = await getTop10(this.filter);

        this.topTenData = res.data.data;
      } catch (error) {}
    },
    async getAuth() {
      try {
        let res = await getAuth(this.filter);
        this.authData = res.data.data;
      } catch (error) {}
    },
    async getVisit() {
      try {
        let res = await getVisit(this.filter);
        this.visitData = res.data.data;
      } catch (error) {}
    },
  },
};
</script>

<style lang="scss" scoped>
.analysis {
  width: 100%;
  height: calc(100vh - 106px);
  overflow-y: scroll;
  padding: 20px;
  box-sizing: border-box;
  .topWrap {
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    box-sizing: border-box;

    .wrap {
      width: 100%;
      .filter {
        .top {
          width: 100%;
          padding: 20px;
          box-sizing: border-box;
          background: #ffffff;
          border-bottom: 1px solid #f2f2f2;
          font-size: 24px;
          font-weight: bold;
          display: flex;
          justify-content: space-between;
          .left {
            display: flex;
            align-items: center;
            .img {
              margin-right: 10px;
            }
            .title {
              margin-right: 20px;
            }
          }
          .right {
            display: flex;
            align-items: center;
            .pickerWrap {
              margin-left: 10px;
            }
          }
        }
      }
      .bottomWrap {
        display: flex;
        padding: 20px 10px;
        justify-content: space-between;
        .content {
          width: calc(16%);
          box-sizing: border-box;
          padding: 10px;
          .bottom {
            margin-top: 10px;
            display: flex;
            align-items: center;
            font-size: 30px;
            font-weight: bold;
            .img {
              margin-right: 20px;
            }
          }
        }
        .border {
          border-right: 1px solid #f2f2f2;
        }
      }
    }
  }
  .lineWrap {
    margin-top: 20px;
    border-radius: 8px;
    width: 100%;

    .echartsWrap {
      display: flex;
      width: 100%;
      height: 480px;
      .access {
        width: 60%;
        margin-right: 20px;
        border-radius: 8px;
        overflow: hidden;
        background: #ffffff;
        .top {
          width: 100%;
          padding: 20px;
          box-sizing: border-box;
          background: #ffffff;
          border-bottom: 1px solid #f2f2f2;
          font-size: 24px;
          font-weight: bold;
          display: flex;
          justify-content: space-between;
          .left {
            display: flex;
            align-items: center;
            .img {
              margin-right: 10px;
            }
            .title {
              margin-right: 20px;
            }
          }
        }
      }
      .accessTable {
        width: 40%;
        border-radius: 8px;
        overflow: hidden;
        background: #ffffff;
        .top {
          width: 100%;
          padding: 20px;
          box-sizing: border-box;
          background: #ffffff;
          border-bottom: 1px solid #f2f2f2;
          font-size: 24px;
          font-weight: bold;
          display: flex;
          justify-content: space-between;
          .left {
            display: flex;
            align-items: center;
            .img {
              margin-right: 10px;
            }
            .title {
              margin-right: 20px;
            }
          }
        }
        .bottom {
          margin: 20px;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
