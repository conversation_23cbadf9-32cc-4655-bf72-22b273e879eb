<template>
  <div>
    <el-form
      :model="form"
      ref="form"
      label-width="110px"
      :rules="rules"
      class="demo-ruleForm"
    >
      <!-- <el-form-item label="行政区划等级" prop="areaLevel">
        <el-select
          style="width: 100%"
          disabled
          v-model="form.areaLevel"
          placeholder="请选择"
        >
          <el-option
            v-for="item in levelOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <div class="row-block">
        <el-form-item label="组织全称" prop="poolareaNoArr">
          <el-cascader
            ref="poolareaNoRef"
            v-model="form.poolareaNoArr"
            :props="selfProps"
            :show-all-levels="true"
            clearable
            @change="changeCasVal"
            class="in-s2"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="规范简称" prop="normName">
          <el-input v-model="form.normName" placeholder="请输入规范简称">
          </el-input>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="习惯简称" prop="usualName">
          <el-input v-model="form.usualName" placeholder="请输入习惯简称">
          </el-input>
        </el-form-item>
        <el-form-item label="组织域名" prop="domainName">
          <el-input v-model="form.domainName" placeholder="请输入组织域名">
          </el-input>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="区域代码" prop="areaCode">
          <el-input v-model="form.areaCode" disabled> </el-input>
        </el-form-item>
        <el-form-item label="区域规格" prop="unit" placeholder="请输入区域规格">
          <!-- <el-input v-model="form.unit"> </el-input> -->

          <el-select
            style="width: 100%"
            v-model="form.unit"
            placeholder="请选择区域规格"
          >
            <el-option
              v-for="item in areaArr"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item
          label="客户端显示该组织"
          prop="clientShow"
          label-width="130"
          placeholder="请选择客户端显示该组织"
        >
          <el-switch
            v-model="form.clientShow"
            active-value="1"
            inactive-value="0"
          >
          </el-switch>
          <!-- <el-radio-group v-model="form.clientShow">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <!-- <el-switch v-model="form.status" active-value="1" inactive-value="0">
        </el-switch> -->
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <el-form-item label="排序" prop="orderNum">
        <el-input-number
          v-model="form.orderNum"
          controls-position="right"
          :min="1"
          class="in-s2"
        ></el-input-number>
      </el-form-item>
      <!-- <el-form-item label="管理员" prop="leader">
        <el-select
          v-model="form.leader"
          filterable
          placeholder="请选择管理员"
          class="in-s2"
          clearable
        >
          <el-option
            v-for="item in userAllArr"
            :key="item.id"
            :label="item.nickName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <!-- <el-input v-model="form.leader" placeholder="请输入管理员"> </el-input> -->
      <el-form-item label="备注信息" prop="remark">
        <el-input
          type="textarea"
          v-model="form.remark"
          placeholder="请输入备注信息"
        >
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          v-loading="loading"
          type="primary"
          @click="submitForm('form')"
          >提交</el-button
        >
        <!-- <el-button @click="resetForm('form')">重置</el-button> -->
        <el-button @click="closeDialog">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import formMixin from "./formMixin";
import { addObj, putObj } from "@/api/admin/sys/dept";
export default {
  mixins: [formMixin],
  data() {
    return {
      rules: {
        poolareaNoArr: [
          { required: true, message: "请选择组织全称", trigger: "change" },
        ],
        normName: [
          { required: true, message: "请输入规范简称", trigger: "blur" },
        ],
        areaCode: [
          { required: true, message: "请输入区划代码", trigger: "change" },
        ],
        unit: [
          { required: true, message: "请选择区划规格", trigger: "change" },
        ],
        clientShow: [
          {
            required: true,
            message: "请选择客户端显示该组织",
            trigger: "change",
          },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
    };
  },
  props: {
    areaArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  methods: {
    closeDialog() {
      this.$emit("closeDialog");
    },
    handleDealEdit(row) {
      this.form = Object.assign(this.form, row);
      console.log("row=222==", this.form);
      if (this.form.deptName) {
        this.form.poolareaNoArr = [{ areaName: this.form.deptName }];

        setTimeout(() => {
          this.$refs.poolareaNoRef.presentText = this.form.deptName;
        }, 200);
      }
    },
    handleAddSonData(row) {
      this.form.parentId = row.id;
    },
    submitForm(formName) {
      console.log("submitForm");
      let that = this;
      this.$refs[formName].validate(async (valid) => {
        console.log("valid---", this.form);
        if (valid) {
          this.form.type = 0;
          try {
            if (this.form.poolareaNoArr && this.form.poolareaNoArr.length > 0) {
              this.form.deptName =
                this.form.poolareaNoArr[
                  this.form.poolareaNoArr.length - 1
                ].areaName;
            }

            console.log("jin-----", this.form);
            if (this.form.id) {
              let res = await putObj(this.form);
              if (res.data.code == 200) {
                that.$message.success("修改成功");
                this.$emit("sureCloseDialog", this.form);
              }
            } else {
              console.log("jin2-----", this.form);
              let res2 = await addObj(this.form);
              // console.log("新增--", res2);
              if (res2.data.code == 200) {
                that.$message.success("新建成功");
                this.$emit("sureCloseDialog", this.form);
              }
            }
          } catch (error) {
            // console.log("error==", error);
          }
        } else {
          return false;
        }
      });
    },
    changeCasVal(arr) {
      // console.log("data=lz=", arr);
      if (arr && arr.length > 0) {
        this.form.areaCode = arr[arr.length - 1].areaCode;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./form.scss";
::v-deep .el-form-item {
  width: 48%;
  margin-right: 12px;
}
</style>