<template>
  <el-card class="box-card">
    <div class="text item">
      <el-row>
        <el-col :span="8">
          <span class="title">节点类型：</span>
          <span class="text">{{
            currentRow.type == 0
              ? "区划"
              : currentRow.type == 1
              ? "类别"
              : currentRow.type == 2
              ? "机构"
              : ""
          }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织全称：</span>
          <span class="text">{{ currentRow.deptName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">规范简称：</span>
          <span class="text">{{ currentRow.normName }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">习惯简称：</span>
          <span class="text">{{ currentRow.usualName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织域名：</span>
          <span class="text">{{ currentRow.domainName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织编码：</span>
          <span class="text">{{ currentRow.orgCode }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">区域代码：</span>
          <span class="text">{{ currentRow.areaCode }}</span>
        </el-col>
        <el-col :span="8" v-if="currentRow.type == 2">
          <span class="title">机构规格：</span>
          <span class="text">{{ getJigouGuige(currentRow.unit) }}</span>
        </el-col>
        <el-col :span="8" v-else>
          <span class="title">区域规格：</span>
          <span class="text">{{ getGuige(currentRow.unit) }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">客户端显示该组织：</span>
          <span class="text">{{
            currentRow.clientShow | showWordYesOrNo
          }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">管理员：</span>
          <span class="text">{{ getUsersName(currentRow.leader) }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">备注信息：</span>
          <span class="text">{{ currentRow.remark }}</span>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script>
import { mapGetters } from "vuex";
import infoMixin from "./infoMixin";
export default {
  name: "info",
  mixins: [infoMixin],
  props: {
    // currentRow: {
    //   type: Object,
    //   default: function () {
    //     return {};
    //   },
    // },
    userAllArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    // orgStandardsArr: {
    //   type: Array,
    //   default: function () {
    //     return [];
    //   },
    // },
  },

  computed: {
    ...mapGetters(["permissions"]),
  },
  // filters: {
  //   yesOrNo(val) {
  //     let result = "";
  //     if (val == 0) {
  //       result = "否";
  //     } else if (val == 1) {
  //       result = "是";
  //     }
  //     return result;
  //   },
  // },
  methods: {
    getUsersName(val) {
      let result = "";
      if (val) {
        let fObj = this.userAllArr.find((item) => {
          return item.id == val;
        });
        if (fObj) {
          return fObj.nickName;
        }
      }
      return result;
    },
    // getGuige(val) {
    //   let result = "";
    //   let fObj = this.areaArr.find((item) => {
    //     return item.dictValue == val;
    //   });
    //   if (fObj) {
    //     return fObj.dictLabel;
    //   }
    //   return result;
    // },
    // getJigouGuige(val) {
    //   let result = "";
    //   let fObj = this.orgStandardsArr.find((item) => {
    //     return item.dictValue == val;
    //   });
    //   if (fObj) {
    //     return fObj.dictLabel;
    //   }
    //   return result;
    // },
    convertLevel(level) {
      switch (level) {
        case "1":
          return "省级";
        case "2":
          return "市级";
        case "3":
          return "区县级";
        case "4":
          return "乡镇级";
        case "5":
          return "村居级";
      }
    },
    addOrUpdateHandle() {
      this.editDep();
    },
    handleDelete() {
      this.delDep();
    },
  },
};
</script>

<style scoped lang="scss">
.box-card {
  .title {
    font-size: 14px;
    font-weight: bold;
  }

  .text {
    font-size: 14px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both;
  }
}
.pos-content {
  display: flex;
  align-items: center;
  padding-top: 1px;
}
</style>
