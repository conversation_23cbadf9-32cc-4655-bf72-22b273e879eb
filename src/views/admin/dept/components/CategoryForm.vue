<template>
  <div>
    <el-form
      :model="form"
      ref="form"
      label-width="130px"
      :rules="rules"
      class="demo-ruleForm"
    >
      <div class="row-block">
        <el-form-item label="组织全称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入组织全称">
          </el-input>
          <!-- <el-cascader
            ref="poolareaNoRef"
            v-model="form.poolareaNoArr"
            :props="selfProps"
            :show-all-levels="true"
            clearable
            class="in-s2"
          ></el-cascader> -->
          <!-- <el-select
            v-model="form.orgFullName"
            placeholder="请选择"
            class="in-s2"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="规范简称" prop="normName">
          <el-input v-model="form.normName" placeholder="请输入规范简称">
          </el-input>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="习惯简称" prop="usualName">
          <el-input v-model="form.usualName" placeholder="请输入习惯简称">
          </el-input>
        </el-form-item>
        <el-form-item label="子类别" prop="subType">
          <el-select
            v-model="form.subType"
            placeholder="请选择子类别"
            class="in-s2"
          >
            <el-option
              v-for="item in sonCategoryArr"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="组织域名" prop="domainName">
          <el-input v-model="form.domainName" placeholder="请输入组织域名">
          </el-input>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number
            v-model="form.orderNum"
            controls-position="right"
            :min="1"
            class="in-s2"
          ></el-input-number>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item
          label="客户端显示该组织"
          prop="clientShow"
          label-width="130"
        >
          <el-switch
            v-model="form.clientShow"
            active-value="1"
            inactive-value="0"
          >
          </el-switch>
          <!-- <el-radio-group v-model="form.clientShow">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <!-- <el-switch v-model="form.status" active-value="1" inactive-value="0">
        </el-switch> -->
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <el-form-item label="备注信息" prop="remark">
        <el-input
          type="textarea"
          v-model="form.remark"
          placeholder="请输入备注信息"
        >
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          v-loading="loading"
          type="primary"
          @click="submitForm('form')"
          >提交</el-button
        >
        <!-- <el-button @click="resetForm('form')">重置</el-button> -->
        <el-button @click="closeDialog">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import formMixin from "./formMixin";
import { addObj, putObj } from "@/api/admin/sys/dept";
export default {
  mixins: [formMixin],
  data() {
    return {
      rules: {
        deptName: [
          { required: true, message: "请输入组织全称", trigger: "blur" },
        ],
        normName: [
          { required: true, message: "请输入规范简称", trigger: "blur" },
        ],
        subType: [
          { required: true, message: "请选择子类别", trigger: "change" },
        ],
        clientShow: [
          { required: true, message: "请选择客户端组织", trigger: "change" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
    };
  },

  props: {
    sonCategoryArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  methods: {
    handleDealEdit(row) {
      this.form = Object.assign(this.form, row);
      console.log("row=222==", this.form);
      // if (this.form.deptName) {
      //   this.form.poolareaNoArr = [{ areaName: this.form.deptName }];

      //   setTimeout(() => {
      //     this.$refs.poolareaNoRef.presentText = this.form.deptName;
      //   }, 200);

      //   console.log(
      //     "this.$refs.poolareaNoRef.presentText==",
      //     this.$refs.poolareaNoRef.presentText
      //   );
      // }
    },
    handleAddSonData(row) {
      this.form.parentId = row.id;
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.form.type = 1;
          let that = this;
          try {
            // if (this.form.poolareaNoArr && this.form.poolareaNoArr.length > 0) {
            //   this.form.deptName =
            //     this.form.poolareaNoArr[
            //       this.form.poolareaNoArr.length - 1
            //     ].areaName;
            // }
            // console.log("jin-----", this.form);
            if (this.form.id) {
              let res = await putObj(this.form);
              if (res.data.code == 200) {
                that.$message.success("修改成功");
                this.$emit("sureCloseDialog", this.form);
              }
            } else {
              console.log("jin2-----", this.form);
              let res2 = await addObj(this.form);
              console.log("新增--", res2);
              if (res2.data.code == 200) {
                that.$message.success("新建成功", this.form);
                this.$emit("sureCloseDialog");
              }
            }
          } catch (error) {}
        } else {
          return false;
        }
      });
    },
    closeDialog() {
      this.$emit("closeDialog");
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./form.scss";
::v-deep .el-form-item {
  width: 48%;
  margin-right: 12px;
}
</style>