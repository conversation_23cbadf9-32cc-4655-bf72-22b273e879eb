<template>
  <el-card class="box-card">
    <div class="text item">
      <el-row>
        <el-col :span="8">
          <span class="title">节点类型：</span>
          <span class="text">{{
            currentRow.type == 0
              ? "区划"
              : currentRow.type == 1
              ? "类别"
              : currentRow.type == 2
              ? "机构"
              : ""
          }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织全称：</span>
          <span class="text">{{ currentRow.deptName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">规范简称：</span>
          <span class="text">{{ currentRow.normName }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">子类别：</span>
          <span class="text">{{ getSonCategor(currentRow.subType) }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织域名：</span>
          <span class="text">{{ currentRow.domainName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织编码：</span>
          <span class="text">{{ currentRow.orgCode }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">客户端显示该组织：</span>
          <span class="text">{{
            currentRow.clientShow | showWordYesOrNo
          }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">习惯简称：</span>
          <span class="text">{{ currentRow.usualName }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">备注信息：</span>
          <span class="text">{{ currentRow.remark }}</span>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script>
import infoMixin from "./infoMixin";
export default {
  name: "info",
  mixins: [infoMixin],
  methods: {
    convertLevel(level) {
      switch (level) {
        case "1":
          return "省级";
        case "2":
          return "市级";
        case "3":
          return "区县级";
        case "4":
          return "乡镇级";
        case "5":
          return "村居级";
      }
    },
    convertStatus(status) {
      switch (status) {
        case "1":
          return "启用";
        case "0":
          return "禁用";
      }
    },
    addOrUpdateHandle() {
      this.editArea();
    },
    handleDelete() {
      this.delArea();
    },
  },
};
</script>

<style scoped lang="scss">
.box-card {
  .title {
    font-size: 14px;
    font-weight: bold;
  }

  .text {
    font-size: 14px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both;
  }
}
</style>
