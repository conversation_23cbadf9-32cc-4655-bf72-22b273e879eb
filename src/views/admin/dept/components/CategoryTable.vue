<template>
  <div>
    <!--  :before-open="handleBeforeOpen" -->
    <avue-crud
      :option="tableOption"
      ref="crud"
      v-model="form"
      :page.sync="page"
      @size-change="sizeChange"
      @current-change="currentChange"
      :table-loading="listLoading"
      @search-change="handleFilter"
      @refresh-change="handleRefreshChange"
      :data="list"
    >
      <!-- 左边插槽 -->
      <!-- v-if="userRole && userRole.parentId == '-2'" -->
      <template slot-scope="scope" slot="menuLeft">
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="handleAddValue(false)"
          v-if="permissions.sys_dept_son_add"
          >新增</el-button
        >
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-edit"
          @click="handleEdit(scope.row)"
          v-if="permissions.sys_dept_edit"
          >编辑</el-button
        >
        <!--  :size="size" -->
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="handleDelete(scope.row)"
          v-if="permissions.sys_dept_del"
          >删除</el-button
        >
      </template>
      <!-- <template slot-scope="scope" slot="areaLevel">
        <div>{{ filterAreaLevel(scope.row) }}</div>
      </template> -->
    </avue-crud>
    <form-dialog
      v-if="addOrUpdateVisible"
      @closeDialog="handleCloseDialog"
      @sureCloseDialog="sureCloseDialog"
      :currentRow="currentRow"
      :areaArr="areaArr"
      :orgStandardsArr="orgStandardsArr"
      :sonCategoryArr="sonCategoryArr"
      ref="addOrUpdate"
    ></form-dialog>
  </div>
</template>
<script>
import { tableOption } from "@/const/crud/admin/sys-category-table";
import { delObj, fetchTree, getSonDeptById } from "@/api/admin/sys/dept";
import { addObj, putObj } from "@/api/admin/sys/area";
import FormDialog from "./FormDialog.vue";
import { mapGetters } from "vuex";
export default {
  components: { FormDialog },
  data() {
    return {
      tableLoading: false,
      listLoading: false,
      tableOption: tableOption,
      list: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条,
        isAsc: false, //是否倒序
      },
      form: {},
      addOrUpdateVisible: false,
      currentRow: {},
    };
  },
  props: {
    parentRow: {
      type: Object,
      default: function () {
        return {};
      },
    },
    treeDeptData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    areaArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    orgStandardsArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    sonCategoryArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  computed: {
    ...mapGetters(["permissions", "userRole"]),
  },
  methods: {
    handleAddValue() {
      // console.log("parentRow==", this.parentRow);
      if (this.parentRow && this.parentRow.id) {
        // this.$emit("addAreaFn");
        this.addOrUpdateVisible = true;
        this.$nextTick(() => {
          this.$refs.addOrUpdate.handleAddSonData(this.parentRow, "add");
        });
      } else {
        this.$message({
          showClose: true,
          message: "请先选择左侧机构列表",
          type: "warning",
        });
      }
    },
    handleCloseDialog() {
      this.addOrUpdateVisible = false;
      this.$emit("closeTwoFormlDialog");
    },
    sureCloseDialog(form) {
      this.addOrUpdateVisible = false;
      this.$emit("closeSureTwoFormlDialog", form);
    },
    handleEdit(row) {
      // console.log("row==", row);
      this.currentRow = row;
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.handleEditData(this.currentRow, "edit");
      });
    },
    handleFilter(param, done) {
      this.query = param;
      this.page.currentPage = 1;
      this.getList(this.page, param);
      done();
    },
    refGetList() {
      this.getList(this.page);
      // console.log("this.treeDeptData==", this.treeDeptData);
      // console.log("this.currentRow==", this.currentRow);
    },
    handleRefreshChange() {
      this.getList();
    },
    getList(page, params) {
      this.rightDateInfo = {};
      this.listLoading = true;
      let newParams = Object.assign(
        {
          current: page.currentPage,
          size: page.pageSize,
          parentId: this.parentRow.id,
          type: 1,
        },
        params,
        this.searchForm
      );

      getSonDeptById(newParams).then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data.records;
          this.page.total = res.data.data.total;
        }
      });
      // this.getArrFromId(this.treeDeptData);
      // this.list = [];
      // if (this.rightDateInfo.children) {
      //   let showArr = JSON.parse(JSON.stringify(this.rightDateInfo.children));
      //   //是否有搜索
      //   if (params && params.name) {
      //     let resultArr = [];
      //     for (let index = 0; index < showArr.length; index++) {
      //       if (this.handleDeptfilterNode(params.name, showArr[index])) {
      //         resultArr.push(showArr[index]);
      //       }
      //     }
      //     showArr = resultArr;
      //   }
      //   let startIndex = (this.page.currentPage - 1) * this.page.pageSize;
      //   let endIndex =
      //     (this.page.currentPage - 1) * this.page.pageSize + this.page.pageSize;
      //   let listArr = showArr.slice(startIndex, endIndex);
      //   this.list = listArr.filter((item) => {
      //     return item.type == "1";
      //   });
      //   this.page.total = this.list.length;
      // } else {
      //   this.page.total = 0;
      // }
      this.listLoading = false;
    },
    getArrFromId(arr) {
      if (!arr) return;
      for (let index = 0; index < arr.length; index++) {
        var data = arr[index];
        if (this.parentRow.id === data.id) {
          this.rightDateInfo = JSON.parse(JSON.stringify(data));
          if (this.rightDateInfo.children) {
            let arr = JSON.parse(JSON.stringify(this.rightDateInfo.children));
            for (let i in arr) {
              arr[i].children = [];
            }
            this.rightDateInfo.children = arr;
          }
          return;
        }
      }
      for (let index = 0; index < arr.length; index++) {
        this.getArrFromId(arr[index].children);
      }
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.getList(this.page);
    },
    currentChange(current) {
      this.page.currentPage = current;
      this.getList(this.page);
    },
    // getList(area) {
    //   if (!area) {
    //     return;
    //   }
    //   this.tableLoading = true;
    //   let query = {
    //     id: area.id,
    //   };
    //   fetchChildren(query)
    //     .then((response) => {
    //       this.tableData = response.data.data;
    //       this.tableLoading = false;
    //       this.itemPage.total = response.data.data.length;
    //     })
    //     .catch(() => {
    //       this.tableLoading = false;
    //     });
    // },
    // rowSave(form, done) {
    //   this.tableLoading = true;
    //   if (this.leftAdd) {
    //     form.parentId = "0";
    //   } else {
    //     form.parentId = this.selectedArea.id;
    //   }
    //   addObj(form)
    //     .then((res) => {
    //       this.tableLoading = false;
    //       done(form);
    //       this.refreshProvince();
    //     })
    //     .catch(() => {
    //       this.tableLoading = false;
    //     });
    // },
    rowEdit(form, index, done) {
      //改变头部信息
      if (index === -1) {
        this.selectedArea = form;
      }
      this.tableLoading = true;
      putObj(form)
        .then((res) => {
          this.refreshProvince();
          this.getList(this.selectedArea);
          done();
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    handleDelete(row) {
      this.$confirm(
        '是否确认删除名称为"' + row.deptName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delObj(row.id);
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("删除成功");
            this.$emit("closeTwoFormlDialog");
          } else {
            this.$message.error(res.data.msg);
          }
        });
    },
    rowDel(form) {
      this.$confirm("你确定要删除该区划吗?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true;
          delObj(form.id)
            .then((res) => {
              this.refreshProvince();
            })
            .catch(() => {
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },
    // handleFilter(param, done) {
    //   this.tableLoading = true;
    //   let query = param;
    //   (query.id = this.selectedArea.id),
    //     fetchChildren(query)
    //       .then((response) => {
    //         this.tableData = response.data.data;
    //         this.tableLoading = false;
    //         this.itemPage.total = response.data.data.length;
    //       })
    //       .catch(() => {
    //         this.tableLoading = false;
    //       });
    //   done();
    // },

    // handleBeforeOpen(done, type) {
    //   if (type === "add") {
    //     const column = this.findObject(this.tableOption.column, "areaLevel");
    //     if (this.leftAdd) {
    //       column.dicData = [{ dictValue: "1", dictLabel: "省级" }];
    //     } else {
    //       if (this.selectedArea.areaLevel === "1") {
    //         column.dicData = [{ dictValue: "2", dictLabel: "市级" }];
    //       } else if (this.selectedArea.areaLevel === "2") {
    //         column.dicData = [{ dictValue: "3", dictLabel: "区县级" }];
    //       } else if (this.selectedArea.areaLevel === "3") {
    //         column.dicData = [{ dictValue: "4", dictLabel: "乡镇级" }];
    //       } else if (this.selectedArea.areaLevel === "4") {
    //         column.dicData = [{ dictValue: "5", dictLabel: "村居级" }];
    //       }
    //     }
    //   } else if (type === "edit") {
    //     const column = this.findObject(this.tableOption.column, "areaLevel");
    //     if (this.selectedArea.areaLevel === "1") {
    //       column.dicData = [{ dictValue: "1", dictLabel: "省级" }];
    //     } else if (this.selectedArea.areaLevel === "2") {
    //       column.dicData = [{ dictValue: "2", dictLabel: "市级" }];
    //     } else if (this.selectedArea.areaLevel === "3") {
    //       column.dicData = [{ dictValue: "3", dictLabel: "区县级" }];
    //     } else if (this.selectedArea.areaLevel === "4") {
    //       column.dicData = [{ dictValue: "4", dictLabel: "乡镇级" }];
    //     } else if (this.selectedArea.areaLevel === "5") {
    //       column.dicData = [{ dictValue: "5", dictLabel: "村居级" }];
    //     }
    //   }
    //   done();
    // },
  },
};
</script>
<style lang="scss" scoped>
</style>