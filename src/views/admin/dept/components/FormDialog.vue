<template>
  <el-dialog
    :title="formType == 'edit' ? '编辑' : '新增'"
    :visible.sync="dialogVisible"
    width="1000px"
    :before-close="closeDialog"
  >
    <!--  :before-leave="handleBeforeLeave" -->
    <template v-if="formType == 'edit'">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="区划" name="1" v-if="editForm.type == '0'">
          <area-form
            ref="areaFormRef"
            :areaArr="areaArr"
            @closeDialog="closeDialog"
            @sureCloseDialog="sureCloseDialog"
          ></area-form>
        </el-tab-pane>
        <el-tab-pane label="类别" name="two" v-else-if="editForm.type == '1'">
          <category-form
            ref="categoryFormRef"
            :sonCategoryArr="sonCategoryArr"
            @closeDialog="closeDialog"
            @sureCloseDialog="sureCloseDialog"
          ></category-form>
        </el-tab-pane>
        <el-tab-pane label="机构" name="3" v-else-if="editForm.type == '2'">
          <org-form
            ref="orgFormRef"
            :orgNatureArr="orgNatureArr"
            :orgStandardsArr="orgStandardsArr"
            :orgServiceArr="orgServiceArr"
            @closeDialog="closeDialog"
            @sureCloseDialog="sureCloseDialog"
          ></org-form>
        </el-tab-pane>
      </el-tabs>
    </template>
    <template v-else>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane
          label="区划"
          name="1"
          v-if="!addSonFlag || editForm.type == '0' || editForm.type == '1'"
        >
          <area-form
            ref="areaFormRef"
            :areaArr="areaArr"
            @closeDialog="closeDialog"
            @sureCloseDialog="sureCloseDialog"
          ></area-form>
        </el-tab-pane>
        <el-tab-pane
          label="类别"
          name="two"
          v-if="(!addSonFlag || editForm.type == '0') && !rootFlag"
        >
          <category-form
            ref="categoryFormRef"
            :sonCategoryArr="sonCategoryArr"
            @closeDialog="closeDialog"
            @sureCloseDialog="sureCloseDialog"
          ></category-form>
        </el-tab-pane>
        <el-tab-pane label="机构" name="3" v-if="!rootFlag">
          <org-form
            ref="orgFormRef"
            :orgNatureArr="orgNatureArr"
            :orgStandardsArr="orgStandardsArr"
            :orgServiceArr="orgServiceArr"
            @closeDialog="closeDialog"
            @sureCloseDialog="sureCloseDialog"
          ></org-form>
        </el-tab-pane>
      </el-tabs>
    </template>
  </el-dialog>
</template>
<script>
import AreaForm from "./AreaForm.vue";
import CategoryForm from "./CategoryForm.vue";
import OrgForm from "./OrgForm.vue";
import { remote } from "@/api/admin/sys/sys-dict";

export default {
  components: { AreaForm, CategoryForm, OrgForm },
  data() {
    return {
      dialogVisible: true,
      form: {},
      activeName: "1",
      options: [],
      // areaArr: [],
      // sonCategoryArr: [],
      // orgStandardsArr: [],
      // orgNatureArr: [],
      orgServiceArr: [],
      editForm: {},
      formType: "",
      addSonFlag: false,
      rootFlag: false,
    };
  },
  props: {
    currentRow: {
      type: Object,
      default: function () {
        return {};
      },
    },
    areaArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    orgStandardsArr: {
      type: Array,
      default: function () {
        return [];
      },
    },

    orgNatureArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    sonCategoryArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },

  created() {
    //area_standards

    // this.getSonCategoryData(); //子类别

    // this.getOrganizationNatureData(); //机构性质
    this.getOrganizationServiceData(); //组织业务类别
  },
  methods: {
    handleEditData(row, formType) {
      this.addSonFlag = false;
      console.log("formType==", formType);
      console.log("row=11=", row);
      this.formType = formType;
      this.editForm = Object.assign(this.editForm, row);
      if (row.type == "0") {
        this.activeName = "1";
        this.$nextTick(() => {
          this.$refs.areaFormRef.handleDealEdit(this.editForm);
        });
      } else if (row.type == "1") {
        console.log("类别---");
        this.activeName = "two";
        this.$nextTick(() => {
          this.$refs.categoryFormRef.handleDealEdit(this.editForm);
        });
      } else if (row.type == "2") {
        this.activeName = "3";
        this.$nextTick(() => {
          this.$refs.orgFormRef.handleDealEdit(this.editForm);
        });
      }
      // setTimeout(() => {
      //   this.formType = formType;
      // }, 500);
    },
    handleRootEditData(row) {
      this.rootFlag = true;
      this.addSonFlag = false;
      this.formType = 0;
      // console.log("row=11=", row);
      // this.editForm = Object.assign(this.editForm, row);
      // if (row.type == "0") {
      //   this.activeName = "1";
      //   this.$nextTick(() => {
      //     this.$refs.areaFormRef.handleDealEdit(this.editForm);
      //   });
      // } else if (row.type == "1") {
      //   console.log("类别---");
      //   this.activeName = "2";
      //   this.$nextTick(() => {
      //     this.$refs.categoryFormRef.handleDealEdit(this.editForm);
      //   });
      // } else if (row.type == "2") {
      //   this.activeName = "3";
      //   this.$nextTick(() => {
      //     this.$refs.orgFormRef.handleDealEdit(this.editForm);
      //   });
      // }
      // setTimeout(() => {
      //   this.formType = formType;
      // }, 500);
    },

    handleAddSonData(row, formType) {
      console.log("row=handleAddSonData=", row);
      this.addSonFlag = true;
      this.rootFlag = false;
      this.formType = formType;
      this.editForm = Object.assign(this.editForm, row);
      if (row.type == "0") {
        this.activeName = "1";
        this.$nextTick(() => {
          this.$refs.areaFormRef.handleAddSonData(this.editForm);
        });
      } else if (row.type == "1") {
        this.activeName = "1";
        this.$nextTick(() => {
          this.$refs.areaFormRef.handleAddSonData(this.editForm);
          // this.$refs.categoryFormRef.handleAddSonData(this.editForm);
        });
      } else if (row.type == "2") {
        this.activeName = "3";
        this.$nextTick(() => {
          this.$refs.orgFormRef.handleAddSonData(this.editForm);
        });
      }
    },
    handleBeforeLeave() {
      if (this.formType == "edit") {
        return false;
      } else {
        return true;
      }
    },

    // getSonCategoryData() {
    //   remote("son_category").then((res) => {
    //     // console.log("子类别==", res);
    //     if (res.data.code == 200) {
    //       this.sonCategoryArr = res.data.data;
    //     }
    //   });
    // },

    // getOrganizationNatureData() {
    //   remote("organization_nature").then((res) => {
    //     if (res.data.code == 200) {
    //       this.orgNatureArr = res.data.data;
    //     }
    //   });
    // },
    getOrganizationServiceData() {
      remote("organization_service").then((res) => {
        if (res.data.code == 200) {
          this.orgServiceArr = res.data.data;
        }
      });
    },

    handleClick() {
      if (this.activeName == "1") {
        this.$nextTick(() => {
          this.$refs.areaFormRef.handleAddSonData(this.editForm);
        });
      } else if (this.activeName == "two") {
        this.$nextTick(() => {
          this.$refs.categoryFormRef.handleAddSonData(this.editForm);
        });
      } else if (this.activeName == "3") {
        this.$nextTick(() => {
          this.$refs.orgFormRef.handleAddSonData(this.editForm);
        });
      }
    },
    closeDialog() {
      console.log("closeDialog");
      this.$emit("closeDialog");
    },
    sureCloseDialog(form) {
      this.$emit("sureCloseDialog", form);
    },
  },
};
</script>
<style lang="scss" scoped>
</style>