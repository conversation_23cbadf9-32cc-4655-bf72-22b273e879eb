<template>
  <div>
    <el-form
      :model="form"
      ref="form"
      label-width="135px"
      :rules="rules"
      class="demo-ruleForm"
    >
      <div class="row-block">
        <el-form-item label="组织全称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入组织全称">
          </el-input>
          <!-- <el-cascader
            ref="poolareaNoRef"
            v-model="form.poolareaNoArr"
            :props="selfProps"
            :show-all-levels="true"
            clearable
            class="in-s2"
          ></el-cascader> -->
        </el-form-item>
        <el-form-item label="规范简称" prop="normName">
          <el-input v-model="form.normName" placeholder="请输入规范简称">
          </el-input>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="习惯简称" prop="usualName">
          <el-input v-model="form.usualName" placeholder="请输入习惯简称">
          </el-input>
        </el-form-item>
        <el-form-item label="所属区划编码" prop="areaCode">
          <el-input v-model="form.areaCode" placeholder="请输入所属区划编码">
          </el-input>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="组织域名" prop="domainName">
          <el-input v-model="form.domainName" placeholder="请输入组织域名">
          </el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="uniformSocialCreditCode">
          <!--   label-width="130" -->
          <el-input
            v-model="form.uniformSocialCreditCode"
            placeholder="请输入统一社会信用代码"
          >
          </el-input>
        </el-form-item>
      </div>

      <div class="row-block">
        <el-form-item label="机构规格" prop="unit">
          <!-- <el-input v-model="form.unit"> </el-input> -->
          <el-select
            style="width: 100%"
            v-model="form.unit"
            placeholder="请选择机构规格"
          >
            <el-option
              v-for="item in orgStandardsArr"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机构性质" prop="nature">
          <el-select
            v-model="form.nature"
            placeholder="请选择机构性质"
            class="in-s2"
          >
            <el-option
              v-for="item in orgNatureArr"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="机构业务类别" prop="orgBizType">
          <el-select
            v-model="form.orgBizType"
            placeholder="请选择机构业务类别"
            class="in-s2"
          >
            <el-option
              v-for="item in orgServiceArr"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="权利事项单位" prop="rightServiceUnit">
          <el-radio-group v-model="form.rightServiceUnit">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item
          label="客户端显示该组织"
          prop="clientShow"
          label-width="130"
        >
          <el-switch
            v-model="form.clientShow"
            active-value="1"
            inactive-value="0"
          >
          </el-switch>
          <!-- <el-radio-group v-model="form.clientShow">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <!-- <el-switch v-model="form.status" active-value="1" inactive-value="0">
        </el-switch> -->
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item
          label="法人或负责人姓名"
          label-width="130"
          prop="credentialName"
        >
          <el-input
            v-model="form.credentialName"
            class="in-s1"
            placeholder="请输入法人或负责人姓名"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系人">
          </el-input>
        </el-form-item>
      </div>
      <div class="row-block">
        <el-form-item label="联系人电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系人电话">
          </el-input>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number
            v-model="form.orderNum"
            controls-position="right"
            :min="1"
          ></el-input-number>
        </el-form-item>
      </div>
      <!-- <el-input v-model="form.leader"> </el-input>
         -->
      <!-- <el-form-item label="管理员" prop="leader">
        <el-select
          v-model="form.leader"
          filterable
          placeholder="请选择管理员"
          class="in-s2"
          clearable
        >
          <el-option
            v-for="item in userAllArr"
            :key="item.id"
            :label="item.nickName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="备注信息" prop="remark">
        <el-input
          type="textarea"
          v-model="form.remark"
          placeholder="请输入备注信息"
        >
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          v-loading="loading"
          type="primary"
          @click="submitForm('form')"
          >提交</el-button
        >
        <!-- <el-button @click="resetForm('form')">重置</el-button> -->
        <el-button @click="closeDialog">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import formMixin from "./formMixin";
import { addObj, putObj } from "@/api/admin/sys/dept";
export default {
  mixins: [formMixin],
  data() {
    return {
      rules: {
        areaCode: [
          { required: true, message: "请输入所属区划编码", trigger: "blur" },
        ],
        deptName: [
          { required: true, message: "请输入组织全称", trigger: "blur" },
        ],
        normName: [
          { required: true, message: "请输入规范简称", trigger: "blur" },
        ],
        unit: [
          { required: false, message: "请选择机构规格", trigger: "change" },
        ],
        nature: [
          { required: true, message: "请选择机构性质", trigger: "change" },
        ],
        clientShow: [
          { required: true, message: "请选择客户端组织", trigger: "change" },
        ],
        rightServiceUnit: [
          { required: true, message: "请选择权利事项单位", trigger: "change" },
        ],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
        uniformSocialCreditCode: [
          {
            required: true,
            message: "请输入统一社会信用代码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  props: {
    orgStandardsArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    orgNatureArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    orgServiceArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  methods: {
    closeDialog() {
      this.$emit("closeDialog");
    },
    handleDealEdit(row) {
      this.form = Object.assign(this.form, row);
      // console.log("row=222==", this.form);
      // if (this.form.deptName) {
      //   this.form.poolareaNoArr = [{ areaName: this.form.deptName }];
      //   setTimeout(() => {
      //     this.$refs.poolareaNoRef.presentText = this.form.deptName;
      //   }, 200);
      // }
    },
    handleAddSonData(row) {
      this.form.parentId = row.id;
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.form.type = 2;
          let that = this;
          try {
            // if (this.form.poolareaNoArr && this.form.poolareaNoArr.length > 0) {
            //   this.form.deptName =
            //     this.form.poolareaNoArr[
            //       this.form.poolareaNoArr.length - 1
            //     ].areaName;
            // }
            if (this.form.id) {
              let res = await putObj(this.form);
              if (res.data.code == 200) {
                that.$message.success("修改成功");
                this.$emit("sureCloseDialog", this.form);
              }
            } else {
              let res2 = await addObj(this.form);
              // this.$message.success("新建成功");
              // console.log("新增--", res2);
              if (res2.data.code == 200) {
                that.$message.success("新建成功");
                this.$emit("sureCloseDialog", this.form);
              }
            }
          } catch (error) {}
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./form.scss";
::v-deep .el-form-item {
  width: 48%;
  margin-right: 12px;
}
</style>