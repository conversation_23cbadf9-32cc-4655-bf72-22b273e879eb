<template>
  <el-card class="box-card">
    <div class="text item">
      <el-row>
        <el-col :span="8">
          <span class="title">节点类型：</span>
          <span class="text">{{
            currentRow.type == 0
              ? "区划"
              : currentRow.type == 1
              ? "类别"
              : currentRow.type == 2
              ? "机构"
              : ""
          }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织全称：</span>
          <span class="text">{{ currentRow.deptName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">规范简称：</span>
          <span class="text">{{ currentRow.normName }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">习惯简称：</span>
          <span class="text">{{ currentRow.usualName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">所属区域代码：</span>
          <span class="text">{{ currentRow.areaCode }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">组织域名：</span>
          <span class="text">{{ currentRow.domainName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">组织编码：</span>
          <span class="text">{{ currentRow.orgCode }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">机构规格：</span>
          <span class="text">{{ getJigouGuige(currentRow.unit) }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">机构性质：</span>
          <span class="text">{{ getJigouXingzhi(currentRow.nature) }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">机构业务类别：</span>
          <span class="text">{{ getJigouLeibie(currentRow.orgBizType) }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">权利事项单位：</span>
          <span class="text">{{ currentRow.rightServiceUnit | yesOrNo }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">统一社会信用代码：</span>
          <span class="text">{{ currentRow.uniformSocialCreditCode }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">客户端显示该组织：</span>
          <span class="text">{{
            currentRow.clientShow | showWordYesOrNo
          }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">法人或负责人姓名：</span>
          <span class="text">{{ currentRow.credentialName }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">联系人：</span>
          <span class="text">{{ currentRow.contact }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">联系人电话：</span>
          <span class="text">{{ currentRow.contactPhone }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">管理员：</span>
          <span class="text">{{ getUsersName(currentRow.leader) }}</span>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <span class="title">备注信息：</span>
          <span class="text">{{ currentRow.remark }}</span>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script>
import infoMixin from "./infoMixin";
import { remote } from "@/api/admin/sys/sys-dict";
export default {
  name: "info",
  mixins: [infoMixin],
  data() {
    return {
      orgServiceArr: [],
    };
  },
  props: {
    userAllArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  created() {
    this.getOrganizationServiceData();
  },
  methods: {
    getUsersName(val) {
      let result = "";
      if (val) {
        let fObj = this.userAllArr.find((item) => {
          return item.id == val;
        });
        if (fObj) {
          return fObj.nickName;
        }
      }
      return result;
    },
    convertLevel(level) {
      switch (level) {
        case "1":
          return "省级";
        case "2":
          return "市级";
        case "3":
          return "区县级";
        case "4":
          return "乡镇级";
        case "5":
          return "村居级";
      }
    },
    convertStatus(status) {
      switch (status) {
        case "1":
          return "启用";
        case "0":
          return "禁用";
      }
    },
    addOrUpdateHandle() {
      this.editArea();
    },
    handleDelete() {
      this.delArea();
    },
    getOrganizationServiceData() {
      remote("organization_service").then((res) => {
        if (res.data.code == 200) {
          this.orgServiceArr = res.data.data;
        }
      });
    },

    getJigouLeibie(val) {
      let result = "";
      let fObj = this.orgServiceArr.find((item) => {
        return item.dictValue == val;
      });
      if (fObj) {
        return fObj.dictLabel;
      }
      return result;
    },
  },
};
</script>

<style scoped lang="scss">
.box-card {
  .title {
    font-size: 14px;
    font-weight: bold;
  }

  .text {
    font-size: 14px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both;
  }
}
</style>
