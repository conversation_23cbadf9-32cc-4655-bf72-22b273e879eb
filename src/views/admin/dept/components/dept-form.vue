<template>
  <!-- 添加或修改机构对话框 -->
  <el-dialog :title="!form.id ? '新增' : '修改'" :visible.sync="visible">
    <el-form ref="dataForm" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="机构名称" prop="deptName">
        <el-input v-model="form.deptName" placeholder="请输入机构名称" />
      </el-form-item>
      <el-row>
        <el-col>
          <el-form-item
            label="上级机构"
            :rules="[
              {
                required: depEnable,
                message: '上级机构不能为空',
                trigger: 'blur',
              },
            ]"
          >
            <!-- deptOptions -->
            <treeselect
              v-model="form.parentId"
              :options="treeDeptData"
              :normalizer="normalizer"
              :show-count="true"
              placeholder="选择上级机构"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="联系人" prop="contact">
        <el-input v-model="form.contact" placeholder="请输入联系人" />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入地址" />
      </el-form-item>
      <!-- <el-form-item label="部门领导" prop="leader">
        <el-select v-model="form.leader" placeholder="请选择领导" clearable>
          <el-option
            v-for="user in users"
            :key="user.id"
            :label="user.nickName"
            :value="user.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="行政区划" prop="poolareaNoArr">
        <!-- <el-input v-model="form.poolareaNo" placeholder="请输入行政区划" /> -->
        <el-cascader
          ref="poolareaNoRef"
          v-model="form.poolareaNoArr"
          :props="props"
          :show-all-levels="true"
          clearable
          style="width: 100%"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="排序" prop="orderNum">
        <el-input-number
          v-model="form.orderNum"
          controls-position="right"
          :min="0"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dataFormSubmit">确 定</el-button>
      <el-button @click="closeDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addObj,
  fetchTree,
  getObj,
  putObj,
  getAllProvinces,
  getAllCitys,
} from "@/api/admin/sys/dept";
import Treeselect from "@riophae/vue-treeselect";
import { listUser } from "@/api/admin/auth/user";

import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "DeptForm",
  components: { Treeselect },
  data() {
    return {
      props: {
        checkStrictly: true,
        lazy: true,
        lazyLoad: (node, resolve) => {
          // console.log("node==", node);
          const { level, value } = node;
          if (level === 0) {
            this.getProvince(resolve);
          } else if (level === 1) {
            this.getCity(value, resolve);
          } else if (level === 2) {
            this.getRegion(value, resolve);
          } else if (level == 3) {
            this.getJieDao(value, resolve);
            // resolve(node);
          } else if (level > 3) {
            resolve(node);
          }
        },
      },
      // 遮罩层
      loading: true,
      // 机构树选项
      deptOptions: [],
      // 下拉用户列表
      users: [],
      // 是否显示弹出层
      visible: false,
      form: {
        deptName: "",
        orderNum: 999,
        contact: "",
        contactPhone: "",
        address: "",
        poolareaNoArr: [],
        areaCode: "",
        areaName: "",
      },
      poolareaNoOptions: [],
      deptArr: [],
      depEnable: true,

      // 表单校验
      rules: {
        deptName: [
          { required: true, message: "机构名称不能为空", trigger: "blur" },
        ],
        orderNum: [
          { required: true, message: "机构顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  props: {
    treeDeptData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  created() {
    // this.getAllProviceFn();
  },
  mounted() {
    if (this.form.areaName) {
      this.$refs.poolareaNoRef.presentText = this.form.areaName;
    }
  },
  methods: {
    closeDialog() {
      this.visible = false;
      this.$emit("closeDialog");
    },
    getAllProviceFn() {
      getAllProvinces().then((res) => {
        // console.log("获取省份数据==", res);
        if (res.data.code == 200) {
          this.deptArr = res.data.data;
        }
      });
    },
    getProvince(resolve) {
      // console.log("获取省份");
      let query = {
        status: 1,
      };
      getAllProvinces(query).then((res) => {
        // console.log("获取省份数据==", res);
        if (res.data && res.data.code === 200 && res.data.data) {
          const provinceList = res.data.data.map((item) => ({
            value: item,
            label: item.areaName,
            leaf: false,
            areaCode: item.areaCode,
          }));
          // console.log("provinceList===", provinceList);
          resolve && resolve(provinceList);
        }
      });
    },
    getCity(data, resolve) {
      // console.log("data==", data);
      getAllCitys({ id: data.id, status: 1 }).then((res) => {
        // console.log("获取城市==", res);
        if (res.data.code === 200 && res.data.data) {
          const cityList = res.data.data.map((item) => ({
            value: item,
            label: item.areaName,
            leaf: false,
            areaCode: item.areaCode,
          }));
          resolve && resolve(cityList);
        }
      });
    },
    getRegion(data, resolve) {
      // console.log("data2===", data);
      getAllCitys({ id: data.id, status: 1 }).then((res) => {
        if (res.data.code === 200 && res.data.data) {
          const regionList = res.data.data.map((item) => ({
            value: item,
            label: item.areaName,
            leaf: false,
            areaCode: item.areaCode,
          }));
          // console.log("regionList---", regionList);
          resolve && resolve(regionList);
        }
        // console.log("666", res);
      });
    },
    getJieDao(data, resolve) {
      // console.log("data2===", data);
      getAllCitys({ id: data.id, status: 1 }).then((res) => {
        if (res.data.code === 200 && res.data.data) {
          const regionList = res.data.data.map((item) => ({
            value: item,
            label: item.areaName,
            leaf: true,
            areaCode: item.areaCode,
          }));
          // console.log("regionList---", regionList);
          resolve && resolve(regionList);
        }
        // console.log("66jiedao6", res);
      });
    },
    resetForm() {
      this.form = {
        deptName: "",
        orderNum: 999,
        contact: "",
        contactPhone: "",
        address: "",
        poolareaNoArr: [],
        areaCode: "",
        areaName: "",
      };
    },
    init(isEdit, id) {
      this.resetForm();
      this.depEnable = id;
      if (id !== null) {
        this.form.parentId = id;
      }
      this.visible = true;
      this.getTreeselect();
      this.listAllUser();
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          getObj(id).then((response) => {
            this.form = response.data.data;
            // console.log("from==", this.form);

            if (this.form.areaName) {
              setTimeout(() => {
                this.$refs.poolareaNoRef.presentText = this.form.areaName;
              }, 400);
            }
          });
        } else {
          this.form.id = undefined;
        }
      });
    },
    listAllUser() {
      listUser().then((response) => {
        this.users = response.data.data;
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if (this.form.poolareaNoArr && this.form.poolareaNoArr.length > 0) {
            // let pArr = [];
            // this.form.poolareaNoArr.forEach((cItem) => {
            //   pArr.push(cItem.areaCode);
            // });
            // this.form.poolareaNo = pArr.join();
            this.form.areaCode =
              this.form.poolareaNoArr[
                this.form.poolareaNoArr.length - 1
              ].areaCode;
            // this.form.areaName =
            //   this.form.poolareaNoArr[
            //     this.form.poolareaNoArr.length - 1
            //   ].areaName;

            let array = this.form.poolareaNoArr;
            let titleArr = [];
            for (let index = 0; index < array.length; index++) {
              const element = array[index];
              titleArr.push(element.areaName);
            }
            this.form.areaName = titleArr.join("/");
          }

          if (this.form.parentId === undefined) {
            if (this.depEnable) {
              this.$message.error("请填写上级机构");
              return;
            }
            this.form.parentId = 0;
          }
          if (this.form.id) {
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          } else {
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          }
        }
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      fetchTree().then((response) => {
        // console.log("lzs---", response);
        this.deptOptions = [];
        const dept = {
          id: 0,
          deptName: "根部门",
          children: response.data.data,
        };
        this.deptOptions.push(dept);
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.deptName,
        children: node.children,
      };
    },
  },
};
</script>

