import {
    
    getAllProvinces,
    getAllCitys,
} from "@/api/admin/sys/dept";
// import {listUser} from "@/api/admin/auth/user"

export default {
    data() {
        return {
          userAllArr:[],
            form: {
              poolareaNoArr: [],
              parentId: "0",
              unit: "",
              clientShow: "",
              status: "",
              deptName:"",
              normName:"",
              usualName:"",
              domainName:"",
              areaCode:"",
              unit:"",
              clientShow:"",
              status:"",
              orderNum:"",
              subType:"",
              leader:"",
              uniformSocialCreditCode:"",
              nature:"",
              orgBizType:"",
              rightServiceUnit:"",
              credentialName:"",
              contact:"",
            contactPhone: "",
            remark:""
            },
            loading: false,
            selfProps: {
                checkStrictly: true,
                lazy: true,
                lazyLoad: (node, resolve) => {
                  console.log("node==", node);
                  const {level, value} = node;
                  if (level === 0) {
                    this.getProvince(resolve);
                  } else if (level === 1) {
                    this.getCity(value, resolve);
                  } else if (level === 2) {
                    this.getRegion(value, resolve);
                  } else if (level == 3) {
                    this.getJieDao(value, resolve);
                    // resolve(node);
                  } else if (level == 4) {
                    this.getJuweihui(value, resolve);
                    // resolve(node);
                  } else if (level > 4) {
                    resolve(node);
                  }
                },
              },
        }
  },
  created() {
    // this.getListUserData();
  },
    methods: {
      // getListUserData() {
      //   listUser().then(res => {
      //     // console.log("获取用户信息--", res)
      //     if (res.data.code == 200) {
            
      //       this.userAllArr = res.data.data;
      //     }
      //   })
      // },
        resetFormNew() {
            console.log("重置了");
            this.form.areaOrder = "";
            this.form.areaName = "";
            this.form.areaCode = "";
        },
        getAllProviceFn() {
            getAllProvinces().then((res) => {
              // console.log("获取省份数据==", res);
              if (res.data.code == 200) {
                this.deptArr = res.data.data;
              }
            });
          },
          getProvince(resolve) {
            // console.log("获取省份");
            let query = {
              status: 1
            }
            getAllProvinces(query).then((res) => {
              // console.log("获取省份数据==", res);
              if (res.data && res.data.code === 200 && res.data.data) {
                const provinceList = res.data.data.map((item) => ({
                  value: item,
                  label: item.areaName,
                  leaf: false,
                  areaCode: item.areaCode,
                }));
                // console.log("provinceList===", provinceList);
                resolve && resolve(provinceList);
              }
            });
          },
          getCity(data, resolve) {
            // console.log("data==", data);
            getAllCitys({id: data.id, status: 1}).then((res) => {
              // console.log("获取城市==", res);
              if (res.data.code === 200 && res.data.data) {
                const cityList = res.data.data.map((item) => ({
                  value: item,
                  label: item.areaName,
                  leaf: false,
                  areaCode: item.areaCode,
                }));
                resolve && resolve(cityList);
              }
            });
          },
          getRegion(data, resolve) {
            // console.log("data2===", data);
            getAllCitys({id: data.id, status: 1}).then((res) => {
              if (res.data.code === 200 && res.data.data) {
                const regionList = res.data.data.map((item) => ({
                  value: item,
                  label: item.areaName,
                  leaf: false,
                  areaCode: item.areaCode,
                }));
                // console.log("regionList---", regionList);
                resolve && resolve(regionList);
              }
              // console.log("666", res);
            });
          },
          getJieDao(data, resolve) {
            // console.log("data2===", data);
            getAllCitys({id: data.id, status: 1}).then((res) => {
              if (res.data.code === 200 && res.data.data) {
                const regionList = res.data.data.map((item) => ({
                  value: item,
                  label: item.areaName,
                  leaf: false,
                  areaCode: item.areaCode,
                }));
                // console.log("regionList---", regionList);
                resolve && resolve(regionList);
              }
      
            });
      },
      
      getJuweihui(data, resolve) {
        // console.log("data2===", data);
        getAllCitys({id: data.id, status: 1}).then((res) => {
          if (res.data.code === 200 && res.data.data) {
            const regionList = res.data.data.map((item) => ({
              value: item,
              label: item.areaName,
              leaf: true,
              areaCode: item.areaCode,
            }));
            // console.log("regionList---", regionList);
            resolve && resolve(regionList);
          }
       
        });
  },
    },
}