<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        "
      >
        <div class="pos-content">
          <span class="pos-line"></span>
          <span>当前位置：</span>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="(pathObj, i) in parentNameArr"
              :key="i"
              >{{ pathObj.deptName }}</el-breadcrumb-item
            >
          </el-breadcrumb>
        </div>
        <div class="status-content">
          <el-tag type="success" v-if="currentRow.status == 1">启用</el-tag>
          <el-tag type="info" v-else-if="currentRow.status == 0">停用</el-tag>
        </div>
        <div>
          <!-- v-if="userRole && userRole.parentId == '-2'" -->
          <!--  -->
          <el-button
            v-if="permissions.sys_dept_edit"
            size="small"
            type=""
            @click="addOrUpdateHandle()"
            >编辑
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleDelete()"
            v-if="permissions.sys_dept_del"
            >删除
          </el-button>
        </div>
      </div>
    </div>

    <org-info
      v-if="currentRow.type == 2"
      :currentRow="currentRow"
      :areaArr="areaArr"
      :orgStandardsArr="orgStandardsArr"
      :orgNatureArr="orgNatureArr"
      :userAllArr="userAllArr"
    ></org-info>
    <category-info
      v-else-if="currentRow.type == 1"
      :currentRow="currentRow"
      :areaArr="areaArr"
      :orgStandardsArr="orgStandardsArr"
      :sonCategoryArr="sonCategoryArr"
    ></category-info>
    <area-info
      v-else
      :currentRow="currentRow"
      :areaArr="areaArr"
      :orgStandardsArr="orgStandardsArr"
      :userAllArr="userAllArr"
    ></area-info>
  </el-card>
</template>

<script>
import { mapGetters } from "vuex";
import AreaInfo from "./AreaInfo.vue";
import CategoryInfo from "./CategoryInfo.vue";
import OrgInfo from "./OrgInfo.vue";
import { listUser } from "@/api/admin/auth/user";

export default {
  name: "info",
  components: { AreaInfo, OrgInfo, CategoryInfo },
  data() {
    return {
      userAllArr: [],
    };
  },
  props: {
    deptInfo: {
      type: Object,
      required: true,
    },
    editDep: {
      type: Function,
      default: null,
    },
    delDep: {
      type: Function,
      default: null,
    },
    currentRow: {
      type: Object,
      default: function () {
        return {};
      },
    },
    areaArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    orgStandardsArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    parentNameArr: {
      type: Array,
      default: function () {
        return [];
      },
    },

    orgNatureArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    sonCategoryArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },

  computed: {
    ...mapGetters(["permissions", "userRole"]),
  },
  filters: {
    yesOrNo(val) {
      let result = "";
      if (val == 0) {
        result = "否";
      } else if (val == 1) {
        result = "是";
      }
      return result;
    },
  },
  created() {
    // this.getListUserData();
  },
  methods: {
    // getListUserData() {
    //   listUser().then((res) => {
    //     // console.log("获取用户信息--", res)
    //     if (res.data.code == 200) {
    //       this.userAllArr = res.data.data;
    //     }
    //   });
    // },
    convertLevel(level) {
      switch (level) {
        case "1":
          return "省级";
        case "2":
          return "市级";
        case "3":
          return "区县级";
        case "4":
          return "乡镇级";
        case "5":
          return "村居级";
      }
    },
    addOrUpdateHandle() {
      this.editDep();
    },
    handleDelete() {
      this.delDep();
    },
  },
};
</script>

<style scoped lang="scss">
.box-card {
  ::v-deep .el-card__header {
    padding: 0;
  }
  .title {
    font-size: 14px;
    font-weight: bold;
  }

  .text {
    font-size: 14px;
  }
  .clearfix {
    padding: 0;
  }
  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both;
  }
}
.pos-content {
  display: flex;
  align-items: center;
  padding-top: 1px;
  font-size: 16px;
  position: relative;
  .pos-line {
    width: 3px;
    height: 16px;
    display: inline-block;
    background: #327fff;
    margin-right: 8px;
  }
}
.status-content {
  position: absolute;
  right: 180px;
}
</style>
