
export default {
    data() {
        return {
            
            
        }
  },
  props: {
    currentRow: {
      type: Object,
      default: function () {
        return {};
      },
    },
    areaArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    orgStandardsArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    orgNatureArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
    sonCategoryArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  filters: {
    yesOrNo(val) {
      let result = "";
      if (val == 0) {
        result = "否";
      } else if (val == 1) {
        result = "是";
      }
      return result;
    },
    showWordYesOrNo(val) {
      let result = "";
      if (val == 0) {
        result = "不显示";
      } else if (val == 1) {
        result = "显示";
      }
      return result;
    },
  },
    methods: {
      getGuige(val) {
        let result = "";
        let fObj = this.areaArr.find((item) => {
          return item.dictValue == val;
        });
        if (fObj) {
          return fObj.dictLabel;
        }
        return result;
      },
      getJigouGuige(val) {
        let result = "";
        let fObj = this.orgStandardsArr.find((item) => {
          return item.dictValue == val;
        });
        if (fObj) {
          return fObj.dictLabel;
        }
        return result;
      },
      getSonCategor(val) {
       
        let result = "";
        let fObj = this.sonCategoryArr.find((item) => {
          return item.dictValue == val;
        });
        if (fObj) {
          return fObj.dictLabel;
        }
        return result;
      },
      getJigouXingzhi(val) {
       
        let result = "";
        let fObj = this.orgNatureArr.find((item) => {
          return item.dictValue == val;
        });
        if (fObj) {
          result = fObj.dictLabel;
        }
        return result;
      }
        
    },
}