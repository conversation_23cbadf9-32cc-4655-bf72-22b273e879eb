<template>
  <div class="user">
    <div class="dept-content">
      <div class="dept-left">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          "
        >
          <span style="font-size: 16px; font-weight: 500">组织列表</span>
          <!--   v-if="userRole && userRole.parentId == '-2'" -->
          <img
            src="@/assets/image/appGroup/addAppGroup.png"
            style="width: 18px; width: 18px; cursor: pointer"
            alt=""
            @click="addOrUpdateHandleRootNew()"
            v-if="permissions.sys_dept_add"
          />
        </div>

        <el-input placeholder="组织名称、编码或规范简称" v-model="deptSearch">
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-tree
          class="filter-tree"
          :data="treeDeptDataArr"
          :props="defaultProps"
          :load="loadNode"
          lazy
          :default-expanded-keys="groupExpandedKeys"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleCheckChange"
          :filter-node-method="handleDeptfilterNode"
          :render-content="renderContent"
          ref="deptTreeRef"
        >
        </el-tree>
        <el-pagination
          v-if="searchFlag"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchPage.current"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="searchPage.size"
          layout=" pager, jumper"
          small
          :total="searchPage.total"
          :pager-count="5"
        >
        </el-pagination>
      </div>
      <div class="dept-right">
        <basic-container style="flex: 0; margin-top: 10px">
          <info
            :deptInfo="rightDateInfo"
            :editDep="editDep"
            :delDep="delDep"
            :areaArr="areaArr"
            :orgStandardsArr="orgStandardsArr"
            :currentRow="currentInfo"
            :parentNameArr="parentNameArr"
            :orgNatureArr="orgNatureArr"
            :sonCategoryArr="sonCategoryArr"
          />
        </basic-container>
        <basic-container style="flex: 1">
          <div class="jie-content">
            <span class="jie-left"></span>
            <span class="jie-right">下级节点配置</span>
          </div>
          <!-- <avue-crud
            :option="option"
            ref="crud"
            v-model="form"
            :page.sync="page"
            @size-change="sizeChange"
            @current-change="currentChange"
            :table-loading="listLoading"
            @search-change="handleFilter"
            @refresh-change="handleRefreshChange"
            :data="list"
          >
            <template slot="menuLeft">
              <el-button
                v-if="permissions.sys_dept_add"
                class="filter-item"
                @click="addOrUpdateHandle(false, rightDateInfo.id)"
                type="primary"
                icon="el-icon-edit"
                >新增
              </el-button>
            </template>

            <template slot="menu" slot-scope="scope">
              <el-button
                size="small"
                type="text"
                icon="el-icon-edit"
                @click="addOrUpdateHandle(true, scope.row.id)"
                v-if="permissions.sys_dept_edit"
                >修改
              </el-button>
              <el-button
                size="small"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-if="permissions.sys_dept_del"
                >删除
              </el-button>
            </template>
          </avue-crud> -->
          <!-- <table-form
            v-if="addOrUpdateVisible"
            ref="addOrUpdate"
            @refreshDataList="hangdleGetTreeDeptFn"
          ></table-form> -->

          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane
              v-if="currentRow.type == 0 || currentRow.type == 1"
              label="区划"
              name="1"
            ></el-tab-pane>
            <el-tab-pane
              v-if="currentRow.type == 0"
              label="类别"
              name="2"
            ></el-tab-pane>
            <el-tab-pane label="机构" name="3"></el-tab-pane>
          </el-tabs>
          <!-- {{ activeTab }} -->
          <org-table
            :parentRow="currentRow"
            ref="orgTabRef"
            @addAreaFn="addAreaFn"
            @closeTwoFormlDialog="closeTwoFormlDialog"
            @closeSureTwoFormlDialog="sureTwoCloseDialog"
            :treeDeptData="treeDeptData"
            :areaArr="areaArr"
            :orgStandardsArr="orgStandardsArr"
            :orgNatureArr="orgNatureArr"
            :sonCategoryArr="sonCategoryArr"
            v-if="activeTab == '3'"
          ></org-table>
          <category-table
            :parentRow="currentRow"
            ref="categoryTabRef"
            @addAreaFn="addAreaFn"
            @closeTwoFormlDialog="closeTwoFormlDialog"
            @closeSureTwoFormlDialog="sureTwoCloseDialog"
            :treeDeptData="treeDeptData"
            :areaArr="areaArr"
            :orgStandardsArr="orgStandardsArr"
            :orgNatureArr="orgNatureArr"
            :sonCategoryArr="sonCategoryArr"
            v-else-if="activeTab == '2'"
          ></category-table>
          <area-table
            v-else
            :parentRow="currentRow"
            ref="areaTabRef"
            @addAreaFn="addAreaFn"
            @closeTwoFormlDialog="closeTwoFormlDialog"
            @closeSureTwoFormlDialog="sureTwoCloseDialog"
            :treeDeptData="treeDeptData"
            :areaArr="areaArr"
            :orgStandardsArr="orgStandardsArr"
            :orgNatureArr="orgNatureArr"
            :sonCategoryArr="sonCategoryArr"
          ></area-table>

          <form-dialog
            v-if="addOrUpdateVisible"
            @closeDialog="handleCloseDialog"
            @sureCloseDialog="sureCloseDialog"
            @closeSureTwoFormlDialog="sureTwoCloseDialog"
            :currentRow="currentRow"
            :areaArr="areaArr"
            :orgStandardsArr="orgStandardsArr"
            :orgNatureArr="orgNatureArr"
            :sonCategoryArr="sonCategoryArr"
            ref="addOrUpdateRef"
          ></form-dialog>
        </basic-container>
      </div>
    </div>
  </div>
</template>

<script>
import {
  delObj,
  fetchTree,
  canUseNetDeptTree,
  canUseDeptSonTree,
  searchDeptByName,
} from "@/api/admin/sys/dept";
import { tableOption } from "@/const/crud/admin/dept";
import { mapGetters } from "vuex";
import ExcelUpload from "@/components/upload/excel";
import Info from "./components/info";
import TableForm from "./components/dept-form";

import FormDialog from "./components/FormDialog.vue";
import { remote } from "@/api/admin/sys/sys-dict";
import AreaTable from "./components/AreaTable.vue";
import CategoryTable from "./components/CategoryTable.vue";

import OrgTable from "./components/OrgTable.vue";
import debounce from "@/util/debounce";
export default {
  name: "table_user",
  components: {
    ExcelUpload,
    Info,
    TableForm,
    FormDialog,
    AreaTable,
    CategoryTable,
    OrgTable,
  },
  data() {
    return {
      addOrUpdateVisible: false,
      option: tableOption,
      treeDeptData: [],
      treeDeptDataArr: [],
      defaultProps: {
        label: "deptName",
        value: "id",
      },
      deptSearch: "",
      // defaultProps: {
      //   children: "children",
      //   label: "name",
      //   key: "id",
      // },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条,
        isAsc: false, //是否倒序
      },
      query: {},
      list: [],
      listLoading: true,
      form: {},
      groupExpandedKeys: [],
      rightDateInfo: {},
      currentRow: {},
      currentInfo: {},
      areaArr: [],
      orgStandardsArr: [],
      parentNameArr: [],
      orgNatureArr: [],
      sonCategoryArr: [],
      activeTab: "3",
      searchFlag: false,
      searchPage: {
        current: 1,
        size: 20,
        total: 0,
      },
    };
  },
  computed: {
    ...mapGetters(["permissions", "userRole"]),
  },
  watch: {
    deptSearch(val) {
      if (val && val.length > 0) {
        this.searchFlag = true;
      } else {
        this.searchFlag = false;
      }
      // this.$refs.deptTreeRef.filter(val);
      this.debounceSearchDept(val);
    },
  },

  created() {
    // console.log("permissions==", this.permissions);
    this.hangdleGetTreeDeptFn();
    this.getAreaStandardsData(); //区划规格
    this.getOrganizationStandardsData(); //机构规格
    this.getOrganizationNatureData(); //机构性质
    this.getSonCategoryData(); //子类别
  },
  methods: {
    debounceSearchDept: debounce(function (name) {
      if (name) {
        this.handleSearchDept(name, true);
      } else {
        this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
      }
    }, 500),
    handleSearchDept(name, searchFlag) {
      let params = {
        keyWord: name,
      };
      if (searchFlag) {
        params.current = this.searchPage.current;
        params.size = this.searchPage.size;
      }
      searchDeptByName(params).then((res) => {
        // console.log("res==搜索记过=", res);
        if (res.data.code == 200) {
          this.treeDeptDataArr = res.data.data.records;
          this.searchPage.total = res.data.data.total;
        }
      });
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchPage.size = val;
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchPage.current = val;
      this.debounceSearchDept(this.deptSearch);
    },
    handleTabClick() {
      // console.log("this.activeTab==", this.activeTab);

      this.$nextTick(() => {
        // this.$refs.areaTabRef.refGetList();
        if (this.activeTab == "1") {
          this.$refs.areaTabRef.refGetList();
        } else if (this.activeTab == "3") {
          this.$refs.orgTabRef.refGetList();
        } else {
          this.$refs.categoryTabRef.refGetList();
        }
      });
    },
    async loadNode(node, resolve) {
      // console.log("node=00=", node);
      if (node.level === 0) {
        return resolve(node.data);
      } else if (node.level > 0) {
        const child = await canUseDeptSonTree({ deptId: node.data.id });
        // console.log("child=11=", child);
        node.data.children = child.data.data;
        // node.children = child.data.data;
        return resolve(child.data.data);
      }
      // console.log("appTreeData==", appTreeData);
    },
    getOrganizationStandardsData() {
      remote("organization_standards").then((res) => {
        if (res.data.code == 200) {
          this.orgStandardsArr = res.data.data;
        }
      });
    },
    getAreaStandardsData() {
      remote("area_standards").then((res) => {
        // console.log("res=区划规格==", res);
        if (res.data.code == 200) {
          this.areaArr = res.data.data;
        }
      });
    },
    getOrganizationNatureData() {
      remote("organization_nature").then((res) => {
        if (res.data.code == 200) {
          this.orgNatureArr = res.data.data;
        }
      });
    },
    getSonCategoryData() {
      remote("son_category").then((res) => {
        // console.log("子类别==", res);
        if (res.data.code == 200) {
          this.sonCategoryArr = res.data.data;
        }
      });
    },
    handleCloseDialog() {
      this.addOrUpdateVisible = false;
      // this.hangdleGetTreeDeptFn();
    },
    sureCloseDialog(form) {
      if (this.currentDeptId == form.id) {
        // console.log("相同的id");
        this.currentInfo = form;
      }
      this.addOrUpdateVisible = false;
      this.hangdleGetTreeDeptFn();
    },
    //新增子集的编辑
    sureTwoCloseDialog(form) {
      this.addOrUpdateVisible = false;
      this.hangdleGetTreeDeptFn();
    },
    closeTwoFormlDialog() {
      this.addOrUpdateVisible = false;
      // this.hangdleGetTreeDeptFn();
    },
    renderContent(h, { node, data, store }) {
      if (data.level == "root") {
        return (
          <span>
            <el-tooltip
              class="item"
              effect="dark"
              content={node.label}
              placement="top"
            >
              <span>
                <i class="el-icon-s-home"></i> {node.label}
              </span>
            </el-tooltip>
          </span>
        );
      } else {
        return (
          <span>
            <el-tooltip
              class="item"
              effect="dark"
              content={node.label}
              placement="top"
            >
              <span>
                <i class="el-icon-document"></i> {node.label}
              </span>
            </el-tooltip>
          </span>
        );
      }
    },
    handleDeptfilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    getArrFromId(arr) {
      if (!arr) return;
      for (let index = 0; index < arr.length; index++) {
        var data = arr[index];
        if (this.currentDeptId === data.id) {
          this.rightDateInfo = JSON.parse(JSON.stringify(data));
          if (this.rightDateInfo.children) {
            let arr = JSON.parse(JSON.stringify(this.rightDateInfo.children));
            for (let i in arr) {
              arr[i].children = [];
            }
            this.rightDateInfo.children = arr;
          }
          return;
        }
      }
      for (let index = 0; index < arr.length; index++) {
        this.getArrFromId(arr[index].children);
      }
    },
    getList(page, params) {
      this.rightDateInfo = {};
      this.listLoading = true;
      this.getArrFromId(this.treeDeptData);
      this.list = [];
      if (this.rightDateInfo.children) {
        let showArr = JSON.parse(JSON.stringify(this.rightDateInfo.children));
        //是否有搜索
        if (params && params.name) {
          let resultArr = [];
          for (let index = 0; index < showArr.length; index++) {
            if (this.handleDeptfilterNode(params.name, showArr[index])) {
              resultArr.push(showArr[index]);
            }
          }
          showArr = resultArr;
        }
        let startIndex = (this.page.currentPage - 1) * this.page.pageSize;
        let endIndex =
          (this.page.currentPage - 1) * this.page.pageSize + this.page.pageSize;
        this.list = showArr.slice(startIndex, endIndex);
        this.page.total = this.list.length;
      } else {
        this.page.total = 0;
      }
      this.listLoading = false;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
      this.getList();
    },
    handleFilter(param, done) {
      this.query = param;
      this.page.currentPage = 1;
      this.getList(this.page, param);
      done();
    },
    handleRefreshChange() {
      this.getList();
    },
    hangdleGetTreeDeptFn() {
      // 查询部门树
      canUseNetDeptTree().then((response) => {
        this.treeDeptData = response.data.data;

        this.treeDeptData.forEach((item, i) => {
          this.groupExpandedKeys.push(item.id);
        });
        if (this.treeDeptData && this.treeDeptData.length > 0) {
          for (let index = 0; index < this.treeDeptData.length; index++) {
            this.treeDeptData[index].level = "root";
          }
        }
        this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
        if (!this.currentDeptId) {
          this.currentDeptId = this.treeDeptData[0].id;
          this.handleCheckChange(this.treeDeptData[0]);
        }
        setTimeout(() => {
          this.$refs.deptTreeRef.setCurrentKey(this.currentDeptId);
        }, 200);

        // this.getList();
        this.$nextTick(() => {
          // this.$refs.areaTabRef.refGetList();
          if (this.activeTab == "1") {
            this.$refs.areaTabRef.refGetList();
          } else if (this.activeTab == "3") {
            this.$refs.orgTabRef.refGetList();
          } else {
            this.$refs.categoryTabRef.refGetList();
          }
        });
      });
    },
    handleCheckChange(data) {
      // console.log("data==", data);
      this.currentRow = data;
      this.currentInfo = data;
      this.currentDeptId = data.id;
      // this.getList(this.page);
      if (data.type == 0 || data.type == 1) {
        this.activeTab = "1";
      } else {
        this.activeTab = "3";
      }
      this.$nextTick(() => {
        // activeTab
        if (this.activeTab == "1") {
          this.$refs.areaTabRef.refGetList();
        } else if ((this.activeTab = "3")) {
          this.$refs.orgTabRef.refGetList();
        } else {
          this.$refs.categoryTabRef.refGetList();
        }
      });
      let parentNameArr = this.getAllParentArr(
        this.treeDeptDataArr,
        data.id,
        "id",
        "children"
      );
      if (parentNameArr && parentNameArr.length > 0) {
        this.parentNameArr = parentNameArr.reverse();
      }
      // console.log(" this.parentNameArr==", this.parentNameArr);
      // console.log(" this.parentNameArr=222=", parentNameArr);
    },
    /**
     * 1、根据节点id,获取其所有父节点
     * @param {*} list 完整的树结构数组
     * @param {*} id 当前点击的id
     * @param {*} name 需要对比的id 的属性节点
     * @param {*} child 子节点名称
     * @returns
     */
    getAllParentArr(list, id, name, child) {
      for (let i in list) {
        if (list[i][name] == id) {
          return [list[i]];
        }
        if (list[i][child]) {
          let node = this.getAllParentArr(list[i][child], id, name, child);
          if (!!node) {
            return node.concat(list[i]);
          }
        }
      }
    },

    addOrUpdateHandleNew() {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdateRef.handleEditData({}, "add");
      });
    },
    addOrUpdateHandleRootNew() {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdateRef.handleRootEditData({}, "add");
      });
    },
    addOrUpdateHandle(isEdit, id) {
      this.addOrUpdateVisible = true;
      // console.log("id-==", id);
      if (!this.currentInfo.id) {
        return;
      }
      this.$nextTick(() => {
        // this.$refs.addOrUpdate.init(isEdit, id);
        if (this.currentInfo.id) {
          this.$refs.addOrUpdateRef.handleEditData(this.currentInfo, "edit");
        }
      });
    },
    //新增子机构
    addAreaFn() {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        // this.$refs.addOrUpdate.init(isEdit, id);
        if (this.currentRow.id) {
          this.$refs.addOrUpdateRef.handleAddSonData(this.currentRow, "add");
        }
      });
    },
    handleDelete(row) {
      this.$confirm(
        '是否确认删除名称为"' + row.deptName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delObj(row.id);
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("删除成功");
            this.currentDeptId = "";
          } else {
            this.$message.error(res.data.msg);
          }

          this.hangdleGetTreeDeptFn();
        });
    },
    editDep() {
      this.addOrUpdateHandle(true, this.rightDateInfo.id);
    },
    delDep() {
      this.handleDelete(this.currentRow);
    },
  },
};
</script>
<style scoped lang="scss">
.dept-content {
  width: 100%;
  display: flex;
  border: 1px solid transparent;

  .dept-left {
    width: 320px;
    min-width: 320px;
    min-height: calc(100vh - 180px);
    max-height: calc(100vh - 180px);
    margin-top: 8px;
    margin-right: 0;
    margin-left: 20px;
    background: #fff;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 8px;
    padding-top: 20px;
    font-size: 14px;
    overflow: auto;
  }

  .dept-right {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 72%;
  }
}
.jie-content {
  display: flex;
  align-items: center;
  .jie-left {
    width: 3px;
    height: 16px;
    display: inline-block;
    background: #327fff;
    margin-right: 8px;
  }
  .jie-right {
    color: #333;
    font-size: 16px;
  }
}
</style>
