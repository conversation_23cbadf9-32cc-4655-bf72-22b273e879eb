<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
      >
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import { fetchList } from "@/api/admin/sys/logon";
import { tableOption } from "@/const/crud/admin/logon";
import { mapGetters } from "vuex";

export default {
  name: "sysconfig",
  data() {
    return {
      searchForm: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
    };
  },
  methods: {
    getList(page, params) {
      this.tableLoading = true;
      if (params) {
        params["createTime"] = null
      }
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
          },
          params,
          this.searchForm
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.tableData.forEach((item) => {
            item.operateType = item.operateType + "";
          });
          this.page.total = response.data.data.total;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    searchChange(form, done) {
      this.searchForm = form;
      if (form.createTime) {
        if (form.createTime[0]) {
          this.searchForm['startTime'] = form.createTime[0] + ' 00:00:00';
        }
        if (form.createTime[1]) {
          this.searchForm['endTime'] = form.createTime[1] + ' 23:59:59';
        }
      }
      this.searchForm["createTime"] = null
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
  },
};
</script>
