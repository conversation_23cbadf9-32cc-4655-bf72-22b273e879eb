<template>
  <basic-container>
    <div class="avue-crud">
      <!-- 搜索工作栏 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="用户名" prop="toUsername">
          <el-input v-model="queryParams.toUsername" placeholder="请输入接收人用户名" clearable
                    @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item label="模板编码" prop="notifyTemplateCode">
          <el-input v-model="queryParams.notifyTemplateCode" placeholder="请输入模板编码" clearable
                    @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item label="模版类型" prop="notifyTemplateType">
          <el-select v-model="queryParams.notifyTemplateType" placeholder="请选择模版类型" clearable size="small">
            <el-option v-for="dict in this.getDictDataByType(DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE)"
                       :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
                          type="daterange"
                          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                          :default-time="['00:00:00', '23:59:59']"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 列表 -->
      <el-table v-loading="loading" :data="list">
        <el-table-column label="编号" align="center" prop="id"/>
        <el-table-column label="发送人" align="center" prop="fromUsername"/>
        <el-table-column label="接收人" align="center" prop="toUsername"/>
        <el-table-column label="消息内容" align="center" prop="notifyContent"/>
        <el-table-column label="模版类型" align="center" prop="notifyTemplateType">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="scope.row.notifyTemplateType"/>
          </template>
        </el-table-column>
        <el-table-column label="模版编码" align="center" prop="notifyTemplateCode"/>

        <el-table-column label="是否已读" align="center" prop="readStatus">
          <template v-slot="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_READ_STATUS" :value="scope.row.readStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="阅读时间" align="center" prop="readTime" width="180">
          <template v-slot="scope">
            <span>{{ scope.row.readTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template v-slot="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template v-slot="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
                       v-if="permissions.sys_notify_msg_detail">详细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.current" :limit.sync="queryParams.size"
                  @pagination="getList"/>

      <!-- 站内信详细-->
      <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
        <el-form ref="form" :model="form" label-width="160px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="日志主键：">{{ form.id }}</el-form-item>
              <el-form-item label="发送时间：">{{ form.createTime }}</el-form-item>
              <el-form-item label="发送人：">{{ form.fromUsername }}</el-form-item>
              <el-form-item label="接收人：">{{ form.toUsername }}</el-form-item>
              <el-form-item label="模板编号：">{{ form.notifyTemplateCode }}</el-form-item>
              <el-form-item label="模板类型：">
                <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="form.notifyTemplateType"/>
              </el-form-item>
              <el-form-item label="内容：">{{ form.notifyContent }}</el-form-item>
              <el-form-item label="是否已读：">
                <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_READ_STATUS" :value="form.readStatus"/>
              </el-form-item>
              <el-form-item label="阅读时间：">{{ form.readTime }}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="open = false">关 闭</el-button>
        </div>
      </el-dialog>
    </div>
  </basic-container>

</template>

<script>
import {pageNotify} from "@/api/admin/notify/sysmsgnotify";
import {mapGetters} from "vuex";

export default {
  name: "NotifyMessage",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 站内信消息列表
      list: [],
      // 弹出层标题
      title: "站内信详细",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        toUsername: null,
        userType: null,
        notifyTemplateCode: null,
        notifyTemplateType: null,
        createTime: [],
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
  },
  computed: {
    ...mapGetters(["permissions"])
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      pageNotify(this.queryParams).then(response => {
        this.list = response.data.data.records;
        this.total = response.data.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    }
  }
};
</script>
