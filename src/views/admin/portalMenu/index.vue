<template>
  <basic-container>
    <div class="avue-crud">
      <el-form :inline="true">
        <el-form-item>
          <el-button
            v-if="permissions.sys_portalMenu_add"
            icon="el-icon-plus"
            type="primary"
            @click="addOrUpdateHandle(false)"
          >
            添加
          </el-button>
          <!-- <el-button
            v-if="permissions.sys_portalMenu_del"
            icon="el-icon-refresh-left"
            type="primary"
            @click="handleClearMenuCache()"
          >
            缓存
          </el-button> -->
        </el-form-item>
      </el-form>

      <el-table
        border
        v-loading="loading"
        :data="menuList"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildrens' }"
      >
        <el-table-column
          prop="name"
          label="菜单名称"
          :show-overflow-tooltip="true"
          width="180"
        ></el-table-column>
        <!-- <el-table-column prop="icon" label="图标" align="center" width="100">
          <template slot-scope="scope">
            <i :class="scope.row.icon" />
          </template>
        </el-table-column> -->
        <el-table-column prop="sort" label="排序" width="60"></el-table-column>
        <el-table-column
          prop="routeUrl"
          label="路由地址"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <!-- <el-table-column
          prop="component"
          label="组件路径"
          :show-overflow-tooltip="true"
        ></el-table-column> -->
        <!-- <el-table-column prop="type" label="类型" width="80" align="center">
          <template slot-scope="scope">
            <el-tag type="primary" v-if="scope.row.menuType === 'C'"
              >菜单</el-tag
            >
            <el-tag type="success" v-if="scope.row.menuType === 'M'"
              >目录</el-tag
            >
            <el-tag type="danger" v-if="scope.row.menuType === 'T'"
              >顶菜单</el-tag
            >
            <el-tag type="info" v-if="scope.row.menuType === 'F'">按钮</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column
          prop="pubStatus"
          label="是否启用"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <!-- <el-tag type="danger" v-if="scope.row.pubStatus === 0">关闭</el-tag>
            <el-tag type="success" v-if="scope.row.pubStatus === 1"
              >开启</el-tag
            > -->
            <!-- <vxe-switch
              v-model="scope.row.articleState"
              @change="handleSwitchChange(scope)"
              open-label="是"
              close-label="否"
            ></vxe-switch> -->
            <el-switch
              class="switchStyle"
              active-value="1"
              inactive-value="0"
              @change="handleSwitchChange(scope.row)"
              active-text="是"
              inactive-text="否"
              v-model="scope.row.pubStatus"
              active-color="#437fff"
              inactive-color="#BFBFBF"
            >
            </el-switch>

            <!-- <el-switch
              v-model="scope.row.pubStatus"
              class="switchStyle"
              active-text="是"
              inactive-color="#BFBFBF"
              inactive-text="否"
              active-value="0"
              inactive-value="1"
             
            /> -->
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="perms"
          label="权限标识"
          :show-overflow-tooltip="true"
        ></el-table-column> -->
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-plus"
              @click="addOrUpdateHandle(false, scope.row.id)"
              v-if="permissions.sys_portalMenu_add"
              >添加
            </el-button>
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="addOrUpdateHandle(true, scope.row.id)"
              v-if="permissions.sys_portalMenu_edit"
              >修改
            </el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-if="permissions.sys_portalMenu_del"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <table-form
        v-if="addOrUpdateVisible"
        ref="addOrUpdate"
        @refreshDataList="getList"
      ></table-form>
    </div>
  </basic-container>
</template>

<script>
import {
  // clearMenuCache,
  delObj,
  fetchMenuTree,
  activePortalMenu,
  disablePortalMenu,
} from "@/api/admin/sys/portalMenu";
import TableForm from "./menu-form";
import { mapGetters } from "vuex";

export default {
  name: "Menu",
  components: { TableForm },
  data() {
    return {
      addOrUpdateVisible: false,
      // 遮罩层
      loading: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
    };
  },
  created() {
    this.getList();
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    handleSwitchChange(row) {
      console.log("row==", row);
      let params = {
        id: row.id,
      };
      if (row.pubStatus == 0) {
        activePortalMenu(params).then((res) => {
          // console.log("启用===", res);
          if (res.data.code == 200) {
            this.$message({
              showClose: true,
              message: "启用成功！",
              type: "success",
            });
            this.getList();
          }
        });
      } else if (row.pubStatus == 1) {
        disablePortalMenu(params).then((res) => {
          // console.log("停用用===", res);
          if (res.data.code == 200) {
            this.$message({
              showClose: true,
              message: "停用成功！",
              type: "success",
            });
            this.getList();
          }
        });
      }
    },
    addOrUpdateHandle(isEdit, id) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(isEdit, id);
      });
    },
    getList() {
      this.loading = true;
      fetchMenuTree(false).then((response) => {
        this.menuList = response.data.data;
        this.loading = false;
      });
    },
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then(() => {
          this.getList();
          this.$message.success("删除成功");
        });
    },
    handleClearMenuCache: function () {
      clearMenuCache()
        .then(() => {
          this.$message.success("清除缓存成功");
        })
        .catch(function () {});
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .switchStyle {
  //开关小盒子
  .el-switch__core {
    width: 52px !important;
    height: 25px !important;
    border-radius: 60px;
    background: #ffffff; //圆球在左时的开关背景色 只改变内部色，不改变边框色
    //圆球在右时的开关背景色在html结构中active-color="#67c23a"设置即可
  }
  //开关内区域
  .el-switch__label {
    position: absolute;
    padding-top: 1px;
    display: none;
    color: #fff;
    font-size: 10px !important;
    //圆球在左的 文字设置
    &--left {
      color: #606266 !important;
      z-index: 1;
      right: 1px;
    }
    //圆球在右的 文字设置
    &--right {
      color: #ffffff !important;
      z-index: 1;
      left: 1px;
      font-size: 10px;
    }
    &.is-active {
      display: block;
    }
  }
  //圆球靠左的 圆球样式
  .el-switch__core:after {
    top: 15%;
    left: 4%;
    background-color: #d2d2d2;
  }
  //圆球靠右的 圆球样式
  &.el-switch.is-checked .el-switch__core::after {
    top: 15%;
    left: 97%;
    margin-left: -1.063rem;
    background-color: #fff;
  }
}
</style>
