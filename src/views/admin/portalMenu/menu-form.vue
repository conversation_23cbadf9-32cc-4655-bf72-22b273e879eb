<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog :title="!form.id ? '新增' : '修改'" :visible.sync="visible">
    <el-form ref="dataForm" :model="form" :rules="rules" label-width="80px">
      <!-- <el-row>
        <el-col :span="12">
          <el-form-item label="菜单类型" prop="type">
            <el-radio-group v-model="form.menuType" size="small">
              <el-radio-button label="C">菜单</el-radio-button>
              <el-radio-button label="F">按钮</el-radio-button>
              <el-radio-button label="M">目录</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级菜单">
            <treeselect
              v-model="form.parentId"
              :options="menuOptions"
              :normalizer="normalizer"
              :show-count="true"
              placeholder="选择上级菜单"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
      <!-- <el-form-item label="图标" prop="icon" v-if="form.menuType !== 'F'">
        <avue-input-icon
          v-model="form.icon"
          :icon-list="iconList"
        ></avue-input-icon>
      </el-form-item> -->
      <el-form-item label="上级菜单">
        <treeselect
          v-model="form.parentId"
          :options="menuOptions"
          :normalizer="normalizer"
          :show-count="true"
          placeholder="选择上级菜单"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入菜单名称"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="路由地址" prop="routeUrl">
        <el-input
          v-model="form.routeUrl"
          placeholder="请输入路由地址"
          style="width: 95%"
        />
        <el-tooltip
          class="item"
          effect="light"
          content="该页面在浏览器地址栏的路径，例如说：/business/queue/list"
          placement="top"
        >
          <i style="padding-left: 5px" class="el-icon-question" />
        </el-tooltip>
      </el-form-item>
      <!-- <el-form-item
        label="组件地址"
        prop="component"
        v-if="form.menuType === 'C'"
      >
        <el-input
          v-model="form.component"
          placeholder="请输入组件地址"
          style="width: 95%"
        />
        <el-tooltip
          class="item"
          effect="light"
          content="组件在代码工程中的位置，不用+加views，例如说：@/views/business/businessQueue/queue/index-- /business/businessQueue/queue/index"
          placement="top"
        >
          <i style="padding-left: 5px" class="el-icon-question" />
        </el-tooltip>
      </el-form-item> -->
      <!-- <el-form-item label="路由参数" prop="query" v-if="form.menuType === 'C'">
        <el-input
          type="textarea"
          v-model="form.query"
          placeholder="请输入路由参数"
          style="width: 95%"
        />
        <el-tooltip
          class="item"
          effect="light"
          content="必须为json结构：{key:value}"
          placement="top"
        >
          <i style="padding-left: 5px" class="el-icon-question" />
        </el-tooltip>
      </el-form-item> -->
      <!-- <el-form-item label="权限标识" prop="perms">
        <el-input
          v-model="form.perms"
          placeholder="请权限标识"
          maxlength="50"
        />
      </el-form-item> -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="pubStatus">
            <el-radio-group v-model="form.pubStatus">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              controls-position="right"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item
            label="路由缓冲"
            prop="keepAlive"
            v-if="form.type !== '1'"
          >
            <el-radio-group v-model="form.keepAlive">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dataFormSubmit">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addObj,
  fetchMenuTree,
  getObj,
  putObj,
} from "@/api/admin/sys/portalMenu";
import Treeselect from "@riophae/vue-treeselect";
import iconList from "@/const/iconList";
import TableForm from "./";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Menu",
  components: { Treeselect, TableForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项
      menuOptions: [],
      // 是否显示弹出层
      visible: false,
      // 图标
      iconList: iconList,
      form: {
        showType: 0,
        name: undefined,
        memo: undefined,
        perms: undefined,
        parentId: undefined,
        pubStatus: undefined,
        routeUrl: "",
        // sort: "0",
        sort: 99,
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
        ],
        sortOrder: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" },
        ],
        path: [
          { required: true, message: "路由地址不能为空", trigger: "blur" },
        ],
        component: [
          { required: true, message: "组件地址不能为空", trigger: "blur" },
        ],
        pubStatus: [
          { required: true, message: "是否启用不能为空", trigger: "blur" },
        ],
        perms: [
          { required: true, message: "权限标识不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    init(isEdit, id) {
      if (id != null) {
        this.form.parentId = id;
      }
      this.visible = true;
      this.getTreeselect();
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          getObj(id).then((response) => {
            this.form = response.data.data;
          });
        } else {
          this.form.id = undefined;
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if (this.form.parentId === undefined) {
            this.form.parentId = -1;
          }

          if (this.form.id) {
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          } else {
            addObj(this.form).then((data) => {
              console.log("data==", data);
              if (data.data.code === 200) {
                this.$message.success("添加成功");
                this.visible = false;
                this.$emit("refreshDataList");
              } else {
                this.$message.success(data.data.error);
              }
            });
          }
        }
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      fetchMenuTree().then((response) => {
        this.menuOptions = [];
        const menu = { id: -1, name: "根菜单", children: [] };
        menu.children = response.data.data;
        this.menuOptions.push(menu);
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    cancel() {
      this.visible = false;
      this.form.parentId = undefined;
    },
  },
};
</script>
