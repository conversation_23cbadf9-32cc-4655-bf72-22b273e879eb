<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      title="分配应用菜单权限"
      v-if="dialogVisible"
    >
      <el-tabs v-model="clientId" @tab-click="handleClick">
        <el-tab-pane
          :label="item.clientName"
          v-for="item in clientArr"
          :key="item.id"
          :name="item.clientId"
        >
          <app-menu-tree
            :ref="'treeRef' + item.clientId"
            :clientId="item.clientId"
            :roleId="roleId"
          ></app-menu-tree>
        </el-tab-pane>
        <!-- <el-tab-pane label="配置管理">配置管理</el-tab-pane>
        <el-tab-pane label="角色管理">角色管理</el-tab-pane>
        <el-tab-pane label="定时任务补偿">定时任务补偿</el-tab-pane> -->
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateAppMenuPermission()"
          >更 新
        </el-button>
        <el-button type="default" @click="cancalApp()">取消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getAllAppList, addAppMenus } from "@/api/admin/sys/role";

import AppMenuTree from "./AppMenuTree.vue";
export default {
  components: { AppMenuTree },
  data() {
    return {
      dialogVisible: true,
      clientArr: [],
      clientId: "",
    };
  },
  props: {
    roleId: "",
  },
  created() {
    this.getAppListFn();
  },
  methods: {
    getAppListFn() {
      let param = {
        syncMenu: 1,
      };
      getAllAppList(param).then((res) => {
        console.log("获取所有应用==", res);
        if (res.data.code == 200) {
          this.clientArr = res.data.data || [];
          if (this.clientArr && this.clientArr.length > 0) {
            this.clientId = this.clientArr[0].clientId;
          }
        }
      });
    },
    cancalApp() {
      this.$emit("closeDialog");
    },
    handleClose() {
      this.$emit("closeDialog");
      this.dialogVisible = false;
    },
    handleClick(tab, event) {
      console.log("this.clientId---", this.clientId);
      console.log(tab, event);
    },
    //更新
    updateAppMenuPermission() {
      //   console.log("refs==", this.$refs);
      //this.appIdsArr = this.$refs.appMenuTree.getCheckedKeys()
      //   let refArr = this.$refs;
      //   console.log("refArr==", refArr);
      //   if (refArr && refArr.length > 0) {
      //     refArr.forEach((itemRef) => {
      //       console.log("itemRef==", itemRef);
      //       console.log("itemRef=22=", itemRef.$refs);
      //     });
      //   }
      //   this.clientArr.forEach((item) => {
      //     // console.log("this.$refs--", this.$refs["treeRef" + item.clientId][0]);
      //     // console.log(
      //     //   "this.$refs-222-",
      //     //   this.$refs[
      //     //     "treeRef" + item.clientId
      //     //   ][0].$refs.appMenuTree.getCheckedKeys()
      //     // );
      //     let checkedKeys =
      //       this.$refs[
      //         "treeRef" + item.clientId
      //       ][0].$refs.appMenuTree.getCheckedKeys();
      //     let params = {
      //       clientMenuIds: checkedKeys.join(),
      //       roleId: this.roleId,
      //     };
      //     addAppMenus(params).then((res) => {
      //       //   console.log("绑定--", res);
      //       if (res.data.code == 200) {
      //         this.$emit("closeDialog");
      //       }
      //     });
      //   });
      // appTreeData

      // console.log(
      //   "3333----",
      //   this.$refs["treeRef" + this.clientId][0].appTreeData
      // );
      let appTreeData = this.$refs["treeRef" + this.clientId][0].appTreeData;
      let id = "";
      if (appTreeData && appTreeData.length > 0) {
        id = appTreeData[0].id;
      }
      let checkedKeys =
        this.$refs[
          "treeRef" + this.clientId
        ][0].$refs.appMenuTree.getCheckedKeys();
      if (checkedKeys && checkedKeys.length > 0) {
        if (checkedKeys[0] != id) {
          checkedKeys.unshift(id);
        }
      }
      let params = {
        clientMenuIds: checkedKeys.join(),
        roleId: this.roleId,
        // id: id,
      };
      addAppMenus(params).then((res) => {
        //   console.log("绑定--", res);
        if (res.data.code == 200) {
          this.$emit("closeDialog");
        }
      });
    },
  },
};
</script>
