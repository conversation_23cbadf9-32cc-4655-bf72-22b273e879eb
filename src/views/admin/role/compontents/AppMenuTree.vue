<template>
  <div class="dialog-main-tree" v-loading="loading">
    <el-tree
      ref="appMenuTree"
      :data="appTreeData"
      :default-checked-keys="appCheckedKeys"
      :check-strictly="false"
      :props="appDefaultProps"
      :filter-node-method="filterNode"
      class="filter-tree"
      node-key="id"
      default-expand-all
      highlight-current
      show-checkbox
      v-model="checkedKeys"
    />
  </div>
</template>
<script>
import { fetchMenuTree } from "@/api/admin/sys/clientMenu";
import { getBindClientAppList } from "@/api/admin/sys/role";

export default {
  data() {
    return {
      loading: false,
      appCheckedKeys: [],
      appTreeData: [],
      checkedKeys: [],
      appDefaultProps: {
        label: "name",
        value: "id",
      },
    };
  },
  props: {
    clientId: "",
    roleId: "",
  },
  mounted() {
    console.log("clientId==", this.clientId);
    this.getTreeList();
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getTreeList() {
      //this.clientId
      console.log("获取树---");
      this.loading = true;
      let params = {
        clientId: this.clientId,
        lazy: false,
      };
      // fetchMenuTree(params).then((response) => {
      //   console.log("response==", response);
      //   this.appTreeData = response.data.data;
      // });

      let params2 = {
        roleId: this.roleId,
      };
      this.appCheckedKeys = [];
      getBindClientAppList(params2)
        .then((res) => {
          this.loading = false;
          if (res.data.code == 200) {
            this.appCheckedKeys = res.data.data;
            // this.appCheckedKeys = this.resolveAllEunuchNodeId(
            //   this.appTreeData,
            //   this.appCheckedKeys,
            //   []
            // );
            return fetchMenuTree(params);
          }
        })
        .then((response) => {
          if (response.data.code == 200) {
            this.appTreeData = response.data.data;
            this.appCheckedKeys = this.resolveAllEunuchNodeId(
              this.appTreeData,
              this.appCheckedKeys,
              []
            );
          }
        });
    },
    /**
     * 解析出所有的太监节点id
     * @param json 待解析的json串
     * @param idArr 原始节点数组
     * @param temp 临时存放节点id的数组
     * @return 太监节点id数组
     */
    resolveAllEunuchNodeId(json, idArr, temp) {
      for (let i = 0; i < json.length; i++) {
        const item = json[i];
        // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
        if (item.children && item.children.length !== 0) {
          this.resolveAllEunuchNodeId(item.children, idArr, temp);
        } else {
          temp.push(idArr.filter((id) => id === item.id));
        }
      }
      return temp;
    },
  },
};
</script>