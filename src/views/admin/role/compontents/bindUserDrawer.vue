<template>
  <div class="execution">
    <el-drawer
      title="绑定用户"
      :visible.sync="enableDrawer"
      direction="rtl"
      :destroy-on-close="true"
      size="70%"
    >
      <basic-container>
        <avue-crud
          ref="userCrud"
          :table-loading="alreadyTableLoading"
          :page.sync="alreadyBindUserPage"
          :search.sync="searchUserParams"
          :data="alreadyUserList"
          :option="alreadyBindUserOption"
          @on-load="alreadyBindUserList"
          @search-reset="alreadyHandleResetSearch"
          @search-change="alreadySearchChange"
          @refresh-change="alreadyRefreshChange"
          @size-change="alreadySizeChange"
          @current-change="alreadyCurrentChange"
          @selection-change="selectUserChange"
        >
          <template slot="img" slot-scope="scope">
            <div>
              <el-image
                v-if="scope.row.img"
                class="defult-img"
                :src="scope.row.img"
                alt=""
                :onerror="defaultImg"
                :preview-src-list="imgList"
                @click="handleImgsClick(scope.row)"
                :style="{ width: '40px', height: '40px' }"
              ></el-image>
              <!-- <img
                v-else
                :src="''"
                alt="头像"
                class="default-img"
                :onerror="defaultImg"
              /> -->
            </div>
          </template>
          <template slot="lockFlag" slot-scope="scope">
            <el-tag v-if="scope.row.lockFlag == 9" type="danger"
              >{{ scope.label }}
            </el-tag>
            <el-tag v-else>{{ scope.label }}</el-tag>
          </template>
          <template slot-scope="{ scope, size }" slot="menuLeft">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="addBindUser"
              >添加用户
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-plus"
              size="small"
              :disabled="multiple"
              @click="cancelAuth"
              >取消授权
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              :size="size"
              @click="cancelUserSelection()"
              >取消选择
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-drawer>

    <el-dialog
      title="添加用户"
      width="70%"
      destroy-on-close
      :visible.sync="addBindUserVisible"
    >
      <div class="dept-content">
        <div class="dept-left">
          <el-input placeholder="搜索" v-model="deptSearch">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <el-tree
            class="flow-tree"
            :data="treeDeptData"
            :default-expanded-keys="groupExpandedKeys"
            :props="defaultProps"
            :expand-on-click-node="false"
            node-key="id"
            @node-click="handleCheckChange"
            :filter-node-method="handleDeptFilterNode"
            ref="deptTreeRef"
            :render-content="renderContent"
          >
          </el-tree>
        </div>
        <basic-container style="flex: 1; width: calc(100% - 220px)">
          <avue-crud
            ref="crud"
            :page.sync="staffPage"
            :search.sync="searchStaffParams"
            :table-loading="staffPageLoading"
            :data="tableData"
            :option="tableOption"
            @on-load="getList"
            @search-reset="handleResetSearch"
            @selection-change="selectStaffChange"
            @search-change="searchChange"
            @refresh-change="refreshChange"
            @current-change="bindCurrentChange"
            @size-change="bindSizeChange"
          >
            <template slot="profileImage" slot-scope="scope">
              <template v-if="scope.row.profileImage">
                <el-image
                  :src="scope.row.profileImage ? scope.row.profileImage : ''"
                  alt="头像"
                  class="default-img"
                  :onerror="defaultImg"
                  :preview-src-list="srcList"
                  @click="handleImgClick(scope.row)"
                />
              </template>
              <img
                v-else
                :src="scope.row.profileImage ? scope.row.profileImage : ''"
                alt="头像"
                class="default-img"
                :onerror="defaultImg"
              />
            </template>

            <template slot="menuLeft" slot-scope="{ size }">
              <el-button
                type="danger"
                icon="el-icon-delete"
                :size="size"
                @click="toggleSelection()"
                >取消选择
              </el-button>
            </template>
          </avue-crud>
        </basic-container>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="commitSelectStaff">提 交</el-button>
        <el-button @click="addBindUserVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import { getGroupDeptTreeInfo } from "@/api/person/personmngdeptinfo";
import { tableOption } from "@/const/crud/admin/role-bind-user";
import { alreadyBindUserOption } from "@/const/crud/admin/role-alread-bind-user";
// import { pageUserWithRole, bindUser, unbindUser } from "@/api/admin/role";
import { pageUserWithRole, bindUser, unbindUser } from "@/api/admin/sys/role";
import { fetchTree } from "@/api/admin/sys/dept";
import { fetchList } from "@/api/admin/auth/user";
export default {
  name: "bindUserDrawer",
  data() {
    return {
      tableOption: tableOption,
      tableData: [],
      alreadyUserList: [],
      alreadyTableLoading: true,
      addBindUserVisible: false,
      currentRoleId: null,
      multiple: true,
      searchStaffParams: {},
      imgList: "",
      searchArrObj: { roleIds: [] },
      srcList: [],
      staffPageLoading: true,
      staffPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
      },
      enableDrawer: false,
      deptSearch: "",
      groupTreeList: [],
      groupExpandedKeys: [], //默认展示的节点
      groupLevelList: [],
      defaultProps: {
        children: "children",
        label: "name",
        key: "id",
      },
      currentDeptObj: {},
      tableOption: tableOption,
      tableData: [],
      selectUserList: [],

      selectedStaffUserName: [],
      alreadyBindUserOption: alreadyBindUserOption,
      searchUserParams: {
        username: null,
        staffName: null,
      },
      alreadyBindUserPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
      },
      // alreadyUserList: [],
      alreadyTableLoading: true,
      addBindUserVisible: false,
      currentRoleId: null,
      treeDeptData: [],
      currentDeptId: "",
      selectStaffArr: [],
    };
  },
  // data() {
  //   return {

  //     // ---------已经绑定的系统用户 --------

  //   };
  // },
  created() {
    console.log("创建初始化---", this.enableDrawer);
  },
  mounted() {
    console.log("mounted");
  },
  watch: {
    enableDrawer: {
      handler(newVal, oldVal) {
        if (newVal) {
          console.log("监听开关", newVal, oldVal);
        }
      },
    },
  },
  props: {
    roleId: "",
  },
  // computed: {
  //   defaultPreImg() {
  //     return require("../../../../assets/image/img/morentouxiang.png");
  //   },
  //   defaultImg() {
  //     return (
  //       'this.src="' +
  //       require("../../../../assets/image/img/morentouxiang.png") +
  //       '"'
  //     );
  //   },
  // },

  methods: {
    /** 提交选择授权用户操作 */
    commitSelectStaff() {
      let params = {
        roleId: this.currentRoleId,
        userIdList: this.selectedStaffUserName,
      };
      console.log("params===", params);
      bindUser(params).then((res) => {
        console.log("绑定结果", res);
        this.$message({
          showClose: true,
          message: "绑定成功",
          type: "success",
        });
        this.addBindUserVisible = false;
        this.selectedStaffUserName = [];
        this.alreadyBindUserList(
          this.alreadyBindUserPage,
          this.searchUserParams
        );
      });
    },
    /** 取消用户授权操作 */
    cancelAuth() {
      let params = {
        roleId: this.currentRoleId,
        userIdList: this.selectUserList,
      };
      unbindUser(params).then((res) => {
        this.$message({
          showClose: true,
          message: "取消授权成功",
          type: "success",
        });
        this.selectUserList = [];
        this.alreadyBindUserList(
          this.alreadyBindUserPage,
          this.searchUserParams
        );
      });
    },
    toggleSelection(val) {
      this.$refs.crud.toggleSelection(val);
    },
    cancelUserSelection(val) {
      this.$refs.userCrud.toggleSelection(val);
    },
    selectStaffChange(list) {
      console.log("list-selectStaffChange--", list);
      this.selectStaffArr = list;
      this.selectedStaffUserName = list.map((item) => item.id);
    },
    selectUserChange(list) {
      console.log("list-rrrr--", list);

      this.selectUserList = list.map((user) => user.id);
      console.log("选中的系统用户列表", this.selectUserList);
      this.multiple = !list.length;
    },
    // 添加用户弹窗按钮
    addBindUser() {
      // console.log("点击添加按钮");
      this.hangdleGetTreeDeptFn();
      this.getList(this.staffPage);
      this.addBindUserVisible = true;
    },
    alreadyBindUserList(alreadyBindUserPage, params) {
      // console.log("this.currentRoleId==", this.currentRoleId);
      // console.log("this.currentRoleId=roleId=", this.roleId);
      pageUserWithRole(
        Object.assign(
          {
            current: alreadyBindUserPage.currentPage,
            size: alreadyBindUserPage.pageSize,
            roleId: this.currentRoleId,
          },
          params,
          this.searchUserParams
        )
      ).then((res) => {
        // console.log("res===", res);
        this.alreadyUserList = res.data.data.records;
        // this.alreadyUserList.forEach((item) => {
        //   // item.profileImage = process.env.VUE_APP_PIC_URL + item.profileImage;
        //   if (item.profileImage) {
        //     item.img = process.env.VUE_APP_PIC_URL + item.profileImage;
        //   }
        // });
        this.alreadyBindUserPage.total = res.data.data.total;
        this.alreadyTableLoading = false;
      });
    },
    alreadyHandleResetSearch() {
      this.searchUserParams.username = null;
      this.searchUserParams.staffName = null;
      this.alreadyBindUserList(this.alreadyBindUserPage, this.searchUserParams);
    },
    alreadySearchChange(form, done) {
      this.searchUserParams = form;
      this.alreadyBindUserPage.currentPage = 1;
      this.alreadyBindUserList(this.alreadyBindUserPage, form);
      done();
    },
    alreadyRefreshChange() {
      this.alreadyBindUserList(this.alreadyBindUserPage);
    },
    alreadySizeChange(pageSize) {
      this.alreadyBindUserPage.pageSize = pageSize;
    },
    alreadyCurrentChange(current) {
      this.alreadyBindUserPage.currentPage = current;
    },
    bindSizeChange(pageSize) {
      console.log("pageSize==", pageSize);
      console.log(
        "选中的系统用户列表,---pageSize==selectedStaffUserName=",
        this.selectedStaffUserName
      );
      this.staffPage.pageSize = pageSize;
      setTimeout(() => {
        this.toggleSelection(this.selectStaffArr);
      }, 100);
    },
    bindCurrentChange(current) {
      console.log("current==", current);
      console.log(
        "选中的系统用户列表,---current==selectStaffArr=",
        this.selectStaffArr
      );
      this.staffPage.currentPage = current;
      setTimeout(() => {
        this.toggleSelection(this.selectStaffArr);
      }, 100);
    },
    handleCheckChange(data) {
      // let params = {
      //   deptId: data.id,
      // };
      // console.log("data==", data);
      this.currentDeptId = data.id;
      this.getList(this.staffPage);
    },
    // handleCheckChange(data) {
    //   this.currentDeptObj = data;
    //   if (data.deptCode) {
    //     let params = { deptId: data.id };
    //     if (this.searchStaffParams.staffName) {
    //       params.staffName = this.searchStaffParams.staffName;
    //     }
    //     params.isAttendance = this.searchStaffParams.isAttendance;
    //     this.getList(this.staffPage, params);
    //   } else {
    //     // 点击分组的时候是没有部门的，getList列表里面对searchStaffParams.deptId进行了赋值，所以要去掉
    //     if (this.searchStaffParams.deptId) {
    //       this.searchStaffParams.deptId = "";
    //     }
    //     let params = { groupId: data.id };
    //     if (this.searchStaffParams.staffName) {
    //       params.staffName = this.searchStaffParams.staffName;
    //     }
    //     // if (this.searchStaffParams.isAttendance) {
    //     // }
    //     params.isAttendance = this.searchStaffParams.isAttendance;
    //     this.getList(this.staffPage, params);
    //   }
    // },
    handleDeptFilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    renderContent(h, { node, data, store }) {
      return (
        <span>
          <el-tooltip
            class="item"
            effect="dark"
            content={node.label}
            placement="top"
          >
            <span>{node.label}</span>
          </el-tooltip>
        </span>
      );
    },
    // 查询分组数据
    // handleGetGroupListTree() {
    //   getGroupDeptTreeInfo().then((res) => {
    //     console.log("部门信息==树==", res);
    //     if (res.data.code === 0) {
    //       this.groupTreeList = res.data.data;
    //       this.groupTreeList.forEach((item) => {
    //         this.groupExpandedKeys.push(item.id);
    //         this.groupLevelList.push(item); // 存储一级分组
    //         if (item.children) {
    //           item.children.forEach((twoItem) => {
    //             this.groupLevelList.push(twoItem); // 存储二级分组
    //           });
    //         }
    //       });
    //     }
    //   });
    // },
    hangdleGetTreeDeptFn() {
      // 查询部门树
      fetchTree().then((response) => {
        this.treeDeptData = response.data.data;
        this.treeDeptData.forEach((item, i) => {
          this.groupExpandedKeys.push(item.id);
        });
        if (this.treeDeptData && this.treeDeptData.length > 0) {
          this.treeDeptData[0].level = "root";
        }
        //groupExpandedKeys
      });
    },
    handleResetSearch() {
      this.searchArrObj.roleIds = [];
      this.getList(this.staffPage);
    },
    searchChange(form, done) {
      this.searchStaffParams = form;
      this.staffPage.currentPage = 1;

      this.getList(this.staffPage, form);
      done();
    },
    refreshChange() {
      this.getList(this.staffPage);
    },
    handleImgClick(row) {
      this.srcList = [];
      if (row.profileImage) {
        this.srcList.push(row.profileImage);
      } else {
        this.srcList.push(this.defaultPreImg);
      }
    },
    handleImgsClick(row) {
      console.log("row==============", row);
      this.imgList = [];
      if (row.img) {
        this.imgList.push(row.img);
      } else {
        this.imgList.push(this.defaultPreImg);
      }
    },
    // 查询所以用户
    getList(page, params) {
      this.staffPageLoading = true;
      if (this.currentDeptObj.deptCode) {
        this.searchStaffParams.deptId = this.currentDeptObj.id;
      }
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            exceptRoleIds: this.currentRoleId,
            deptId: this.currentDeptId,
          },
          params,
          this.searchStaffParams,
          this.searchArrObj
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;

          this.staffPage.total = response.data.data.total;
          this.staffPageLoading = false;
        })
        .catch(() => {
          this.staffPageLoading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.base-status {
  width: 50px;
  height: 32px;
  line-height: 32px;
  border-radius: 2px;
  display: inline-block;
}

.s-dc {
  background: #fef0f0;
  color: #f56c6c;
}

.s-jz {
  background: #e2f3d9;
  color: #67c23a;
}

.dept-content {
  width: 100%;
  display: flex;

  .dept-left {
    width: 220px;
    min-width: 220px;

    margin: 19px 0 8px 20px;
    background: #fff;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 8px;

    .tip-top {
      margin-top: 20px;
      margin-bottom: 24px;
      display: flex;
      justify-content: space-between;

      .tip-name {
        color: #22242c;
        font-size: 14px;
        font-weight: 500;
      }

      .tip-btn {
        background: #3272ce;
        width: 72px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 4px;
        display: inline-block;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        cursor: pointer;
      }
    }
  }
}

.flow-tree {
  overflow: auto;
  // height: 300px;
  // margin: 10px;

  ::deep(.el-tree-node) {
    .el-tree-node__children {
      overflow: visible !important;
    }
  }
}

.default-img {
  width: 40px;
  height: 40px;
}
</style>
