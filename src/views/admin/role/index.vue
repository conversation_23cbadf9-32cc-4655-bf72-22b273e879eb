

<template>
  <div class="app-container calendar-list-container">
    <basic-container>
      <avue-crud
        ref="crud"
        :option="tableOption"
        :data="list"
        :page.sync="page"
        v-model="form"
        :table-loading="listLoading"
        @on-load="getList"
        @search-change="handleFilter"
        @refresh-change="handleRefreshChange"
        @row-update="update"
        @row-save="create"
      >
        <template slot="menuLeft">
          <el-button
            v-if="permissions.sys_role_add"
            class="filter-item"
            type="primary"
            icon="el-icon-edit"
            @click="$refs.crud.rowAdd()"
            >添加
          </el-button>
          <!-- <el-button
            v-if="permissions.sys_role_import_export"
            class="filter-item"
            plain
            type="primary"
            size="small"
            icon="el-icon-upload"
            @click="$refs.excelUpload.show()"
            >导入
          </el-button>
          <el-button
            v-if="permissions.sys_role_import_export"
            class="filter-item"
            plain
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="exportExcel"
            >导出
          </el-button> -->
        </template>

        <template slot="menu" slot-scope="scope">
          <el-button
            v-if="permissions.sys_role_edit"
            type="text"
            icon="el-icon-edit"
            size="small"
            @click="handleUpdate(scope.row, scope.index)"
            >编辑
          </el-button>
          <el-button
            v-if="permissions.sys_role_perm"
            type="text"
            size="small"
            icon="el-icon-plus"
            @click="handlePermission(scope.row, scope.index)"
            >系统权限
          </el-button>
          <el-button
            v-if="permissions.sys_role_app"
            type="text"
            size="small"
            icon="el-icon-plus"
            @click="handleAppPermission(scope.row, scope.index)"
            >应用权限
          </el-button>
          <el-button
            v-if="permissions.sys_role_menu_app"
            type="text"
            size="small"
            icon="el-icon-plus"
            @click="handleAppMenuPermission(scope.row, scope.index)"
            >应用菜单权限
          </el-button>
          <el-button
            type="text"
            size="small"
            icon="el-icon-user"
            v-if="permissions.sys_role_bind"
            @click="handleBindUser(scope.row, scope.index)"
            >分配用户
          </el-button>
          <el-button
            v-if="permissions.sys_role_del"
            type="text"
            size="small"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, scope.index)"
            >删除
          </el-button>
        </template>
      </avue-crud>

      <!--excel 模板导入 -->
      <excel-upload
        ref="excelUpload"
        title="角色信息导入"
        url="/admin/role/import"
        temp-name="角色信息.xlsx"
        temp-url="/admin/sys-file/local/role.xlsx"
        @refreshDataList="handleRefreshChange"
      ></excel-upload>
    </basic-container>
    <el-dialog
      :visible.sync="dialogPermissionVisible"
      :close-on-click-modal="false"
      title="分配权限"
    >
      <div class="dialog-main-tree">
        <el-tree
          ref="menuTree"
          :data="treeData"
          :default-checked-keys="checkedKeys"
          :check-strictly="false"
          :props="defaultProps"
          :filter-node-method="filterNode"
          class="filter-tree"
          node-key="id"
          highlight-current
          show-checkbox
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updatePermission(roleId)"
          >更 新
        </el-button>
        <el-button type="default" @click="cancal()">取消 </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="appPermissionVisible"
      :close-on-click-modal="false"
      title="分配应用权限"
      v-if="appPermissionVisible"
    >
      <div class="dialog-main-tree">
        <el-tree
          ref="appMenuTree"
          :data="appTreeData"
          :default-checked-keys="appCheckedKeys"
          :check-strictly="false"
          :props="appDefaultProps"
          :filter-node-method="filterNode"
          class="filter-tree"
          node-key="id"
          highlight-current
          show-checkbox
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateAppPermission(roleId)"
          >更 新
        </el-button>
        <el-button type="default" @click="cancalApp()">取消 </el-button>
      </div>
    </el-dialog>
    <app-menu-dialog
      v-if="appMenuFlag"
      :roleId="roleId"
      @closeDialog="handleCloseAppMenu"
    ></app-menu-dialog>
    <bind-user-drawer ref="drawer" :roleId="roleId"></bind-user-drawer>
  </div>
</template>

<script>
import {
  addObj,
  delObj,
  fetchList,
  fetchRoleTree,
  permissionUpd,
  putObj,
  addBindApp,
  getBindAppList,
} from "@/api/admin/sys/role";
import { tableOption } from "@/const/crud/admin/role";
import { fetchMenuTree } from "@/api/admin/sys/menu";
import { getClientGroupTree } from "@/api/admin/sys/appGroup";
import { mapGetters } from "vuex";
import ExcelUpload from "@/components/upload/excel";

import AppMenuDialog from "./compontents/AppMenuDialog.vue";

import BindUserDrawer from "./compontents/bindUserDrawer.vue";
export default {
  name: "TableRole",
  components: { ExcelUpload, AppMenuDialog, BindUserDrawer },
  data() {
    return {
      searchForm: {},
      tableOption: tableOption,
      treeData: [],
      appTreeData: [],
      checkedKeys: [],
      appCheckedKeys: [],
      checkedDsScope: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      appDefaultProps: {
        label: "clientName",
        value: "id",
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      menuIds: "",
      list: [],
      listLoading: true,
      form: {},
      roleId: undefined,
      roleCode: undefined,
      rolesOptions: undefined,
      dialogPermissionVisible: false,
      appPermissionVisible: false,
      appMenuFlag: false,
      appIdsArr: [],
    };
  },
  computed: {
    ...mapGetters(["elements", "permissions"]),
  },
  methods: {
    // 绑定用户
    handleBindUser(row, index) {
      console.log("this.$refs.drawer==", row);
      this.$refs.drawer.enableDrawer = true;
      this.$refs.drawer.currentRoleId = row.id;
      // this.roleId = row.id;
    },
    getList(page, params) {
      this.listLoading = true;
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
          },
          params,
          this.searchForm
        )
      )
        .then((response) => {
          this.list = response.data.data.records;
          this.page.total = response.data.data.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    handleRefreshChange() {
      this.getList(this.page);
    },
    handleFilter(form) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
    },
    handleUpdate(row, index) {
      this.$refs.crud.rowEdit(row, index);
    },
    cancal() {
      this.dialogPermissionVisible = false;
    },
    cancalApp() {
      this.appPermissionVisible = false;
    },

    handlePermission(row) {
      fetchRoleTree(row.id)
        .then((response) => {
          this.checkedKeys = response.data.data;
          return fetchMenuTree();
        })
        .then((response) => {
          this.treeData = response.data.data;
          // 解析出所有的太监节点
          this.checkedKeys = this.resolveAllEunuchNodeId(
            this.treeData,
            this.checkedKeys,
            []
          );
          this.dialogPermissionVisible = true;
          this.roleId = row.id;
          this.roleCode = row.roleCode;
        });
    },
    handleAppPermission(row) {
      this.roleId = row.id;
      this.appCheckedKeys = [];
      this.handleGetBindApp();
      setTimeout(() => {
        this.appPermissionVisible = true;
      }, 300);

      // getAllAppList().then((res) => {
      //   // console.log("res=获取所有的app=", res);
      //   if (res.data.code == 200) {
      //     this.appTreeData = res.data.data;
      //     // this.handleGetBindApp();
      //   }
      // });
    },
    handleAppMenuPermission(row) {
      this.roleId = row.id;
      // this.appCheckedKeys = [];
      // this.handleGetBindApp();
      setTimeout(() => {
        this.appMenuFlag = true;
      }, 300);
    },
    handleCloseAppMenu() {
      this.appMenuFlag = false;
    },
    handleGetBindApp() {
      let params = {
        roleId: this.roleId,
      };
      this.appCheckedKeys = [];
      getBindAppList(params)
        .then((res) => {
          if (res.data.code == 200) {
            this.appCheckedKeys = res.data.data;
            // this.appCheckedKeys = this.resolveAllEunuchNodeId(
            //   this.appTreeData,
            //   this.appCheckedKeys,
            //   []
            // );
            return getClientGroupTree();
          }
        })
        .then((response) => {
          if (response.data.code == 200) {
            this.appTreeData = [];
            let dataArr = response.data.data;
            for (let i in dataArr) {
              let param = {
                clientName: dataArr[i].groupName,
                children: dataArr[i].clientResList,
              };
              this.appTreeData.push(param);
            }
            this.appCheckedKeys = this.resolveAllEunuchNodeId(
              this.appTreeData,
              this.appCheckedKeys,
              []
            );
          }
        });
    },

    /**
     * 解析出所有的太监节点id
     * @param json 待解析的json串
     * @param idArr 原始节点数组
     * @param temp 临时存放节点id的数组
     * @return 太监节点id数组
     */
    resolveAllEunuchNodeId(json, idArr, temp) {
      for (let i = 0; i < json.length; i++) {
        const item = json[i];
        // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
        if (item.children && item.children.length !== 0) {
          this.resolveAllEunuchNodeId(item.children, idArr, temp);
        } else {
          temp.push(idArr.filter((id) => id === item.id));
        }
      }
      return temp;
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getNodeData(data, done) {
      done();
    },
    handleDelete(row, index) {
      var _this = this;
      this.$confirm(
        '是否确认删除名称为"' + row.roleName + '"' + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delObj(row.id);
        })
        .then(() => {
          this.getList(this.page);
          this.$message.success("删除成功");
        });
    },
    create(row, done, loading) {
      if (this.form.dsType === 1) {
        this.form.dsScope = this.$refs.scopeTree.getCheckedKeys().join(",");
      }
      addObj(this.form)
        .then((res) => {
          let data = res.data;
          if (data.code === 200) {
            done();
            this.$message.success("创建成功");
          } else {
            this.$message.error(data.msg);
          }
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    update(row, index, done, loading) {
      if (this.form.dsType === 1) {
        this.form.dsScope = this.$refs.scopeTree.getCheckedKeys().join(",");
      }
      putObj(this.form)
        .then(() => {
          this.getList(this.page);
          done();
          this.$message.success("修改成功");
        })
        .catch(() => {
          loading();
        });
    },
    updatePermission(roleId) {
      this.menuIds = "";
      this.menuIds = this.$refs.menuTree
        .getCheckedKeys()
        .join(",")
        .concat(",")
        .concat(this.$refs.menuTree.getHalfCheckedKeys().join(","));

      let params = {
        roleId: roleId,
        menuIds: this.menuIds,
        authType: 0, // 授权类型 0可用1可分配
        roleType: 1, //角色类型 0安全角色 1业务角色
      };
      permissionUpd(params).then(() => {
        this.dialogPermissionVisible = false;
        this.$store.dispatch("GetMenu", { type: false });
        this.$message.success("修改成功");
      });
    },
    updateAppPermission(roleId) {
      console.log("roleId==", roleId);
      this.menuIds = "";
      // console.log("应用", this.$refs.appTreeData.getCheckedKeys());
      // if (this.$refs.appTreeData.getCheckedKeys().length !== 0) {
      //   this.appIdsArr = this.$refs.appTreeData.getCheckedKeys();
      // }
      this.appIdsArr = this.$refs.appMenuTree.getCheckedKeys();
      console.log("this.appIdsArr==", this.appIdsArr);
      let params = {
        roleId: roleId,
        appIds: this.appIdsArr,
        // authType: 0, //授权类型  0可用  1可分配
        // roleType: 1, // 0安全角色  1业务角色
      };
      addBindApp(params).then(() => {
        // this.dialogClientPermissionVisible = false;
        this.appPermissionVisible = false;
        this.$message.success("修改成功");
      });
    },
    exportExcel() {
      this.downBlobFile("/admin/role/export", {}, "role.xlsx");
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dialog__wrapper {
  .el-dialog {
    width: 61% !important;

    .dialog-main-tree {
      max-height: 400px;
      overflow-y: auto;
    }
  }

  .el-form-item__label {
    width: 20% !important;
    padding-right: 20px;
  }

  .el-form-item__content {
    margin-left: 20% !important;
  }
}
</style>
