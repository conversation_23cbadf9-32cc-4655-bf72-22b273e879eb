<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog
    title="添加用户"
    width="70%"
    :before-close="handleCloseDialog"
    :visible.sync="addBindUserVisible"
    top="5vh"
  >
    <div class="dept-content">
      <!-- <div class="dept-left">
        <el-input placeholder="搜索" v-model="deptSearch">
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-tree
          class="flow-tree"
          :data="treeDeptData"
          :default-expanded-keys="groupExpandedKeys"
          :props="defaultProps"
          :expand-on-click-node="false"
          node-key="id"
          @node-click="handleCheckChange"
          :filter-node-method="handleDeptFilterNode"
          ref="deptTreeRef"
          :render-content="renderContent"
        >
        </el-tree>
      </div> -->
      <basic-container style="flex: 1; width: calc(100% - 220px)">
        <avue-crud
          ref="crud"
          :page.sync="staffPage"
          :search.sync="searchStaffParams"
          :table-loading="staffPageLoading"
          :data="tableData"
          :option="tableOption"
          @on-load="getList"
          @search-reset="handleResetSearch"
          @selection-change="selectStaffChange"
          @search-change="searchChange"
          @refresh-change="refreshChange"
          @current-change="bindCurrentChange"
          @size-change="bindSizeChange"
        >
          <!-- <template slot="profileImage" slot-scope="scope">
            <template v-if="scope.row.profileImage">
              <el-image
                :src="scope.row.profileImage ? scope.row.profileImage : ''"
                alt="头像"
                class="default-img"
                :onerror="defaultImg"
                :preview-src-list="srcList"
                @click="handleImgClick(scope.row)"
              />
            </template>
            <img
              v-else
              :src="scope.row.profileImage ? scope.row.profileImage : ''"
              alt="头像"
              class="default-img"
              :onerror="defaultImg"
            />
          </template> -->

          <template slot="menuLeft" slot-scope="{ size }">
            <el-button
              type="primary"
              icon="el-icon-remove"
              :size="size"
              @click="toggleSelection()"
              >取消选择
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="commitSelectStaff">提 交</el-button>
      <el-button @click="handleCloseDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import debounce from "@/util/debounce";
import { tableOption } from "@/const/crud/admin/role-bind-user";
import { addObj, putObj } from "@/api/admin/sys/role";
import { fetchList } from "@/api/admin/auth/user";
import { fetchTree } from "@/api/admin/sys/dept";
import {
  pageUserWithRole,
  bindUser,
  unbindUser,
  pageUserWithoutRole,
} from "@/api/admin/sys/role";
import {
  queryNoRoleUser,
  bindUserToSecureRole,
} from "@/api/admin/sys/roleManage";

export default {
  name: "roleForm",
  components: {},
  data() {
    return {
      deptSearch: "",
      addBindUserVisible: false,
      // 遮罩层
      loading: true,
      // 菜单树选项
      deptOptions: [],
      // 下拉用户列表
      users: [],
      // 是否显示弹出层
      visible: true,
      staffPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
        pageSizes: [10],
      },
      searchStaffParams: {},
      tableOption: tableOption,
      treeDeptData: [],
      tableData: [],
      currentDeptId: "",
      selectStaffArr: [],
      selectedStaffUserName: [],
      poolareaNoOptions: [],
      staffPageLoading: false,
      deptArr: [],
      chooseArrObj: {},
      currentDeptObj: {},
      groupExpandedKeys: [], //默认展示的节点
      defaultProps: {
        children: "children",
        label: "name",
        key: "id",
      },
      rowId: "",

      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        roleKey: [
          { required: true, message: "请输入角色编码", trigger: "blur" },
        ],
      },
    };
  },
  props: {
    // sonSystemList: {
    //   // type: Array,
    //   // default: function () {
    //   //   return [];
    //   // },
    // },
    // roleId: {
    //   type: String,
    //   default: function () {
    //     return "";
    //   },
    // },
  },
  watch: {
    deptSearch(val) {
      this.$refs.deptTreeRef.filter(val);
    },
  },
  created() {
    // this.getAllProviceFn();
    this.handleGetDaohangTreeFn();
  },
  mounted() {
    if (this.form.areaName) {
      this.$refs.poolareaNoRef.presentText = this.form.areaName;
    }
  },
  methods: {
    /** 提交选择授权用户操作 */
    commitSelectStaff() {
      // chooseArrObj
      let userIds = [];
      Object.keys(this.chooseArrObj).forEach((key) => {
        // console.log(key, this.chooseArrObj[key]);
        let arr = this.chooseArrObj[key];
        if (arr && arr.length > 0) {
          arr.forEach((item) => {
            userIds.push(item.id);
          });
        }
      });
      console.log("userIds==", userIds);
      // return; userIdList:this.selectedStaffUserName
      let params = {
        roleId: this.roleId,
        userIdList: userIds,
      };
      console.log("params===", params);
      bindUserToSecureRole(params).then((res) => {
        console.log("绑定结果", res);
        this.$message({
          showClose: true,
          message: "添加成功",
          type: "success",
        });
        this.addBindUserVisible = false;
        this.selectedStaffUserName = [];
        this.$emit("refushList");
        // this.alreadyBindUserList(
        //   this.alreadyBindUserPage,
        //   this.searchUserParams
        // );
      });
    },
    toggleRowSelection(row) {
      //第一个参数为数据，第二个参数为是否勾选
      this.$refs.crud.toggleRowSelection(row, true);
    },
    addBindUser(rowId) {
      console.log("点击添加按钮=lzs=", rowId);
      this.roleId = rowId;
      // this.hangdleGetTreeDeptFn();
      this.getList(this.staffPage);
      this.addBindUserVisible = true;
    },
    handleResetSearch() {
      this.searchArrObj.roleIds = [];
      this.getList(this.staffPage);
    },
    searchChange(form, done) {
      this.searchStaffParams = form;
      this.staffPage.currentPage = 1;

      this.getList(this.staffPage, form);
      done();
    },
    selectStaffChange(list) {
      console.log("list-selectStaffChange--", list);
      this.selectStaffArr = list;
      this.selectedStaffUserName = list.map((item) => item.id);
      let chooseUser = "role" + this.staffPage.currentPage;
      this.chooseArrObj[chooseUser] = list;
      console.log(" this.chooseArrObj==", this.chooseArrObj);
    },
    bindSizeChange(pageSize) {
      // console.log("pageSize==", pageSize);
      // console.log(
      //   "选中的系统用户列表,---pageSize==selectedStaffUserName=",
      //   this.selectedStaffUserName
      // );
      this.staffPage.pageSize = pageSize;
      // setTimeout(() => {
      //   this.toggleSelection(this.selectStaffArr);
      // }, 100);
    },
    toggleSelection(data) {
      //传递数组进去，会勾选数组中的对象，如果已经勾选则会取消勾选
      this.$refs.crud.toggleSelection(data);
    },
    // toggleSelection(val) {
    //   this.$refs.crud.toggleSelection(val);
    // },
    refreshChange() {
      this.getList(this.staffPage);
    },
    bindCurrentChange(current) {
      console.log("current==", current);
      console.log(
        "选中的系统用户列表,---current==selectStaffArr=",
        this.selectStaffArr
      );
      this.staffPage.currentPage = current;
      // setTimeout(() => {
      //   this.toggleSelection(this.selectStaffArr);
      // }, 100);
    },
    handleCheckChange(data) {
      // let params = {
      //   deptId: data.id,
      // };
      // console.log("data==", data);
      this.currentDeptId = data.id;
      this.getList(this.staffPage);
    },
    handleCloseDialog() {
      this.addBindUserVisible = false;
      this.$emit("handleCloseDialog");
    },
    handleDeptFilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    hangdleGetTreeDeptFn() {
      // 查询部门树
      fetchTree().then((response) => {
        this.treeDeptData = response.data.data;
        this.treeDeptData.forEach((item, i) => {
          this.groupExpandedKeys.push(item.id);
        });
        if (this.treeDeptData && this.treeDeptData.length > 0) {
          this.treeDeptData[0].level = "root";
        }
        //groupExpandedKeys
      });
    },
    renderContent(h, { node, data, store }) {
      return (
        <span>
          <el-tooltip
            class="item"
            effect="dark"
            content={node.label}
            placement="top"
          >
            <span>{node.label}</span>
          </el-tooltip>
        </span>
      );
    },
    // 查询所以用户
    getList(page, params) {
      this.staffPageLoading = true;
      // if (this.currentDeptObj.deptCode) {
      //   this.searchStaffParams.deptId = this.currentDeptObj.id;
      // }
      //pageUserWithoutRole fetchList
      queryNoRoleUser(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            // exceptRoleIds: this.currentRoleId,
            // deptId: this.currentDeptId,
            secureRoleId: this.roleId,
          },
          params,
          this.searchStaffParams,
          this.searchArrObj
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;

          this.staffPage.total = response.data.data.total;
          this.staffPageLoading = false;

          // this.toggleSelection();

          let chooseUser = "role" + page.currentPage;
          let chooseArr = this.chooseArrObj[chooseUser];
          // console.log("chooseArr====", chooseArr);
          setTimeout(() => {
            if (chooseArr && chooseArr.length > 0) {
              // console.log("chooseArr===222222222233333=");
              chooseArr.forEach((item) => {
                // this.toggleRowSelection(item);
                let fIndex = this.tableData.findIndex((fItem) => {
                  return fItem.id == item.id;
                });
                // console.log("fIndex==", fIndex);
                if (fIndex != -1) {
                  this.toggleRowSelection(this.tableData[fIndex]);
                }
              });
            }
          }, 300);
        })
        .catch(() => {
          this.staffPageLoading = false;
        });
    },
    getAllProviceFn() {
      getAllProvinces().then((res) => {
        // console.log("获取省份数据==", res);
        if (res.data.code == 200) {
          this.deptArr = res.data.data;
        }
      });
    },

    init(isEdit, id) {
      if (id !== null) {
        this.form.parentId = id;
      }
      this.form.remark = "";
      this.visible = true;
      // this.getTreeselect();
      this.handleGetDaohangTreeFn();
      // this.listAllUser();
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          // getObj(id).then((response) => {
          //   this.form = response.data.data;
          //   console.log("from==", this.form);
          //   if (this.form.areaName) {
          //     setTimeout(() => {
          //       this.$refs.poolareaNoRef.presentText = this.form.areaName;
          //     }, 400);
          //   }
          // });
        } else {
          this.form.id = undefined;
        }
      });
    },
    listAllUser() {
      listUser().then((response) => {
        this.users = response.data.data;
      });
    },
    debounceDataFormSubmitFn: debounce(function () {
      this.dataFormSubmit();
    }, 500),
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        // this.form.menuType = "C";
        if (valid) {
          if (this.form.id) {
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          } else {
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          }
        }
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getDaohangTree().then((response) => {
        this.deptOptions = [];
        const dept = { id: -1, name: "根", children: response.data.data };
        this.deptOptions.push(dept);
      });
    },
    handleGetDaohangTreeFn() {
      fetchMenuTree().then((res) => {
        // console.log("获取导航树", res);
        if (res.data.code == 200) {
          let treeDeptData = res.data.data;
          treeDeptData.forEach((item) => {
            item.show = false;
          });
          this.treeDeptData = treeDeptData;
          this.deptOptions = [];
          const dept = { id: -1, name: "根", children: res.data.data };
          this.deptOptions.push(dept);
          // this.$refs.deptTreeRef.setCurrentKey(this.currentDhId);

          //
        }
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.dept-content {
  width: 100%;
  display: flex;

  .dept-left {
    width: 220px;
    min-width: 220px;

    margin: 19px 0 8px 20px;
    background: #fff;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 8px;

    .tip-top {
      margin-top: 20px;
      margin-bottom: 24px;
      display: flex;
      justify-content: space-between;

      .tip-name {
        color: #22242c;
        font-size: 14px;
        font-weight: 500;
      }

      .tip-btn {
        background: #3272ce;
        width: 72px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        border-radius: 4px;
        display: inline-block;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        cursor: pointer;
      }
    }
  }
}
</style>
