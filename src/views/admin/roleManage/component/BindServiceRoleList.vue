<template>
  <div>
    <div class="top-content">
      <div class="title-content">
        <span class="t-line"></span>
        <span class="t-word">业务角色列表</span>
      </div>

      <div class="btn-content">
        <!-- <div class="search-content">
          <el-input placeholder="输入菜单名称过滤" v-model="searchMenuName">
          </el-input>
        </div> -->
        <template
          v-if="
            currentRole.parentId == '-2' ||
            (treeDataArr &&
              treeDataArr.length > 0 &&
              treeDataArr[0].id != currentRole.id &&
              currentRole.parentId == treeDataArr[0].id) ||
            (treeDataArr &&
              treeDataArr.length > 0 &&
              treeDataArr[0].parentId == '-2')
          "
        >
          <el-button type="" @click="addServiceRole">新增 </el-button>
          <el-button type="danger" @click="delServiceRole">删除 </el-button>
        </template>
      </div>
    </div>
    <div
      class="table-container"
      v-loading="loading"
      element-loading-text="数据加载中"
      element-loading-spinner="el-icon-loading"
    >
      <el-table
        class="table-content"
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <!--  @current-change="handleCurrentRowChange" -->

        <el-table-column type="selection" width="55"> </el-table-column>
        <!-- <el-table-column label="日期" width="120">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column> -->
        <!-- <el-table-column label="选择" width="55">
            <template slot-scope="scope">
              
              <el-radio :label="scope.row.id" v-model="rowId"
                ><span></span
              ></el-radio>
            </template>
          </el-table-column> -->
        <el-table-column
          type="index"
          width="50"
          label="序号"
          :index="indexMethod"
        >
        </el-table-column>
        <el-table-column prop="roleName" label="角色名称"> </el-table-column>
        <el-table-column prop="roleKey" label="角色编码"> </el-table-column>
        <el-table-column prop="roleDesc" label="说明"> </el-table-column>

        <!-- <el-table-column
          prop="systemType"
          label="接入类型"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.systemType == 0"> 应用级 </span>
            <span v-else-if="scope.row.systemType == 1"> 菜单级 </span>
          </template>
        </el-table-column> -->
      </el-table>
      <div class="page-content">
        <el-pagination
          v-if="pageObj.total > 0"
          background
          style="position: relative"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageObj.pageCurrent"
          :page-sizes="[10]"
          :page-size="pageObj.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageObj.total"
        >
        </el-pagination>
      </div>
    </div>
    <add-bind-service-role-dialog
      ref="addBindRef"
      @refushList="refushbindServiceRoleList"
    ></add-bind-service-role-dialog>
  </div>
</template>
<script>
import debounce from "@/util/debounce";
import {
  safeRoleTree,
  safeRoleCreate,
  queryBindServiceRoleList,
  delServiceRoleInfo,
} from "@/api/admin/sys/roleManage";
import { fetchList, delObj, unbindUser, putObj } from "@/api/admin/sys/role";

import AddBindServiceRoleDialog from "./AddBindServiceRoleDialog.vue";
export default {
  name: "bindServiceRoleLisst",
  components: {
    AddBindServiceRoleDialog,
  },
  data() {
    return {
      pageObj: {
        current: 1,
        size: 10,
        total: 0,
      },
      tableData: [],
      roleId: "",
      chooseArrObj: {},
      loading: false,
      currentRole: {},
    };
  },
  props: {
    treeDataArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  methods: {
    refushbindServiceRoleList() {
      this.initRoleList(this.currentRole);
    },
    initRoleList(row) {
      this.currentRole = row;
      // console.log("lzs----", roleId);
      this.roleId = row.id;
      this.getRolesList();
    },
    addServiceRole() {
      // console.log("addServiceRole---", this.roleId);
      this.$refs.addBindRef.addBindService(this.roleId);
    },
    handleSelectionChange(val) {
      this.chooseSelection = val;
      // console.log("val====", val);

      let chooseUser = "delRole" + this.pageObj.current;
      this.chooseArrObj[chooseUser] = val;
    },
    delServiceRole() {
      // console.log("delServiceRole----删除----");
      let that = this;
      let userIds = [];
      Object.keys(this.chooseArrObj).forEach((key) => {
        // console.log(key, this.chooseArrObj[key]);
        let arr = this.chooseArrObj[key];
        if (arr && arr.length > 0) {
          arr.forEach((item) => {
            userIds.push(item.id);
          });
        }
      });
      let serviceIds = userIds.join(",");
      let params = {
        secureRoleId: that.roleId,
        operateRoleIds: serviceIds,
      };

      this.$confirm("是否确认删除该角色下的业务角色", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delServiceRoleInfo(params);
        })
        .then((res) => {
          // console.log("data=解绑用户下的角色---=", res);
          if (res.status && res.data.code == 200) {
            this.refushbindServiceRoleList();
            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    handleSizeChange(val) {
      console.log("handleSizeChange==", val);
      this.pageObj.size = val;
      this.getRolesList();
    },
    handleCurrentChange(val) {
      console.log("handleCurrentChange==", val);
      this.pageObj.current = val;
      this.getRolesList();
    },
    debounceGetRoleList: debounce(function () {
      this.getRolesList();
    }, 1000),
    getRolesList() {
      this.loading = true;
      let params = {
        current: this.pageObj.current,
        size: this.pageObj.size,
        roleName: this.searchRoleName,
        secureRoleId: this.roleId,
      };
      queryBindServiceRoleList(params)
        .then((res) => {
          // console.log("res=lzs==", res);
          this.loading = false;
          if (res.data.code == 200) {
            this.tableData = res.data.data.records;
            this.pageObj.total = res.data.data.total;
          }
        })
        .catch((err) => {
          // console.log("err==", err);
          this.loading = false;
        });
    },
    indexMethod(index) {
      return (this.pageObj.current - 1) * this.pageObj.size + index + 1;
    },
  },
};
</script>
<style scoped lang="scss">
@import "@/views/serviceRole/compontents/role.scss";

.table-container {
  position: relative;
  padding: 20px;
  .page-content {
    margin-top: 30px;
    text-align: right;
  }
}
</style>