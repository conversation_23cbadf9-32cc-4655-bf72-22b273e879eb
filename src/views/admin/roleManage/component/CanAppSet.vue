<template>
  <div class="dialog-main-tree">
    <div
      class="tree-container"
      v-loading="loading"
      element-loading-text="数据加载中"
      element-loading-spinner="el-icon-loading"
    >
      <div class="top-content">
        <div class="title-content">
          <span class="t-line"></span>
          <span class="t-word">选择可用应用</span>
        </div>

        <div class="btn-content">
          <div class="search-content">
            <el-input
              placeholder="输入可用应用名称过滤"
              v-model="searchMenuName"
              class="sc-input"
              clearable
            >
            </el-input>
          </div>
          <el-button type="" @click="updateResetTree">重置 </el-button>
          <el-button
            type="primary"
            @click="updateAppPermission"
            v-if="
              currentRole.parentId == '-2' ||
              (treeDataArr &&
                treeDataArr.length > 0 &&
                treeDataArr[0].id != currentRole.id &&
                currentRole.parentId == treeDataArr[0].id) ||
              (treeDataArr &&
                treeDataArr.length > 0 &&
                treeDataArr[0].parentId == '-2')
            "
            >保存
          </el-button>
        </div>
      </div>

      <!-- <el-tree
        v-if="treeData && treeData.length > 0"
        ref="menuTree"
        :data="treeData"
        :default-checked-keys="checkedKeys"
        :check-strictly="false"
        :props="defaultProps"
        :filter-node-method="filterNode"
        class="filter-tree"
        node-key="id"
        highlight-current
        show-checkbox
      /> -->
      <el-tree
        v-if="appTreeData && appTreeData.length > 0"
        ref="appMenuTree"
        :data="appTreeData"
        :default-checked-keys="appCheckedKeys"
        :check-strictly="false"
        :props="appDefaultProps"
        :filter-node-method="filterNode"
        class="filter-tree"
        node-key="id"
        highlight-current
        show-checkbox
      />
    </div>
  </div>
</template>
<script>
import {
  addBindApp,
  permissionUpd,
  getBindAppList,
  getBindAnquanAppList,
} from "@/api/admin/sys/role";
// import { fetchMenuTree } from "@/api/admin/sys/menu";
import { getClientGroupTree, getCanUseAppList } from "@/api/admin/sys/appGroup";

export default {
  data() {
    return {
      appTreeData: [],
      checkedKeys: [],
      appCheckedKeys: [],
      oldCheckedKeys: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      appDefaultProps: {
        label: "clientName",
        value: "id",
      },
      appIdsArr: [],
      loading: false,
      searchMenuName: "",
      roleId: "",
      menuIds: "",
      currentRole: {},
    };
  },
  props: {
    treeDataArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {
    searchMenuName(val) {
      this.$refs.appMenuTree.filter(val);
    },
  },
  methods: {
    filterNode(value, data) {
      // console.log("data==", data);
      if (!value) return true;
      return data.clientName.indexOf(value) !== -1;
    },
    updateAppPermission() {
      // this.roleId = roleId;
      //   console.log("roleId==", roleId);
      this.menuIds = "";

      this.appIdsArr = this.$refs.appMenuTree.getCheckedKeys();
      //   console.log("this.appIdsArr==", this.appIdsArr);
      let params = {
        roleId: this.roleId,
        appIds: this.appIdsArr,
        authType: 0, //授权类型  0可用  1可分配
        roleType: 0, // 0安全角色  1业务角色
      };
      addBindApp(params).then((res) => {
        // console.log("修改--", res);
        if (res.data.code == 200) {
          this.$message.success("修改成功");
          this.getNewChecksData();
        }
      });
    },
    handleInitData(row) {
      // console.log("roww---", row);
      this.currentRole = row;

      if (row && row.id) {
        this.roleId = row.id;
        this.handleGetBindApp(row);
      }
    },
    updateResetTree() {
      this.appCheckedKeys = JSON.parse(JSON.stringify(this.oldCheckedKeys));

      // this.$refs.menuTree.setCheckedKeys([3]);
      this.$refs.appMenuTree.setCheckedKeys(this.appCheckedKeys);
      this.$forceUpdate();
    },

    handleGetBindApp() {
      this.loading = true;
      let params = {
        roleId: this.roleId,
        authType: 0, //授权类型：0可用，1可分配
        roleType: 0, //角色类型：0安全角色，1业务角色
      };
      this.appCheckedKeys = [];
      getBindAnquanAppList(params)
        .then((res) => {
          // console.log("res--lzs3===", res);
          this.loading = false;
          if (res.data.code == 200) {
            // this.appCheckedKeys = res.data.data;

            let keysObj = res.data.data;
            if (keysObj instanceof Array) {
              let arr = res.data.data;

              // this.appCheckedKeys = res.data.data;
            } else {
              let arr1 = keysObj[0];
              if (arr1 && arr1.length > 0) {
                arr1.forEach((cItem) => {
                  this.appCheckedKeys.push(cItem.id);
                });
              }
            }

            this.oldCheckedKeys = JSON.parse(
              JSON.stringify(this.appCheckedKeys)
            );
            // this.appCheckedKeys = this.resolveAllEunuchNodeId(
            //   this.appTreeData,
            //   this.appCheckedKeys,
            //   []
            // );
            let params = {
              authType: 0, // authType 0：可用 1：可分配  不传默认1
            };
            return getCanUseAppList(params);
            // return getClientGroupTree();
          }
        })
        .then((response) => {
          // console.log("应用数据==", response);
          if (response.data.code == 200) {
            this.appTreeData = [];
            this.appTreeData = response.data.data;
            // let dataArr = response.data.data;
            // for (let i in dataArr) {
            //   let param = {
            //     clientName: dataArr[i].groupName,
            //     children: dataArr[i].clientResList,
            //   };
            //   this.appTreeData.push(param);
            // }
          }
        })
        .catch((err) => {
          // console.log("err==", err);
          this.loading = false;
        });
    },
    getNewChecksData() {
      let params = {
        roleId: this.roleId,
        authType: 0, //授权类型：0可用，1可分配
        roleType: 0, //角色类型：0安全角色，1业务角色
      };
      this.appCheckedKeys = [];
      getBindAnquanAppList(params).then((res) => {
        // this.loading = false;
        if (res.data.code == 200) {
          let keysObj = res.data.data;
          if (keysObj instanceof Array) {
            let arr = res.data.data;
          } else {
            let arr1 = keysObj[0];
            if (arr1 && arr1.length > 0) {
              arr1.forEach((cItem) => {
                this.appCheckedKeys.push(cItem.id);
              });
            }
          }

          this.oldCheckedKeys = JSON.parse(JSON.stringify(this.appCheckedKeys));
        }
      });
    },
    /**
     * 解析出所有的太监节点id
     * @param json 待解析的json串
     * @param idArr 原始节点数组
     * @param temp 临时存放节点id的数组
     * @return 太监节点id数组
     */
    resolveAllEunuchNodeId(json, idArr, temp) {
      for (let i = 0; i < json.length; i++) {
        const item = json[i];
        // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
        if (item.children && item.children.length !== 0) {
          this.resolveAllEunuchNodeId(item.children, idArr, temp);
        } else {
          temp.push(idArr.filter((id) => id === item.id));
        }
      }
      return temp;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/views/serviceRole/compontents/role.scss";
</style>
<style lang="scss" scoped>
.dialog-main-tree {
  min-height: 600px;
  //   height: 900px;

  position: relative;
  .tree-container {
    // width: 50%;
    position: relative;
    padding: 10px;
    // border: 1px solid #ebeef5;
  }
  .search-content {
    // width: 30%;
    width: 200px;
    margin-right: 12px;
  }
  .filter-tree {
    width: 100%;
    // height: 800px;
    // overflow-y: scroll;
    overflow-y: scroll;
    height: calc(100vh - 298px);
  }
  .btn-content {
    display: flex;
    // position: absolute;
    // left: 55%;
    // top: 2px;
  }
}
</style>