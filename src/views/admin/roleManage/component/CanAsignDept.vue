<template>
  <div class="dialog-main-tree">
    <div
      class="tree-container"
      v-loading="loading"
      element-loading-text="数据加载中"
      element-loading-spinner="el-icon-loading"
    >
      <div class="top-content">
        <div class="title-content">
          <span class="t-line"></span>
          <span class="t-word">选择可管理的组织机构</span>
        </div>

        <div class="btn-content">
          <div class="search-content">
            <el-input
              placeholder="输入组织机构名称过滤"
              v-model="searchMenuName"
              clearable
              class="sc-input"
            >
            </el-input>
          </div>
          <el-button type="" @click="updateResetTree">重置 </el-button>
          <el-button
            type="primary"
            @click="updateAppPermission"
            v-if="
              currentRole.parentId == '-2' ||
              (treeDataArr &&
                treeDataArr.length > 0 &&
                treeDataArr[0].id != currentRole.id &&
                currentRole.parentId == treeDataArr[0].id) ||
              (treeDataArr &&
                treeDataArr.length > 0 &&
                treeDataArr[0].parentId == '-2')
            "
            >保存
          </el-button>
        </div>
      </div>
      <div class="had-content">
        <div class="hc-title">已保存组织机构:</div>
        <div class="hc-content">
          <span v-for="(sItem, i) in hadSelectArr" :key="i">
            {{ sItem.deptName }}{{ i == hadSelectArr.length - 1 ? "" : "，" }}
          </span>
        </div>
      </div>

      <!-- <el-tree
        v-if="treeData && treeData.length > 0"
        ref="menuTree"
        :data="treeData"
        :default-checked-keys="checkedKeys"
        :check-strictly="false"
        :props="defaultProps"
        :filter-node-method="filterNode"
        class="filter-tree"
        node-key="id"
        highlight-current
        show-checkbox
      /> -->
      <el-tree
        v-if="appTreeData && appTreeData.length > 0"
        ref="appMenuTree"
        :data="appTreeData"
        :default-checked-keys="appCheckedKeys"
        :check-strictly="false"
        :props="appDefaultProps"
        :filter-node-method="filterNode"
        :load="loadNode"
        lazy
        class="filter-tree"
        node-key="id"
        highlight-current
        show-checkbox
        @check="checkOrgUnitNode"
        @check-change="checkChangeNode"
      />
      <el-pagination
        v-if="searchFlag"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchPage.current"
        :page-sizes="[100, 200, 300, 400]"
        :page-size="searchPage.size"
        layout=" pager, jumper"
        small
        :total="searchPage.total"
        :pager-count="5"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import {
  addBindApp,
  permissionUpd,
  getBindAppList,
} from "@/api/admin/sys/role";
import {
  fetchTree,
  canUseNetDeptTree,
  canUseDeptSonTree,
  searchDeptByName,
} from "@/api/admin/sys/dept";
import { updateRoleDept, querySecureOrg } from "@/api/admin/sys/roleManage";
import debounce from "@/util/debounce";
export default {
  data() {
    return {
      appTreeData: [],
      checkedKeys: [],
      appCheckedKeys: [],
      oldCheckedKeys: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      appDefaultProps: {
        label: "deptName",
        value: "id",
      },
      appIdsArr: [],
      loading: false,
      searchMenuName: "",
      roleId: "",
      menuIds: "",
      currentRole: {},
      assignableOrgList: [],
      searchFlag: false,
      searchPage: {
        current: 1,
        size: 100,
        total: 0,
      },
      treeDeptDataArr: [],
      hadSelectArr: [], //保存后已勾选的机构
    };
  },
  props: {
    treeDataArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {
    searchMenuName(val) {
      // console.log("val---", val);
      // this.$refs.appMenuTree.filter(val);
      if (val && val.length > 0) {
        this.searchFlag = true;
      } else {
        this.searchFlag = false;
      }

      this.debounceSearchDept(val);
    },
  },
  methods: {
    async loadNode(node, resolve) {
      // console.log("node==", node);
      // let oldNodeCheck = JSON.parse(JSON.stringify(node));
      let oldNodeCheck = node.checked;

      if (!node.checked) {
        node.data.disabled = false;
      }
      setTimeout(() => {
        if (!oldNodeCheck) {
          this.updateResetTree();
        }
      }, 1000);
      if (node.level === 0) {
        // 当前节点可用并未勾选

        return resolve(node.data);
      } else if (node.level > 0) {
        // return resolve([{ name: "deptName" }]);
        //canUseDeptSonTree
        // if (node.data.deptName == "郑州大数据1") {
        //   return;
        // }
        const child = await canUseDeptSonTree({ deptId: node.data.id });

        node.data.children = child.data.data;

        // resolve(child.data.data);

        if (oldNodeCheck) {
          this.setChildrenStatus(node.data.children, true, true);
        } else if (!node.disabled) {
          // 父节点可用，子节点可用
          this.setChildrenStatus(node.data.children, false, false);
          // node.data.children.forEach(s => {
          //   let a = this.assignabledOrgUnitIds.indexOf(s.id)
          //   // 子节点是否授权
          //   if (a !== -1) {
          //     this.setCurrentNodeStatus(s, true, false)
          //   }
          // })
        }
        return resolve(child.data.data);
      }
    },
    filterNode(value, data) {
      // console.log("data==", data);
      if (!value) return true;
      return data.deptName.indexOf(value) !== -1;
    },

    /**复选框选中/取消选中时触发check */
    checkOrgUnitNode(data, treeStatus) {
      // console.log("1122----", data);
      // console.log("1122--treeStatus--", treeStatus);
      let selected = treeStatus.checkedKeys.indexOf(data.id);
      // this.selectTree = false
      // this.secRoleTreeId = this.secRoleTreeIding
      // 验证是否有变更

      if (
        this.assignableOrgList == [] ||
        this.assignableOrgList == "" ||
        this.assignableOrgList == undefined ||
        this.assignableOrgList == null
      ) {
        this.assignableOrgList.push(data.id);
      } else {
        for (let i = 0; i < this.assignableOrgList.length; i++) {
          if (this.assignableOrgList[i] == data.id) {
            this.assignableOrgList.splice(i, 1);

            i--;
          } else {
            this.assignableOrgList.push(data.id);
          }
        }
      }
      //this.assignableOrgList.filter(e => e.id !== data.id)

      // -1 时选中
      if (selected !== -1) {
        //this.selectTree = false
        if (data.children && data.children.length > 0) {
          this.setChildrenStatus(data.children, true, true);
        }
      } else {
        //this.selectTree = true
        if (data.children && data.children.length > 0) {
          this.setChildrenStatus(data.children, false, false);
        }
      }
    },
    checkChangeNode(data, s1, s2) {
      //传递给 data 属性的数组中该节点所对应的对象、s1节点本身是否被选中、s1节点的子树中是否有被选中的节点
      // console.log("data==", data);
      // console.log("s1==", s1);
      // console.log("说==", s2);
      //如果s1 为true,说明被选中，s2为false 说明是全选
      if (s1 && !s2) {
        if (data.children && data.children.length > 0) {
          data.children.forEach((sItem) => {
            sItem.disabled = true;
          });
        }
      }
    },

    /** 统一处理子节点为相同的勾选状态和禁用状态*/
    setChildrenStatus(treeList, isSelected, isDisabled) {
      treeList.forEach((s) => {
        s.disabled = isDisabled;
        // console.log("s==", s);
        setTimeout(() => {
          this.$refs.appMenuTree.setChecked(s.id, isSelected);
        }, 200);
        // this.$refs.appMenuTree.setChecked(s.id, isSelected);
        if (s.children && s.children.length > 0) {
          this.setChildrenStatus(s.children, isSelected, isDisabled);
        }
      });
    },

    updateAppPermission() {
      // this.roleId = roleId;
      //   console.log("roleId==", roleId);
      this.menuIds = "";

      let checkedNodesArr = this.$refs.appMenuTree.getCheckedNodes();
      let checkedOrgunt = checkedNodesArr.filter((e) => !e.disabled);
      // console.log("checkedNodesArr==", checkedNodesArr);
      // console.log("checkedOrgunt==", checkedOrgunt);
      let parr = [];
      if (checkedOrgunt && checkedOrgunt.length > 0) {
        checkedOrgunt.forEach((item) => {
          let childFlag = 1;
          // if (item.children && item.children.length > 0) {
          //   childFlag = 1;
          // }
          let obj = { contSubFlag: childFlag, deptId: item.id };
          parr.push(obj);
        });
      }
      this.appIdsArr = this.$refs.appMenuTree.getCheckedKeys();
      //   console.log("this.appIdsArr==", this.appIdsArr);
      let params = {
        roleId: this.roleId,
        deptIds: this.appIdsArr.join(),
        sysSecureRoleDeptBindVos: parr,
      };
      updateRoleDept(params).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("修改成功");
          this.getNewChecksData();
        }
      });
    },
    handleInitData(row) {
      // console.log("roww---", row);
      this.searchMenuName = "";
      this.currentRole = row;
      if (row && row.id) {
        this.roleId = row.id;
        this.handleGetBindApp(row);
      }
    },
    updateResetTree() {
      this.appCheckedKeys = JSON.parse(JSON.stringify(this.oldCheckedKeys));
      // console.log("this.appCheckedKeys==", this.appCheckedKeys);
      // this.$refs.menuTree.setCheckedKeys([3]);
      this.$refs.appMenuTree.setCheckedKeys(this.appCheckedKeys);
      this.$forceUpdate();
    },

    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchPage.size = val;
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchPage.current = val;
      this.debounceSearchDept(this.searchMenuName);
    },
    debounceSearchDept: debounce(function (name) {
      if (name) {
        this.handleSearchDept(name, true);
      } else {
        this.appTreeData = JSON.parse(JSON.stringify(this.treeDeptDataArr));
      }
    }, 500),
    handleSearchDept(name, searchFlag) {
      let params = {
        keyWord: name,
      };
      if (searchFlag) {
        params.current = this.searchPage.current;
        params.size = this.searchPage.size;
      }
      searchDeptByName(params).then((res) => {
        // console.log("res==搜索记过=", res);
        if (res.data.code == 200) {
          this.appTreeData = res.data.data.records;
          this.searchPage.total = res.data.data.total;
        }
        // console.log("this.appMenuTree=", this.appMenuTree);
      });
    },
    handleGetBindApp() {
      let params = {
        sysSecureRoleId: this.roleId,
        roleId: this.roleId,
      };
      this.appCheckedKeys = [];
      // querySecureOrg(params) getBindAppList
      querySecureOrg(params)
        .then((res) => {
          if (res.data.code == 200) {
            this.hadSelectArr = res.data.data;
            let keysArr = res.data.data;
            if (keysArr && keysArr.length > 0) {
              keysArr.forEach((citem) => {
                this.appCheckedKeys.push(citem.id);
              });
            }

            this.oldCheckedKeys = JSON.parse(
              JSON.stringify(this.appCheckedKeys)
            );

            // return fetchTree();

            return canUseNetDeptTree({ status: 1 });
          }
        })
        .then((response) => {
          // console.log("部门树形--", response);
          if (response.data.code == 200) {
            this.appTreeData = [];
            // let dataArr = response.data.data;
            this.appTreeData = response.data.data;
            this.treeDeptDataArr = JSON.parse(JSON.stringify(this.appTreeData));
          }
        });
    },
    getNewChecksData() {
      let params = {
        sysSecureRoleId: this.roleId,
        roleId: this.roleId,
      };
      this.appCheckedKeys = [];

      querySecureOrg(params).then((res) => {
        if (res.data.code == 200) {
          this.hadSelectArr = res.data.data;
          let keysArr = res.data.data;
          if (keysArr && keysArr.length > 0) {
            keysArr.forEach((citem) => {
              this.appCheckedKeys.push(citem.id);
            });
          }

          this.oldCheckedKeys = JSON.parse(JSON.stringify(this.appCheckedKeys));
        }
      });
    },
    /**
     * 解析出所有的太监节点id
     * @param json 待解析的json串
     * @param idArr 原始节点数组
     * @param temp 临时存放节点id的数组
     * @return 太监节点id数组
     */
    resolveAllEunuchNodeId(json, idArr, temp) {
      for (let i = 0; i < json.length; i++) {
        const item = json[i];
        // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
        if (item.children && item.children.length !== 0) {
          this.resolveAllEunuchNodeId(item.children, idArr, temp);
        } else {
          temp.push(idArr.filter((id) => id === item.id));
        }
      }
      return temp;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/views/serviceRole/compontents/role.scss";
</style>
<style lang="scss" scoped>
.dialog-main-tree {
  min-height: 600px;
  //   height: 900px;

  position: relative;
  .tree-container {
    // width: 50%;
    position: relative;
    padding: 10px;
    // border: 1px solid #ebeef5;
  }
  .search-content {
    // width: 30%;
    width: 200px;
    margin-right: 12px;
    height: 42px;
  }
  .filter-tree {
    width: 100%;
    // height: 800px;
    // overflow-y: scroll;
    overflow-y: scroll;
    // height: calc(100vh - 298px);
  }
  .btn-content {
    display: flex;
    // position: absolute;
    // left: 55%;
    // top: 2px;
  }
}
.had-content {
  margin: 20px 10px;
  padding-left: 10px;
  display: flex;
  .hc-title {
    color: #222;
    font-size: 14px;
    // font-weight: bold;
    min-width: 132px;
  }
  .hc-content {
    color: #222;
    font-size: 14px;
    padding-left: 10px;
  }
}
</style>