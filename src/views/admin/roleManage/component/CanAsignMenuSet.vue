<template>
  <div class="dialog-main-tree">
    <div
      class="tree-container"
      v-loading="loading"
      element-loading-text="数据加载中"
      element-loading-spinner="el-icon-loading"
    >
      <div class="top-content">
        <div class="title-content">
          <span class="t-line"></span>
          <span class="t-word">选择可用菜单</span>
        </div>

        <div class="btn-content">
          <div class="search-content">
            <el-input
              placeholder="输入可分配菜单名称过滤"
              v-model="searchMenuName"
              class="sc-input"
              clearable
            >
            </el-input>
          </div>
          <el-button type="" @click="updateResetTree">重置 </el-button>
          <el-button
            type="primary"
            @click="updatePermission"
            v-if="
              currentRole.parentId == '-2' ||
              (treeDataArr &&
                treeDataArr.length > 0 &&
                treeDataArr[0].id != currentRole.id &&
                currentRole.parentId == treeDataArr[0].id) ||
              (treeDataArr &&
                treeDataArr.length > 0 &&
                treeDataArr[0].parentId == '-2')
            "
            >保存
          </el-button>
        </div>
      </div>

      <el-tree
        v-if="treeData && treeData.length > 0"
        ref="menuTree"
        :data="treeData"
        :default-checked-keys="checkedKeys"
        :check-strictly="false"
        :props="defaultProps"
        :filter-node-method="filterNode"
        class="filter-tree"
        node-key="id"
        highlight-current
        show-checkbox
      />
    </div>
  </div>
</template>
<script>
import {
  queryCanUserAndAsignTree,
  saveMenuData,
} from "@/api/admin/sys/roleManage";
import { fetchMenuTree, fetchMenuTreeNew } from "@/api/admin/sys/menu";
import {
  addBindApp,
  permissionUpd,
  getBindAppList,
} from "@/api/admin/sys/role";
export default {
  data() {
    return {
      treeData: [],
      checkedKeys: [],
      oldCheckedKeys: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      appIdsArr: [],
      loading: false,
      searchMenuName: "",
      roleId: "",
      menuIds: "",
      currentRole: {},
    };
  },
  props: {
    treeDataArr: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {
    searchMenuName(val) {
      this.$refs.menuTree.filter(val);
    },
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    handleInitData(row) {
      //   console.log("roww---", row);
      this.currentRole = row;

      if (row && row.id) {
        this.roleId = row.id;
        this.handlePermission(row);
      }
    },
    updateResetTree() {
      this.checkedKeys = JSON.parse(JSON.stringify(this.oldCheckedKeys));

      // this.$refs.menuTree.setCheckedKeys([3]);
      this.$refs.menuTree.setCheckedKeys(this.oldCheckedKeys);
      this.$forceUpdate();
    },
    updatePermission() {
      this.menuIds = "";
      // console.log("---11===", this.$refs.menuTree.getCheckedKeys());
      // console.log("---1221===", this.$refs.menuTree.getHalfCheckedKeys());
      this.menuIds = this.$refs.menuTree
        .getCheckedKeys()
        .join(",")
        .concat(",")
        .concat(this.$refs.menuTree.getHalfCheckedKeys().join(","));

      let params = {
        roleId: this.roleId,
        menuIds: this.menuIds,
        authType: 1, // 授权类型 0可用1可分配
        roleType: 0, //角色类型 0安全角色 1业务角色
      };
      permissionUpd(params).then((res) => {
        // this.dialogPermissionVisible = false;
        // this.$store.dispatch("GetMenu", { type: false });
        if (res.data.code == 200) {
          this.$message.success("修改成功");
          this.getNewChecksData(this.currentRole);
        }
      });
    },
    handlePermission(row) {
      this.loading = true;
      let params = {
        roleId: row.id,

        authType: 1, // 授权类型 0可用1可分配
        roleType: 0, //角色类型 0安全角色 1业务角色
      };
      queryCanUserAndAsignTree(params)
        .then((response) => {
          // console.log("response.data.data==", response.data.data);
          let arr = response.data.data || [];
          let checkArr = [];
          if (arr && arr.length > 0) {
            arr.forEach((item) => {
              checkArr.push(item.id);
            });
            this.checkedKeys = checkArr;
          } else {
            this.checkedKeys = [];
          }
          // this.checkedKeys = response.data.data;
          this.oldCheckedKeys = JSON.parse(JSON.stringify(this.checkedKeys));
          // return fetchMenuTree(null, null, true);
          let params = {
            needOther: true,
            authType: 1, //authType 0：可用 1：可分配  不传默认1
          };
          return fetchMenuTreeNew(params);
        })
        .then((response) => {
          this.loading = false;
          this.treeData = response.data.data;

          this.checkedKeys = this.resolveAllEunuchNodeId(
            this.treeData,
            this.checkedKeys,
            []
          );

          this.oldCheckedKeys = JSON.parse(JSON.stringify(this.checkedKeys));
          //   this.dialogPermissionVisible = true;
          //   this.roleId = row.id;
          //   this.roleCode = row.roleCode;
        });
    },
    getNewChecksData(row) {
      let params = {
        roleId: row.id,

        authType: 1, // 授权类型 0可用1可分配
        roleType: 0, //角色类型 0安全角色 1业务角色
      };
      queryCanUserAndAsignTree(params).then((response) => {
        // console.log("response.data.data==", response.data.data);
        let arr = response.data.data || [];
        let checkArr = [];
        if (arr && arr.length > 0) {
          arr.forEach((item) => {
            checkArr.push(item.id);
          });
          this.checkedKeys = checkArr;
        } else {
          this.checkedKeys = [];
        }
        this.checkedKeys = this.resolveAllEunuchNodeId(
          this.treeData,
          this.checkedKeys,
          []
        );

        // this.checkedKeys = response.data.data;
        this.oldCheckedKeys = JSON.parse(JSON.stringify(this.checkedKeys));
      });
    },
    /**
     * 解析出所有的太监节点id
     * @param json 待解析的json串
     * @param idArr 原始节点数组
     * @param temp 临时存放节点id的数组
     * @return 太监节点id数组
     */
    resolveAllEunuchNodeId(json, idArr, temp) {
      for (let i = 0; i < json.length; i++) {
        const item = json[i];
        // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
        if (item.children && item.children.length !== 0) {
          this.resolveAllEunuchNodeId(item.children, idArr, temp);
        } else {
          temp.push(idArr.filter((id) => id === item.id));
        }
      }
      return temp;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/views/serviceRole/compontents/role.scss";
</style>
<style lang="scss" scoped>
.dialog-main-tree {
  min-height: 600px;
  //   height: 900px;

  position: relative;
  .tree-container {
    // width: 50%;
    position: relative;
    padding: 10px;
    // border: 1px solid #ebeef5;
  }
  .search-content {
    // width: 30%;
    width: 200px;
    margin-right: 12px;
  }
  .filter-tree {
    width: 100%;
    // height: 800px;
    // overflow-y: scroll;
    overflow-y: scroll;
    height: calc(100vh - 298px);
  }
  .btn-content {
    display: flex;
    // position: absolute;
    // left: 55%;
    // top: 2px;
  }
}
</style>