<template>
  <div>
    <div class="dr-content" v-if="currentRole">
      <div class="ros-main">
        <div class="title-content">
          <span class="t-line"></span>
          <span>角色维护</span>
        </div>
        <div
          class="btn-content"
          v-if="
            currentRole.parentId == '-2' ||
            (treeData && treeData.length > 0 && treeData[0].parentId == '-2') ||
            (treeData &&
              treeData.length > 0 &&
              treeData[0].id != currentRole.id &&
              currentRole.parentId == treeData[0].id)
          "
        >
          <el-button @click="handleEdit" type="primary">编辑</el-button>
          <!-- <el-button @click="handleReset">重置</el-button> -->
          <el-button
            type="danger"
            @click="handleRemove"
            v-if="
              currentRole.parentId != '-2' ||
              (treeData &&
                treeData.length > 0 &&
                treeData[0].id != currentRole.id)
            "
            >删除</el-button
          >
          <!-- <el-button
                    v-else
                    type="primary"
                    @click="debounceAddOrUpdateDaohangFn"
                    >保存</el-button
                  > -->
        </div>
      </div>

      <el-form label-width="120px" :rules="rules" disabled>
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="currentRole.name" readonly></el-input>
        </el-form-item>
        <el-form-item label="说明" prop="remark">
          <el-input v-model="currentRole.remark" readonly></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="dr-system">
      <div class="sys-main">
        <div class="title-content">
          <span class="t-line"></span>
          <span>角色帐号列表</span>
        </div>
        <div class="ds-content">
          <template
            v-if="
              currentRole.parentId == '-2' ||
              (treeData &&
                treeData.length > 0 &&
                treeData[0].parentId == '-2') ||
              (treeData &&
                treeData[0].id != currentRole.id &&
                currentRole.parentId == treeData[0].id)
            "
          >
            <el-button type="primary" @click="hangdleAddSafeRole"
              >新增</el-button
            >
            <el-button type="danger" @click="handleDelSafeRole">删除</el-button>
          </template>
          <template v-else>
            <!-- <el-button type="primary" disabled class="no-view">新增</el-button>
            <el-button type="danger" disabled class="no-view">删除</el-button> -->
          </template>
        </div>
      </div>
      <save-role-list ref="saveRoleRef"></save-role-list>
      <!-- <system-list ref="sysListRef" class="sys-content"></system-list> -->
    </div>
    <add-save-role-user-dialog
      ref="addRoleUserRef"
      @refushList="refushList"
      @handleCloseDialog="handleCloseDialog"
      v-if="addUserFlag"
    ></add-save-role-user-dialog>
  </div>
</template>
<script>
// import TreeNode from "./compontents/TreeNode.vue";

import {
  safeRoleTree,
  safeRoleCreate,
  delUserToSecureRole,
} from "@/api/admin/sys/roleManage";
import debounce from "@/util/debounce";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import SaveRoleList from "./SaveRoleList.vue";
import AddSaveRoleUserDialog from "./AddSaveRoleUserDialog.vue";

export default {
  components: {
    Treeselect,
    SaveRoleList,
    AddSaveRoleUserDialog,
  },
  data() {
    return {
      nameSearch: "",
      form: {
        parentId: -1,
      },
      roleObj: {},
      addUserFlag: false,

      rules: {
        name: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
        parentId: [
          { required: false, message: "请选择上级导航", trigger: "change" },
        ],
      },
    };
  },
  props: {
    currentRole: {
      type: Object,
      require: true,
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  created() {
    console.log(this.currentRole);
  },
  methods: {
    initRoleData(roleObj) {
      // console.log("roleObj==", roleObj);
      this.roleObj = roleObj;
      this.$refs.saveRoleRef.handleGetRolesList(roleObj.id);
    },
    hangdleAddSafeRole() {
      // console.log("新增用户");
      // this.$refs.addRoleUserRef()
      this.addUserFlag = true;
      this.$nextTick(() => {
        this.$refs.addRoleUserRef.addBindUser(this.roleObj.id);
      });
    },
    refushList() {
      this.initRoleData(this.roleObj);
      this.addUserFlag = false;
    },
    handleCloseDialog() {
      this.addUserFlag = false;
    },

    handleDelSafeRole() {
      let that = this;
      // console.log("删除已选==", this.$refs.saveRoleRef.chooseSelection);
      // console.log("删除已选=2=", this.$refs.saveRoleRef.chooseArrObj);
      // let arr = [];
      // let chooseSelection = this.$refs.roleNumRef.chooseSelection;
      // if (chooseSelection && chooseSelection.length > 0) {
      //   chooseSelection.forEach((item) => {
      //     arr.push(item.id);
      //   });
      // }
      let userIds = [];
      Object.keys(this.$refs.saveRoleRef.chooseArrObj).forEach((key) => {
        // console.log(key, this.chooseArrObj[key]);
        let arr = this.$refs.saveRoleRef.chooseArrObj[key];
        if (arr && arr.length > 0) {
          arr.forEach((item) => {
            userIds.push(item.id);
          });
        }
      });
      if (userIds.length == 0) {
        this.$message({
          showClose: true,
          message: "请勾选要删除用户！",
          type: "warning",
        });
        return;
      }
      let params = { roleId: that.roleObj.id, userIdList: userIds };

      this.$confirm("是否确认删除该角色下的用户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delUserToSecureRole(params);
        })
        .then((res) => {
          console.log("data=解绑用户下的角色---=", res);
          if (res.status && res.data.code == 200) {
            this.refushList();
            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    handleEdit() {
      this.$emit("roleEdit", this.currentRole);
    },
    handleReset() {
      this.$emit("roleReset", this.currentRole);
    },
    handleRemove() {
      // console.log("删除角色", this.$refs.saveRoleRef.tableData);
      // console.log("删除角色22", this.$refs.saveRoleRef.tableData.length);
      if (
        this.$refs.saveRoleRef &&
        this.$refs.saveRoleRef.tableData &&
        this.$refs.saveRoleRef.tableData.length > 0
      ) {
        this.$message({
          showClose: true,
          message: "该角色下存在用户，请删除角色下的用户后再删除角色！",
          type: "warning",
        });
        return;
      }
      // console.log()
      this.$emit("roleRemove", this.currentRole);
    },
    async safeRoleTree() {
      let res = await safeRoleTree();
      if (res.data.code == 200) {
        let treeDeptData = res.data.data.children;
        treeDeptData.forEach((item) => {
          item.show = false;
        });
        this.treeDeptData = treeDeptData;
        this.deptOptions = [];
        const dept = { id: -1, name: "根", children: res.data.data.records };
        this.deptOptions.push(dept);
      }
    },
    async submitForm() {
      console.log(this.form);
      let res = await safeRoleCreate(this.form);

      if (res.data.code == 200) {
        this.$message.success("添加角色成功");
        this.safeRoleTree();
      }
    },
    handleClick(e) {
      console.log("change index", e.index);
      // this.activeName = e.index;
    },
    handleMouseAdd(data) {
      console.log("add===", data);
    },
  },
};
</script>
<style lang="scss" scoped>
.dh-container {
  //   background: #f0f2f5;
  position: relative;
  min-height: calc(100vh - 240px);
  .dd-content {
    margin-left: 20px;
    margin-top: 20px;
    display: flex;

    .dept-left {
      width: 240px;
      // background: #f0f2f5;
      // border: 1px solid #dcdfe6;
      padding: 4px;
      min-height: calc(100vh - 200px);
      background: #fff;
      border-radius: 4px;

      .ns-input {
        margin-bottom: 10px;
      }
    }
    .de-right {
      flex: 1;

      margin-left: 20px;
      .dr-content {
        // border: 1px solid #dcdfe6;
        width: calc(100% - 10px);
        background: #fff;
        border-radius: 4px;
        padding: 10px;

        .ros-main {
          display: flex;
          margin: 20px 0;
          align-items: center;
          justify-content: space-between;
          // padding: 0 24px;
        }
      }
      .dr-system {
        position: relative;
        margin-top: 36px;
        padding: 10px;
        width: calc(100% - 10px);
        background: #fff;
        .ds-content {
          text-align: right;
        }
        .sys-main {
          display: flex;
          justify-content: space-between;
        }
      }
      .title-content {
        display: flex;
        align-items: center;
        .t-line {
          height: 12px;
          width: 3px;
          // background: #002fa7;
          background: #327fff;
          display: inline-block;
          margin-right: 6px;
        }
      }

      .sys-content {
        // width: 60%;
      }
    }
  }
  .form-row {
    display: flex;
  }
  .row-input {
    width: 60%;
  }
  .label-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 0;
  }
}
.no-view {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>