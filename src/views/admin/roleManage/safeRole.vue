<template>
  <div class="dh-container">
    <div class="dd-content">
      <div class="dept-left">
        <div class="top-content bt-content">
          <div class="title-content">
            <span class="t-line"></span>
            <span class="t-word">安全角色列表</span>
          </div>
          <img
            v-if="selfFalg"
            src="@/assets/image/appGroup/addAppGroup.png"
            class="img-btn"
            alt=""
            @click="addRole"
          />
          <img
            v-else
            src="@/assets/image/appGroup/addAppGroup.png"
            class="img-btn img-btn2"
            alt=""
          />
        </div>

        <el-input class="ns-input" placeholder="搜索" v-model="nameSearch">
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-tree
          class="filter-tree"
          :data="treeDeptData"
          :props="defaultProps"
          node-key="id"
          :expand-on-click-node="false"
          :default-expand-all="true"
          :default-checked-keys="checkedkeys"
          @node-click="handleCheckChange"
          :filter-node-method="handleDeptfilterNode"
          ref="deptTreeRef"
        >
        </el-tree>
      </div>
      <div class="de-right">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="角色维护" name="1">
            <AccountList
              ref="accountRef"
              :currentRole="currentRole"
              :treeData="treeDeptData"
              @roleEdit="updateRole"
              @roleRemove="removeRole"
              @roleReset="resetRole"
            />
          </el-tab-pane>
          <el-tab-pane label="可用菜单" name="2">
            <can-use-menu-set
              ref="canUseRef"
              :currentRole="currentRole"
              :treeDataArr="treeDeptData"
            ></can-use-menu-set>
          </el-tab-pane>
          <el-tab-pane label="可分配菜单" name="3">
            <can-asign-menu-set
              ref="canAsignRef"
              :treeDataArr="treeDeptData"
            ></can-asign-menu-set>
          </el-tab-pane>
          <el-tab-pane label="可用应用" name="4">
            <can-app-set
              ref="canAppRef"
              :treeDataArr="treeDeptData"
            ></can-app-set>
          </el-tab-pane>
          <el-tab-pane label="可分配应用" name="5">
            <can-asign-app-set
              ref="canAsignAppRef"
              :treeDataArr="treeDeptData"
            ></can-asign-app-set>
          </el-tab-pane>
          <el-tab-pane label="可管理的组织机构" name="6">
            <can-asign-dept
              ref="canAsignDeptRef"
              :treeDataArr="treeDeptData"
            ></can-asign-dept>
          </el-tab-pane>
          <el-tab-pane label="授权业务角色" name="7">
            <bind-service-role-list
              ref="bindServiceRoleRef"
              :treeDataArr="treeDeptData"
            ></bind-service-role-list>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <el-dialog
      :title="`${dialogType === 'add' ? '新增' : '编辑'}安全角色`"
      :visible.sync="pulishVisible"
      width="30%"
      center
      v-if="pulishVisible"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="上级角色">
          <!-- prop="name" -->
          <el-input
            v-model="currentRole.name"
            disabled
            v-if="dialogType === 'add'"
          ></el-input>
          <el-input
            v-model="parentGen"
            v-else-if="form.parentId == '-2'"
            disabled
          ></el-input>
          <treeselect
            v-model="form.parentId"
            :options="treeDeptData"
            :normalizer="normalizer"
            :show-count="true"
            placeholder="选择上级导航"
            disabled
            v-else
          />
        </el-form-item>
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="form.roleName"
            placeholder="请输入角色名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="说明" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入说明"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="pulishVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
// import TreeNode from "./compontents/TreeNode.vue";

import {
  addDaohangData,
  getDaohangTree,
  updateDaohangData,
  delDaohangData,
  getDaohangSystemList,
  delBatchDaohangData,
} from "@/api/daohang/index";
import {
  safeRoleTree,
  safeRoleCreate,
  safeRoleUpdate,
  safeRoleDel,
} from "@/api/admin/sys/roleManage";
import debounce from "@/util/debounce";
import AccountList from "./component/accountList";
import iconList from "@/const/iconList";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import BindServiceRoleList from "./component/BindServiceRoleList.vue";

import CanUseMenuSet from "./component/CanUseMenuSet.vue";
import CanAsignMenuSet from "./component/CanAsignMenuSet.vue";
import CanAppSet from "./component/CanAppSet.vue";

import CanAsignAppSet from "./component/CanAsignAppSet.vue";
import CanAsignDept from "./component/CanAsignDept.vue";
export default {
  components: {
    Treeselect,
    AccountList,
    BindServiceRoleList,
    CanUseMenuSet,
    CanAsignMenuSet,
    CanAppSet,
    CanAsignAppSet,
    CanAsignDept,
  },
  data() {
    return {
      pulishVisible: false,
      nameSearch: "",
      parentGen: "根",
      form: {
        roleName: "",
        remark: "",
        parentName: "",
      },
      sonSysArr: [],
      iconList: iconList,
      chooseObj: {},
      treeDeptData: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      editFlag: true,
      dhSystemFlag: false,
      roleId: "",
      currentRole: {},
      activeName: "1",
      dialogType: "",
      selfFalg: false,
      checkedkeys: [],
      rules: {
        menuName: [
          { required: true, message: "请输入菜单名称", trigger: "blur" },
        ],
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        name: [{ required: true, message: "请输入上级角色", trigger: "blur" }],
        parentId: [
          { required: false, message: "请选择上级导航", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    nameSearch(val) {
      this.$refs.deptTreeRef.filter(val);
    },
  },
  created() {
    this.safeRoleTree();
  },
  methods: {
    async safeRoleTree() {
      let res = await safeRoleTree();
      this.currentRole = {};
      if (res.data.code == 200) {
        let treeDeptData = res.data.data.children;
        treeDeptData.forEach((item) => {
          item.show = false;
        });
        this.treeDeptData = treeDeptData;
        if (this.treeDeptData && this.treeDeptData.length > 0) {
          if (!this.roleId) {
            this.$nextTick(() => {
              this.$refs.deptTreeRef.setCurrentKey(this.treeDeptData[0].id); //默认选中第一条
              this.handleCheckChange(this.treeDeptData[0]); //同时调用树点击事件
            });
          }
        }
        // this.roleId
      }
    },
    addRole() {
      if (this.currentRole.id) {
        this.form.parentId = this.currentRole.id;
      } else {
        this.$message.error("请先选择父级角色");
        return;
      }
      this.form.roleName = "";
      this.form.remark = "";
      this.pulishVisible = true;
      this.dialogType = "add";
      // this.$nextTick(() => {
      //   this.$refs.daohangFormRef.init(true, this.form.id);
      // });
    },
    updateRole(role) {
      // console.log("role==", role);
      if (this.treeDeptData[0].id == role.id && role.parentId != "-2") {
        return;
      }
      this.form = {
        parentId: role.parentId,
        id: role.id,
        roleName: role.name,
        remark: role.remark,
        parentName: "",
      };
      this.pulishVisible = true;
      this.dialogType = "update";
      // this.$nextTick(() => {
      //   this.$refs.daohangFormRef.init(true, this.form.id);
      // });
    },
    resetRole(role) {
      this.form = {
        parentId: role.parentId,
        id: role.id,
        roleName: "",
        remark: "",
      };
      this.pulishVisible = true;
      this.dialogType = "update";
      this.$nextTick(() => {
        this.$refs.daohangFormRef.init(true, this.form.id);
      });
    },
    removeRole(role) {
      this.$confirm(`确定删除角色吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // 确定操作
          let res = await safeRoleDel(role.id);
          if (res.data.code == 200) {
            this.$message.success("删除角色成功");
            this.roleId = "";
            this.safeRoleTree();
          }
        })
        .catch(() => {});
    },
    submitForm() {
      // console.log(this.form);
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.submitFormAsync();
        } else {
          // console.log("error submit!!");
          return false;
        }
      });
    },
    async submitFormAsync() {
      let res;
      if (this.dialogType == "add") {
        res = await safeRoleCreate(this.form);
      } else {
        res = await safeRoleUpdate(this.form);
      }
      if (res.data.code == 200) {
        this.$message.success(
          `${this.dialogType == "add" ? "添加" : "编辑"}角色成功`
        );
        this.pulishVisible = false;
        this.roleId = "";
        this.safeRoleTree();
        // console.log("河南省管理员==", this.form);
      }
    },

    handleCheckChange(data) {
      // console.log("data===", data);
      this.currentRole = data;
      this.form.parentName = this.currentRole.name;
      this.roleId = data.id;

      if (
        this.treeDeptData[0].id == data.id ||
        this.treeDeptData[0].parentId == "-2"
      ) {
        this.selfFalg = true;
      } else {
        this.selfFalg = false;
      }

      if (this.activeName == "1") {
        // console.log("this.currentRole==", this.currentRole);
        this.$refs.accountRef.initRoleData(this.currentRole);
      } else if (this.activeName == "2") {
        this.$refs.canUseRef.handleInitData(this.currentRole);
      } else if (this.activeName == "3") {
        this.$refs.canAsignRef.handleInitData(this.currentRole);
      } else if (this.activeName == "4") {
        this.$refs.canAppRef.handleInitData(this.currentRole);
      } else if (this.activeName == "5") {
        this.$refs.canAsignAppRef.handleInitData(this.currentRole);
      } else if (this.activeName == "6") {
        this.$refs.canAsignDeptRef.handleInitData(this.currentRole);
      } else if (this.activeName == "7") {
        this.$refs.bindServiceRoleRef.initRoleList(this.currentRole);
      }

      //   this.getList(this.page);
      // this.editFlag = true;
      // this.$refs.sysListRef.handleGetDaohangList(data.id);
    },

    /** 转换菜单数据结构 */
    normalizer(node) {
      // console.log("node==", node);
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
        remark: node.remark,
      };
    },
    handleClick(e) {
      // console.log("change index", e.index);

      if (this.activeName == "1") {
        // console.log("this.currentRole==", this.currentRole);
        this.$refs.accountRef.initRoleData(this.currentRole);
      } else if (this.activeName == "2") {
        this.$refs.canUseRef.handleInitData(this.currentRole);
      } else if (this.activeName == "3") {
        this.$refs.canAsignRef.handleInitData(this.currentRole);
      } else if (this.activeName == "4") {
        this.$refs.canAppRef.handleInitData(this.currentRole);
      } else if (this.activeName == "5") {
        this.$refs.canAsignAppRef.handleInitData(this.currentRole);
      } else if (this.activeName == "6") {
        this.$refs.canAsignDeptRef.handleInitData(this.currentRole);
      } else if (this.activeName == "7") {
        // console.log("this.currentRole==", this.currentRole);
        this.$refs.bindServiceRoleRef.initRoleList(this.currentRole);
      }
    },
    handleMouseAdd(data) {
      // console.log("add===", data);
    },

    //chooseSelection
    handleDelSystem() {
      //sysListRef
      // console.log("this.9999---===", this.$refs.sysListRef);
      let chooseSelection = this.$refs.sysListRef.chooseSelection;
      if (chooseSelection && chooseSelection.length > 0) {
        let idList = [];
        chooseSelection.forEach((item) => {
          idList.push(item.id);
        });
        let params = {
          idList: idList,
        };

        let that = this;
        this.$confirm("是否确认批量删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return delBatchDaohangData(params);
          })
          .then((res) => {
            // console.log("data==", res);
            if (res.data.code == 200) {
              this.$message.success("删除成功");
              this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
            } else {
              this.$message.error("删除失败");
            }
          });
      }
    },

    renderContent(h, { node, data, store }) {
      // on-mouseover={() => this.clickMenu(data)}
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
          <span>
            <el-button
              size="mini"
              type="text"
              on-click={() => this.clickMenu(data)}
            >
              Append
            </el-button>
            {/* <div>固定123456</div> */}
          </span>
        </span>
      );
    },
    mouseenter(data) {
      //   console.log(data);
      this.$set(data, "show", true);
      //   this.$set(data, "show", true);
      // console.log("data.show==", data.show);
    },
    hangdleAddSystem() {
      // console.log("this.chooseObj==", this.chooseObj);
      if (!this.chooseObj.id) {
        this.$message({
          message: "请先选择左侧导航",
          type: "warning",
        });
        return;
      }

      this.dhSystemFlag = true;

      this.$nextTick(() => {
        this.$refs.daohangSystemRef.handleGetDaohangList();
      });
    },
    handleCloseSystem() {
      this.dhSystemFlag = false;
      this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
    },
    mouseleave(data) {
      this.$set(data, "show", false);
    },
    clickMenu(data) {
      // console.log("menu--", data);
    },
    handleEdit() {
      if (!this.form.id) {
        this.$message({
          message: "请先选择左侧导航",
          type: "warning",
        });
        return;
      }
      this.editFlag = false;
    },
    handleResetForm() {
      this.form.sortOrder = undefined;
      this.form.roleName = "";
      this.form.remark = "";
    },
    // handleDel() {
    //   if (!this.form.id) {
    //     return;
    //   }
    //   let that = this;
    //   this.$confirm("是否确认删除:" + this.form.name, "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   })
    //     .then(function () {
    //       return delDaohangData(that.form.id);
    //     })
    //     .then((res) => {
    //       console.log("data==", res);
    //       if (res.status && res.data.code == 200) {
    //         this.roleId = ""; //清空
    //         this.$message.success("删除成功");
    //         this.safeRoleTree();
    //         // this.form = {};
    //       } else {
    //         this.$message.error("删除失败");
    //       }
    //     });
    // },

    handleDeptfilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    hangdleGetTreeDeptFn() {
      this.pulishVisible = false;
      this.safeRoleTree();
    },
    closeDaohangDialog() {
      this.pulishVisible = false;
    },

    debounceAddOrUpdateDaohangFn: debounce(function () {
      this.addOrUpdateDaohang();
    }, 500),
    addOrUpdateDaohang() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.parentId === undefined) {
            this.form.parentId = -1;
          }
          if (this.form.id) {
            updateDaohangData(this.form).then((res) => {
              // console.log("修改导航的数据==", res);
              if (res.data.code == 200) {
                this.$message({
                  showClose: true,
                  message: "修改成功",
                  type: "success",
                });
                // this.form = {};
                this.safeRoleTree();
              }
            });
          } else {
            addDaohangData(this.form).then((res) => {
              // console.log("新增导航的数据==", res);
              if (res.data.code == 200) {
                this.$message({
                  showClose: true,
                  message: "新增成功",
                  type: "success",
                });
                // this.form = {};
                this.safeRoleTree();
              }
            });
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/views/serviceRole/compontents/role.scss";
.dh-container {
  //   background: #f0f2f5;
  position: relative;
  min-height: calc(100vh - 240px);
  .dd-content {
    margin-left: 20px;
    margin-top: 20px;
    display: flex;

    .img-btn {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
    .img-btn2 {
      cursor: not-allowed;
      opacity: 0.6;
    }

    .dept-left {
      width: 280px;
      // background: #f0f2f5;
      // border: 1px solid #dcdfe6;
      padding: 10px;
      min-height: calc(100vh - 200px);
      background: #fff;
      border-radius: 4px;
      .bt-content {
        margin: 8px 0 16px 0;
      }

      .ns-input {
        margin-bottom: 10px;
      }
    }
    .de-right {
      flex: 1;
      background: #fff;
      padding: 0 12px;

      margin-left: 20px;
      .dr-content {
        // border: 1px solid #dcdfe6;
        width: calc(100% - 10px);
        background: #fff;
        border-radius: 4px;
        padding: 10px;

        .ros-main {
          display: flex;
          margin: 20px 0;
          align-items: center;
          justify-content: space-between;
          // padding: 0 24px;
        }
      }
      .dr-system {
        position: relative;
        margin-top: 36px;
        padding: 10px;
        width: calc(100% - 10px);
        background: #fff;
        .ds-content {
          text-align: right;
        }
        .sys-main {
          display: flex;
          justify-content: space-between;
        }
      }
      .title-content {
        display: flex;
        align-items: center;
        .t-line {
          height: 12px;
          width: 3px;
          // background: #002fa7;
          background: #327fff;
          display: inline-block;
          margin-right: 6px;
        }
      }

      .sys-content {
        // width: 60%;
      }
    }
  }
  .form-row {
    display: flex;
  }
  .row-input {
    width: 60%;
  }
  .label-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 0;
  }

  .no-content {
    text-align: center;
  }
  .no-data {
    background: url("../../../assets/image/no-data.png") no-repeat;
    background-size: 100%;
    width: 197px;
    height: 132px;
    margin: 0 auto;
  }
}
::v-deep .vue-treeselect--disabled .vue-treeselect__control {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
</style>