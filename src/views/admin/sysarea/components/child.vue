<template>
  <el-card>
    <div slot="header">
      {{area.areaName}}<span v-if="area.areaName">-下级行政区划列表</span>
    </div>
    <avue-crud
      :option="tableOption"
      ref="crud"
      :table-loading="tableLoading"
      @on-load="getList"
      :data="tableData"
      @row-save="rowSave"
      @row-update="rowEdit"
      @row-del="rowDel"
    >
    </avue-crud>
  </el-card>
</template>

<script>
import { tableOption } from "@/const/crud/admin/sysarea";
import {fetchChildren,addObj,delObj,putObj} from "@/api/admin/sys/area";
export default {
  name: "child",
  props: {
    area: {
      type: Object,
      required: false
    },
    refreshProvince:{
      type: Function,
      default: null
    }
  },
  data(){
    return {
      searchForm: {},
      tableData: [],
      tableLoading: false,
      tableOption: tableOption,
      itemPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 100 // 每页显示多少条
      },
    }
  },
  methods:{
    getList(area) {
      if(!area){
        return;
      }
      this.tableLoading = true;
      let query = {
        id: area.id,
      }
      fetchChildren(query)
        .then((response) => {
          this.tableData = response.data.data;
          this.tableLoading = false;
          this.itemPage.total = response.data.data.length
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowSave(form,done){
      this.tableLoading = true;
      form.parentId = this.area.id;
      addObj(form).then(res=>{
        this.tableLoading = false;
        done(form);
        this.refreshProvince()
        // this.getList(this.area);
      }).catch(()=>{
        this.tableLoading = false
      })
    },
    rowEdit(form,index,done){
      this.tableLoading = true;
      putObj(form).then(res=>{
        this.getList(this.area);
        done(form);
      }).catch(()=>{
        this.tableLoading = false
      });
    },
    rowDel(form){
      this.$confirm('你确定要删除该区划吗?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableLoading=true;
        delObj(form.id).then(res=>{
          this.getList(this.area);
        }).catch(()=>{
          this.tableLoading = false
        })
      }).catch(() => {
      });
    },
  }
}
</script>
