<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
        "
      >
        <span
          >{{ area.areaName
          }}<span v-if="area.areaName">-行政区划信息</span></span
        >
        <div>
          <el-button size="small" type="" @click="addOrUpdateHandle()"
            >编辑
          </el-button>
          <el-button size="small" type="primary" @click="handleDelete()"
            >删除
          </el-button>
        </div>
      </div>
    </div>
    <div class="text item">
      <el-row>
        <el-col :span="8">
          <span class="title">行政区划编码：</span>
          <span class="text">{{ area.areaCode }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">行政区划名称：</span>
          <span class="text">{{ area.areaName }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">行政区划等级：</span>
          <span class="text">{{ convertLevel(area.areaLevel) }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <span class="title">排序：</span>
          <span class="text">{{ area.areaOrder }}</span>
        </el-col>
        <el-col :span="8">
          <span class="title">是否生效：</span>
          <span class="text">{{ getShifouValue(area.status) }}</span>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script>
import { remote } from "@/api/admin/sys/sys-dict";
export default {
  name: "info",
  data() {
    return {
      yesNoArr: [],
    };
  },
  props: {
    area: {
      type: Object,
      required: true,
    },
    editArea: {
      type: Function,
      default: null,
    },
    delArea: {
      type: Function,
      default: null,
    },
  },
  created() {
    this.getYesOrNoData();
  },
  methods: {
    getYesOrNoData() {
      remote("boole_status").then((res) => {
        if (res.data.code == 200) {
          this.yesNoArr = res.data.data;
        }
      });
    },
    getShifouValue(val) {
      let result = "";
      let fObj = this.yesNoArr.find((item) => {
        return item.dictValue == val;
      });
      if (fObj) {
        result = fObj.dictLabel;
      }
      return result;
    },

    convertLevel(level) {
      switch (level) {
        case "1":
          return "省级";
        case "2":
          return "市级";
        case "3":
          return "区县级";
        case "4":
          return "乡镇级";
        case "5":
          return "村居级";
      }
    },
    convertStatus(status) {
      switch (status) {
        case "1":
          return "启用";
        case "0":
          return "禁用";
      }
    },
    addOrUpdateHandle() {
      this.editArea();
    },
    handleDelete() {
      this.delArea();
    },
  },
};
</script>

<style scoped lang="scss">
.box-card {
  .title {
    font-size: 14px;
    font-weight: bold;
  }

  .text {
    font-size: 14px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both;
  }
}
</style>
