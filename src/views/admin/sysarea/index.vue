<template>
  <div class="user">
    <div class="dept-content">
      <div class="dept-left">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          "
        >
          <span style="font-size: 16px; font-weight: 500">区划列表</span>
          <img
            src="@/assets/image/appGroup/addAppGroup.png"
            style="width: 18px; width: 18px; cursor: pointer"
            alt=""
            @click="handleAddArea()"
          />
        </div>
        <el-input placeholder="区划名称或编码" v-model="keyWord">
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-tree
          class="filter-tree"
          :props="defaultProps"
          node-key="areaCode"
          :load="getChildTree"
          :data="treeDeptDataBase"
          lazy
          accordion
          :expand-on-click-node="false"
          :default-expanded-keys="expandedKeys"
          :default-checked-keys="expandedKeys"
          @node-click="handleCheckChange"
          :render-content="renderContent"
          ref="deptTreeRef"
        >
        </el-tree>
      </div>
      <div class="dept-right">
        <basic-container style="flex: 1">
          <info
            :area="selectedArea"
            :editArea="handleEdit"
            :delArea="delArea"
          />
        </basic-container>
        <basic-container style="flex: 1">
          <el-card>
            <div slot="header">
              {{ selectedArea.areaName
              }}<span v-if="selectedArea.areaName">-下级行政区划列表</span>
            </div>
          </el-card>
          <avue-crud
            :option="tableOption"
            ref="crud"
            :table-loading="tableLoading"
            @on-load="getList"
            :data="tableData"
            @row-save="rowSave"
            @row-update="rowEdit"
            @row-del="rowDel"
            @search-change="handleFilter"
            :before-open="handleBeforeOpen"
          >
            <!-- 左边插槽 -->
            <template slot-scope="scope" slot="menuLeft">
              <el-button
                icon="el-icon-plus"
                type="primary"
                @click="handleAddValue(false)"
                >新增</el-button
              >
            </template>
            <template slot-scope="scope" slot="menu">
              <el-button
                type="text"
                icon="el-icon-edit"
                @click="handleEdit(scope.row, index)"
                >编辑</el-button
              >
              <!--  :size="size" -->
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row, index)"
                >删除</el-button
              >
            </template>
            <template slot-scope="scope" slot="areaLevel">
              <div>{{ filterAreaLevel(scope.row) }}</div>
            </template>
          </avue-crud>
        </basic-container>
      </div>
    </div>

    <el-dialog
      :title="form.id ? '编辑' : '新增'"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <el-form
        :model="form"
        ref="form"
        label-width="110px"
        :rules="rules"
        class="demo-ruleForm"
        v-if="dialogVisible"
      >
        <el-form-item label="行政区划等级" prop="areaLevel">
          <el-select
            style="width: 100%"
            v-model="form.areaLevel"
            placeholder="请选择"
          >
            <el-option
              v-for="item in levelOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.areaLevel != 1"
          label="上级行政区划"
          prop="parentName"
        >
          <!-- <el-input v-model="form.parentName"> </el-input> -->
          <tree-select
            v-model="form.parentName"
            :data="treeDeptTreeData"
            :props="defaultProps"
            :loadNode="getChildTree2"
            placeholder=""
            @selfInput="selfInput"
            filterable
            slefFilter
            lazy
          ></tree-select>
        </el-form-item>
        <el-form-item label="行政区划名称" prop="areaName">
          <el-input v-model="form.areaName"> </el-input>
        </el-form-item>
        <el-form-item label="行政区划编码" prop="areaCode">
          <el-input v-model="form.areaCode"> </el-input>
        </el-form-item>
        <el-form-item label="排序" prop="areaOrder">
          <el-input-number v-model="form.areaOrder"> </el-input-number>
        </el-form-item>
        <el-form-item label="是否生效" prop="status">
          <el-switch v-model="form.status" active-value="1" inactive-value="0">
          </el-switch>
        </el-form-item>
        <el-form-item>
          <el-button
            v-loading="loading"
            type="primary"
            @click="submitForm('form')"
            >提交</el-button
          >
          <!-- <el-button @click="resetForm('form')">重置</el-button> -->
          <el-button @click="resetFormNew">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  fetchAllProvince,
  fetchChildren,
  addObj,
  delObj,
  putObj,
  searchOrgByKeyWord,
  getSysareaObj,
} from "@/api/admin/sys/area";
import Info from "./components/info";
import { tableOption } from "@/const/crud/admin/sysarea";
import debounce from "@/util/debounce";
import TreeSelect from "@/components/TreeSelect";
export default {
  name: "table_area",
  components: { Info, TreeSelect },
  data() {
    return {
      defaultProps: {
        label: "areaName",
        value: "areaCode",
      },
      loading: false,
      leftAdd: false,
      treeDeptDataBase: [],
      treeDeptTreeData: [],
      treeDeptData: [],
      expandedKeys: ["410000000000"],
      selectedArea: {},
      searchForm: {},
      tableData: [],
      tableLoading: false,
      tableOption: tableOption,
      itemPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 100, // 每页显示多少条
      },
      dialogVisible: false,
      keyWord: "",
      form: {
        status: 1,
        areaName: "",
        areaCode: "",
        areaOrder: "",
        parentName: "",
      },
      rules: {
        areaLevel: [
          { required: true, message: "请选择行政区划", trigger: "blur" },
        ],
        areaName: [
          { required: true, message: "请输入行政区划名称", trigger: "blur" },
        ],
        areaCode: [
          { required: true, message: "请输入行政区划编码", trigger: "blur" },
        ],
        areaOrder: [{ required: true, message: "请输入排序", trigger: "blur" }],
        status: [
          { required: true, message: "请选择是否生效", trigger: "blur" },
        ],
      },
      levelOption: [
        {
          label: "省级",
          value: "1",
        },
        {
          label: "市级",
          value: "2",
        },
        {
          label: "区县级",
          value: "3",
        },
        {
          label: "乡镇级",
          value: "4",
        },
        {
          label: "村居级",
          value: "5",
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  watch: {
    keyWord(val) {
      if (val && val.length > 0) {
        this.searchFlag = true;
      } else {
        this.searchFlag = false;
      }
      // this.$refs.deptTreeRef.filter(val);
      this.debounceSearchOrg(val);
    },
  },
  methods: {
    selfInput(val) {
      this.form.parentId = val;
      // form.deptName
      // console.log("this.form.parentId==", this.form.parentId);
    },
    selfInputObj(data) {
      console.log("data=lzs=", data);
    },
    debounceSearchOrg: debounce(function (name) {
      if (name) {
        this.handleSearchOrg(name);
      } else {
        this.treeDeptDataBase = JSON.parse(JSON.stringify(this.treeDeptData));
        this.treeDeptTreeData = JSON.parse(JSON.stringify(this.treeDeptData));
      }
    }, 500),
    handleSearchOrg(name) {
      let params = {
        keyWord: name,
        current: 1,
        size: 1000,
      };
      searchOrgByKeyWord(params).then((res) => {
        // console.log("res=kkwwoorrdd=", res);
        if (res.data.code == 200) {
          this.treeDeptDataBase = res.data.data.records;
          this.treeDeptTreeData = res.data.data.records;
        }
      });
    },
    filterAreaLevel(row) {
      let obj = {
        1: "省级",
        2: "市级",
        3: "区县级",
        4: "乡镇级",
        5: "村居级",
      };
      return obj[row.areaLevel];
    },
    handleDelete(form) {
      this.$confirm("你确定要删除该区划吗?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true;
          delObj(form.id)
            .then((res) => {
              this.refreshProvince();
            })
            .catch(() => {
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },
    handleEdit(row) {
      if (!row) {
        this.form = JSON.parse(JSON.stringify(this.selectedArea));
      } else {
        this.form = JSON.parse(JSON.stringify(row));
      }
      if (this.form.parentId != "0") {
        getSysareaObj(this.form.parentId).then((res) => {
          if (res.data.code == 200) {
            this.form.parentName = res.data.data.areaName;
          }
        });
      }
      // this.form.parentName = this.selectedArea.areaName;
      this.dialogVisible = true;
    },
    renderContent(h, { node, data, store }) {
      if (node.childNodes.length > 0) {
        return (
          <span>
            <i class="el-icon-folder"></i> {node.label}
          </span>
        );
      } else {
        return (
          <span>
            <i class="el-icon-document"></i> {node.label}
          </span>
        );
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    resetFormNew() {
      console.log("重置了");
      this.form.areaOrder = "";
      this.form.areaName = "";
      this.form.areaCode = "";
    },
    refreshProvince() {
      fetchAllProvince().then((root) => {
        this.handleCheckChange(root);
        this.refreshRoot();
      });
    },
    async getChildTree(node, resolve) {
      // console.log("data--lxd-", node);
      if (node.level === 0) {
        this.rootNode = node;
        this.rootResolve = resolve;
        const root = await fetchAllProvince();
        if (root.data.code == 200) {
          this.treeDeptData = root.data.data;
          this.treeDeptDataBase = root.data.data;
          this.treeDeptTreeData = root.data.data;
          // console.log("root==", root);
          this.handleCheckChange(root.data.data[0]);
          return resolve(root.data.data);
        }
      } else if (node.level > 0) {
        const areaId = node.data.id;
        const child = await fetchChildren({ id: areaId });
        node.data.children = child.data.data;
        return resolve(child.data.data);
      }
    },
    async getChildTree2(node, resolve) {
      // console.log("data--lxd-", node);
      if (node.level === 0) {
        this.rootNode = node;
        this.rootResolve = resolve;
        const root = await fetchAllProvince();
        if (root.data.code == 200) {
          // this.treeDeptData = root.data.data;
          // this.treeDeptDataBase = root.data.data;
          this.treeDeptTreeData = root.data.data;
          // console.log("root==", root);
          this.handleCheckChange(root.data.data[0]);
          return resolve(root.data.data);
        }
      } else if (node.level > 0) {
        const areaId = node.data.id;
        const child = await fetchChildren({ id: areaId });
        node.data.children = child.data.data;
        return resolve(child.data.data);
      }
    },
    async refreshRoot() {
      this.rootNode.childNodes = [];
      await this.getChildTree(this.rootNode, this.rootResolve);
    },
    handleCheckChange(node) {
      this.currentAreaId = node.id;
      this.selectedArea = node;
      this.getList(this.selectedArea);
    },
    getList(area) {
      if (!area) {
        return;
      }
      this.tableLoading = true;
      let query = {
        id: area.id,
      };
      fetchChildren(query)
        .then((response) => {
          this.tableData = response.data.data;
          this.tableLoading = false;
          this.itemPage.total = response.data.data.length;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    handleAdd(leftAdd) {
      this.leftAdd = leftAdd;
      this.$refs.crud.rowAdd();
    },
    handleAddValue() {
      this.form = {
        status: "1",
        areaName: "",
        areaCode: "",
        areaOrder: "",
        parentName: "",
      };
      this.form.parentName = this.selectedArea.areaName;
      this.form.parentId = this.selectedArea.id;
      this.form.areaLevel = (
        Number(this.selectedArea.areaLevel) + 1
      ).toString();
      this.dialogVisible = true;
    },
    handleAddArea() {
      this.form = {
        status: "1",
        areaName: "",
        areaCode: "",
        areaOrder: "",
        parentName: "",
      };
      this.dialogVisible = true;
      this.form.areaLevel = "1";
      this.form.parentId = "0";
    },
    rowSave(form, done) {
      this.tableLoading = true;
      if (this.leftAdd) {
        form.parentId = "0";
      } else {
        form.parentId = this.selectedArea.id;
      }
      addObj(form)
        .then((res) => {
          this.tableLoading = false;
          done(form);
          this.refreshProvince();
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowEdit(form, index, done) {
      //改变头部信息
      if (index === -1) {
        this.selectedArea = form;
      }
      this.tableLoading = true;
      putObj(form)
        .then((res) => {
          this.refreshProvince();
          this.getList(this.selectedArea);
          done();
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel(form) {
      this.$confirm("你确定要删除该区划吗?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableLoading = true;
          delObj(form.id)
            .then((res) => {
              this.refreshProvince();
            })
            .catch(() => {
              this.tableLoading = false;
            });
        })
        .catch(() => {});
    },
    delArea() {
      this.rowDel(this.selectedArea);
    },
    editArea() {
      this.$refs.crud.rowEdit(this.selectedArea, -1);
    },
    handleFilter(param, done) {
      this.tableLoading = true;
      let query = param;
      (query.id = this.selectedArea.id),
        fetchChildren(query)
          .then((response) => {
            this.tableData = response.data.data;
            this.tableLoading = false;
            this.itemPage.total = response.data.data.length;
          })
          .catch(() => {
            this.tableLoading = false;
          });
      done();
    },
    handleBeforeOpen(done, type) {
      if (type === "add") {
        const column = this.findObject(this.tableOption.column, "areaLevel");
        if (this.leftAdd) {
          column.dicData = [{ dictValue: "1", dictLabel: "省级" }];
        } else {
          if (this.selectedArea.areaLevel === "1") {
            column.dicData = [{ dictValue: "2", dictLabel: "市级" }];
          } else if (this.selectedArea.areaLevel === "2") {
            column.dicData = [{ dictValue: "3", dictLabel: "区县级" }];
          } else if (this.selectedArea.areaLevel === "3") {
            column.dicData = [{ dictValue: "4", dictLabel: "乡镇级" }];
          } else if (this.selectedArea.areaLevel === "4") {
            column.dicData = [{ dictValue: "5", dictLabel: "村居级" }];
          }
        }
      } else if (type === "edit") {
        const column = this.findObject(this.tableOption.column, "areaLevel");
        if (this.selectedArea.areaLevel === "1") {
          column.dicData = [{ dictValue: "1", dictLabel: "省级" }];
        } else if (this.selectedArea.areaLevel === "2") {
          column.dicData = [{ dictValue: "2", dictLabel: "市级" }];
        } else if (this.selectedArea.areaLevel === "3") {
          column.dicData = [{ dictValue: "3", dictLabel: "区县级" }];
        } else if (this.selectedArea.areaLevel === "4") {
          column.dicData = [{ dictValue: "4", dictLabel: "乡镇级" }];
        } else if (this.selectedArea.areaLevel === "5") {
          column.dicData = [{ dictValue: "5", dictLabel: "村居级" }];
        }
      }
      done();
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            if (this.form.id) {
              let res = await putObj(this.form);
              this.$message.success("修改成功");
            } else {
              await addObj(this.form);
              this.$message.success("新建成功");
            }
            // console.log("刷新了");
            this.refreshProvince(); //新增后刷新
            this.dialogVisible = false;
            // this.form = {};
            this.form = {
              status: "1",
              areaName: "",
              areaCode: "",
              areaOrder: "",
              parentName: "",
            };
            this.getData();
          } catch (error) {}
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.dept-content {
  width: 100%;
  display: flex;
  border: 1px solid transparent;

  .dept-left {
    width: 320px;
    min-width: 320px;

    margin: 8px 0 5px 20px;
    background: #fff;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 8px;
    padding-top: 20px;
    font-size: 14px;
  }

  .dept-right {
    width: 73%;
  }
}
</style>
