<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud"
                 :page.sync="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 @on-load="getList"
                 @search-change="searchChange"
                 @refresh-change="refreshChange"
                 @size-change="sizeChange"
                 @current-change="currentChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="rowDel">
        <template
          slot-scope="scope"
          slot="menu">
          <el-button
            v-if="permissions.admin_sysdicttype_add"
            type="text"
            size="small"
            icon="el-icon-menu"
            @click="handleItem(scope.row,scope.index)">字典项
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
    <el-dialog
      :visible.sync="dialogFormVisible"
      title="字典项管理"
      width="90%"
      @close="dictItemVisible">
      <avue-crud
        ref="crudItem"
        :page.sync="itemPage"
        :data="tableDictItemData"
        :permission="permissionList"
        v-model="form"
        :before-open="handleBeforeOpen"
        :option="tableDictItemOption"
        @size-change="itemSizeChange"
        @current-change="itemCurrentChange"
        @row-update="handleItemUpdate"
        @row-save="handleItemSave"
        @row-del="rowItemDel">
      </avue-crud>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchList,
  addObj,
  putObj,
  delObj,
  addItemObj,
  delItemObj,
  fetchItemList,
  putItemObj
} from '@/api/admin/sys/sys-dict'
import {tableOption} from '@/const/crud/admin/sys-dict'
import {mapGetters} from 'vuex'
import {tableDictItemOption} from "@/const/crud/admin/sys-dict";

export default {
  name: 'sysdicttype',
  data() {
    return {
      searchForm: {},
      form: {
        dictType: undefined,
        dictId: undefined
      },
      dictType: undefined,
      dictId: undefined,
      dialogFormVisible: false,
      tableData: [],
      tableDictItemData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20 // 每页显示多少条
      },
      itemPage: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20 // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      tableDictItemOption: tableDictItemOption
    }
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.admin_sysdicttype_add, false),
        delBtn: this.vaildData(this.permissions.admin_sysdicttype_del, false),
        editBtn: this.vaildData(this.permissions.admin_sysdicttype_edit, false)
      };
    }
  },
  methods: {
    getList(page, params) {
      this.tableLoading = true
      fetchList(Object.assign({
        current: page.currentPage,
        size: page.pageSize
      }, params, this.searchForm)).then(response => {
        this.tableData = response.data.data.records
        this.page.total = response.data.data.total
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    rowDel: function (row, index) {
      this.$confirm('是否确认删除ID为' + row.id, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(row.id)
      }).then(data => {
        this.$message.success('删除成功')
        this.getList(this.page)
      })
    },
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(data => {
        this.$message.success('修改成功')
        done()
        this.getList(this.page)
      }).catch(() => {
        loading();
      });
    },
    handleSave: function (row, done, loading) {
      addObj(row).then(data => {
        this.$message.success('添加成功')
        done()
        this.getList(this.page)
      }).catch(() => {
        loading();
      });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    currentChange(current) {
      this.page.currentPage = current
    },
    searchChange(form, done) {
      this.searchForm = form
      this.page.currentPage = 1
      this.getList(this.page, form)
      done()
    },
    refreshChange() {
      this.getList(this.page)
    },

    //======字典项表格相关=====
    dictItemVisible() {
      this.dialogFormVisible = false
      this.itemPage.currentPage = 1
    },
    handleItem(row) {
      this.dictId = row.id
      this.dictType = row.dictType
      this.getDictItemList()
    },
    getDictItemList() {
      this.dialogFormVisible = true
      fetchItemList(Object.assign({
        current: this.itemPage.currentPage,
        size: this.itemPage.pageSize
      }, {dictId: this.dictId})).then(response => {
        this.tableDictItemData = response.data.data.records
        this.itemPage.total = response.data.data.total
      })
    },
    handleBeforeOpen(done) {
      this.form.dictType = this.dictType
      this.form.dictId = this.dictId
      done()
    },
    handleItemSave(row, done) {
      addItemObj(row).then(() => {
        this.$message.success('添加成功')
        this.getDictItemList()
        done()
      })
    },
    handleItemUpdate(row, index, done) {
      putItemObj(row).then(() => {
        this.$message.success('修改成功')
        this.getDictItemList()
        done()
      })
    },
    itemSizeChange(pageSize) {
      this.itemPage.pageSize = pageSize
      this.getDictItemList()
    },
    itemCurrentChange(current) {
      this.itemPage.currentPage = current
      this.getDictItemList()
    },
    rowItemDel(row) {
      this.$confirm('是否确认删除数据为"' + row.dictLabel + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delItemObj(row.id)
      }).then(() => {
        this.getDictItemList()
        this.$message.success('删除成功')
      }).catch(function () {
      })
    }
  }
}
</script>
