<template>
  <basic-container>
    <div class="avue-crud">
      <el-form :inline="true">
        <el-form-item v-if="showAddBtn && permissions.sys_system_add">
          <el-button
            icon="el-icon-plus"
            type="primary"
            @click="addOrUpdateHandle(false)"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="menuList" row-key="id">
        <el-table-column
          prop="sysTitle"
          label="系统名称"
          :show-overflow-tooltip="true"
        ></el-table-column>

        <el-table-column prop="sysIcon" label="ico图标">
          <template slot-scope="scope">
            <el-image
              style="width: 30px; height: 30px"
              :src="
                scope.row.sysIcon ? $getUrlByProcess(scope.row.sysIcon) : ''
              "
              :preview-src-list="[$getUrlByProcess(scope.row.sysIcon)]"
            >
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="sysLogo" label="logo图标">
          <template slot-scope="scope">
            <el-image
              style="width: 100px; height: 100px"
              :src="
                scope.row.sysLogo ? $getUrlByProcess(scope.row.sysLogo) : ''
              "
              :preview-src-list="[$getUrlByProcess(scope.row.sysLogo)]"
            >
            </el-image>
          </template>
        </el-table-column>

        <el-table-column prop="cmsInfoPortal" label="信息门户显示">
          <template slot-scope="scope">
            <span v-if="scope.row.cmsInfoPortal == '1'">是</span>
            <span v-else> 否</span>
          </template>
        </el-table-column>

        <!-- <el-table-column
          prop="pubStatus"
          label="是否启用"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-switch
              class="switchStyle"
              active-value="0"
              inactive-value="1"
              @change="handleSwitchChange(scope.row)"
              active-text="是"
              inactive-text="否"
              v-model="scope.row.pubStatus"
              active-color="#437fff"
              inactive-color="#BFBFBF"
            >
            </el-switch>
          </template>
        </el-table-column> -->

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="addOrUpdateHandle(true, scope.row)"
              v-if="permissions.sys_system_edit"
              style="color: #3272ce"
              >修改
            </el-button>
            <!-- <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-if="permissions.sys_system_del"
              >删除
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <table-form
        v-if="addOrUpdateVisible"
        ref="addOrUpdate"
        @refreshDataList="getList"
      ></table-form>
    </div>
  </basic-container>
</template>

<script>
import {
  // clearMenuCache,
  // delObj,
  addObj,
  getFetchList,
} from "@/api/admin/sys/system";
import TableForm from "./menu-form";
import { mapGetters } from "vuex";

export default {
  name: "Menu",
  components: { TableForm },
  data() {
    return {
      addOrUpdateVisible: false,
      // 遮罩层
      loading: true,
      // 菜单表格树数据
      menuList: [],
      showAddBtn: false,
    };
  },
  created() {
    this.getList();
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  methods: {
    addOrUpdateHandle(isEdit, row) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(isEdit, row);
      });
    },
    getList() {
      // console.log("getListgetListgetList==");
      this.loading = true;
      let params = {
        key: "sys_setting",
      };
      this.menuList = [];
      getFetchList(params).then((response) => {
        this.loading = false;
        // console.log("responseresponseresponse==", response);
        if (response.data.code == 200) {
          let obj = response.data.data || null;
          if (obj) {
            this.menuList.push(obj);
          }

          if (this.menuList.length > 0) {
            this.showAddBtn = false;
          } else {
            this.showAddBtn = true;
          }
        }
      });
    },
    // handleDelete(row) {
    //   this.$confirm('是否确认删除名称为"' + row.name + '"的数据项?', "警告", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   })
    //     .then(function () {
    //       return delObj(row.id);
    //     })
    //     .then(() => {
    //       this.getList();
    //       this.$message.success("删除成功");
    //     });
    // },
    handleClearMenuCache: function () {
      clearMenuCache()
        .then(() => {
          this.$message.success("清除缓存成功");
        })
        .catch(function () {});
    },
  },
};
</script>
<style scoped lang="scss">
</style>
