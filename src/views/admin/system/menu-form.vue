<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog :title="!form.id ? '新增' : '修改'" :visible.sync="visible">
    <el-form class="my-form" :model="form" :rules="rules" ref="dataForm">
      <el-form-item label="系统名称" prop="sysTitle">
        <el-input
          v-model="form.sysTitle"
          placeholder="请输入系统名称"
          style="width: 80%"
        />
      </el-form-item>
      <el-form-item label="系统ico图标" label-width="90px" prop="sysIcon">
        <!-- <file-upload-zujian
          ref="fjRef"
          v-model="fjList"
          :files="form.sysIcon"
          :fileType="['ico']"
          :limit="1"
          :fileSize="2"
        ></file-upload-zujian> -->
        <file-upload
          v-model="form.sysIcon"
          :fileUrls="form.sysIcon"
          tipsStr="只能上传ico文件，且不超过2M"
          :fileSize="2"
          :fileType="['ico']"
          ref="sysIconRef"
        ></file-upload>
      </el-form-item>
      <el-form-item label="系统logo" label-width="90px" prop="sysLogo">
        <file-upload
          v-model="form.sysLogo"
          :fileUrls="form.sysLogo"
          ref="sysLogoRef"
        ></file-upload>
      </el-form-item>
      <el-form-item label="信息门户显示" prop="cmsInfoPortal">
        <el-radio-group v-model="form.cmsInfoPortal">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dataFormSubmit">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addObj,

  // putObj,
} from "@/api/admin/sys/system";
import Treeselect from "@riophae/vue-treeselect";
import iconList from "@/const/iconList";
import TableForm from "./";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import FileUpload from "@/views/message/components/FileUpload.vue";
import FileUploadZujian from "@/components/upload/fileUpload.vue";
export default {
  name: "Menu",
  components: { Treeselect, TableForm, FileUpload, FileUploadZujian },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项
      menuOptions: [],
      // 是否显示弹出层
      visible: false,
      // 图标
      iconList: iconList,
      fjList: [],
      form: {
        key: "sys_setting",
        sysIcon: "",
        sysLogo: "",
        sysTitle: "",
        cmsInfoPortal: "0",
      },
      // 表单校验
      rules: {
        sysTitle: [
          { required: true, message: "系统名称不能为空", trigger: "blur" },
        ],
        sysIcon: [
          { required: true, message: "系统ico不能为空", trigger: "change" },
        ],
        sysLogo: [
          { required: true, message: "系统logo不能为空", trigger: "change" },
        ],
        sortOrder: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" },
        ],
        path: [
          { required: true, message: "路由地址不能为空", trigger: "blur" },
        ],
        component: [
          { required: true, message: "组件地址不能为空", trigger: "blur" },
        ],
        pubStatus: [
          { required: true, message: "是否启用不能为空", trigger: "blur" },
        ],
        cmsInfoPortal: [
          {
            required: true,
            message: "信息门户显示不能为空",
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    init(isEdit, row) {
      this.visible = true;
      console.log("row---", row);
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          this.form.sysIcon = row.sysIcon;
          this.form.sysLogo = row.sysLogo;
          this.form.sysTitle = row.sysTitle;
          this.form.cmsInfoPortal = row.cmsInfoPortal;
          if (this.form.sysLogo) {
            this.$nextTick(() => {
              this.$refs.sysLogoRef.handleDoFile();
            });
          }
          if (this.form.sysIcon) {
            this.$nextTick(() => {
              this.$refs.sysIconRef.handleDoFile();
            });
          }

          // getObj(id).then((response) => {
          //   this.form = response.data.data;
          // });
        } else {
          this.form.id = undefined;
        }
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          // if (this.form.parentId === undefined) {
          //   this.form.parentId = -1;
          // }
          addObj(this.form).then((data) => {
            if (data.data.code === 200) {
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit("refreshDataList");
            } else {
              this.$message.success(data.data.msg);
            }
          });

          // if (this.form.id) {
          //   putObj(this.form).then((data) => {
          //     this.$message.success("修改成功");
          //     this.visible = false;
          //     this.$emit("refreshDataList");
          //   });
          // } else {

          // }
        }
      });
    },

    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
