<template>
  <div>
    <el-descriptions :column="1" size="medium">
      <el-descriptions-item label="用户名">{{profileInfo.userName}}</el-descriptions-item>
      <el-descriptions-item label="岗位">{{postList.map((post) => post.postName).join(',')}}</el-descriptions-item>
      <el-descriptions-item label="角色">{{roleList.map((post) => post.roleName).join(',')}}</el-descriptions-item>
      <el-descriptions-item label="部门">{{deptInfo.deptName}}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import {mapState} from "vuex";

export default {
  name: "ProfileInfo",
  data() {
    return {
      profileInfo: {},
      postList: [],
      roleList: [],
      deptInfo: {}
    }
  },
  created() {
    this.profileInfo = this.userInfo
    this.postList = this.posts
    this.roleList = this.roles
    this.deptInfo = this.dept
    console.log("个人信息",this.profileInfo)
    console.log("岗位",this.postList)
    console.log("角色",this.roleList)
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo,
      posts: state => state.user.posts,
      roles: state => state.user.roles,
      dept: state => state.user.dept
    }),
  },
}
</script>

<style scoped>

</style>
