<template>
  <div class="info-container">
    <!-- <el-descriptions :column="1" size="medium">
      <el-descriptions-item label="用户名">{{profileInfo.userName}}</el-descriptions-item>
      <el-descriptions-item label="岗位">{{postList.map((post) => post.postName).join(',')}}</el-descriptions-item>
      <el-descriptions-item label="角色">{{roleList.map((post) => post.roleName).join(',')}}</el-descriptions-item>
      <el-descriptions-item label="部门">{{deptInfo.deptName}}</el-descriptions-item>
    </el-descriptions> -->
    <div class="ic-row">
      <div class="r-title">用户名</div>
      <div class="r-value">{{ profileInfo.userName }}</div>
    </div>
    <div class="ic-row">
      <div class="r-title">姓名</div>
      <div class="r-value">{{ profileInfo.nickName }}</div>
    </div>
    <!-- <div class="ic-row">
      <div class="r-title">身份证号</div>
      <div class="r-value">
        {{ profileInfo.icCard ? profileInfo.icCard : "无" }}
      </div>
    </div> -->
    <div class="ic-row">
      <div class="r-title">部门名称</div>
      <div class="r-value">{{ deptInfo.deptName }}</div>
    </div>
    <div class="ic-row">
      <div class="r-title">手机号</div>
      <div class="r-value">
        {{ profileInfo.phone ? profileInfo.phone : "无" }}
      </div>
    </div>
    <div class="ic-row">
      <div class="r-title">邮箱</div>
      <div class="r-value">{{ deptInfo.email ? deptInfo.email : "无" }}</div>
    </div>
    <div class="ic-row">
      <div class="r-title">岗位</div>
      <div class="r-value">
        {{ postList.map((post) => post.postName).join(",") }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "ProfileInfo",
  data() {
    return {
      profileInfo: {},
      postList: [],
      roleList: [],
      deptInfo: {},
    };
  },
  created() {
    this.profileInfo = this.userInfo;
    this.postList = this.posts;
    this.roleList = this.roles;
    this.deptInfo = this.dept;
    // console.log("个人信息", this.profileInfo);
    // console.log("岗位", this.postList);
    // console.log("角色", this.roleList);
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      posts: (state) => state.user.posts,
      roles: (state) => state.user.roles,
      dept: (state) => state.user.dept,
    }),
  },
};
</script>

<style scoped lang="scss">
.info-container {
  .ic-row {
    display: flex;
    align-items: center;
    height: 52px;
    line-height: 52px;
    .r-title {
      color: #666666;
      font-size: 16px;
      width: 80px;
      text-align: right;
    }
    .r-value {
      color: #333333;
      font-size: 16px;
      margin-left: 18px;
    }
  }
}
</style>
