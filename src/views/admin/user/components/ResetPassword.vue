<template>
  <el-form
    :model="passwordForm"
    :rules="rules"
    ref="passwordForm"
    label-width="100px"
    class="demo-ruleForm"
  >
    <el-form-item label="原密码" prop="oldPassword" class="row-bot">
      <el-input
        :type="passwordType"
        v-model="passwordForm.oldPassword"
        auto-complete="off"
        size="medium"
      >
        <i
          class="el-icon-view el-input__icon"
          slot="suffix"
          @click="showPassword"
        ></i>
      </el-input>
    </el-form-item>
    <el-form-item label="新密码" prop="newPassword" class="row-bot">
      <el-input
        :type="passwordType"
        v-model="passwordForm.newPassword"
        auto-complete="off"
        size="medium"
      >
        <i
          class="el-icon-view el-input__icon"
          slot="suffix"
          @click="showPassword"
        ></i>
      </el-input>
    </el-form-item>
    <el-form-item class="row-bot" label="确认密码" prop="confirmPassword">
      <el-input
        :type="passwordType"
        v-model="passwordForm.confirmPassword"
        auto-complete="off"
        size="medium"
      >
        <i
          class="el-icon-view el-input__icon"
          slot="suffix"
          @click="showPassword"
        ></i>
      </el-input>
    </el-form-item>
    <div class="tip">
      温馨提示：请设置由大小写字母、数字、字符或特殊符号组成的8-16位密码。
    </div>
    <el-form-item>
      <el-button type="primary" @click="submitPassword">提交 </el-button>
      <el-button @click="resetPassword()">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updatePassword } from "@/api/login";
import { encryption } from "@/util/util";
import { rule } from "@/util/validateRules.js";
export default {
  name: "ResetPassword",
  data() {
    // var validatePass = (rule, value, callback) => {
    //   console.log("确认密码", value);
    //   if (this.passwordForm.oldPassword !== "") {
    //     if (value !== this.passwordForm.newPassword) {
    //       callback(new Error("两次输入密码不一致!"));
    //     } else {
    //       callback();
    //     }
    //   } else {
    //     callback();
    //   }
    // };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.passwordForm.confirmPassword !== "") {
          this.$refs.passwordForm.validateField("confirmPassword");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      passwordType: "password",
      passwordForm: {
        oldpassword: undefined,
        newpassword: undefined,
        confirmpassword: undefined,
      },
      rules: {
        oldPassword: [{ validator: validatePass, trigger: "blur" }],
        newPassword: [{ validator: rule.validatePassword, trigger: "blur" }],
        confirmPassword: [{ validator: validatePass2, trigger: "blur" }],
      },
      passwordRules: {
        oldPassword: [
          {
            required: true,
            min: 6,
            message: "原密码不能为空且不少于6位",
            trigger: "change",
          },
        ],
        newPassword: [
          {
            required: true,
            min: 6,
            message: "请输入新密码，且不少于6位",
            trigger: "change",
          },
        ],
        confirmPassword: [
          { required: true, message: "请输入确认密码", trigger: "blur" },
          { required: true, validator: validatePass, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    submitPassword() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          let params = {
            oldPassword: this.passwordForm.oldPassword,
            newPassword: this.passwordForm.newPassword,
          };
          const user = encryption({
            data: params,
            key: "user_center_2023",
            param: ["newPassword"],
          });
          const user2 = encryption({
            data: params,
            key: "user_center_2023",
            param: ["oldPassword"],
          });
          let newParams = {
            newPassword: user.newPassword,
            oldPassword: user2.oldPassword,
          };
          updatePassword(newParams).then((res) => {
            if (res.data.code === 200) {
              this.$notify({
                title: "成功",
                message: "修改密码成功",
                type: "success",
                duration: 2000,
              });
              // 修改密码之后强制重新登录
              this.$store.dispatch("LogOut").then((res2) => {
                // console.log("res===", res2);
                // location.reload(); // 为了重新实例化vue-router对象 避免bug
                let redirect = localStorage.getItem("redirect");
                if (redirect) {
                  this.$router.push({
                    path: "/sso",
                    query: {
                      redirect: redirect,
                    },
                  });
                } else {
                  this.$router.push({ path: "/sso" });
                }
              });
            } else {
              this.$notify({
                title: "失败",
                message: "修改密码失败" + res.data.msg,
                type: "error",
                duration: 2000,
              });
            }
          });
        }
      });
    },
    resetPassword() {
      if (this.$refs["passwordForm"]) {
        this.$refs["passwordForm"].resetFields();
      }
    },
    showPassword() {
      this.passwordType == ""
        ? (this.passwordType = "password")
        : (this.passwordType = "");
    },
  },
};
</script>

<style scoped lang="scss">
.demo-ruleForm {
  width: 500px;
  .row-bot {
    margin-bottom: 28px;
  }
}
.tip {
  font-size: 15px;
  margin: 40px 0;
}
</style>
