<template>
  <el-form :model="infoForm"
           :rules="infoRule"
           ref="infoForm"
           label-width="100px"
           class="demo-ruleForm">
    <el-form-item label="用户昵称"
                  prop="nickName">
      <el-input type="text"
                v-model="infoForm.nickName"
                disabled></el-input>
    </el-form-item>
    <el-form-item label="手机号" prop="phone">
      <el-input v-model="infoForm.phone" placeholder="验证码登录使用"></el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary"
                 @click="submitInfo">提交
      </el-button>
      <el-button @click="resetInfo()">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import {mapState} from "vuex";
export default {
  name: "ResetUserProfile",
  data() {
    return {
      switchStatus: '',
      avatarUrl: '',
      passwordType: "password",
      infoForm: {
        nickName: '',
        avatar: '',
        phone: ''
      },
      infoRule: {
        nickName: [{required: true, message: '用户名不能为空', trigger: 'change'}],
        phone: [{required: true, message: '手机号不能为空', trigger: 'blur'}]
      },
    }
  },
  created() {
    this.infoForm.nickName = this.userInfo.nickName
    this.infoForm.phone = this.userInfo.phone
    this.switchStatus = 'userManager'
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
  },
  methods: {
    switchTab(tab, event) {
      this.switchStatus = tab.name
    },
    submitInfo() {
      this.$refs.infoForm.validate((valid) => {
        if (valid) {
          console.log("个人信息提交", this.infoForm)
        }
      })
    },
    resetInfo() {
      if (this.$refs['infoForm']) {
        this.$refs['infoForm'].resetFields();
      }
    }

  }
}
</script>

<style scoped>

</style>
