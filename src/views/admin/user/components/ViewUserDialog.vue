<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog
    title="查看用户信息"
    width="1000px"
    :before-close="handleCloseDialog"
    :visible.sync="addBindUserVisible"
    top="5vh"
  >
    <div class="dept-content">
      <el-descriptions
        class="des-content"
        title=""
        :column="2"
        :size="size"
        border
      >
        <el-descriptions-item>
          <template slot="label"> 系统用户名： </template>
          {{ currentRow.userName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 姓名： </template>
          {{ currentRow.nickName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 身份证号： </template>
          {{ currentRow.certNo }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 手机号 </template>
          {{ currentRow.phone }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 所属部门 </template>
          <!-- <el-tag size="small">学校</el-tag> -->
          {{ currentRow.deptName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 所属处室 </template>
          <div class="tag-content">{{ currentRow.office }}</div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 岗位 </template>
          <div class="tag-content">
            <div
              v-for="(postObj, i) in detailObj.postList"
              :key="i"
              class="tag-sty"
            >
              <el-tag size="small"> {{ postObj.postName }}</el-tag>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 安全角色 </template>
          <div class="tag-content" v-if="detailObj.secureRoleList">
            <div
              v-for="anObj in detailObj.secureRoleList"
              :key="anObj.id"
              class="tag-sty"
            >
              <el-tag size="small"> {{ anObj.roleName }}</el-tag>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">业务角色</template>
          <div class="tag-content" v-if="detailObj.roleList">
            <div
              v-for="seObj in detailObj.roleList"
              :key="seObj.id"
              class="tag-sty"
            >
              <el-tag size="small"> {{ seObj.roleName }}</el-tag>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">状态</template>
          {{ currentRow.$status }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div slot="footer" class="dialog-footer">
      <!-- <el-button type="primary" @click="commitSelectStaff">提 交</el-button> -->
      <el-button @click="handleCloseDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import debounce from "@/util/debounce";
import { tableOption } from "@/const/crud/admin/role-bind-user";
//
import { getObjDetail } from "@/api/admin/auth/user";
export default {
  name: "roleForm",
  components: {},
  data() {
    return {
      size: "",

      addBindUserVisible: true,
      // 是否显示弹出层
      visible: true,
      currentRow: {},
      detailObj: {},
      labelStyle: {
        width: "140px",
      },
    };
  },
  props: {
    // sonSystemList: {
    //   // type: Array,
    //   // default: function () {
    //   //   return [];
    //   // },
    // },
    // roleId: {
    //   type: String,
    //   default: function () {
    //     return "";
    //   },
    // },
  },
  watch: {
    // deptSearch(val) {
    //   this.$refs.deptTreeRef.filter(val);
    // },
  },
  created() {},
  mounted() {},
  methods: {
    initForm(row) {
      this.currentRow = row;
      console.log("initForm==", row);
      getObjDetail(row.id).then((res) => {
        console.log("返回用户相亲--", res);
        if (res.data.code == 200) {
          this.detailObj = res.data.data;
        }
      });
    },
    handleCloseDialog() {
      this.$emit("closeDialog");
    },
  },
};
</script>
<style lang="scss" scoped>
.dept-content {
  width: 100%;
  display: flex;

  .des-content {
    width: 90%;
  }
}
.tag-content {
  display: flex;
  width: 318px;
  flex-wrap: wrap;
}
.tag-sty {
  margin: 8px 8px 0 0;
}
</style>
