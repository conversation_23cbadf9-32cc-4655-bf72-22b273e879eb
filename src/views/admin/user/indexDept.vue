<template>
  <div class="user">
    <div class="dept-content">
      <div class="dept-left">
        <el-input placeholder="组织名称、编码或规范简称" v-model="deptSearch">
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-tree
          class="filter-tree"
          :data="treeDeptDataBase"
          :props="defaultProps"
          :load="loadNode"
          lazy
          :default-expanded-keys="groupExpandedKeys"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleCheckChange"
          :filter-node-method="handleDeptfilterNode"
          :render-content="renderContent"
          ref="deptTreeRef"
        >
        </el-tree>
        <el-pagination
          v-if="searchFlag"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchPage.current"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="searchPage.size"
          layout=" pager, jumper"
          small
          :total="searchPage.total"
          :pager-count="5"
        >
        </el-pagination>
      </div>
      <basic-container style="flex: 1; width: 72%">
        <avue-crud
          :option="option"
          ref="crud"
          v-model="form"
          :page.sync="page"
          @on-load="getList"
          @size-change="sizeChange"
          @current-change="currentChange"
          :table-loading="listLoading"
          @search-change="handleFilter"
          @refresh-change="handleRefreshChange"
          @row-update="update"
          @row-save="create"
          :before-open="handleOpenBefore"
          :data="list"
        >
          <template slot="menuLeft">
            <el-button
              v-if="permissions.sys_user_add"
              class="filter-item"
              @click="$refs.crud.rowAdd()"
              type="primary"
              icon="el-icon-edit"
              >新增
            </el-button>
            <!-- <el-button
              v-if="permissions.sys_user_import_export"
              class="filter-item"
              plain
              type="primary"
              size="small"
              icon="el-icon-upload"
              @click="$refs.excelUpload.show()"
              >导入
            </el-button>
            <el-button
              v-if="permissions.sys_user_import_export"
              class="filter-item"
              plain
              type="primary"
              size="small"
              icon="el-icon-download"
              @click="exportExcel"
              >导出
            </el-button> -->
          </template>
          <template slot="userName" slot-scope="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
          <template slot="role" slot-scope="scope">
            <span v-for="(role, index) in scope.row.roleList" :key="index">
              <el-tag>{{ role.roleName }} </el-tag>&nbsp;&nbsp;
            </span>
          </template>
          <template slot="post" slot-scope="scope">
            <span v-for="(role, index) in scope.row.postList" :key="index">
              <el-tag>{{ role.postName }} </el-tag>&nbsp;&nbsp;
            </span>
          </template>
          <template slot="deptId" slot-scope="scope">
            {{ scope.row.deptName }}
          </template>
          <template slot="lockFlag" slot-scope="scope">
            <el-tag>{{ scope.label }}</el-tag>
          </template>
          <template slot="menu" slot-scope="scope">
            <el-button
              v-if="permissions.sys_user_reset"
              type="text"
              icon="el-icon-set-up"
              @click="handleResetPassword(scope.row)"
              >重置密码
            </el-button>
            <el-button
              type="text"
              icon="el-icon-view"
              @click="handleViewForm(scope.row)"
              >查看
            </el-button>
            <el-button
              v-if="permissions.sys_user_edit"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row, scope.index)"
              >编辑
            </el-button>
            <el-button
              v-if="permissions.sys_user_del"
              type="text"
              icon="el-icon-delete"
              @click="deletes(scope.row, scope.index)"
              >删除
            </el-button>
          </template>
          <!--   :node-click="getNodeData" -->

          <!--   <template slot="deptIdForm" slot-scope="scope">
           <avue-input-tree
              v-model="form.deptId"
              placeholder="请选择所属部门"
              :dic="treeDeptData"
              :props="defaultProps"
              node-key="id"
              :treeLoad="treeLoad"
              :expand-on-click-node="false"
              lazy
              :filter-node-method="filterNodeMethod"
            ></avue-input-tree>
          </template> -->
          <!-- <template slot="deptIdForm" slot-scope="scope">
            <avue-tree
              placeholder="请选择所属部门"
              :option="deptIdOption"
              :data="treeDeptData"
              v-model="form.deptId"
              :filter-node-method="filterNodeMethod"
            >
            </avue-tree>
          </template> -->
          <template slot="deptIdForm" slot-scope="scope">
            <tree-select
              v-model="form.deptName"
              :data="treeDeptDataArr"
              :props="defaultProps"
              :loadNode="treeLoad"
              :selfSearchPage="selfSearchPage"
              placeholder="请选择，输入组织名称、编码或规范简称可搜索"
              @selfSearch="selfSearch"
              @selfInput="selfInput"
              @selfCurrentChange="selfCurrentChange"
              pageFlag
              filterable
              slefFilter
              lazy
            ></tree-select>
          </template>
          <template slot="roleForm" slot-scope="scope">
            <avue-select
              v-model="role"
              multiple
              placeholder="请选择角色"
              :dic="rolesOptions"
              :props="roleProps"
            ></avue-select>
          </template>
          <template slot="postForm" slot-scope="scope">
            <!-- <avue-select
              v-model="post"
              multiple
              placeholder="请选择岗位"
              :dic="postOptions"
              :props="postProps"
            ></avue-select> -->
            <el-select
              v-model="post"
              multiple
              placeholder="请选择岗位"
              clearable
            >
              <el-option
                v-for="item in postOptions"
                :key="item.id"
                :label="item.postName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </template>
        </avue-crud>

        <!--excel 模板导入 -->
        <excel-upload
          ref="excelUpload"
          title="用户信息导入"
          url="/admin/user/import"
          temp-name="用户信息.xlsx"
          temp-url="/admin/sys-file/local/user.xlsx"
          @refreshDataList="handleRefreshChange"
        ></excel-upload>
      </basic-container>
    </div>
    <view-user-dialog
      v-if="viewFlag"
      ref="viewUserRef"
      @closeDialog="handleCloseViewUser"
    ></view-user-dialog>
  </div>
</template>

<script>
import {
  addObj,
  delObj,
  fetchList,
  putObj,
  updateResetUserPassword,
} from "@/api/admin/auth/user";
import { deptRoleList } from "@/api/admin/sys/role";
import { listPosts } from "@/api/admin/sys/post";
import {
  fetchTree,
  canUseNetDeptTree,
  canUseDeptSonTree,
  searchDeptByName,
} from "@/api/admin/sys/dept";
import { tableOption } from "@/const/crud/admin/user";
import { mapGetters } from "vuex";
import ExcelUpload from "@/components/upload/excel";

import ViewUserDialog from "./components/ViewUserDialog.vue";
import TreeSelect from "@/components/TreeSelect";
import debounce from "@/util/debounce";
import { getObjDetail } from "@/api/admin/auth/user";
export default {
  name: "table_user",
  components: { ExcelUpload, ViewUserDialog, TreeSelect },
  data() {
    return {
      option: tableOption,
      treeDeptData: [],
      treeDeptDataBase: [],
      treeDeptDataArr: [],
      checkedKeys: [],
      postProps: {
        label: "postName",
        value: "id",
      },
      roleProps: {
        label: "roleName",
        value: "id",
      },
      defaultProps: {
        label: "deptName",
        value: "id",
      },
      deptSearch: "",
      // defaultProps: {
      //   children: "children",
      //   label: "name",
      //   key: "id",
      // },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条,
        isAsc: false, //是否倒序
      },
      searchPage: {
        current: 1,
        size: 20,
        total: 0,
      },
      selfSearchPage: {
        current: 1,
        size: 20,
        total: 0,
      },
      query: {},
      list: [],
      listLoading: true,
      post: [],
      newPost: [],
      role: [],
      form: {},
      currentRow: {},
      postOptions: [],
      rolesOptions: [],
      groupExpandedKeys: [],
      selectNode: {},
      viewFlag: false,
      searchFlag: false,
      deptIdOption: {
        label: "所属部门",
        prop: "deptId",
        // formslot: true,
        type: "tree",
        // slot: true,
        span: 24,
        dicUrl: `/admin/dept/currentUserCanOperateDeptTree`,
        props: {
          label: "deptName",
          value: "id",
        },
        cacheData: [
          {
            deptName: "未加载数据",
            id: -1,
          },
        ],

        hide: true,
        lazy: true,
        treeLoad: async (node, resolve) => {
          // console.log("node=js---=", node);
          if (node.level === 0) {
            let params = { status: 1 };
            let res1 = await canUseNetDeptTree(params);
            // console.log("no--else----js---",res1);
            return resolve(res1.data.data);
          } else if (node.level > 0) {
            // console.log("else---js-");
            const child = await canUseDeptSonTree({ deptId: node.data.id });
            // console.log("child==", child);
            node.data.children = child.data.data;

            return resolve(child.data.data);
          }
        },

        rules: [
          {
            required: true,
            message: "请选择部门",
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
  },
  watch: {
    role() {
      this.form.role = this.role;
    },
    post() {
      this.form.post = this.post;
    },
    newPost() {
      this.form.post = this.post;
    },
    deptSearch(val) {
      if (val && val.length > 0) {
        this.searchFlag = true;
      } else {
        this.searchFlag = false;
      }
      // this.$refs.deptTreeRef.filter(val);
      this.debounceSearchDept(val);
    },
    "form.deptName": {
      handler(val, oldVal) {
        // console.log("val=333=", val);
        // console.log("val=treeDeptData=", this.treeDeptData);
        // this.form.deptId = val;
      },
    },
  },

  created() {
    this.hangdleGetTreeDeptFn();
    this.getPostData();
  },
  methods: {
    debounceSearchDept: debounce(function (name) {
      if (name) {
        this.handleSearchDept(name, true);
      } else {
        this.treeDeptDataBase = JSON.parse(JSON.stringify(this.treeDeptData));
      }
    }, 500),
    handleSearchDept(name, searchFlag) {
      let params = {
        keyWord: name,
      };
      if (searchFlag) {
        params.current = this.searchPage.current;
        params.size = this.searchPage.size;
      }
      searchDeptByName(params).then((res) => {
        // console.log("res==搜索记过=", res);
        if (res.data.code == 200) {
          this.treeDeptDataBase = res.data.data.records;
          this.searchPage.total = res.data.data.total;
        }
      });
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchPage.size = val;
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchPage.current = val;
      this.debounceSearchDept(this.deptSearch);
    },
    selfCurrentChange(val, keyWord) {
      // console.log("val==", val);
      // console.log("keyWord==", keyWord);
      this.selfSearchPage.current = val;
      this.debounceSearchDept2(keyWord);
    },
    selfSearch(val) {
      // console.log("val-33--", val);
      this.debounceSearchDept2(val);
    },
    selfInput(val) {
      this.form.deptId = val;
      // console.log("this.form.deptId==", this.form.deptId);
    },
    debounceSearchDept2: debounce(function (name) {
      // console.log("name==", name);
      if (name) {
        this.handleSearchDept2(name);
      } else {
        this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
      }
    }, 500),
    handleSearchDept2(name) {
      // console.log("搜索--", name);
      let params = {
        keyWord: name,
      };
      params.current = this.selfSearchPage.current;
      params.size = this.selfSearchPage.size;
      searchDeptByName(params).then((res) => {
        // console.log("res==搜索记过=", res);
        if (res.data.code == 200) {
          this.treeDeptDataArr = res.data.data.records;
          this.selfSearchPage.tatal = res.data.data.total;
        }
      });
    },
    treeLoad: async (node, resolve) => {
      // console.log("treeLoad=js---=", node);
      if (node.level === 0) {
        let params = { status: 1 };
        let res1 = await canUseNetDeptTree(params);
        // console.log("no--else----js---",res1);
        return resolve(res1.data.data);
      } else if (node.level > 0) {
        // console.log("else---js-");
        const child = await canUseDeptSonTree({ deptId: node.data.id });
        // console.log("child==", child);
        node.data.children = child.data.data;

        return resolve(child.data.data);
      }
    },
    filterNodeMethod(value, data) {
      // console.log("value==", value);
      // console.log("data==", data);
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    renderContent(h, { node, data, store }) {
      if (data.level == "root") {
        return (
          <span>
            <el-tooltip
              class="item"
              effect="dark"
              content={node.label}
              placement="top"
            >
              <span>
                <i class="el-icon-s-home"></i> {node.label}
              </span>
            </el-tooltip>
          </span>
        );
      } else {
        return (
          <span>
            <el-tooltip
              class="item"
              effect="dark"
              content={node.label}
              placement="top"
            >
              <span>
                <i class="el-icon-document"></i> {node.label}
              </span>
            </el-tooltip>
          </span>
        );
      }
    },
    handleCloseViewUser() {
      this.viewFlag = false;
    },
    // renderContent: function (h, { node, data, store }) {
    //   let addElement = arguments[0];
    //   if (data.children) {
    //     return addElement("span", [
    //       addElement("i", { class: "el-icon-heavy-rain" }),
    //       addElement("span", "    "),
    //       addElement("span", arguments[1].node.label),
    //     ]);
    //   } else {
    //     return addElement("span", [
    //       addElement("i", { class: "el-icon-sunny" }),
    //       addElement("span", "    "),
    //       addElement("span", arguments[1].node.label),
    //     ]);
    //   }
    // },
    handleDeptfilterNode(value, data) {
      if (!value) return true;
      // console.log("data=11=", data);
      return data.name.indexOf(value) !== -1;
    },
    async loadNode(node, resolve) {
      // console.log("node=12=", node);
      if (node.level === 0) {
        // console.log("no--else--");
        return resolve(node.data);
      } else if (node.level > 0) {
        // console.log("else--");
        const child = await canUseDeptSonTree({ deptId: node.data.id });
        // console.log("child==", child);
        node.data.children = child.data.data;

        return resolve(child.data.data);
      }
      // console.log("appTreeData==", appTreeData);
    },
    getList(page, params) {
      // console.log("分页查询=====", params);
      this.listLoading = true;
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            deptId: this.currentDeptId,
          },
          params,
          this.query
        )
      )
        .then((response) => {
          this.list = response.data.data.records;
          this.page.total = response.data.data.total;
          this.listLoading = false;
        })
        .catch((err) => {
          this.listLoading = false;
        });
    },
    getNodeData(data) {
      deptRoleList().then((response) => {
        this.rolesOptions = response.data.data;
      });
    },
    getPostData() {
      listPosts().then((response) => {
        this.postOptions = response.data.data;
        // console.log(" this.postOptions=lzs----=", this.postOptions);
        // this.post = ["1781123995346911234"];
      });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      // console.log("ll--dd---", current);
      this.page.currentPage = current;
    },
    handleFilter(param, done) {
      // console.log("param==", param);
      this.query = param;
      this.page.currentPage = 1;
      this.getList(this.page, param);
      done();
    },
    handleRefreshChange() {
      this.getList(this.page);
    },
    hangdleGetTreeDeptFn() {
      // 查询部门树
      let params = {
        status: 1,
      };
      canUseNetDeptTree(params).then((response) => {
        this.treeDeptData = response.data.data;
        this.treeDeptData.forEach((item, i) => {
          this.groupExpandedKeys.push(item.id);
        });
        if (this.treeDeptData && this.treeDeptData.length > 0) {
          for (let index = 0; index < this.treeDeptData.length; index++) {
            this.treeDeptData[index].level = "root";
          }
        }
        this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
        this.treeDeptDataBase = JSON.parse(JSON.stringify(this.treeDeptData));
        //groupExpandedKeys
      });
    },
    handleCheckChange(data) {
      console.log("data==", data);
      // let params = {
      //   deptId: data.id,
      // };
      this.selectNode = data;
      this.currentDeptId = data.id;
      this.getList(this.page);
    },
    handleOpenBefore(show, type) {
      console.log("type==", type);

      if (type == "add") {
        if (this.selectNode.id) {
          this.form.deptName = this.selectNode.deptName;
          this.form.deptId = this.selectNode.id;
        }
      }

      window.boxType = type;
      // // 查询部门树
      // fetchTree().then((response) => {
      //   this.treeDeptData = response.data.data;
      // });
      // 查询角色列表
      deptRoleList().then((response) => {
        this.rolesOptions = response.data.data;
      });
      //查询岗位列表
      listPosts().then((response) => {
        this.postOptions = response.data.data;
      });
      // console.log("this.form-open==", this.form);
      // 若是编辑、查看回显角色名称
      if (["edit", "views"].includes(type)) {
        // this.role = [];
        // for (let i = 0; i < this.form.roleList.length; i++) {
        //   this.role[i] = this.form.roleList[i].id;
        // }
        // this.post = [];
        // console.log(" this.post=11=", this.post);
        let that = this;
        // if (this.form.postList && this.form.postList.length > 0) {
        //   for (let i = 0; i < this.form.postList.length; i++) {
        //     this.post[i] = this.form.postList[i].id;
        //   }
        // }
        getObjDetail(this.form.id).then((res) => {
          // console.log("返回用户detail22--", res);
          if (res.data.code == 200) {
            // this.detailObj = res.data.data;
            let detaileObj = res.data.data;
            if (
              detaileObj &&
              detaileObj.postList &&
              detaileObj.postList.length > 0
            ) {
              this.post = [];
              this.newPost = [];
              let postArr = [];
              detaileObj.postList.forEach((item) => {
                // postArr.push(item.id);
                this.post.push(item.id);
                // this.newPost.push(item.id);
              });

              // this.$nextTick(() => {
              //   this.$forceUpdate();
              // });
            }
          }
        });
      } else if (type === "add") {
        // 若是添加角色列表设置为空
        this.role = [];
        this.post = [];
      }
      show();
    },
    handleViewForm(row) {
      this.currentRow = row;
      this.viewFlag = true;
      this.$nextTick(() => {
        this.$refs.viewUserRef.initForm(row);
      });
    },
    handleUpdate(row, index) {
      this.$refs.crud.rowEdit(row, index);
      this.form.password = undefined;
    },
    handleResetPassword(row) {
      // console.log("row--", row);
      this.$confirm("确定要重置用户" + row.userName + "的密码?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          userId: row.id,
          // newpassword: "",
        };
        updateResetUserPassword(params).then((res) => {
          if (res.data.code === 200) {
            this.$message.success("重置成功！初始密码为：ZsAdmin1!");
          } else {
            this.$message.error(res.data.msg);
          }
          this.getList(this.page);
        });
        // delObj(row.id)
        //   .then((res) => {
        //     if (res.data.code === 200) {
        //       this.$message.success("删除成功");
        //     } else {
        //       this.$message.error(res.data.msg);
        //     }
        //     this.getList(this.page);
        //   })
        //   .catch(() => {
        //     this.$message.error("删除失败");
        //   });
      });
    },
    create(row, done, loading) {
      addObj(this.form)
        .then(() => {
          this.getList(this.page);
          done();
          this.$message.success("创建成功，初始密码为：ZsAdmin1!");
        })
        .catch(() => {
          loading();
        });
    },
    update(row, index, done, loading) {
      console.log("this.form", this.form);
      this.form.deptName = ""; //2024年4月26日 添加
      putObj(this.form)
        .then(() => {
          this.getList(this.page);
          done();
          this.$message.success("修改成功");
        })
        .catch(() => {
          loading();
        });
    },
    deletes(row) {
      this.$confirm(
        "此操作将永久删除该用户(用户名:" + row.userName + "), 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        delObj(row.id)
          .then((res) => {
            if (res.data.code === 200) {
              this.$message.success("删除成功");
            } else {
              this.$message.error(res.data.msg);
            }
            this.getList(this.page);
          })
          .catch(() => {
            this.$message.error("删除失败");
          });
      });
    },
    exportExcel() {
      this.downBlobFile("/admin/user/export", this.query, "user.xlsx");
    },
  },
};
</script>
<style scoped lang="scss">
.dept-content {
  width: 100%;
  display: flex;
  border: 1px solid transparent;
  .dept-left {
    width: 320px;
    min-width: 320px;

    margin: 8px 0 18px 20px;
    background: #fff;
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 8px;
    padding-top: 20px;
    font-size: 14px;
  }
}
</style>
