<template>
  <div class="profile">
    <el-card shadow="hover" class="card-class" style="width: 33%">
      <profile-info></profile-info>
    </el-card>
    <el-card shadow="hover" class="card-class"  style="width: 67%">
      <el-tabs v-model="switchStatus" @tab-click="switchTab">
        <el-tab-pane label='信息管理' name='userManager'>
          <reset-user-profile></reset-user-profile>
        </el-tab-pane>
        <el-tab-pane label='密码管理' name='passwordManager'>
          <reset-password></reset-password>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>


<script>
import {mapState} from 'vuex'
import ResetPassword from "@/views/admin/user/components/ResetPassword.vue";
import ResetUserProfile from "@/views/admin/user/components/ResetUserProfile.vue";
import ProfileInfo from "@/views/admin/user/components/ProfileInfo.vue";

export default {
  components: {ResetPassword, ResetUserProfile, ProfileInfo},
  data() {
    return {
      switchStatus: '',
    }
  },
  created() {
    this.switchStatus = 'userManager'
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),
  },
  methods: {
    switchTab(tab, event) {
      console.log("=====")
      this.switchStatus = tab.name
    },
  }
}
</script>
<style>
.profile {
  display: flex;
}
.card-class {
  max-height: 960px;
  padding: 15px 20px 20px 20px;
}

</style>
