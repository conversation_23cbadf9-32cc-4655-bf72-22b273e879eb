<template>
  <div class="profile">
    <div class="pro-main">
      <div class="pro-left">
        <div class="pro-user-bg"></div>
        <div
          class="pro-title"
          :class="type == 1 ? 'title-active' : ''"
          @click="changeTab(1)"
        >
          基本信息
        </div>
        <div
          class="pro-title"
          :class="type == 2 ? 'title-active' : ''"
          @click="changeTab(2)"
        >
          修改密码
        </div>
      </div>
      <div class="pro-right">
        <profile-info v-if="type == 1"></profile-info>
        <reset-password v-else-if="type == 2"></reset-password>
      </div>
    </div>
  </div>
</template>


<script>
import { mapState } from "vuex";
import ResetPassword from "@/views/admin/user/components/ResetPassword.vue";
import ResetUserProfile from "@/views/admin/user/components/ResetUserProfile.vue";
import ProfileInfo from "@/views/admin/user/components/ProfileInfo.vue";

export default {
  components: { ResetPassword, ResetUserProfile, ProfileInfo },
  data() {
    return {
      switchStatus: "",
      type: 1,
    };
  },
  created() {
    this.switchStatus = "userManager";
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
    }),
  },
  methods: {
    switchTab(tab, event) {
      console.log("=====");
      this.switchStatus = tab.name;
    },
    changeTab(type) {
      this.type = type;
    },
  },
};
</script>
<style scoped lang="scss">
.profile {
  width: 100%;
  height: 1000px;
  background: #eef2fb;
  .pro-main {
    display: flex;
    width: 1160px;
    margin: 20px auto;

    .pro-left {
      width: 350px;
      height: 910px;
      background: url("../../../assets/image/img/user-line.png") no-repeat;
      background-size: 100%;
      position: relative;
      .pro-user-bg {
        width: 100px;
        height: 100px;
        background: url("../../../assets/image/img/morentouxiang.png") no-repeat;
        background-size: 100%;
        margin: 125px 0 0 125px;
        background-color: #fff;
        border-radius: 50%;
        margin-bottom: 70px;
      }
      .pro-title {
        text-align: center;
        font-size: 16px;
        color: #333333;
        height: 64px;
        line-height: 64px;
        border-bottom: 1px solid #dee6ec;
        cursor: pointer;
      }
      .title-active {
        background: #f5f8fa;
        color: #0357ca;
      }
    }
    .pro-right {
      margin-left: 20px;
      background-color: #fff;
      width: 780px;
      display: flex;
      // align-items: center;
      justify-content: center;
      padding-top: 100px;
    }
  }
}
.card-class {
  max-height: 960px;
  padding: 15px 20px 20px 20px;
}
</style>
