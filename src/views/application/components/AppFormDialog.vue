<template>
  <!-- 添加或修改菜单对话框 -->
  <cust-dialog
    :title="addType == 'add' ? '新增' : addType == 'edit' ? '修改' : '查看'"
    :visible.sync="visible"
    @close="handleCloseDialog"
  >
    <el-form
      ref="appForm"
      :model="form"
      :disabled="addType == 'view'"
      :rules="rules"
      label-width="80px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="来源" prop="source">
            <el-radio-group v-model="form.source" @input="handleChangeRadio">
              <el-radio label="0">用户中心</el-radio>
              <el-radio label="1">其他</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="应用名称" prop="name">
            <el-select
              v-model="form.uid"
              placeholder="请选择应用名称"
              v-if="form.source == 0"
              clearable
              style="width: 100%"
              @change="changeNameFn"
            >
              <el-option
                v-for="appObj in appArr"
                :key="appObj.id"
                :label="appObj.name"
                :value="appObj.id"
              />
            </el-select>
            <el-input v-model="form.name" v-else placeholder="请输入应用名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="首页地址" prop="portalUrl">
            <el-input v-model="form.portalUrl" placeholder="请输入首页地址" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="应用图标" prop="logo">
            <img-upload v-model="form.logo"></img-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本号" prop="appVersion">
            <el-input v-model="form.appVersion" placeholder="请输入版本号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="发布位置" prop="publishSite">
            <el-checkbox-group v-model="form.publishSite">
              <el-checkbox label="0">移动端</el-checkbox>
              <el-checkbox label="1">PC端</el-checkbox>
              <el-checkbox label="2">平板端</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接入类型" prop="accessType">
            <el-radio-group v-model="form.accessType">
              <el-radio label="0">站点链接</el-radio>
              <el-radio label="1">平台级对接</el-radio>
              <el-radio label="2">系统级对接</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="管理员姓名" prop="contact" label-width="120">
            <el-input
              v-model="form.contact"
              placeholder="请输入管理员姓名"
              style="width: calc(100% - 120px)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactTel">
            <el-input v-model="form.contactTel" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <file-upload-zujian
          ref="fjRef"
          :fileSize="10"
          uploadFileUrl="/api/storage/upload"
          :fileType="['png,jpg']"
          v-model="form.fjList"
          :files="form.fileVOList"
          resultType="Object"
        ></file-upload-zujian> -->

      <!-- <el-form-item label="部门领导" prop="leader">
       
      </el-form-item> -->
      <!-- <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          controls-position="right"
          :min="0"
        />
      </el-form-item> -->
      <el-form-item label="应用描述">
        <el-input
          type="textarea"
          v-model="form.description"
          style="width: 60%"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="bottom-footer" class="dialog-footer">
      <el-button
        type="primary"
        @click="debounceDataFormSubmitFn"
        v-if="addType != 'view'"
        >确 定</el-button
      >
      <el-button @click="handleCloseDialog">取 消</el-button>
    </div>
  </cust-dialog>
</template>

<script>
// import Treeselect from "@riophae/vue-treeselect";
import { getAppList, addAppObj, updateAppObj } from "@/api/application/index";

// import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import FileUploadZujian from "@/components/upload/fileUpload.vue";

import debounce from "@/util/debounce";

import imgUpload from "@/components/upload/imgUpload.vue";
export default {
  name: "DeptForm",
  components: { FileUploadZujian, imgUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项
      deptOptions: [],
      // 下拉用户列表
      users: [],
      // 是否显示弹出层
      visible: true,
      form: {
        name: undefined,
        portalUrl: "",
        logo: "",
        source: "0",
        publishSite: [],
        accessType: "",
        contact: "",
        contactTel: "",
        description: "",
      },
      poolareaNoOptions: [],
      deptArr: [],
      appArr: [],
      addType: "add",
      // 表单校验
      rules: {
        source: [
          { required: true, message: "来源不能为空", trigger: "change" },
        ],
        name: [
          { required: true, message: "导航名称不能为空", trigger: "blur" },
        ],
        portalUrl: [
          { required: true, message: "首页地址不能为空", trigger: "blur" },
        ],
        logo: [
          { required: true, message: "应用图标不能为空", trigger: "blur" },
        ],
        contact: [
          { required: true, message: "管理员姓名不能为空", trigger: "blur" },
        ],
        contactTel: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
        // sortOrder: [
        //   { required: true, message: "菜单顺序不能为空", trigger: "blur" },
        // ],
      },
    };
  },
  created() {
    // 获取应用列表
    this.getAppListFn();
  },
  mounted() {
    // if (this.form.areaName) {
    //   this.$refs.poolareaNoRef.presentText = this.form.areaName;
    // }
  },
  methods: {
    changeNameFn(val) {
      console.log("val--", val);
      let fObj = this.appArr.find((idObj) => {
        return idObj.id == val;
      });
      console.log("fobj===", fObj);
      if (fObj) {
        this.form.portalUrl = fObj.frontUrl;
        this.form.contact = fObj.contact;
        this.form.contactTel = fObj.contactTel;
        this.form.description = fObj.description;
        this.form.logo = fObj.backGroundIcon;
        this.form.name = fObj.name;
      }
    },
    handleInitEdit(row) {
      this.addType = "edit";
      console.log("r====", row);
      this.form = Object.assign({}, row);
      if (this.form.publishSite) {
        this.form.publishSite = this.form.publishSite.split(",");
      }
      if (this.form.logo) {
        this.form.logo = this.$getUrlByProcess(this.form.logo);
      }
    },
    handleInitView(row) {
      this.addType = "view";
      this.form = Object.assign({}, row);
      if (this.form.publishSite) {
        this.form.publishSite = this.form.publishSite.split(",");
      }
    },
    handleChangeRadio() {
      this.form.name = "";
    },
    getAppListFn() {
      getAppList().then((res) => {
        console.log("res=获取的应用==", res);
        if (res.data.code == 200) {
          this.appArr = res.data.data;
        }
      });
    },
    handleCloseDialog() {
      // this.visible = false;
      console.log("close");
      this.$emit("handleCloseDiaolog");
    },

    init(isEdit, id) {
      if (id !== null) {
        this.form.parentId = id;
      }
      this.form.remark = "";
      this.visible = true;
      this.getTreeselect();
      // this.listAllUser();
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          // getObj(id).then((response) => {
          //   this.form = response.data.data;
          //   console.log("from==", this.form);
          //   if (this.form.areaName) {
          //     setTimeout(() => {
          //       this.$refs.poolareaNoRef.presentText = this.form.areaName;
          //     }, 400);
          //   }
          // });
        } else {
          this.form.id = undefined;
        }
      });
    },

    debounceDataFormSubmitFn: debounce(function () {
      this.dataFormSubmit();
    }, 500),
    // 表单提交
    dataFormSubmit() {
      this.$refs["appForm"].validate((valid) => {
        if (valid) {
          if (this.form.publishSite && this.form.publishSite.length > 0) {
            this.form.publishSite = this.form.publishSite.toString();
          }
          let fileUrl = process.env.VUE_APP_FILE_URL;
          if (this.form.logo) {
            this.form.logo = this.form.logo.replace(fileUrl, "");
            let fileUrl2 =
              window.location.protocol + "//" + window.location.host;
            this.form.logo = this.form.logo.replace(fileUrl2, "");
          }
          if (this.form.id) {
            updateAppObj(this.form).then((data) => {
              this.$message.success("修改成功");
              // this.visible = false;
              this.$emit("refreshDataList");
            });
          } else {
            addAppObj(this.form).then((data) => {
              this.$message.success("添加成功");
              // this.visible = false;
              this.$emit("refreshDataList");
            });
          }
        }
      });
    },
    /** 查询菜单下拉树结构 */
    // getTreeselect() {
    //   getDaohangTree().then((response) => {
    //     this.deptOptions = [];
    //     const dept = { id: -1, name: "根", children: response.data.data };
    //     this.deptOptions.push(dept);
    //   });
    // },
    /** 转换菜单数据结构 */
    // normalizer(node) {
    //   if (node.children && !node.children.length) {
    //     delete node.children;
    //   }

    //   return {
    //     id: node.id,
    //     label: node.name,
    //     children: node.children,
    //   };
    // },
  },
};
</script>
