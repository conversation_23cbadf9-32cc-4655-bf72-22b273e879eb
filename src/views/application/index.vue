<template>
  <div class="app-container">
    <div>
      <basic-container>
        <el-tabs v-model="activeName" @tab-click="handleChageTab">
          <el-tab-pane label="待提交" name="0"></el-tab-pane>
          <el-tab-pane label="待审核" name="1"></el-tab-pane>
          <el-tab-pane label="已审核" name="2"></el-tab-pane>
        </el-tabs>
        <avue-crud
          ref="crud"
          :page.sync="page"
          :data="tableData"
          :permission="permissionList"
          :table-loading="tableLoading"
          :option="tableOption"
          @on-load="getList"
          @size-change="sizeChange"
          @current-change="currentChange"
          @search-change="searchChange"
          v-model="form"
        >
          <template slot="menuLeft">
            <el-button type="primary" @click="handleCreateApp" size="small"
              >新增申请</el-button
            >
          </template>
          <!--  :before-open="handleBeforeOpen"
          @refresh-change="refreshChange"   @row-update="handleUpdate"
          @row-save="handleSave"
          @row-del="rowDel"-->
          <!-- <template slot="menuLeft">
          <el-button type="primary" @click="handlePublish" size="small"
            >发布公告</el-button
          >
        </template> -->
          <!-- <template slot="attachmentListForm">
            <el-upload
              class="upload-demo"
              action="/portal-api/storage/upload"
              :on-success="handleUploadDocSuccess"
              :on-error="handleUploadDocError"
              :on-preview="handlePreview"
              multiple
              :limit="10"
              :headers="headerObj"
              :file-list="fileList"
              :on-remove="handleRemove"
              :before-upload="beforeAvatarUpload"
              accept=".jpg,.jpeg,.png,.pdf,.PDF,.text,.doc,.docx,.xls,.ppt"
            >

              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">
                文件格式支持jpg、png、jpeg、pdf、txt、doc、docx、xls、ppt格式，文件不超过100M。
              </div>
            </el-upload>
          </template> -->
          <template slot="logo" slot-scope="{ row }">
            <div class="avue-crud__img">
              <div class="imgbox">
                <el-image
                  class="defult-img"
                  :src="row.logo ? $getUrlByProcess(row.logo) : ''"
                  alt="logo"
                  :preview-src-list="[
                    row.logo ? $getUrlByProcess(row.logo) : '',
                  ]"
                  :style="{ width: '40px', height: '40px' }"
                ></el-image>
                <!-- @click="handleImgClick(row, item)" -->
              </div>
            </div>
          </template>
          <template slot="source" slot-scope="{ row }">
            <span v-if="row.source == 0">用户中心</span>
            <span v-else-if="row.source == 1">其他</span>
          </template>
          <template slot="accessType" slot-scope="{ row }">
            <span v-if="row.accessType == 0">站点链接</span>
            <span v-else-if="row.accessType == 1">台级对接</span>
            <span v-else-if="row.accessType == 2">系统级对接</span>
          </template>
          <template slot="status" slot-scope="{ row }">
            <el-tag type="info" v-if="row.status == 0">待提交</el-tag>
            <el-tag v-else-if="row.status == 1">待审核</el-tag>
            <el-tag type="success" v-else-if="row.status == 2">已审核</el-tag>
          </template>
          <template slot="status1" slot-scope="{ row }">
            <el-switch
              class="switch-style"
              active-value="1"
              inactive-value="0"
              @change="handleSwitchChange(row)"
              active-text="是"
              inactive-text="否"
              v-model="row.enable"
              active-color="#437fff"
              inactive-color="#615b5b"
            >
            </el-switch>
          </template>
          <template slot="menu" slot-scope="{ row, index }">
            <el-button
              type="text"
              icon="el-icon-top"
              @click="handleSubmit(row, index)"
              v-if="activeName == 0"
            >
              提交
            </el-button>
            <!--  v-if="!row.status && permissions.update_notice" -->
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              @click="handleEdit(row, index)"
              v-if="activeName == 0"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              size="small"
              @click="handleRowDel(row, index)"
              v-if="activeName == 0"
              >删除</el-button
            >
            <el-button
              type="text"
              icon="el-icon-view"
              size="small"
              @click="handleViewRow(row, index)"
              >查看</el-button
            >
            <el-button
              type="text"
              icon="el-icon-s-promotion"
              @click="handleShenhe(row, index)"
              v-if="activeName == 1"
            >
              审核
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </div>
    <app-form-dialog
      v-if="formDialogFlag"
      @handleCloseDiaolog="handleCloseAppDiaolog"
      @refreshDataList="refreshList"
      ref="appFormRef"
    ></app-form-dialog>
    <cust-dialog
      title="审核"
      v-if="shenheFlag"
      :dialog-status.sync="shenheFlag"
      width="900px"
      height="540px"
      class="mydialog"
      @closed="handleCloseDialog"
    >
      <div class="form-main">
        <el-form
          :model="shenheForm"
          :rules="dealRules"
          label-width="100px"
          ref="dealForm"
        >
          <div style="display: flex">
            <el-form-item
              class="item-item"
              label="审核结果 "
              prop="dealDeptCode"
            >
              <el-radio-group v-model="shenheForm.shenheStatus">
                <el-radio label="1">审核通过</el-radio>
                <el-radio label="2">审核不通过</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <div>
            <el-form-item
              class="item-item"
              label="审核意见"
              prop="refuseReason"
            >
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="shenheForm.refuseReason"
                style="width: 500px"
              >
              </el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div slot="bottom-footer">
        <el-button @click="handleCloseded">取消</el-button>
        <el-button @click="handleDealShenHe" type="primary">提交</el-button>
      </div>
    </cust-dialog>
  </div>
</template>
<script>
import { tableOption } from "@/const/crud/application/index";
import { mapGetters } from "vuex";
import {
  getPageData,
  deleteAppObj,
  doSubmitObj,
  doShenheObj,
  doShenheRefuseObj,
  doEnableApp,
  doNotEnableApp,
} from "@/api/application/index";
import AppFormDialog from "./components/AppFormDialog.vue";
export default {
  components: { AppFormDialog },
  data() {
    return {
      activeName: "0",
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableOption: tableOption,
      tableLoading: false,
      form: {},
      tableData: [],
      formDialogFlag: false,
      status: this.activeName,
      searchForm: {},
      shenheForm: { shenheStatus: "1", refuseReason: "" },
      shenheFlag: false,
      currentRow: {},
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permissions.create_notice, false),
        // delBtn: this.vaildData(this.permissions.del_notice, false),
        // editBtn: this.vaildData(this.permissions.update_notice, false),
      };
    },
    token() {
      return this.$store.getters.access_token;
    },
  },
  created() {
    console.log("created");
    this.tableOption.column[7].hide = true;
  },
  methods: {
    handleDealShenHe() {
      let params = {
        id: this.currentRow.id,
        refuseReason: this.shenheForm.refuseReason,
      };
      if (this.shenheForm.shenheStatus == "1") {
        doShenheObj(params).then((res) => {
          // console.log("审核通过==", res);
          this.shenheFlag = false;
          if (res.data.code == 200) {
            this.getList(this.page);
          }
        });
      } else {
        doShenheRefuseObj(params).then((res) => {
          // console.log("审核通过==", res);
          if (res.data.code == 200) {
            this.shenheFlag = false;
            this.getList(this.page);
          }
        });
      }

      // this.currentRow
    },
    handleRowDel(row) {
      this.$confirm('是否确认删除应用名称为"' + row.name + '"的应用?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return deleteAppObj(row.id);
        })
        .then(() => {
          this.getList(this.page);
          this.$message.success("删除成功");
        });
    },
    handleSubmit(row) {
      let params = {
        id: row.id,
      };

      this.$confirm('是否确认提交应用名称为"' + row.name + '"的应用?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return doSubmitObj(params);
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.getList(this.page);
          }
        });
    },
    searchChange(form, done) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    handleShenhe(row) {
      this.shenheFlag = true;
      this.currentRow = row;
      // let params = {
      //   id: row.id,
      // };

      // this.$confirm('是否确认审核应用名称为"' + row.name + '"的应用?', "警告", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // })
      //   .then(function () {
      //     return doShenheObj(params);
      //   })
      //   .then((res) => {
      //     if (res.data.code == 200) {
      //       this.getList(this.page);
      //     }
      //   });
    },
    handleEdit(row) {
      this.formDialogFlag = true;
      this.$nextTick(() => {
        this.$refs.appFormRef.handleInitEdit(row);
      });
    },
    handleViewRow(row) {
      this.formDialogFlag = true;
      if (row.logo) {
        row.logo = this.$getUrlByProcess(row.logo);
      }
      this.$nextTick(() => {
        this.$refs.appFormRef.handleInitView(row);
      });
    },
    handleCloseAppDiaolog() {
      this.formDialogFlag = false;
    },
    refreshList() {
      this.formDialogFlag = false;
      this.getList(this.page);
    },
    getList(page, params) {
      this.tableLoading = true;
      let newParams = Object.assign(
        {
          current: page.currentPage,
          size: page.pageSize,
          status: this.activeName,
        },
        params,
        this.searchForm
      );
      getPageData(newParams).then((res) => {
        this.tableLoading = false;

        if (res.data.code == 200) {
          this.page.total = res.data.data.total;
          this.tableData = res.data.data.records;
        }
      });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    handleSwitchChange(row) {
      console.log("row==", row);
      let params = {
        id: row.id,
      };
      if (row.enable == 1) {
        doEnableApp(params).then((res) => {
          console.log("res===", res);
          if (res.data.code == 200) {
            this.$message({
              message: "启用成功",
              type: "success",
            });
            this.getList(this.page);
          }
        });
      } else {
        doNotEnableApp(params).then((res) => {
          console.log("res===", res);
          if (res.data.code == 200) {
            this.$message({
              message: "禁用成功",
              type: "success",
            });
            this.getList(this.page);
          }
        });
      }
    },
    handleChageTab(tab, event) {
      this.getList(this.page);
      console.log("tableOption==", this.tableOption);
      if (this.activeName == 2) {
        this.tableOption.column[7].hide = false;
      } else {
        this.tableOption.column[7].hide = true;
      }

      // this.status
    },
    handleCreateApp() {
      this.formDialogFlag = true;
    },
  },
};
</script>
<style scoped lang="scss">
@import "@/styles/switch.scss";

.app-container {
}
</style>
