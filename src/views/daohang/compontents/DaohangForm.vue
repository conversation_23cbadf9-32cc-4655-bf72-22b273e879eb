<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog
    :title="!form.id ? '新增' : '修改'"
    :visible.sync="visible"
    :before-close="handleCloseDialog"
  >
    <el-form ref="dataForm" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col>
          <el-form-item label="上级导航">
            <treeselect
              v-model="form.parentId"
              :options="deptOptions"
              :normalizer="normalizer"
              :show-count="true"
              placeholder="选择上级导航"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="导航名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入导航名称" />
      </el-form-item>
      <!-- <el-form-item label="行政区划" prop="poolareaNoArr">
    
        <el-cascader
          ref="poolareaNoRef"
          v-model="form.poolareaNoArr"
          :props="props"
          :show-all-levels="true"
          clearable
        ></el-cascader>
      </el-form-item> -->
      <!-- <el-form-item label="部门领导" prop="leader">
        <el-select v-model="form.leader" placeholder="请选择领导" clearable>
          <el-option
            v-for="user in users"
            :key="user.id"
            :label="user.nickName"
            :value="user.id"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          controls-position="right"
          :min="0"
        />
      </el-form-item>
      <el-form-item label="说明">
        <el-input
          type="textarea"
          v-model="form.remark"
          style="width: 60%"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="debounceDataFormSubmitFn"
        >确 定</el-button
      >
      <el-button @click="handleCloseDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import { listUser } from "@/api/admin/auth/user";

import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import debounce from "@/util/debounce";
import {
  addDaohangData,
  getDaohangTree,
  updateDaohangData,
} from "@/api/daohang/index";
export default {
  name: "DeptForm",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项
      deptOptions: [],
      // 下拉用户列表
      users: [],
      // 是否显示弹出层
      visible: true,
      form: {
        name: undefined,
        sortOrder: 999,
        leader: "",
        poolareaNoArr: [],
        remark: "",
      },
      poolareaNoOptions: [],
      deptArr: [],

      // 表单校验
      rules: {
        name: [
          { required: true, message: "导航名称不能为空", trigger: "blur" },
        ],
        // sortOrder: [
        //   { required: true, message: "菜单顺序不能为空", trigger: "blur" },
        // ],
      },
    };
  },
  created() {
    // this.getAllProviceFn();
  },
  mounted() {
    if (this.form.areaName) {
      this.$refs.poolareaNoRef.presentText = this.form.areaName;
    }
  },
  methods: {
    handleCloseDialog() {
      this.visible = false;
      this.$emit("handleCloseDiaolog");
    },
    getAllProviceFn() {
      getAllProvinces().then((res) => {
        // console.log("获取省份数据==", res);
        if (res.data.code == 200) {
          this.deptArr = res.data.data;
        }
      });
    },

    init(isEdit, id) {
      if (id !== null) {
        this.form.parentId = id;
      }
      this.form.remark = "";
      this.visible = true;
      this.getTreeselect();
      // this.listAllUser();
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          // getObj(id).then((response) => {
          //   this.form = response.data.data;
          //   console.log("from==", this.form);
          //   if (this.form.areaName) {
          //     setTimeout(() => {
          //       this.$refs.poolareaNoRef.presentText = this.form.areaName;
          //     }, 400);
          //   }
          // });
        } else {
          this.form.id = undefined;
        }
      });
    },
    listAllUser() {
      listUser().then((response) => {
        this.users = response.data.data;
      });
    },
    debounceDataFormSubmitFn: debounce(function () {
      this.dataFormSubmit();
    }, 500),
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if (this.form.parentId === undefined) {
            this.form.parentId = -1;
          }

          if (this.form.id) {
            updateDaohangData(this.form).then((data) => {
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          } else {
            addDaohangData(this.form).then((data) => {
              this.$message.success("添加成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          }
          this.$root.$emit("updateDaohangDataFn");
        }
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getDaohangTree().then((response) => {
        this.deptOptions = [];
        const dept = { id: -1, name: "根", children: response.data.data };
        this.deptOptions.push(dept);
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
