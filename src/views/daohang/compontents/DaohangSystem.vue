<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog
    :title="!form.id ? '新增' : '修改'"
    :visible.sync="visible"
    :before-close="handleCloseDialog"
  >
    <div class="query-content">
      <div class="qc-left">
        <span class="qi-label">系统接入类型</span>
        <el-select
          v-model="queryForm.systemType"
          placeholder="请选择系统接入类型"
          class="qcc-input"
          clearable
        >
          <el-option label="菜单级" :value="1"></el-option>
          <el-option label="应用级" :value="0"></el-option>
        </el-select>
      </div>
      <div class="qc-left qc-center">
        <span class="qi-label">系统名称</span>
        <el-input
          class="qcc-input"
          v-model="queryForm.systemName"
          placeholder="请输入系统名称"
          clearable
        ></el-input>
      </div>
      <div class="qc-right">
        <el-button>重置</el-button>
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </div>
    </div>
    <el-table
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <!-- <el-table-column label="日期" width="120">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column> -->
      <el-table-column prop="systemName" label="系统名称" width="120">
      </el-table-column>
      <el-table-column prop="systemType" label="接入类型" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.systemType == 0"> 应用级 </span>
          <span v-else-if="scope.row.systemType == 1"> 菜单级 </span>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-content">
      <el-pagination
        v-if="pageObj.total > 0"
        background
        style="position: relative"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageObj.pageCurrent"
        :page-sizes="[10]"
        :page-size="pageObj.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageObj.total"
      >
      </el-pagination>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="debounceDataFormSubmitFn"
        >确 定</el-button
      >
      <el-button @click="handleCloseDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import debounce from "@/util/debounce";
import {
  getDaohangTree,
  getDaohangSystemList,
  getDaohangSystemPage,
  addDaohangSystemData,
} from "@/api/daohang/index";
export default {
  name: "DeptForm",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项

      // 是否显示弹出层
      visible: true,
      tableData: [],
      pageObj: {
        current: 1,
        size: 10,
        total: 0,
      },
      form: {
        systemType: 1,
      },
      queryForm: {
        systemName: "",
        systemType: 1,
      },
      chooseSelection: [],
    };
  },
  props: {
    chooseObj: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  created() {
    // this.getAllProviceFn();
  },
  mounted() {},
  methods: {
    handleCloseDialog() {
      this.visible = false;
      this.$emit("handleCloseDialog");
    },
    handleSelectionChange(val) {
      this.chooseSelection = val;
      console.log("val====", val);
    },
    handleGetDaohangList() {
      // console.log("handleGetDaohangList");
      let params = {
        // navigationId: navigationId,
        current: this.pageObj.current,
        size: this.pageObj.size,
      };
      params.systemType = this.queryForm.systemType;
      params.systemName = this.queryForm.systemName;
      console.log("params==", params);
      getDaohangSystemPage(params).then((res) => {
        console.log("res===", res);
        if (res.data.code == 200) {
          this.tableData = res.data.data.records || [];
          this.pageObj.total = res.data.data.total;
        }
      });
    },
    handleQuery() {
      this.handleGetDaohangList();
    },

    debounceDataFormSubmitFn: debounce(function () {
      this.dataFormSubmit();
    }, 500),
    // 表单提交
    dataFormSubmit() {
      console.log("dataFormSubmit==");
      if (this.chooseSelection && this.chooseSelection.length > 0) {
        let params = {
          navigationId: this.chooseObj.id,
          sysVoList: this.chooseSelection,
        };
        addDaohangSystemData(params).then((res) => {
          console.log("res===", res);
          if (res.data.code == 200) {
            this.$message({
              showClose: true,
              message: "添加成功",
              type: "success",
            });
            this.handleCloseDialog();
          }
        });
      }

      // this.$refs["dataForm"].validate((valid) => {
      //   if (valid) {
      //     // if (this.form.parentId === undefined) {
      //     //   this.form.parentId = 0;
      //     // }
      //     // if (this.form.id) {
      //     //   updateDaohangData(this.form).then((data) => {
      //     //     this.$message.success("修改成功");
      //     //     this.visible = false;
      //     //     this.$emit("refreshDataList");
      //     //   });
      //     // } else {
      //     //   addDaohangData(this.form).then((data) => {
      //     //     this.$message.success("添加成功");
      //     //     this.visible = false;
      //     //     this.$emit("refreshDataList");
      //     //   });
      //     // }
      //   }
      // });
    },

    handleSizeChange(val) {
      console.log("handleSizeChange==", val);
      this.pageObj.size = val;
      this.handleGetDaohangList();
    },
    handleCurrentChange(val) {
      console.log("handleCurrentChange==", val);
      this.pageObj.current = val;
      this.handleGetDaohangList();
    },
    /** 查询菜单下拉树结构 */
    // getTreeselect() {
    //   getDaohangTree().then((response) => {
    //     this.deptOptions = [];
    //     const dept = { id: -1, name: "根", children: response.data.data };
    //     this.deptOptions.push(dept);
    //   });
    // },
  },
};
</script>
<style lang="scss" scoped>
.page-content {
  margin-top: 30px;
  text-align: right;
}
.query-content {
  display: flex;
  align-items: center;
  .qc-left {
    width: 300px;
  }
  .qc-center {
    margin-left: 16px;
  }
  .qcc-input {
    width: 200px;
  }
  .qi-label {
    color: #333;
    margin-right: 10px;
  }
}
</style>
