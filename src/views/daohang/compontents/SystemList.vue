<template>
  <div class="table-container">
    <el-table
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <!-- <el-table-column label="日期" width="120">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column> -->
      <el-table-column prop="systemName" label="系统名称"> </el-table-column>
      <el-table-column prop="systemType" label="接入类型" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.systemType == 0"> 应用级 </span>
          <span v-else-if="scope.row.systemType == 1"> 菜单级 </span>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-content">
      <el-pagination
        v-if="pageObj.total > 0"
        background
        style="position: relative"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageObj.pageCurrent"
        :page-sizes="[10]"
        :page-size="pageObj.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageObj.total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { getDaohangSystemList } from "@/api/daohang/index";
export default {
  data() {
    return {
      chooseSelection: [],
      tableData: [],
      pageObj: {
        current: 1,
        size: 10,
        total: 0,
      },
      navigationId: "",
    };
  },
  methods: {
    handleGetDaohangList(navigationId) {
      this.navigationId = navigationId;
      let params = {
        navigationId: navigationId,
        current: this.pageObj.current,
        size: this.pageObj.size,
      };
      console.log("params==", params);
      getDaohangSystemList(params).then((res) => {
        console.log("res=1122==", res);
        if (res.data.code == 200) {
          this.tableData = res.data.data.records || [];
          this.pageObj.total = res.data.data.total;
        }
      });
    },
    handleSelectionChange(val) {
      this.chooseSelection = val;
      console.log("val====", val);
    },
    handleSizeChange(val) {
      console.log("handleSizeChange==", val);
      this.pageObj.size = val;
      this.handleGetDaohangList(this.navigationId);
    },
    handleCurrentChange(val) {
      console.log("handleCurrentChange==", val);
      this.pageObj.current = val;
      this.handleGetDaohangList(this.navigationId);
    },
  },
};
</script>
<style lang="scss" scoped>
.table-container {
  position: relative;
  .page-content {
    margin-top: 30px;
    text-align: right;
  }
}
</style>