<template>
  <div class="node-container">
    <div class="nc-content">
      {{ nodeData.name }}
      <ul v-if="hasChildren" class="children">
        <li v-for="child in children" :key="child.id">
          <tree-node :node-data="child"></tree-node>
        </li>
      </ul>
    </div>
  </div>
</template>
  <script>
// import TreeNode from "./TreeNode.vue";
export default {
  name: "TreeNode",
  props: ["nodeData"],
  //   components: { TreeNode },
  computed: {
    hasChildren() {
      return (
        this.nodeData &&
        Array.isArray(this.nodeData.children) &&
        this.nodeData.children.length > 0
      );
    },
    children() {
      return this.nodeData ? this.nodeData.children : [];
    },
  },
};
</script>
<style lang="scss" scoped>
.node-container {
  .nc-content {
    ul {
      list-style: none;
    }
  }
}
</style>