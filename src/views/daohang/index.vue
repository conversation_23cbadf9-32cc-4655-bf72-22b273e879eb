<template>
  <div class="dh-container">
    <div class="dd-content">
      <div class="dept-left">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          "
        >
          <span style="font-size: 16px; font-weight: 500">工作台导航列表</span>
          <img
            src="@/assets/image/appGroup/addAppGroup.png"
            style="width: 18px; width: 18px; cursor: pointer"
            alt=""
            @click="addOneLevelDaohang"
          />
        </div>

        <el-input class="ns-input" placeholder="搜索" v-model="nameSearch">
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <!-- treeDeptData   :default-expanded-keys="groupExpandedKeys"  :render-content="renderContent"  -->
        <el-tree
          class="filter-tree"
          :data="treeDeptData"
          :props="defaultProps"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleCheckChange"
          :filter-node-method="handleDeptfilterNode"
          ref="deptTreeRef"
        >
          <!-- <span
            class="custom-tree-node label-content"
            slot-scope="{ node, data }"
            style="width: 100%"
            @mouseenter="mouseenter(data)"
            @mouseleave="mouseleave(data)"
          >
            <span>{{ node.label }}</span>
            <span>
              <span
                v-show="data.show"
                style="margin-left: 5px"
                type="primary"
                class="el-icon-plus"
                @click.stop="handleMouseAdd(data)"
              ></span>

              <span
                v-show="data.show"
                size="mini"
                style="
                  margin-left: 5px;
                  display: inline-block;
                  width: 10px;
                  height: 10px;
                "
                type="primary"
                class="el-icon-delete"
              >
              </span>
            </span>
          </span> -->
        </el-tree>
      </div>
      <div class="de-right">
        <div class="dr-content" v-if="treeDeptData && treeDeptData.length > 0">
          <div class="ros-main">
            <div class="title-content">
              <span class="t-line"></span>
              <span>工作台导航维护</span>
            </div>
            <div class="btn-content">
              <el-button v-if="editFlag" @click="handleEdit">编辑</el-button>
              <el-button v-else @click="handleResetForm">重置</el-button>
              <el-button
                v-if="editFlag && this.form.id"
                @click="handleDel"
                type="danger"
                >删除</el-button
              >
              <el-button
                v-else
                type="primary"
                @click="debounceAddOrUpdateDaohangFn"
                >保存</el-button
              >
            </div>
          </div>

          <el-form
            ref="form"
            :model="form"
            label-width="80px"
            :disabled="editFlag"
            :rules="rules"
          >
            <el-row>
              <el-col :span="16">
                <el-form-item label="上级导航" prop="parentId">
                  <treeselect
                    v-model="form.parentId"
                    :options="deptOptions"
                    :normalizer="normalizer"
                    :show-count="true"
                    placeholder="选择上级导航"
                    :disabled="editFlag"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="form-row">
              <el-form-item label="导航名称" prop="name">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
              <el-form-item label="排序">
                <el-input-number
                  v-model="form.sortOrder"
                  controls-position="right"
                  :min="0"
                ></el-input-number>
              </el-form-item>
            </div>

            <el-form-item label="说明">
              <el-input
                type="textarea"
                v-model="form.remark"
                style="width: 60%"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="dr-content" v-else>
          <div class="ros-main">
            <div class="title-content">
              <span class="t-line"></span>
              <span>工作台导航维护</span>
            </div>
          </div>
          <div class="no-content">
            <div class="no-data"></div>
            暂无数据
          </div>
        </div>
        <div class="dr-system">
          <div class="sys-main">
            <div class="title-content">
              <span class="t-line"></span>
              <span>关联系统</span>
            </div>
            <div class="ds-content">
              <el-button type="primary" @click="hangdleAddSystem"
                >新增</el-button
              >
              <el-button type="danger" @click="handleDelSystem">删除</el-button>
            </div>
          </div>

          <system-list ref="sysListRef" class="sys-content"></system-list>
        </div>
      </div>
    </div>

    <daohang-form
      v-if="dhFormVisible"
      ref="daohangFormRef"
      @refreshDataList="hangdleGetTreeDeptFn"
      @handleCloseDiaolog="closeDaohangDialog"
    >
    </daohang-form>
    <daohang-system
      v-if="dhSystemFlag"
      @handleCloseDialog="handleCloseSystem"
      ref="daohangSystemRef"
      :chooseObj="chooseObj"
    ></daohang-system>
  </div>
</template>
<script>
// import TreeNode from "./compontents/TreeNode.vue";

import {
  addDaohangData,
  getDaohangTree,
  updateDaohangData,
  delDaohangData,
  getDaohangSystemList,
  delBatchDaohangData,
} from "@/api/daohang/index";
import debounce from "@/util/debounce";
import DaohangForm from "./compontents/DaohangForm.vue";
import SystemList from "./compontents/SystemList.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import DaohangSystem from "./compontents/DaohangSystem.vue";
export default {
  components: {
    DaohangForm,
    SystemList,
    DaohangSystem,
    Treeselect,
    // TreeNode,
  },
  data() {
    return {
      dhFormVisible: false,
      nameSearch: "",
      form: {
        parentId: -1,
        // parentId: undefined,
      },
      chooseObj: {},
      treeDeptData: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      editFlag: true,
      dhSystemFlag: false,
      currentDhId: "",
      deptOptions: [],
      rules: {
        name: [{ required: true, message: "请输入导航名称", trigger: "blur" }],
        parentId: [
          { required: false, message: "请选择上级导航", trigger: "change" },
        ],
      },
      //   data: [
      //     {
      //       id: 0,
      //       name: "水果",
      //       show: false,
      //       children: [
      //         {
      //           id: 1,
      //           name: "苹果",
      //           show: false,
      //         },
      //         {
      //           id: 2,
      //           name: "芒果",
      //           show: false,
      //         },
      //       ],
      //     },
      //   ],
      //   nodeData: {
      //     name: "菜单1",
      //     id: "cd001",
      //     children: [
      //       {
      //         name: "菜单1-1",
      //         id: "cd00101",
      //         children: [
      //           {
      //             name: "菜单1-1-1",
      //             id: "cd0010101",
      //           },
      //         ],
      //       },
      //     ],
      //   },
    };
  },
  watch: {
    nameSearch(val) {
      this.$refs.deptTreeRef.filter(val);
    },
  },
  created() {
    this.handleGetDaohangTreeFn();
  },
  methods: {
    handleMouseAdd(data) {
      console.log("add===", data);
    },
    /** 查询菜单下拉树结构 */
    // getTreeselect() {
    //   getDaohangTree().then((response) => {
    //     this.deptOptions = [];
    //     const dept = { id: -1, name: "根", children: response.data.data };
    //     this.deptOptions.push(dept);
    //   });
    // },

    //chooseSelection
    handleDelSystem() {
      //sysListRef
      // console.log("this.9999---===", this.$refs.sysListRef);
      let chooseSelection = this.$refs.sysListRef.chooseSelection;
      if (chooseSelection && chooseSelection.length > 0) {
        let idList = [];
        chooseSelection.forEach((item) => {
          idList.push(item.id);
        });
        let params = {
          idList: idList,
        };

        let that = this;
        this.$confirm("是否确认批量删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return delBatchDaohangData(params);
          })
          .then((res) => {
            // console.log("data==", res);
            if (res.data.code == 200) {
              this.$message.success("删除成功");
              this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
            } else {
              this.$message.error("删除失败");
            }
          });

        // delBatchDaohangData(params).then((res) => {
        //   console.log("res----", res);
        //   if (res.data.code == 200) {
        //     this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
        //   }
        // });
      }
    },
    // renderContent(h, { node, data, store }) {
    //   if (data.level == "root") {
    //     return (
    //       <span>
    //         <el-tooltip
    //           class="item"
    //           effect="dark"
    //           content={node.label}
    //           placement="top"
    //         >
    //           <span>
    //             <i class="el-icon-s-home"></i> {node.label}
    //           </span>
    //         </el-tooltip>
    //       </span>
    //     );
    //   } else {
    //     return (
    //       <span>
    //         <el-tooltip
    //           class="item"
    //           effect="dark"
    //           content={node.label}
    //           placement="top"
    //         >
    //           <span>
    //             <i class="el-icon-document"></i> {node.label}
    //           </span>
    //         </el-tooltip>
    //       </span>
    //     );
    //   }
    // },
    renderContent(h, { node, data, store }) {
      // on-mouseover={() => this.clickMenu(data)}
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
          <span>
            <el-button
              size="mini"
              type="text"
              on-click={() => this.clickMenu(data)}
            >
              Append
            </el-button>
            {/* <div>固定123456</div> */}
          </span>
        </span>
      );
    },
    mouseenter(data) {
      //   console.log(data);
      this.$set(data, "show", true);
      //   this.$set(data, "show", true);
      console.log("data.show==", data.show);
    },
    hangdleAddSystem() {
      console.log("this.chooseObj==", this.chooseObj);
      if (!this.chooseObj.id) {
        this.$message({
          message: "请先选择左侧导航",
          type: "warning",
        });
        return;
      }

      this.dhSystemFlag = true;

      this.$nextTick(() => {
        this.$refs.daohangSystemRef.handleGetDaohangList();
      });
    },
    handleCloseSystem() {
      this.dhSystemFlag = false;
      this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
    },
    mouseleave(data) {
      //   console.log(data);

      this.$set(data, "show", false);
    },
    clickMenu(data) {
      console.log("menu--", data);
    },
    handleEdit() {
      if (!this.form.id) {
        this.$message({
          message: "请先选择左侧导航",
          type: "warning",
        });
        return;
      }
      this.editFlag = false;
    },
    handleResetForm() {
      this.form.sortOrder = undefined;
      this.form.name = "";
      this.form.remark = "";
    },
    handleDel() {
      if (!this.form.id) {
        return;
      }
      let that = this;
      this.$confirm("是否确认删除:" + this.form.name, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delDaohangData(that.form.id);
        })
        .then((res) => {
          console.log("data==", res);
          if (res.status && res.data.code == 200) {
            this.$message.success("删除成功");
            this.handleGetDaohangTreeFn();
            this.$root.$emit("updateDaohangDataFn");
            this.form = {};
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    addOneLevelDaohang() {
      // this.form.parentId = -1;
      // this.form.name = "";
      // this.form.sortOrder = "";
      // this.form.remark = "";
      this.dhFormVisible = true;
      this.$nextTick(() => {
        this.$refs.daohangFormRef.init(true, this.form.id);
      });
      //init
    },
    handleDeptfilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    hangdleGetTreeDeptFn() {
      this.dhFormVisible = false;
      this.handleGetDaohangTreeFn();
    },
    closeDaohangDialog() {
      this.dhFormVisible = false;
    },
    handleGetDaohangTreeFn() {
      getDaohangTree().then((res) => {
        // console.log("获取导航树", res);
        if (res.data.code == 200) {
          let treeDeptData = res.data.data;
          treeDeptData.forEach((item) => {
            item.show = false;
          });
          this.treeDeptData = treeDeptData;
          this.deptOptions = [];
          const dept = { id: -1, name: "根", children: res.data.data };
          this.deptOptions.push(dept);
        }
      });
    },
    debounceAddOrUpdateDaohangFn: debounce(function () {
      this.addOrUpdateDaohang();
    }, 500),
    addOrUpdateDaohang() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.parentId === undefined) {
            this.form.parentId = -1;
          }
          if (this.form.id) {
            updateDaohangData(this.form).then((res) => {
              // console.log("修改导航的数据==", res);
              if (res.data.code == 200) {
                this.$message({
                  showClose: true,
                  message: "修改成功",
                  type: "success",
                });
                this.form = {};
                this.handleGetDaohangTreeFn();
              }
            });
          } else {
            addDaohangData(this.form).then((res) => {
              // console.log("新增导航的数据==", res);
              if (res.data.code == 200) {
                this.$message({
                  showClose: true,
                  message: "新增成功",
                  type: "success",
                });
                this.form = {};
                this.handleGetDaohangTreeFn();
              }
            });
          }
          this.$root.$emit("updateDaohangDataFn");
        }
      });
    },
    handleCheckChange(data) {
      // console.log("data===", data);
      this.currentDhId = data.id;
      //   this.getList(this.page);
      this.editFlag = true;
      this.form = Object.assign({}, data);
      this.chooseObj = data;
      this.$refs.sysListRef.handleGetDaohangList(data.id);
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.dh-container {
  //   background: #f0f2f5;
  position: relative;
  min-height: calc(100vh - 240px);
  .dd-content {
    margin-left: 20px;
    margin-top: 20px;
    display: flex;

    .dept-left {
      width: 240px;
      // background: #f0f2f5;
      // border: 1px solid #dcdfe6;
      padding: 4px;
      min-height: calc(100vh - 200px);
      background: #fff;
      border-radius: 4px;

      .ns-input {
        margin-bottom: 10px;
      }
    }
    .de-right {
      flex: 1;

      margin-left: 20px;
      .dr-content {
        // border: 1px solid #dcdfe6;
        width: calc(100% - 10px);
        background: #fff;
        border-radius: 4px;
        padding: 10px;

        .ros-main {
          display: flex;
          margin: 20px 0;
          align-items: center;
          justify-content: space-between;
          // padding: 0 24px;
        }
      }
      .dr-system {
        position: relative;
        margin-top: 36px;
        padding: 10px;
        width: calc(100% - 10px);
        background: #fff;
        .ds-content {
          text-align: right;
        }
        .sys-main {
          display: flex;
          justify-content: space-between;
        }
      }
      .title-content {
        display: flex;
        align-items: center;
        .t-line {
          height: 12px;
          width: 3px;
          // background: #002fa7;
          background: #327fff;
          display: inline-block;
          margin-right: 6px;
        }
      }

      .sys-content {
        // width: 60%;
      }
    }
  }
  .form-row {
    display: flex;
  }
  .label-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 0;
  }

  .no-content {
    text-align: center;
  }
  .no-data {
    background: url("../../assets/image/no-data.png") no-repeat;
    background-size: 100%;
    width: 197px;
    height: 132px;
    margin: 0 auto;
  }
}
::v-deep .vue-treeselect--disabled .vue-treeselect__control {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
</style>