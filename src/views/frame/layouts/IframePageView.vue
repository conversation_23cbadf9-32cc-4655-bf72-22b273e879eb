<template>
  <div v-loading="loading" :style="'height:' + height">
    <iframe
      :id="id"
      :src="url"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto"
    />
  </div>
</template>
<script>
import store from "@/store";

export default {
  data() {
    return {
      height: document.documentElement.clientHeight - 94.5 + "px;",
      loading: true,
      id: "",
      url: ""
    };
  },
  mounted: function () {
    setTimeout(() => {
      this.loading = false;
    }, 300);
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 94.5 + "px;";
    };
  },
  created() {
    this.getUrl()
  },
  updated() {
    this.getUrl()
  },
  watch: {
    $route(to, from) {
      this.getUrl();
    }
  },
  methods: {
    getUrl() {
      let params = this.$route.query;
      console.log("iframe参数", JSON.stringify(params))
      if (params && params.path) {
        let path = params.path;
        // 是否需要token：0-需要，1-不需要
        this.url = params.neadToken === 'yes' ? path = path + "?token="+ store.getters.access_token : path;
        console.log("------url------" + this.url)
        this.id = this.$route.path
      }
    }
  }
};
</script>
