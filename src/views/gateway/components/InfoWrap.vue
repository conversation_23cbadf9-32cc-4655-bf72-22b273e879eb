<template>
  <div class="content">
    <div
      v-for="(item, index) in newsList"
      @click="handleClick(item)"
      :key="index"
      class="news"
    >
      <div class="left">
        <!-- <el-tooltip
          class="item"
          effect="dark"
          :content="item.title"
          placement="top-start"
        >
          <div class="wrap">{{ item.title }}</div>
        </el-tooltip> -->
        <div class="wrap" :title="item.title">{{ item.title }}</div>
      </div>
      <div class="right" :title="item.createTime">
        {{ filterTime(item.createTime) }}
      </div>
    </div>
    <el-empty
      v-if="newsList.length == 0"
      description="暂无数据"
      :image-size="140"
    ></el-empty>
  </div>
</template>
<script>
import dayjs from "dayjs";
export default {
  props: {
    newsList: {
      type: Array,
      default: [],
    },
    type: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  methods: {
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
      this.$router.push(key);
      this.activeIndex = key;
    },
    filterTime(value) {
      return dayjs(value).format("YYYY-MM-DD");
    },
    handleClick(item) {
      if (this.type == 1) {
        this.$router.push("/gateway/publicity/detail?id=" + item.id);
      } else {
        this.$router.push("/gateway/news/detail?id=" + item.id);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 90%;
  overflow-y: scroll;
  .news {
    line-height: 27px;
    width: 100%;
    margin: 10px 0;
    display: flex;
    justify-content: space-around;
    cursor: pointer;
    .left {
      width: calc(100% - 110px);

      .wrap {
        width: 100%;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 超过容器大小的部分将被裁切 */
        text-overflow: ellipsis;
        cursor: pointer;
      }
    }
    .right {
      width: 150px;
      text-align: right;
      cursor: pointer;
    }
  }
}
</style>
