<template>
  <div>
    <highSearch ref="highSearch" @searchFn="searchFn" />

    <el-card class="content-wrapper">
      <el-empty :image-size="200" v-if="dataArr.length == 0" />
      <ul class="list-wrap"  v-else>
        <li
          class="list-item"
          v-for="(item, index) in dataArr"
          :key="index"
          @click="getDetail(item)"
        >
          <img
            class="item-img"
            :src="item.pic | filterImgByProcess"
            alt="图片"
          />
          <div class="item-cont">
            <p class="title">{{ item.title }}</p>
            <div class="i_c_t">{{ item.memo }}</div>
            <div class="ft">
              <div>
                <span class="source">{{ item.source }}</span>
                <span>{{ item.createTime }}</span>
              </div>
              <div>
                <span style="margin-right: 4px;">阅读</span>
                {{ item.viewNum }}
              </div>
            </div>
          </div>
        </li>
      </ul>

      <el-pagination
        background
        @current-change="handleCurrentChange"
        :current-page.sync="queryParams.current"
        :page-size="queryParams.size"
        layout="total, prev, pager, next, jumper"
        :total="total"
        style="text-align: right; margin-top: 10px;"
      />
    </el-card>
  </div>
</template>

<script>
import highSearch from "@/components/highSearch";
import { seniorSearch } from "@/api/admin/sys/system";

export default {
  components: {
    highSearch,
  },
  data() {
    return {
      total: 0,
      queryParams: {
        current: 1, 
        size: 10,
      },
      searchParams: {},
      dataArr: []
    };
  },
  computed: {
    curAreaCode() {
      return this.$store.getters.curAreaCode || "410000000000"
    }
  },
  watch: {
    curAreaCode: {
      handler() {
        this.queryParams.current = 1;
        this.getList();
      },
      deep: true,
    }
  },
  methods: {
    getDetail(item) {
      let url = item.articleType == 0 ? "/gateway/news/detail" : "/gateway/publicity/detail";
      this.$router.push({
        path: url,
        query: {
          id: item.id,
        },
      });
    },
    searchFn(data = {}) {
      this.queryParams.current = 1;
     
      this.searchParams = {
        ...data,
        startTime: data.valueDate && data.valueDate.length && data.valueDate[0],
        endTime: data.valueDate && data.valueDate.length && data.valueDate[1],
      };
      this.getList();
    },
    handleCurrentChange(val) {
      this.searchParams.current = val;
      this.getList();
    },
    async getList() {
      let params = {
        ...this.queryParams,
        ...this.searchParams,
        areaCode: this.curAreaCode,
      };
      delete params.valueDate

      const { data } = await seniorSearch(params);
      this.dataArr = data.data.records || [];
      this.total = data.data.total || 0;
    },
  },
}
</script>
<style lang='scss' scoped>
.content-wrapper {
  width: 1320px;
  margin: 40px auto 20px;
  .list-wrap {
    padding: 0;
    .list-item {
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      margin-bottom: 20px;
      .item-img {
        width: 173px;
        height: 120px;
      }
      .item-cont {
        width: calc(100% - 188px);
        margin-left: 15px;
        .title {
          font-size: 16px;
          font-weight: 700;
          color: #333333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin: 0;
        }
        .i_c_t {
          font-size: 14px;
          color: #666666;
          margin: 12px 0;
          height: 58px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }
      .ft {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #999999;
        .source {
          display: inline-block;
          max-width: 300px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: bottom;
          margin-right: 12px;
        }
      }
    }
  }
}
</style>