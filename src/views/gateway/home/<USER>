<template>
  <div class="mainWrap">
    <highSearch ref="highSearch" :isHome="true" />
    <div class="block" v-if="newsBannerList.length || newsData.length">
      <!-- <el-carousel
        height="270px"
        :loop="true"
        indicator-position="none"
        arrow="never"
      >
        <el-carousel-item v-for="(item, index) in bannerList" :key="index">
          <el-image
            class="img"
            style="width: 100%; height: 100%"
            :src="item.src"
          ></el-image>
        </el-carousel-item>
      </el-carousel> -->

      <div class="contentWrap">
        <div class="importNews">
          <div class="leftWrap">
            <el-carousel
              class="carousel"
              :loop="true"
              :autoplay="true"
              indicator-position="outside"
              arrow="never"
              height="340px"
            >
              <el-carousel-item
                v-for="(item, index) in newsBannerList"
                :key="index"
              >
                <div class="bannerWrap" @click="handleToDetail(item)">
                  <el-image
                    class="img"
                    fit="cover"
                    style="width: 100%; height: 100%"
                    :src="item.pic ? $getUrlByProcess(item.pic) : ''"
                  ></el-image>
                  <div class="titleWrap">{{ item.title }}</div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div class="text" v-for="(v, i) in newsData.slice(0, 1)">
              <div class="titleWrap">
                <div class="titleLeft">
                  <el-tabs
                    @tab-click="(value) => handleClick(value, 0)"
                    v-model="v.activeName"
                  >
                    <el-tab-pane
                      v-for="(item, index) in v.titleList"
                      :key="index"
                      :label="item.name"
                      :name="item.code"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="titleRight">
                  <el-button
                    type="text"
                    @click="handleMore(v, '/gateway/news')"
                    class="btn"
                    >查看更多 ></el-button
                  >
                </div>
              </div>
              <InfoWrap :newsList="v.newsList"></InfoWrap>
            </div>
          </div>
          <!-- <Login class="rightWrap2"></Login> -->
        </div>
        <Special class="special" :refresh="refresh"></Special>
      </div>
      <div class="footWrap">
        <div class="wrap">
          <div class="column">
            <div
              class="leftWrap"
              v-for="(item, i) in newsData.slice(1)"
              :key="i"
            >
              <div class="titleWrap liyubtm">
                <div class="titleLeft">
                  <el-tabs
                    @tab-click="(value) => handleClick(value, i + 1)"
                    v-model="item.activeName"
                  >
                    <el-tab-pane
                      v-for="(item, index) in item.titleList"
                      :key="index"
                      :label="item.name"
                      :name="item.code"
                    ></el-tab-pane>
                  </el-tabs>
                </div>
                <div class="titleRight lygsgg">
                  <el-button
                    type="text"
                    @click="handleMore(item, i, '/gateway/publicity')"
                    class="btn"
                    >查看更多 ></el-button
                  >
                </div>
              </div>
              <InfoWrap :newsList="item.newsList" :type="1"></InfoWrap>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" :image-size="190"></el-empty>
  </div>
</template>
<script>
import InfoWrap from "../components/InfoWrap";
import Special from "./components/Special";
import Login from "./components/Login";
import highSearch from "@/components/highSearch";
import { categoryPage } from "@/api/gateway";
import { getBannerList, getNoLoginBannerList } from "@/api/admin/banner";
import { getPublishCategorys } from "@/api/message/publish";
import { getStore, removeStore, setStore } from "@/util/store";

export default {
  components: {
    InfoWrap,
    Special,
    Login,
    highSearch,
  },
  data() {
    return {
      refresh: 0,
      bannerList: [
        {
          src: require("@/assets/banner.png"),
        },
      ],
      newsData: [],
      newsBannerList: [],
      newsInfo: {},
    };
  },
  computed: {
    curAreaCode() {
      return this.$store.getters.curAreaCode
        ? this.$store.getters.curAreaCode
        : "410000000000";
    },
  },
  watch: {
    curAreaCode: {
      handler(val, oldVal) {
        console.log("watch---curAreaCode", val, oldVal);
        // if (
        //   this.$store.getters.access_token &&
        //   this.$store.getters.dept.areaCode
        // ) {
        //   this.$store.commit(
        //     "SET_CUR_AREA_CODE",
        //     this.$store.getters.dept.areaCode
        //   );
        // }
        setStore({
          name: "curAreaCode",
          content: val,
          // type: 'session'
        });

        this.getCategorys();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},

  methods: {
    arrToObj(arr) {
      let obj = {};
      arr.forEach((item) => {
        obj[item.value] = item.label;
      });
      return obj;
    },
    async getCategorys() {
      try {
        let res = await getPublishCategorys({ areaCode: this.curAreaCode });
        let datas = res.data.data.map((v) => ({
          name: v.name,
          activeName: v.children[0]?.code,
          code: v.code,
          titleList: v.children,
          newsList: [],
        }));

        this.newsData = datas;
        this.newsData.forEach((item, i) => {
          this.getData({ code: item.activeName }, i);
        });
        console.log("newsData", this.newsData);
        this.refresh = new Date().getTime();
        this.getBanner();
      } catch (error) {}
    },
    init() {
      // let arr = Object.keys(this.newsInfo);
      // this.newsData=[]
      // this.getCategorys()
    },
    handleToDetail(item) {
      if (item.type == 0) {
        this.$router.push("/gateway/news/detail?id=" + item.articleId);
      } else {
        if (item.webUrl) {
          var newOpen = window.open();
          newOpen.opener = null;
          newOpen.location = item.webUrl;
        }
      }
      // this.$router.push("/gateway/news/detail?id=" + item.id);
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
    },
    async getBanner() {
      // let res = await getBannerList();
      let res = await getNoLoginBannerList({ areaCode: this.curAreaCode });
      this.newsBannerList = res.data.data.records;
      // this.newsBannerList = this.newsInfo.one.newsList.filter((item) => {
      //   return item.pic;
      // });
      // console.log(this.newsBannerList, 26222);
    },
    handleClick(value, key) {
      this.getData(
        {
          code: value.name,
        },
        key
      );
    },
    async getData(data, value) {
      let res = await categoryPage({
        ...data,
        areaCode: this.curAreaCode,
        current: 1,
        size: 8,
      });
      this.newsData[value].newsList = res.data.data.records;
      if (value === 0) {
        this.getBanner();
      }
    },
    handleMore(value, i, path) {
      this.$router.push(
        `${path}?typely=${value.name}&number=${i + 1}`
        // `/gateway/news?type=${value.code}&activeName=${value.activeName}`
        // '/gateway/publicity'
      );
    },
    // handleMore2(value) {
    //   console.log('噢噢噢噢11111我问问',value)
    //   this.$router.push(
    //     `/gateway/news?type=${value.code}`
    //   );
    // },
  },
};
</script>
<style lang="scss" scoped>
.mainWrap {
  box-sizing: border-box;
  margin-top: 60px;

  .block {
    .contentWrap {
      width: 1320px;
      margin: 0 auto;

      .importNews {
        width: 100%;
        margin-top: 40px;

        display: flex;
        height: 384px;
        overflow: hidden;
        justify-content: space-between;

        .leftWrap {
          // width: 900px;
          width: 100%;
          padding: 20px;
          box-sizing: border-box;
          // margin-right: 20px;
          background: #ffffff;
          display: flex;
          border-radius: 4px 4px 4px 4px;
          justify-content: space-between;

          .carousel {
            // width: 380px;
            width: calc(50% - 20px);

            .bannerWrap {
              width: 100%;
              height: 100%;
              position: relative;
              cursor: pointer;

              .img {
                border-radius: 4px;
              }

              .titleWrap {
                position: absolute;
                width: 100%;
                // background: red;

                background: rgba(0, 6, 40, 0.35);
                color: #fff;
                z-index: 1000;
                white-space: nowrap;
                text-overflow: ellipsis;
                bottom: 0;
                padding: 5px;
                box-sizing: border-box;
              }
            }
          }

          .text {
            // width: calc(100% - 400px);
            width: 50%;

            .titleWrap {
              display: flex;
              justify-content: space-between;
              border-bottom: 1px solid #e4e7ed;

              .titleLeft {
                display: flex;

                .title {
                  font-size: 22px;
                  font-weight: bold;
                  margin-right: 20px;
                }
              }
            }
          }
        }

        .rightWrap {
          padding: 20px;
          box-sizing: border-box;
          width: calc(100% - 900px);
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          height: 386px;
        }

        .rightWrap2 {
          padding: 10px 20px;
          box-sizing: border-box;
          width: calc(100% - 900px);
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          height: 386px;
        }
      }

      .special {
        width: 100%;
        height: 324px;
        background: #ffffff;
        margin-top: 20px;
        border-radius: 4px 4px 4px 4px;
        padding: 20px 20px;
        box-sizing: border-box;
      }
    }

    .footWrap {
      width: 100%;
      // background: #ffffff;
      padding: 20px 0 0;

      // margin-top: 40px;
      .wrap {
        width: 1320px;
        margin: 0 auto;

        .column {
          width: 100%;
          display: flex;
          justify-content: space-between;
          padding-bottom: 20px;

          .leftWrap {
            padding: 20px;
            box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.6);
            border-radius: 4px 4px 4px 4px;
            box-sizing: border-box;
            width: 650px;
            background: #ffffff;
            height: 380px;

            .titleWrap {
              display: flex;
              justify-content: space-between;
              border-bottom: 1px solid #e4e7ed;

              .titleLeft {
                display: flex;

                .title {
                  font-size: 22px;
                  font-weight: bold;
                  margin-right: 20px;
                }
              }
            }
          }

          .rightWrap {
            box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.6);
            border-radius: 4px 4px 4px 4px;

            .titleWrap {
              display: flex;
              justify-content: space-between;
              border-bottom: 1px solid #e4e7ed;

              .titleLeft {
                display: flex;

                .title {
                  font-size: 22px;
                  font-weight: bold;
                  margin-right: 20px;
                }
              }
            }

            padding: 20px;
            box-sizing: border-box;
            width: 650px;
            background: #ffffff;
            height: 380px;
          }
        }
      }
    }
  }
}

.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

::v-deep .el-tabs__header {
  margin: 0 !important;
}

// ::v-deep .el-carousel__indicators--horizontal {
//   bottom: 6px;
//   width: 100%;
//   display: flex;
//   // flex-direction: row-reverse;
//   // left: 47% !important;
//   margin-left: 85%;
// }
// ::v-deep .el-carousel__button {
//   background-color: white;
//   width: 6px;
//   height: 6px;
//   opacity: 1;
// }
// ::v-deep .el-carousel__indicator.is-active button {
//   background-color: blue;
// }
.carousel {
  ::v-deep .el-carousel__button {
    background-color: white;
    width: 6px;
    height: 6px;
    opacity: 1;
  }

  ::v-deep .el-carousel__indicator.is-active button {
    background-color: blue;
  }

  ::v-deep .el-carousel__indicators--outside {
    position: absolute;
    bottom: 2px;
    right: 20px;
    text-align: right;
  }
}

::v-deep .el-tabs__nav-wrap::after {
  height: 0 !important;
}

::v-deep .el-tabs__active-bar {
  height: 3px;
}

::v-deep .el-tabs__item {
  font-size: 18px;
  font-weight: bold;
}

::v-deep .is-active {
  color: #0357ca;
}

.btn {
  font-weight: bold;
  color: #0357ca !important;
  font-size: 16px;
}

// ::v-deep .el-carousel__arrow {
//   display: none !important;
// }
</style>
