<template>
  <div class="login">
    <div v-if="userInfo && userInfo.userName" class="userInfo">
      <div class="imgWrap m-t-20">
        <img class="img" src="@/assets/image/img/morentouxiang.png" />
      </div>
      <div class="m-t-20 userName">
        {{ userInfo.userName }}
      </div>
      <!-- <div class="subTitle m-t-20">河南省市场监督管理局</div> -->

      <div class="m-t-40">
        <el-button
          size="large"
          type="primary"
          @click="handleGoGongzuotai"
          style="background: #437fff; width: 100%; border: 1px solid #437fff"
          >进入工作台</el-button
        >
      </div>
      <div class="m-t-30">
        <el-button size="large" @click="handleLogout" style="width: 100%"
          >退出登录</el-button
        >
      </div>
    </div>
    <div v-else>
      <div class="ri-tips">
        <div class="dl-title dl-active">账号登录</div>
        <div class="dl-title">手机号登录</div>
        <div class="dl-title">CA登录</div>
        <!-- 账号密码 -->
        <!-- <el-menu
              :default-active="activeIndex"
              class="el-menu-demo"
              mode="horizontal"
              @select="handleSelect"
              text-color="#606266"
              active-text-color="#3272CE"
            >
              <el-menu-item class="item-two" index="1"
                ><span v-if="this.changeKey == '1'" class="text-active"
                  >账号登陆</span
                >
                <span v-else>账号登陆</span></el-menu-item
              >
            </el-menu> -->
      </div>
      <div class="login-line"></div>
      <el-form
        :model="loginForm"
        ref="loginForm"
        :rules="loginRules"
        class="demo-ruleForm"
      >
        <el-form-item class="m-t-10" prop="username">
          <el-input
            size="large"
            placeholder="请输入登录账号"
            v-model="loginForm.username"
          >
            <i slot="prefix" class="el-input__icon el-icon-user-solid"></i>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            size="large"
            placeholder="请输入登录密码"
            show-password
            v-model="loginForm.password"
          >
            <i slot="prefix" class="el-input__icon el-icon-unlock"></i>
          </el-input>
        </el-form-item>
        <el-form-item prop="captcha">
          <div class="codeWrap">
            <div class="inputWrap">
              <el-input
                size="large"
                placeholder="请输入图形验证码"
                v-model="loginForm.captcha"
              >
                <i
                  slot="prefix"
                  class="el-input__icon el-icon-mobile-phone"
                ></i>
              </el-input>
            </div>
            <div @click="getCaptcha()" class="yzm-tp">
              <el-image
                class="img"
                style="width: 100px"
                :src="captchaPath"
              ></el-image>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <div class="change" @click="getCaptcha()">看不清，换一张</div>
        </el-form-item>
        <el-form-item label-width="0">
          <el-button
            class="btn ns-btn"
            type="primary"
            size="large"
            @click="handleLogin('loginForm')"
            >登 录</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getUUID, encryption } from "@/util/util";
import { liLoginByUsername, getUserInfo } from "@/api/login";
import store from "@/store";
export default {
  name: "login",
  data() {
    return {
      loginForm: {
        username: "",
        password,
        uuid: "",
        captcha: "",
      },
      captchaPath: "",
      loginRules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "请输入密码", trigger: "blur" },
        ],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      userInfo: {},
    };
  },
  mounted() {
    this.getCaptcha();
    this.getUserInfoDataFn();
    this.getUser();
    document.addEventListener("visibilitychange", this.visibilityState); //监听页面显隐
  },
  beforeDestroy() {
    document.removeEventListener("visibilitychange", this.visibilityState); //移除监听
  },
  methods: {
    getUserInfoDataFn() {
      getUserInfo().then((res) => {
        // console.log("res=login==获取用户信息==", res);
        if (!res) {
          localStorage.removeItem("userInfo");
          this.userInfo = null;
        }
      });
    },
    visibilityState(e) {
      let visibilityState = e.target.visibilityState;
      switch (visibilityState) {
        case "visible": //显示
          // console.log("显示了---");
          this.getUser();
          break;
        case "hidden": //隐藏
          break;
        default:
      }
    },
    getUser() {
      let userInfo = localStorage.getItem("userInfo");
      // console.log(userInfo, 11777);
      if (userInfo) {
        this.userInfo = JSON.parse(userInfo);
        // console.log(this.userInfo, 11777);
      } else {
        this.userInfo = null;
      }
    },
    // 获取验证码
    getCaptcha() {
      this.loginForm.uuid = getUUID();
      // this.captchaPath =
      //   window.location.protocol +
      //   "//" +
      //   window.location.hostname +
      //   ":" +
      //   window.location.port +
      //   "/api/admin/auth/createCaptcha" +
      //   `?uuid=${this.loginForm.uuid}`;
      this.captchaPath =
        this.$getVerifycodeUrlByProcess("/api/admin/auth/createCaptcha") +
        `?uuid=${this.loginForm.uuid}`;

      console.log("验证码===", this.captchaPath);
    },
    handleLogin() {
      let that = this;
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          const myUserInfo = {
            username: that.loginForm.username,
            password: that.loginForm.password,
          };
          const user = encryption({
            data: myUserInfo,
            key: "user_center_2023",
            param: ["password"],
          });
          const newParams = {
            username: user.username,
            password: user.password,
            captcha: that.loginForm.captcha,
            uuid: that.loginForm.uuid,
          };
          liLoginByUsername(newParams)
            .then((res) => {
              that.$store.dispatch(
                "StoreAccessToken",
                res.data.data.accessToken
              );
              localStorage.setItem("userInfo", JSON.stringify(res.data.data));
              this.userInfo = res.data.data;
              // window.open(window.location.origin + "/#/home/<USER>", "_blank");
              var newOpen = window.open();
          newOpen.opener = null;
          newOpen.location = this.getOriginStr() + "/#/main/home";
            })
            .catch(() => {
              that.getCaptcha();
            });
        }
      });
    },
    getOriginStr() {
      return window.location.origin
    },
    handleLogout() {
      this.$store.dispatch("LogOut").then(() => {
        // this.$router.push({ path: "/" });
        localStorage.clear();
        this.userInfo = {};
        this.$message.success("退出成功！");
      });
      // localStorage.clear();
    },
    handleGoGongzuotai() {
      let userInfo = localStorage.getItem("userInfo");
      if (userInfo) {
        // this.$router.push("/home/<USER>");
        this.$router.push("/main/home");
      } else {
        this.userInfo = null;
        this.$message({
          showClose: true,
          message: "请重新登录！",
          type: "warning",
        });
      }
    },
  },
  computed: {},
};
</script>
<style lang="scss" scoped>
.login {
  height: 100%;
  .btn {
    background: #0357ca;
    width: 100%;
    color: #ffffff !important;
  }
  .codeWrap {
    width: 100%;
    display: flex;
    .yzm-tp {
      width: 100px;
      height: 39px;
    }
    .inputWrap {
      width: calc(100% - 110px);
    }
    .img {
      margin-left: 10px;
      ::v-deep .el-image__inner {
        height: 39px !important;
        border-radius: 4px;
      }
    }
  }
  .change {
    margin-left: 10px;
    color: #0357ca;
    float: right;
    margin-top: -20px;
    cursor: pointer;
  }
  ::v-deep .el-input__inner {
    border: 1px solid #c0c6cc;
  }

  .userInfo {
    display: flex;
    align-items: center;
    flex-direction: column;
    height: 100%;
    .imgWrap {
      .img {
        width: 80px;
        height: 80px;
      }
    }
    .userName {
      color: #000000;
      font-size: 14px;
      font-weight: bold;
    }
    .subTitle {
      color: #999999;
      font-size: 14px;
    }
  }
}
.m-t-20 {
  margin-top: 20px;
}
.m-t-40 {
  margin-top: 40px;
  width: 80%;
}
.m-t-30 {
  margin-top: 20px;
  width: 80%;
}
.ri-tips {
  // font-size: 30px;
  // font-weight: 500;
  // color: #333333;
  // text-shadow: 4px 4px 10px rgba(0,0,0,0.1);
  // // padding-left: 26px;
  // padding-left: 62px;
  // // margin-top: 120px;

  // width: 419px;
  height: 48px;
  line-height: 48px;
  // border-bottom: 1px solid #e2e5ed;
  margin: 10px auto 10px;
  display: flex;
  justify-content: space-between;
}
.login-line {
  height: 2px;
  background: #e2e5ed;
  margin-top: -12px;
}
.dl-title {
  font-size: 20px;
  color: #666666;
  cursor: pointer;
}
.dl-active {
  font-weight: 600;
  color: #0357ca;
  border-bottom: 3px solid #0357ca;
}
.ns-btn {
  margin-top: -12px;
  position: absolute;
}
</style>
