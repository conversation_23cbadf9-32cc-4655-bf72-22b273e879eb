<template>
  <div class="carouselBox" v-if="dataList.length">
    <div class="top">专题专栏</div>
    <!-- <el-carousel :loop="false" :autoplay="false" class="carousel1">
      <el-carousel-item class="el-car-item" v-for="(list, index) in dataList" :key="index">
        <div @click="handleClick(list)">
          <img class="img" :src="$getUrlByProcess(list.backPic)" />
        </div>
      </el-carousel-item>
    </el-carousel> -->
    <divn class="newimg" style="display:flex">
      <img @click="handleClick(dataList[0])" :src="$getUrlByProcess(dataList[0].backPic)" alt="" />
      <img @click="handleClick(dataList[1])" v-show="dataList.length > 1" :src="$getUrlByProcess(dataList[1].backPic)"
        alt="" />
    </divn>

  </div>
</template>

<script>
import { getZtzlList } from '@/api/message/publish'
export default {
  props: {
    refresh: {
      type: Number,
      default: 0
    }
  },
  watch: {
    refresh: {
      handler(val, oldVal) {
        console.log('val', val, oldVal)
        this.getList()
      },
      immediate: true
    }
  },
  data() {
    return {
      fileUrl: process.env.VUE_APP_FILE_URL,
      dataList: [
        // {
        //   img: require("@/assets/a.png"),
        //   title: "标题",
        //   code: "ZTJY",
        // },
        // {
        //   img: require("@/assets/b.png"),
        //   title: "标题",
        //   code: "NLZFJSN",
        // },
      ],
    };
  },
  computed: {
    curAreaCode() {
      return this.$store.getters.curAreaCode ? this.$store.getters.curAreaCode : "410000000000"
    }
  },
  mounted() {

    // this.byEvents();
  },
  methods: {
    async getList() {
      try {
        let res = await getZtzlList({ areaCode: this.curAreaCode })
        this.dataList = res.data.data;
        console.log('pppp1111p', this.dataList)
      } catch (error) {

      }
    },
    handleClick(data) {
      console.log(data, 4222);
      this.$router.push(`/gateway/subject?code=${data.code}&name=${data.name}&img=${encodeURIComponent(data.backPic)}`);
    },
    byEvents() {
      let newDataList = [];
      let current = 0;
      if (this.dataList && this.dataList.length > 0) {
        for (let i = 0; i <= this.dataList.length - 1; i++) {
          if (i % 2 !== 0 || i === 0) {
            //数据处理成几张展示
            if (!newDataList[current]) {
              newDataList.push([this.dataList[i]]);
            } else {
              newDataList[current].push(this.dataList[i]);
            }
          } else {
            current++;
            newDataList.push([this.dataList[i]]);
          }
        }
      }
      this.dataList = [...newDataList];
    },
  },
};
</script>
<style lang="scss" scoped>
.newimg {
  display: flex;
  justify-content: space-between;

  img {
    width: 625px;
    height: 200px;
    cursor: pointer;
  }
}

.carouselBox {
  width: calc(100%);
  height: 100%;
  overflow: hidden;

  .top {
    font-size: 28px;
    font-weight: bold;
  }

  .carousel {
    width: 100%;
    height: 200px;
    margin-top: 20px;
    overflow: hidden;
  }

  .el-car-item {
    width: 100%;
    display: flex;
    height: 100%;
    overflow: hidden;
    justify-content: space-between;

    .img {
      width: calc(100% - 5px);
      height: 200px;
      overflow: hidden;
      cursor: pointer;
    }
  }
}
</style>
