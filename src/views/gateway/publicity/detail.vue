<template>
  <div class="content-container">
    <div class="breadcrumb-nav">
      <span>当前位置：</span>
      <router-link to="/gateway/publicity">
        <span>公示公告</span>
      </router-link>
      <span style="margin: 0 10px">></span>
      <!-- <span>通知公告</span> -->
      <span>{{ typeOption[model.categoryCode] }}</span>
    </div>
    <div class="article">
      <div class="info_summary">
        <div class="title_wrapper title">
          {{ model.title }}
        </div>
        <div class="desc_info">
          <span>时间：{{ model.createTime }}</span>
          <span>发布部门：{{ model.source }}</span>
          <span v-if="model.showUser == 1">发布人：{{ model.createBy }}</span>
          <!-- <span>新闻板块：时政要闻</span> -->
          <span>阅读：{{ model.viewNum }}</span>
          <!-- <span style="text-align: right;">字号：【<span @click="doZoom(0.8, 'mini')">小</span> <span
              @click="doZoom(1.2, 'middle')">中</span>
            <span @click="doZoom(2, 'large')">大</span>】</span> -->
        </div>
      </div>
      <div
        class="detail"
        v-loading="loading"
        element-loading-text="正在加载中..."
      >
        <div v-if="!model.content">
          <div
            style="color: #cecece; font-size: 24px; margin-left: 20px"
            v-if="totalNumber > 5"
          >
            附件太大，请点击附件链接下载查看
          </div>
          <div v-else>
            <pdf
              v-for="(item, index) in totalNumber"
              :key="index + 1"
              :page="index + 1"
              :src="fileList[0].path ? $getUrlByProcess(fileList[0].path) : ''"
            ></pdf>
          </div>
        </div>
        <div v-else ref="content" class="content" v-html="model.content" />
      </div>
      <div v-if="fileList && fileList.length > 0" class="attachment">
        <div class="file-name">附件：</div>
        <div v-for="(file, index) in fileList" :key="file.id">
          <a class="attachment_item" @click="downLoadFile(file)">
            <span>{{ index + 1 }}、{{ file.fileName }}</span>
          </a>
        </div>
      </div>
    </div>
    <!-- <div id="components-back-top-demo-custom">
      <a-back-top>
        <div class="ant-back-top-inner">
          <a-icon type="up" />
        </div>
      </a-back-top>
    </div> -->
  </div>
</template>
<script>
import { categoryChild, categoryNum } from "@/api/gateway/index";
import pdf from "vue-pdf";
export default {
  components: { pdf },
  data() {
    return {
      id: "",
      model: {},
      currentType: 0,
      fileList: [],
      pdfObj: null,
      totalNumber: 0,
      loading: false,
      relatedList: [],
      collectTypeObj: {
        1: "21",
        2: "22",
        3: "23",
        4: "34",
      },
      typeOption: {
        XWDT_DWYW: "单位要闻",
        XWDT_SZYW: "时政要闻",
        XWDT_SJYW: "省局要闻",
        XWDT_LDJH: "领导讲话",
        XWDT_PAJS: "平安建设",
        GGGS_TZGG: "通知公告",
        GGGS_MCDB: "明传电报",
        GGGS_FW: "发文",
        GGGS_LW: "来文",
        GGGS_JYJL: "经验交流",
        PAJS: "平安建设",
        JYJL: "经验交流",
        ZTJY_LDJH: "领导讲话",
        ZTJY_WJTZ: "文件通知",
        ZTJY_JBXX: "简报信息",
        NLZFJSN_LDJH: "领导讲话",
        NLZFJSN_WJTZ: "文件通知",
        NLZFJSN_JBXX: "简报信息",
      },
    };
  },
  created() {
    const { id } = this.$route.query;
    this.id = id;
    this.loadData();
    this.categoryNumFn();
  },

  methods: {
    getNumPages() {
      if (this.pdfObj) {
        let url = this.$getUrlByProcess(this.pdfObj.path);
        // console.log("url==", url);
        let loadingTask = pdf.createLoadingTask(url);
        let that = this;
        loadingTask.promise
          .then((pdf) => {
            // console.log("pdf.numPages==", pdf.numPages);
            that.totalNumber = pdf.numPages;
            setTimeout(() => {
              this.loading = false;
            }, 800);
          })
          .catch((err) => {
            // console.error("pdf 加载失败", err);
          });
      } else {
        this.loading = false;
      }
    },
    categoryNumFn() {
      const detailUrl = {
        id: this.id,
      };
      categoryNum(detailUrl).then((res) => {});
    },
    loadData() {
      // this.loading = true;
      const detailUrl = this.id;
      categoryChild(detailUrl).then((res) => {
        this.model = res.data.data;
        this.fileList = res.data.data.files
          ? JSON.parse(res.data.data.files)
          : [];
        // this.getNumPages();
        for (let i = 0; i < this.fileList.length; i++) {
          let fileType = this.fileList[i].path
            .substring(this.fileList[i].path.lastIndexOf("."))
            .toLowerCase();
          console.log("fileType==", fileType);
          if (fileType == ".pdf") {
            this.pdfObj = this.fileList[i];
            break;
          }
        }
        if (!this.model.content) {
          this.getNumPages();
        } else {
          this.loading = false;
        }
      });
    },
    downLoadFile(file) {
      const url = this.$getUrlByProcess(file.path);
      window.open(url);
    },
  },
};
</script>
<style lang="scss" scoped>
.content-container {
  padding-bottom: 24px;
}
.content {
  padding-bottom: 24px;
}

.breadcrumb-nav {
  width: 1200px;
  margin: 0 auto;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #666666;
  padding: 20px 0;
}

.article {
  width: 1200px;
  margin: 0 auto;
  background: #fff;
  padding: 20px;

  .info_summary {
    margin-top: 20px;

    .title_wrapper {
      font-weight: bold;
      font-size: 26px;
      text-align: center;
      color: #333333;
      margin: 20px 0;
    }

    .desc_wrapper {
      width: 1160px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      color: #333;
      font-size: 14px;
      padding: 20px;
      border: solid 1px #ddd;

      .wrapper_con {
        width: 900px;
        display: flex;
        flex-wrap: wrap;
        margin: 0 auto;

        span {
          display: block;
          width: 300px;
          display: flex;
          height: 30px;
          line-height: 30px;

          label {
            width: 140px;
            text-align: right;
            margin-right: 5px;
          }
        }
      }
    }
  }
}

.detail {
  padding: 20px;

  ::v-deep img {
    max-width: 100%;
  }
}

.attachment_item {
  display: block;
  margin-bottom: 10px;
  height: 19px;
  font-size: 16px;
  font-weight: 400;
  color: #0357ca;
  line-height: 22px;
  cursor: pointer;

  &:hover {
    color: rgb(28, 106, 255);
  }
}

.attachment {
  padding: 20px;

  .file-name {
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 400;
    color: #313131;
    line-height: 25px;
  }
}

.title-name {
  padding: 20px 30px 0 10px;
  font-size: 18px;
  font-weight: 400;
  color: #0057df;
  line-height: 31px;

  .line {
    display: inline-block;
    width: 3px;
    height: 16px;
    background: #0057df;
    border-radius: 2px;
    margin: 0 4px;

    &:first-child {
      transform: rotate(30deg) translateY(-3px);
    }

    &:last-child {
      transform: rotate(30deg) translateY(3px);
    }
  }
}

.related-list {
  padding: 10px 20px;
  font-size: 14px;

  .list-item {
    display: flex;
    justify-content: space-between;

    span {
      width: 160px;
      display: block;
      text-align: right;
    }

    a {
      flex: 1;
      color: #0057df;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      position: relative;

      &::before {
        position: absolute;
        content: "";
        left: -10px;
        top: 10px;
        width: 5px;
        height: 5px;
        background: #0057df;
      }
    }
  }
}

.headline {
  font-size: 22px;
  font-weight: 400;
  color: #00102f;
  line-height: 31px;
  margin-bottom: 20px;

  .line {
    display: inline-block;
    width: 3px;
    height: 16px;
    background: #0057df;
    border-radius: 2px;
    margin: 0 4px;

    &:first-child {
      transform: rotate(30deg) translateY(-3px);
    }

    &:last-child {
      transform: rotate(30deg) translateY(3px);
    }
  }
}

.interpret-item {
  width: 100%;
  display: flex;
  margin-bottom: 20px;

  .interpret-title {
    flex: 1;
    padding-left: 15px;
    height: 23px;
    font-size: 18px;
    font-weight: 400;
    color: #313131;
    line-height: 22px;
    position: relative;
    cursor: pointer;

    &::before {
      content: "";
      position: absolute;
      left: 0px;
      top: 9px;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: #313131;
    }

    &:hover {
      color: rgb(28, 106, 255);
    }
  }

  .interpret-date {
    width: 200px;
    height: 23px;
    text-align: right;
    font-size: 18px;
    font-weight: 400;
    color: rgba(96, 96, 96, 0.5);
    line-height: 22px;
  }
}

.desc_info {
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #666666;

  & span:not(:first-child) {
    margin-left: 37px;
  }

  & span:last-child {
    span {
      cursor: pointer;
      margin: 0;

      &:not(:first-child) {
        margin-left: 10px;
      }
    }
  }
}
</style>
