<template>
  <div class="main" style="margin-top: 60px">
    <div class="contentTop">
      <div class="contentInput">
        <!-- <div class="titleName">河南省市场监督管理局</div> -->
        <div class="titleInfo">
          <!-- <img src="@/assets/image/news/gongTitle.png" alt="" /> -->
          {{ $route.query.typely }}
        </div>
        <div class="searchInput">
          <input
            v-model.trim="queryParam"
            class="input"
            placeholder="请输入搜索内容"
          />
          <div class="search" @click="searchBtn">搜索</div>
        </div>
      </div>
      <!-- <img :src="imgSrc" alt="图片" /> -->
    </div>
    <div class="contentCenter ly2">
      <div class="leftMun" v-if="meunArr && meunArr.length">
        <div
          v-for="(item, index) in meunArr"
          :key="index"
          class="left-list"
          :class="{ active: currentIndex === index }"
          @click="classificationFn(index, item)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div
        class="rightMain"
        v-if="rightContentArr && rightContentArr.length > 0"
      >
        <div
          class="rightContent"
          v-for="(item, index) in rightContentArr"
          :key="index"
          @click="detailFn(item)"
        >
          <div class="left">
            <img
              v-if="item.pic"
              :src="item.pic ? $getUrlByProcess(item.pic) : ''"
              alt=""
            />
          </div>
          <div class="right">
            <div class="title" :title="item.title">{{ item.title }}</div>
            <div class="titleList" :title="item.memo">{{ item.memo }}</div>
            <div class="titleBottom">
              <div class="titleBottomLeft">
                <div style="margin-right: 12px">{{ item.source }}</div>
                <div>{{ item.createTime }}</div>
              </div>
              <div class="titleBottomRight">
                <span style="margin-right: 4px">阅读</span>
                {{ item.viewNum }}
              </div>
            </div>
          </div>
        </div>
        <div class="paginationStyle">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="pageSize"
            layout="total, prev, pager, next, jumper"
            :total="totalPage"
          >
          </el-pagination>
        </div>
      </div>
      <div style="margin: 0 auto" v-if="rightContentArr.length == 0">
        <el-empty description="暂无数据" style="margin: 40px 0" />
      </div>
    </div>
  </div>
</template>
<script>
import {
  categoryAll,
  categoryCode,
  categoryChild,
  categoryPage,
} from "@/api/gateway/index";
import { getPublishCategorys } from "@/api/message/publish";
export default {
  data() {
    return {
      totalPage: 0,
      currentPage: 1, // 当前页面
      pageSize: 10, // 当前页面显示条数
      queryParam: "",
      imgSrc: require("@/assets/image/news/newbg.png"),
      searchType: 0,
      newsData: [],
      meunArr: [
        // { id: '0', name: '单位要闻' },
        // { id: '1', name: '时政要闻' },
        // { id: '2', name: '省局要闻' },
        // { id: '3', name: '领导讲话' },
        // { id: '4', name: '平安建设' },
      ],
      currentIndex: 0,
      code: "",
      rightContentArr: [
        // { id: '11', title: '省政府与交通运输部签署合作协议 王浩...', pro: '综合处（新闻宣传处）', time: '2023-12-31', list: '12月1日，河南省政府食安办会同省教育厅、民政厅、卫生健康委、市场监管局、事管局联合召开全省集中用餐单位...', num: '300' },
        // { id: '22', title: '王凯在三门峡督导检查生态环境保护工作时强调扛稳政治', pro: '综合处（新闻宣传处）', time: '2023-12-31', list: '　    近日，河南省政协常委、教科卫体委员会主任胡广坤一行到省市场监管局（知识产权局）视察知识产权工作。省市场监管局党组书记、局长景劲松参加视察活动。汇报会上产...', num: '200' },
        // { id: '33', title: '111', pro: '综合处（新闻宣传处）22', time: '2023-12-31', list: '12月1日，河南省政府食安办会同省教育厅、民政厅、卫生健康委、市场监管局、事管局联合召开全省集中用餐单位...', num: '100' },
      ],
    };
  },
  computed: {
    curAreaCode() {
      return this.$store.getters.curAreaCode || "410000000000";
    },
  },

  watch: {
    curAreaCode: {
      handler() {
        // this.categoryPageFn(this.code, 1);
        this.getCategorys();
      },
      deep: true,
    },
  },
  beforeRouteEnter(to, from, next) {
    // console.log("to===", to.name);
    // console.log("from.name ===", from);
    // console.log("from ===", from);
    next((vm) => {
      vm.fromPath = from.path;
      if (vm.fromPath != "/gateway/publicity/detail") {
        vm.getCategorys();
      }
    });
  },
  created() {
    this.getCategorys();
  },
  activated() {
    // this.categoryPageFn(this.code);
  },
  methods: {
    async getCategorys() {
      try {
        let res = await getPublishCategorys({ areaCode: this.curAreaCode });
        console.log("data", res.data.data);
        this.newsData = res.data.data;
        this.categoryAllFn();
      } catch (error) {
        console.log("error", error);
      }
    },
    searchBtn() {
      this.currentIndex = null;
      this.searchType = 1;

      var params = {
        current: this.currentPage,
        size: this.pageSize,
        searchCode: this.code,
        title: this.queryParam,
        areaCode: this.curAreaCode,
      };
      categoryPage(params)
        .then((response) => {
          this.rightContentArr = response.data.data.records;
          this.totalPage = Number(response.data.data.total)
            ? Number(response.data.data.total)
            : 0;
        })
        .catch(() => {});
    },
    // 获取查询所有栏目
    categoryAllFn() {
      if (this.$route.query.type) {
        this.categoryCodeFn(this.$route.query.type);
      } else {
        // let defaultCate = this.newsData.filter(v => v.code === 'GGGS')
        // if (defaultCate.length) {
        this.categoryCodeFn(this.newsData[this.$route.query.number].code);
        // }
      }
    },
    // 根据栏目编码查询子栏目列表
    // categoryCodeFn(code) {
    //   categoryCode(code)
    //     .then((response) => {
    //       let paramsArr = response.data.data;
    //       this.meunArr = paramsArr;
    //       if (this.meunArr.length > 0) {
    //         // this.code = this.meunArr[0].code;
    //         this.categoryPageFn(this.meunArr[0].code);
    //       }
    //     })
    //     .catch(() => {});
    // },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);

      if (this.searchType == 1) {
        this.searchBtn();
      } else {
        this.categoryPageFn(this.code);
      }
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      // console.log("this.searchType = 1;==", this.searchType);
      if (this.searchType == 1) {
        this.searchBtn();
      } else {
        this.categoryPageFn(this.code, val);
      }
    },
    classificationFn(index, item) {
      this.searchType = 0;
      // if (index == this.currentIndex) return;
      this.currentIndex = index;
      this.categoryPageFn(item.code);
      this.code = item.code;
      this.currentPage = 1;
    },

    // 分页查询内容
    categoryPageFn(code, val) {
      let newCode = code;
      if (this.meunArr && this.meunArr.length > 0) {
        newCode = code ? code : this.meunArr[0].code;
      }
      var params = {
        current: val ? val : this.currentPage,
        size: this.pageSize,
        code: newCode,
      };
      categoryPage(params)
        .then((response) => {
          this.rightContentArr = response.data.data.records;
          this.totalPage = Number(response.data.data.total)
            ? Number(response.data.data.total)
            : 0;
        })
        .catch(() => {});
    },

    // 根据id查询内容详情
    categoryChildFn(code) {
      categoryChild(code)
        .then((response) => {})
        .catch(() => {});
    },
    detailFn(item) {
      this.$router.push({
        path: "/gateway/publicity/detail",
        query: {
          id: item.id,
        },
      });
    },
    // // 获取查询所有栏目
    // categoryAllFn() {
    //   categoryAll()
    //     .then((response) => {
    //       let paramsArr = response.data.data;
    //       if (paramsArr.some((item) => item.code == "GGGS")) {
    //         this.categoryCodeFn("GGGS");
    //       }
    //     })
    //     .catch(() => {});
    // },
    // 根据栏目编码查询子栏目列表
    categoryCodeFn(code) {
      categoryCode(code)
        .then((response) => {
          let paramsArr = response.data.data;
          this.meunArr = paramsArr;

          if (this.$route.query.type) {
            this.meunArr.forEach((item, index) => {
              if (item.code === this.$route.query.type) {
                this.currentIndex = index;
              }
            });
            this.categoryPageFn(this.$route.query.type);
            console.log("this.meunArr1", this.meunArr);
          } else {
            if (this.meunArr.length > 0) {
              this.categoryPageFn(this.meunArr[this.$route.query.number].code);
              this.code = this.meunArr[this.$route.query.number].code;
              this.currentIndex = 0;
              console.log("this.meunArr2", this.meunArr);
            }
          }
        })
        .catch(() => {});
    },
    // handleSizeChange(val) {
    //   console.log(`每页 ${val} 条`);
    //   this.categoryPageFn(this.code);
    // },
    // handleCurrentChange(val) {
    //   console.log(`当前页: ${val}`);
    //   console.log(`当前cpde----页:`, this.code);
    //   this.categoryPageFn(this.code, val);
    // },
    classificationFn(index, item) {
      // if (index == this.currentIndex) return;
      this.currentIndex = index;
      this.categoryPageFn(item.code);
      this.code = item.code;
      // console.log("classificationFn==", this.code);
    },

    // 分页查询内容
    categoryPageFn(code, val) {
      var params = {
        current: val ? val : this.currentPage,
        size: this.pageSize,
        code: code ? code : this.meunArr[0].code,
        areaCode: this.curAreaCode,
      };
      categoryPage(params)
        .then((response) => {
          this.rightContentArr = response.data.data.records;
          this.totalPage = Number(response.data.data.total)
            ? Number(response.data.data.total)
            : 0;
        })
        .catch(() => {});
    },

    // 根据id查询内容详情
    categoryChildFn(code) {
      categoryChild(code)
        .then((response) => {})
        .catch(() => {});
    },
    detailFn(item) {
      this.$router.push({
        path: "/gateway/publicity/detail",
        query: {
          id: item.id,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.main {
  .contentTop {
    width: 100%;
    height: 270px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url("~@/assets/image/news/newbg.png");
    background-size: 100% 100%;

    .contentInput {
      text-align: center;

      .titleInfo {
        width: 610px;
        margin: auto;
        font-size: 62px;
        color: #ffffff;
        letter-spacing: 25px;
      }

      .titleName {
        font-size: 24px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: rgba(89, 155, 255, 0.8);
      }

      .searchInput {
        margin-top: 24px;
        display: flex;
        width: 610px;
        height: 44px;
        line-height: 44px;
        position: relative;

        .input {
          width: 100%;
          padding: 0 18px;
          border: none;
          outline: none;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 46px 46px 46px 46px;
          opacity: 1;
          border-image: linear-gradient(
              264deg,
              rgba(89, 155, 255, 1),
              rgba(56, 100, 255, 1)
            )
            1 1;

          &::placeholder {
            padding-left: 10px;
            font-size: 18px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: rgba(153, 153, 153, 0.8);
            line-height: 22px;
          }
        }

        .search {
          position: absolute;
          right: 5px;
          top: 5px;
          width: 84px;
          height: 34px;
          line-height: 34px;
          background: #ffffff;
          border-radius: 29px 29px 29px 29px;
          opacity: 1;
          font-size: 18px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          color: #0357ca;
          cursor: pointer;
        }
      }
    }

    .contentCenter {
      margin: 15px 300px 15px 300px;
      display: flex;

      .leftMun {
        width: 230px;
        min-height: calc(100vh - 120px);
        background: #ffffff;
        box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
        border-radius: 6px 6px 6px 6px;
        opacity: 1;
        padding: 10px;

        .active {
          background: url("~@/assets/image/bg-menu.png") 0 0 no-repeat;
          background-size: cover;

          span {
            color: #ffffff !important;
          }
        }

        .left-list {
          cursor: pointer;
          height: 80px;
          border-radius: 12px 12px 12px 12px;
          background-color: #f2f6ff;
          margin-bottom: 11px;
          line-height: 80px;
          text-align: center;
          padding: 0 5px;

          span {
            font-size: 16px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 400;
            color: #00102f;

            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: inline-block;
          }
        }
      }

      .leftMun::-webkit-scrollbar {
        display: none;
      }

      .rightMain {
        width: calc(100% - 243px);
        padding: 10px 20px;
        background: #ffffff;
        box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.8);
        border-radius: 4px 4px 4px 4px;
        opacity: 1;

        .rightContent {
          padding: 20px;
          border-bottom: 1px solid #e8e8e8;
          margin-top: 5px;
          cursor: pointer;
          display: flex;

          .left {
            width: 173px;
            height: 108px;
            border-radius: 0px 0px 0px 0px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .right {
            width: calc(100% - 190px);
            flex: 1;
            margin-left: 15px;

            .title {
              font-size: 16px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 700;
              color: #333333;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .titleList {
              font-size: 14px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              color: #666666;
              margin: 12px 0;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
            }

            .titleBottom {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              color: #999999;

              .titleBottomLeft {
                display: flex;
              }

              .titleBottomRight {
                float: right;
              }
            }
          }
        }

        .paginationStyle {
          margin-top: 50px;
          text-align: center;
        }
      }

      .rightMain::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .contentCenter {
    margin: 15px 300px 15px 300px;
    display: flex;

    .leftMun {
      width: 230px;
      min-height: calc(100vh - 120px);
      background: #ffffff;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
      border-radius: 6px 6px 6px 6px;
      opacity: 1;
      padding: 10px;

      .active {
        background: url("~@/assets/image/bg-menu.png") 0 0 no-repeat;
        background-size: cover;

        span {
          color: #ffffff !important;
        }
      }

      .left-list {
        cursor: pointer;
        height: 80px;
        border-radius: 12px 12px 12px 12px;
        background-color: #f2f6ff;
        margin-bottom: 11px;
        line-height: 80px;
        text-align: center;
        padding: 0 5px;

        span {
          font-size: 16px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 400;
          color: #00102f;

          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          display: inline-block;
        }
      }
    }

    .leftMun::-webkit-scrollbar {
      display: none;
    }

    .rightMain {
      width: calc(100% - 243px);
      padding: 10px 20px;
      background: #ffffff;
      box-shadow: 0px 0px 20px 0px rgba(238, 242, 251, 0.8);
      border-radius: 4px 4px 4px 4px;
      opacity: 1;

      .rightContent {
        padding: 20px;
        border-bottom: 1px solid #e8e8e8;
        margin-top: 5px;
        cursor: pointer;
        display: flex;

        .left {
          width: 173px;
          height: 108px;
          border-radius: 0px 0px 0px 0px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .right {
          width: calc(100% - 190px);
          flex: 1;
          margin-left: 15px;

          .title {
            font-size: 16px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 700;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .titleList {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #666666;
            margin: 12px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }

          .titleBottom {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #999999;

            .titleBottomLeft {
              display: flex;
            }

            .titleBottomRight {
              float: right;
            }
          }
        }
      }

      .paginationStyle {
        margin-top: 50px;
        text-align: center;
      }
    }

    .rightMain::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
