<template>
  <div class="main" style="margin-top: 60px">
    <div class="contentTop">
      <img :src="imgSrc" alt="图片" />
    </div>

    <div class="contentCenter">
      <div class="breadcrumb-nav">
        <span>当前位置：</span>
        <router-link to="/gateway/home">
          <span>首页</span>
        </router-link>
        <span style="margin: 0 10px">></span>
        <span>专题专栏</span>
        <!-- <span style="margin: 0 10px">></span>
        <span v-if="queryRouter == 'ZTJY'">主题教育</span>
        <span v-else>能力作风设年</span> -->
      </div>

      <div class="one" v-for="(item, index) in hTitleArr" :key="index">
        <div class="hTitle">
          <div class="left"></div>
          <div class="title">{{ item.name }}</div>
          <div class="right"></div>
        </div>
        <div class="main">
          <div class="title">
            <div>{{ item.name }}</div>
            <div style="color: #0357ca" @click="moreFn(item)">
              <span style="font-size: 14px; cursor: pointer" class="mainColor"
                >查看更多</span
              >
              <span style="margin-left: 5px; font-size: 14px" class="mainColor"
                >></span
              >
            </div>
          </div>

          <div
            class="content"
            v-for="(items, index0) in newsList[index]"
            :key="index0"
          >
            <div class="contentName" @click="handleDetail(items)">
              <span></span>{{ items.title }}
            </div>
            <div>{{ items.createTime }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  categoryAll,
  categoryCode,
  categoryChild,
  categoryPage,
} from "@/api/gateway/index";
export default {
  data() {
    return {
      rightContentArr: [],
      imgSrc: require("@/assets/image/news/banner.png"),
      hTitleArr: [],
      newsList: [],
      queryRouter: "ZTJY",
      newsObj: {},
    };
  },
  created() {
    let params = this.$route.query;
    this.queryRouter = this.$route.query.code;
    this.categoryCodeFn(params.code);
    this.imgSrc =
      process.env.VUE_APP_FILE_URL + decodeURIComponent(this.$route.query.img);
    // if (params.code == "ZTJY") {
    //   this.imgSrc = require("@/assets/image/news/banner1.png");
    // } else {
    //   this.imgSrc = require("@/assets/image/news/banner.png");
    // }
  },
  methods: {
    handleDetail(item) {
      this.$router.push(`/gateway/news/detail?id=${item.id}`);
    },
    moreFn(item) {
      console.log(item, "000");
      this.$router.push({
        path: "/gateway/subject/more",
        query: {
          paramsCode: this.queryRouter,
          code: item.code,
          name: item.name,
          img: encodeURIComponent(item.backPic),
          preImg: decodeURIComponent(this.$route.query.img),
        },
      });
    },
    // 根据栏目编码查询子栏目列表
    categoryCodeFn(code) {
      categoryCode(code)
        .then(async (response) => {
          let paramsArr = response.data.data;
          this.hTitleArr = paramsArr;
          // console.log("this.hTitleArr==", this.hTitleArr);
          this.newsList = [];
          for (var i = 0; i < this.hTitleArr.length; i++) {
            if (this.hTitleArr[i].code) {
              await this.categoryPageFn(this.hTitleArr[i].code, i);
            }
          }
        })
        .catch(() => {});
    },
    // 分页查询内容
    async categoryPageFn(code, i) {
      var params = {
        current: 1,
        size: 10,
        code: code ? code : this.meunArr[0].code,
      };
      categoryPage(params)
        .then((response) => {
          let paramsArr = response.data.data.records;

          // this.hTitleArr[i].infoList = paramsArr;
          // console.log(this.hTitleArr, 8111);
          // this.newsList[i] = paramsArr;
          // this.newsList.push(paramsArr);
          //由于原来的push方法，页面会显示，但顺序会变，根据接口的响应，最先请求的接口，返回数据不一定是最快的，所以使用$set
          this.$set(this.newsList, i, paramsArr);
          // console.log("this.newsList==", this.newsList);
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.main {
  .contentTop {
    width: 100%;
    height: 330px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .contentCenter {
    margin: 15px 300px 15px 300px;

    .one {
      .hTitle {
        display: flex;
        justify-content: center;
        line-height: 0;

        .left {
          margin-top: 50px;
          width: 200px;
          height: 1px;
          background: linear-gradient(
            90deg,
            rgba(67, 152, 255, 0) 0%,
            #0357ca 100%
          );
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
        }

        .title {
          margin-top: 50px;
          font-size: 34px;
          font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
          font-weight: bold;
          color: #333333;
          padding: 0 10px 0 10px;
        }

        .right {
          margin-top: 50px;
          width: 200px;
          height: 1px;
          background: linear-gradient(
            90deg,
            rgba(67, 152, 255, 0) 0%,
            #0357ca 100%
          );
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
          transform: rotate(180deg);
        }
      }

      .main {
        background-color: #f7faff;
        margin-top: 40px;
        padding: 20px;

        .title {
          font-size: 18px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          color: #333333;
          line-height: 24px;
          margin-bottom: 30px;
          display: flex;
          justify-content: space-between;
        }

        .content {
          cursor: pointer;
          padding: 0 20px;
          width: 100%;
          height: 44px;
          line-height: 44px;
          background: #ffffff;
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
          display: flex;
          justify-content: space-between;

          .contentName {
            span {
              display: inline-block;
              width: 5px;
              height: 5px;
              background: #0357ca;
              border-radius: 50%;
              margin-right: 8px;
              line-height: 10px;
            }
          }
        }

        .content:hover {
          color: #0357ca;
        }
      }
    }
  }
}
</style>
