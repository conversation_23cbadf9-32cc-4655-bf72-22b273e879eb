<template>
  <cust-dialog :dialog-status.sync="dialogFlag" width="1260px" dialogTop="5%" @closed="handleCloseDialog" class="high-cust">
    <div class="group-container">
      <div class="group-title">分组管理</div>
      <div class="group-main">
        <div class="group-left">
          <div class="search-box">
            <el-input placeholder="搜索" v-model="deptSearch" clearable>
              <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
          <div class="group-line"></div>
          <div class="group-btn" @click="handleAddOneGroup">新增一级分组</div>

          <el-tree class="eltree" :data="treeData" :props="defaultProps" :expand-on-click-node="false" :filter-node-method="handleDeptfilterNode" @node-click="handleNodeClick" node-key="id" ref="deptTreeRef">
          </el-tree>
          <!--  default-expand-all -->
        </div>
        <div class="group-right">
          <div v-if="oneGroupFlag && levelNote == '1'" class="one-group">
            <div class="gp-title">
              <span v-if="type == 'add'">新增一级分组</span>
              <span v-else-if="type == 'edit' && levelVal == '1'">编辑一级分组</span>
            </div>

            <el-form :model="oneGroupForm" ref="oneGroupForm" label-width="100px" class="one-form">
              <div style="display: flex">
                <el-form-item label="名称" prop="name" :rules="[{ required: true, message: '名称不能为空' }]">
                  <el-input :disabled="editFlag" @change="handleChangeName" class="input-sty" clearable v-model="oneGroupForm.name"></el-input>
                </el-form-item>

                <!-- <el-form-item
                  label="是否进入进驻列表"
                  label-width="160px"
                  prop="groupType"
                  :rules="[{ required: true, message: '不能为空' }]"
                >
                  <el-radio-group v-model="oneGroupForm.groupType">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item> -->
              </div>
              <div class="flex-content">
                <el-form-item label="排序" prop="sort" :rules="[{ required: true, message: '排序不能为空' }]">
                  <!-- <el-input
                  class="input-sty"
                  clearable
                  v-model="oneGroupForm.sort"
                ></el-input> -->
                  <el-input-number :disabled="editFlag" class="input-sty" clearable v-model="oneGroupForm.sort" controls-position="right" :min="1"></el-input-number>
                </el-form-item>
                <el-form-item label="是否启用" prop="pubStatus">
                  <el-radio-group v-model="oneGroupForm.pubStatus" :disabled="editFlag">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div class="flex-content">
                <el-form-item label="内容类型" prop="articleType" :rules="[
                  {
                    required: true,
                    message: '内容类型不能为空',
                    trigger: 'change',
                  },
                ]">
                  <el-select :disabled="editFlag" v-model="oneGroupForm.articleType" placeholder="请选择">
                    <el-option v-for="item in articleTypeArr" :key="item.id" :label="item.dictLabel" :value="item.dictValue">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="地市选择" prop="articleType" :rules="[
                  {
                    required: true,
                    message: '地市选择不能为空',
                    trigger: 'change',
                  },
                ]">
                  <el-select v-model="oneGroupForm.areaCode" placeholder="请选择" :disabled="true">
                    <el-option v-for="item in areaArr" :key="item.id" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
              <el-form-item label="背景图片" v-show="oneGroupForm.articleType==2">
                <!-- <file-upload v-model="postForm.image" /> -->
                <el-upload :show-file-list="true" action="/portal-api/storage/upload" :on-success="handleUploadDocSuccess" :on-preview="handlePreview" :on-remove="handleRemoveDocSuccess" :on-exceed="handleUpLimit" :on-error="handleError" :headers="headerObj" :on-change="handleFileChange" :file-list="fileList" :before-upload="beforeUploadFile" list-type="picture-card" accept="image/png, image/jpeg,image/jpg" :limit="1" :class="fileList.length > 0 ? 'show-upload' : ''">
                  <i class="el-icon-plus"></i>
                  <!-- :limit="5" -->
                  <!-- <div class="btn-space">
                      <el-button size="small" type="primary"
                        >图片上传</el-button
                      >
                    </div> -->

                  <div slot="tip" class="el-upload__tip">
                    最多上传1张5M以内的图，支持格式：jpg，png，jpeg
                  </div>
                </el-upload>
              </el-form-item>

              <!-- <el-form-item>
                <el-button @click="handleCancelForm">取消</el-button>
                <el-button
                  type="primary"
                  @click="debounceHadleSubmitFn('oneGroupForm')"
                  >提交</el-button
                >
              </el-form-item> -->
            </el-form>
            <div class="form-btns">
              <template v-if="editFlag && type == 'edit'">
                <el-button type="primary" @click="handleUpdateForm" v-if="editFlag && type == 'edit'">编辑</el-button>
                <el-button type="danger" @click="handleDeleteForm">删除</el-button>
              </template>
              <template v-else>
                <el-button type="primary" @click="debounceHadleSubmitFn('oneGroupForm')">提交</el-button>
                <el-button @click="handleCancelForm">取消</el-button>
              </template>
            </div>

            <!-- <div class="form-line"></div> -->
          </div>
          <div class="material-list" v-if="oneGroupFlag">
            <div class="my-list">
              <div class="list-topic">
                <div class="list-topic-item width-one">序号</div>
                <div class="list-topic-item width-two">
                  <span style="color: red">*</span> 分组名称
                </div>

                <div class="list-topic-item width-two">
                  <span style="color: red">*</span> 排序号
                </div>
                <div class="list-topic-item width-two">是否启用</div>
                <!-- <div class="list-topic-item width-two">背景图片</div> -->

                <div class="list-topic-item width-four"></div>
              </div>
              <div class="list-content">
                <div v-for="(item, index) in groupList" :key="index">
                  <div class="list-content-item">
                    <div class="row width-one row-sort">{{ index + 1 }}</div>
                    <div class="row width-two">
                      <el-input v-model="item.name" class="input" placeholder="请输入内容" :disabled="!item.isedit"></el-input>
                    </div>

                    <div class="row width-two">
                      <el-input-number style="width: 100%" v-model="item.sort" controls-position="right" :min="1" placeholder="请输入排序号" :disabled="!item.isedit"></el-input-number>
                    </div>
                    <div class="row width-two">
                      <el-radio-group v-model="item.pubStatus" :disabled="!item.isedit">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                      </el-radio-group>
                    </div>
                    <!-- <div class="row width-two">
                      <file-upload v-model="item.backPic" :fileUrls="item.backPic" :tipsFlag="false"></file-upload>

                    </div> -->

                    <div class="row width-four caozuo-sty">
                      <span style="
                          color: #f56c6c;
                          margin-top: 10px;
                          margin-left: 6px;
                          margin-right: 10px;
                          cursor: pointer;
                          font-size: 22px;
                        " class="el-icon-remove-outline" @click="deleteRow(index, item)" v-show="item.isedit"></span>
                      <el-button v-if="!item.id" type="primary" class="btn-sty" @click="debounceHandleSvae(item)">提交</el-button>
                      <el-button v-show="item.isedit && item.id" type="primary" class="btn-sty" @click="debounceHandleUpdate(item)">提交</el-button>
                      <el-button type="primary" class="btn-sty" v-show="!item.isedit" @click="editchange(item)">编辑</el-button>
                    </div>
                  </div>
                  <!-- <div class="attachment" v-show="item.isOpenAttachment"></div> -->
                </div>
              </div>
              <div class="new-add" @click="handleAddGroupFn" v-if="routeData && routeData.goType === 'edit'">
                <span class="el-icon-plus"></span><span>新增</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div slot="bottom-footer">
      <el-button @click="handleCloseDialog">取消</el-button>
      <!-- <el-button type="primary" @click="debounceHandleSure">确定</el-button> -->
    </div>
  </cust-dialog>
</template>
<script>
import {
  addPersonGroup,
  updatePersonGroup,
  batchAddGroup,
  delGroupById,
} from "@/api/message/publishArticle";
import { remote } from "@/api/admin/sys/sys-dict";
import { getPublishCategory } from "@/api/message/publish";
import debounce from "@/util/debounce";
import FileUpload from "./FileUpload.vue";
export default {
  name: "categoryManage",
  components: { FileUpload },

  data() {
    return {
      isimg: false,
      submitFlag: false,
      treeData: [],
      levelNote: "",
      dialogFlag: true,
      oneGroupFlag: false,
      editFlag: false,
      lineHight: 166,
      levelVal: "3",
      deptSearch: "",
      groupList: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
      routeData: {
        goType: "edit",
      },
      deptArr: [],
      currentGroupObj: {},
      oneGroupForm: {
        name: "",
        level: 0,
        groupType: 1,
        areaCode: "",
        code: "",
        pubStatus: "1", //是否启用 1启用 0停用
        articleType: "",
      },
      type: "",
      twoFlag: false,
      headerObj: {},
      softDocObj: {},
      fileArr: [],
      fileList: [],
      treeAllArr: [],
      articleTypeArr: [],
    };
  },
  props: {
    // visibling: {
    //   type: Boolean,
    //   default: function () {
    //     return true;
    //   },
    // },
    pressData: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {
    deptSearch(val) {
      this.$refs.deptTreeRef.filter(val);
    },
  },
  computed: {
    token() {
      return this.$store.getters.access_token;
    },
  },
  created() {
    setTimeout(() => {
      this.areaArr = [
        {
          label:
            this.$store.getters.dept.areaName ||
            window.localStorage.getItem("areaNamely"),
          value:
            this.$store.getters.dept.areaCode ||
            window.localStorage.getItem("areaCodely"),
        },
      ];
    }, 1000);
    this.handleGetTreeData();
    this.handleGetSysData();
  },
  mounted() {
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  methods: {
    editchange(params) {
      var that = this;
      let aitem = "";
      this.groupList.forEach((item, index) => {
        if (params.id == item.id) {
          aitem = item;
          aitem.isedit = true;
          that.$nextTick(() => {
            // that.groupList.splice(index, 1);
            that.groupList.splice(index, 1, aitem);
          });
          that.$forceUpdate();
        }
      });
    },
    handleGetSysData() {
      remote("portal_article_type").then((res) => {
        console.log("获取的数据字典==", res);
        if (res.data.code == 200) {
          this.articleTypeArr = res.data.data;
        }
      });
    },
    handleChangeName() {
      console.log("oneoneoneone", this.oneGroupForm.name);
    },
    handleGetTreeData() {
      let _this = this;
      getPublishCategory({
        areaCode:
          this.$store.getters.dept.areaCode ||
          window.localStorage.getItem("areaCodely"),
      }).then((res) => {
        let data = res.data.data;
        _this.treeData = data;
        this.treeAllArr = [];
        this.handleForTree(_this.treeData);
        //
        setTimeout(() => {
          console.log("_this.treeAllArr==", _this.treeAllArr);
          console.log("_this.currentGroupObj==", _this.currentGroupObj);
          if (this.currentGroupObj) {
            let fObj = _this.treeAllArr.find((item) => {
              return item.id == this.currentGroupObj.id;
            });
            console.log("fObj===", fObj);
            if (fObj) {
              if (fObj.children && fObj.children.length > 0) {
                this.groupList = JSON.parse(JSON.stringify(fObj.children));

                this.groupList.forEach((item) => {
                  item.isedit = false;
                });
                console.log(
                  "this.groupList --data=====================",
                  this.groupList
                );
              }
            }
            console.log("fObj===", fObj);
          }
        }, 300);
      });
    },
    handleForTree(arr) {
      arr.forEach((item) => {
        this.treeAllArr.push(item);
        if (item.children && item.children.length > 0) {
          this.handleForTree(item.children);
        }
      });
    },
    handleNodeClick(data, node, el) {
      console.log("data==", data);
      console.log("node==", node);
      console.log("item==", el);
      // if (node.level == 2) {
      //   return;
      // }
      this.levelVal = node.level;
      this.levelNote = node.level;
      this.currentGroupObj = data;
      this.editFlag = true;
      this.type = "edit";
      if (node.level == 1) {
        this.oneGroupFlag = true;
      }

      if (node.level) {
        // this.oneGroupForm = {};
        this.oneGroupForm = Object.assign(this.oneGroupForm, data);
        console.log("this.oneGroupForm=======", this.oneGroupForm);
        if (data.children && data.children.length > 0) {
          this.groupList = JSON.parse(JSON.stringify(data.children));
          this.groupList.forEach((item) => {
            item.isedit = false;
          });
        } else {
          this.groupList = [];
        }
        console.log(
          "handleNodeClick-groupList=====================",
          this.groupList
        );
        if (this.oneGroupForm.backPic) {
          let imageArr = this.oneGroupForm.backPic.split(",");
          if (imageArr && imageArr.length > 0) {
            this.fileList = [];
            imageArr.forEach((item) => {
              if (item) {
                let obj = Object.assign({}, item);
                let url = "";
                // obj.name = item.attachmentName;
                if (process.env.NODE_ENV == "development") {
                  if (window.location.host.includes("localhost:")) {
                    url = process.env.VUE_APP_FILE_URL + item;
                  }
                } else {
                  url =
                    window.location.protocol +
                    "//" +
                    window.location.host +
                    item;
                }
                // obj.url = process.env.VUE_APP_PIC_URL + item;
                obj.url = url;
                obj.response = {
                  data: {
                    uri: item,
                  },
                };
                this.fileList.push(obj);
              }
            });
          }
        } else {
          this.fileList = [];
          this.fileArr = [];
        }
      }

      // 校验表单 //
      console.log("---", this.oneGroupForm.articleType);
      setTimeout(() => {
        this.$refs.oneGroupForm.clearValidate("articleType");
      }, 300);
    },
    handleAddGroupFn() {
      this.groupList.push({
        isedit: true,
        name: "",
        level: 1,
        code: "",
        parentId: this.currentGroupObj.id,
        sort: this.groupList.length + 1,
        backPic: "",
        pubStatus: "1",
        areaCode: this.$store.getters.dept.areaCode,
      });
    },
    deleteRow(index, item) {
      console.log("item==", item);
      if (item.id) {
        //数据库里面有的
        delGroupById(item.id).then((res) => {
          console.log("res==", res);
          if (res.data.code == 200) {
            this.groupList.splice(index, 1);
            this.handleGetTreeData();
            setTimeout(() => {
              this.$refs.deptTreeRef.setCurrentKey(this.currentGroupObj.id);
            }, 500);
          }
        });
      } else {
        this.groupList.splice(index, 1);
      }

      //
    },
    handleDeptfilterNode(value, data) {
      console.log("value==", value);
      console.log("data==", data);
      if (!value) return true;
      // return data.categoryName.indexOf(value) !== 0;
      return data.name.indexOf(value) !== -1;
    },
    handleCloseDialog() {
      this.dialogFlag = false;
      this.$emit("closeDialogFn");
    },
    debounceHandleSvae: debounce(function (item) {
      this.handleSaveFn(item);
    }, 500),
    debounceHandleUpdate: debounce(function (item) {
      this.handleUpdateFn(item);
    }, 500),
    handleSaveFn(item) {
      console.log("item===", item);
      if (!this.oneGroupForm.articleType) {
        this.$message.error("请先添加一级分组的内容类型");
        return;
      }
      item.articleType = this.oneGroupForm.articleType;
      if (item.name.length == 0) {
        this.$message.error("请填写名称！");
        this.submitFlag = false;
      } else if (item.sort.length == 0) {
        this.$message.error("请填写排序号！");
        this.submitFlag = false;
      } else {
        this.submitFlag = true;
      }
      if (!this.submitFlag) {
        return;
      }
      addPersonGroup(item).then((res) => {
        console.log("res", res);
        if (res.data.code == 200) {
          this.$message({
            showClose: true,
            message: "保存成功！",
            type: "success",
          });
          //刷新组
          // this.oneGroupFlag = false;
          // this.oneGroupForm.name = "";
          this.handleGetTreeData(); //重新获取分组
          // this.levelVal = 3; // 新增分组成功后，不让二级列表显示新增
        }
      });
    },
    handleUpdateFn(item) {
      var that = this;

      console.log("item===", item);
      if (!this.oneGroupForm.articleType) {
        this.$message.error("请先添加一级分组的内容类型");
        return;
      }
      item.articleType = this.oneGroupForm.articleType;
      if (item.name.length == 0) {
        this.$message.error("请填写名称！");
        this.submitFlag = false;
      } else if (item.sort.length == 0) {
        this.$message.error("请填写排序号！");
        this.submitFlag = false;
      } else {
        this.submitFlag = true;
      }
      if (!this.submitFlag) {
        return;
      }
      updatePersonGroup(item).then((res) => {
        console.log("res", res);
        if (res.data.code == 200) {
          this.$message({
            showClose: true,
            message: "修改成功！",
            type: "success",
          });
          let aitem = "";
          that.groupList.forEach((items, index) => {
            if (item.id == items.id) {
              aitem = items;
              aitem.isedit = false;
              that.$nextTick(() => {
                // that.groupList.splice(index, 1);
                that.groupList.splice(index, 1, aitem);
              });
              that.$forceUpdate();
            }
          });
          //刷新组
          // this.oneGroupFlag = false;
          // this.oneGroupForm.name = "";
          // this.handleGetTreeData(); //重新获取分组
          // this.levelVal = 3; // 新增分组成功后，不让二级列表显示新增
        }
      });
    },

    // 点击新增一级分组
    handleAddOneGroup() {
      this.oneGroupFlag = true;
      this.type = "add";
      this.editFlag = false;
      this.levelVal = 3;
      this.levelNote = 1;

      this.oneGroupForm = {
        name: "",
        level: 0,
        areaCode: this.$store.getters.dept.areaCode,
        pubStatus: "1",
        backPic: "",
      };
      this.fileList = [];
    },
    //取消一级分组
    handleCancelForm() {
      if (this.type == "edit") {
        this.editFlag = true;
      } else {
        this.oneGroupFlag = false;
      }
    },
    debounceHadleSubmitFn: debounce(function (formName) {
      this.handleSubmitForm(formName);
    }, 500),
    handleSubmitForm(formName) {
      // this.fetchTest();
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log("oneGroupForm==", this.oneGroupForm);
          if (this.type == "add") {
            this.handleAddPersonGroup(this.oneGroupForm);
          } else if (this.type == "edit") {
            this.handleUpdatePersonGroup(this.oneGroupForm);
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // fetchTest() {
    //   let cloudPivotToken = this.$store.getters.access_token;
    //   let url =
    //     "http://192.168.117.4:8002/api/business-cms-article-category/create";
    //   let croObj = {
    //     headers: {
    //       mode: "no-cors",
    //       Authorization: "Bearer " + cloudPivotToken,
    //     },
    //     method: "POST",
    //     body: JSON.stringify({
    //       categoryCode: "t001",
    //       categoryDesc: "",
    //       categoryName: "code001",
    //       isParent: 0,
    //       orderNum: 1,
    //       parentId: "",
    //     }),
    //   };
    //   console.log("url==", url);
    //   let that = this;
    //   fetch(url, croObj)
    //     .then((data) => {
    //       return data.json();
    //     })
    //     .then((res) => {
    //       console.log("获取fetch==", res);
    //     });
    // },
    handleUpdateForm() {
      this.editFlag = false;
    },

    handleAddPersonGroup(form) {
      console.log("form-================", form);
      let attachmentList = [];
      console.log("this.fileList==", this.fileList);
      if (this.fileList && this.fileList.length > 0) {
        this.fileList.forEach((item) => {
          // let obj = {};
          // obj.attachmentName = item.response.data.fileName;
          // obj.attachmentUri = item.response.data.url;
          // obj.id = item.response.data.bizId;
          attachmentList.push(item.response.data.uri);
        });
        form.backPic = attachmentList.join();
      } else {
        form.backPic = "";
      }
      form.parentId = "-1";
      addPersonGroup(form).then((res) => {
        console.log("res", res);
        if (res.data.code == 200) {
          this.$message({
            showClose: true,
            message: "新增分组成功！",
            type: "success",
          });
          //刷新组
          this.oneGroupFlag = false;
          this.oneGroupForm.name = "";
          this.handleGetTreeData(); //重新获取分组
          this.levelVal = 3; // 新增分组成功后，不让二级列表显示新增
        }
      });
    },
    // 编辑
    handleUpdatePersonGroup(form) {
      form.parentId = "-1";

      let attachmentList = [];
      console.log("this.fileList==", this.fileList);
      if (this.fileList && this.fileList.length > 0) {
        this.fileList.forEach((item) => {
          attachmentList.push(item.response.data.uri);
        });
        form.backPic = attachmentList.join();
      } else {
        form.backPic = "";
      }
      updatePersonGroup(form).then((res) => {
        console.log("res==", res);
        if (res.data.code == 200) {
          this.$message({
            showClose: true,
            message: "编辑分组成功！",
            type: "success",
          });
          //刷新组
          // this.oneGroupFlag = false;
          // this.oneGroupForm.name = "";
          this.editFlag = true;
          this.handleGetTreeData(); //重新获取分组
          setTimeout(() => {
            console.log("设置keys", this.currentGroupObj.id);
            // this.$refs.deptTreeRef.setCheckedKeys([this.currentGroupObj.id]);
            this.$refs.deptTreeRef.setCurrentKey(this.currentGroupObj.id);
          }, 500);
        }
      });
    },
    // 删除
    handleDeleteForm() {
      console.log("this.currentGroupObj==", this.currentGroupObj);
      delGroupById(this.currentGroupObj.id).then((res) => {
        console.log("删除==", res);
        if (res.data.code == 0 || res.data.code == 200) {
          this.$message({
            showClose: true,
            message: "删除分组成功！",
            type: "success",
          });
          this.handleGetTreeData(); //重新获取分组
          this.currentGroupObj = {};
          this.oneGroupFlag = false;
          // this.oneGroupForm = { name: "" };
        }
      });
    },
    beforeUploadFile(file) {
      const isLt500M = file.size / 1024 / 1024 < 5;
      if (!isLt500M) {
        this.$message.error("上传文件大小不能超过 5MB!");
        return false;
      }
      let fileName = file.name;
      let idx = fileName.lastIndexOf(".");
      let suffix = fileName.substring(idx + 1);
      const isTypeFlag =
        suffix.toLowerCase() === "jpg" ||
        suffix.toLowerCase() === "jpeg" ||
        suffix.toLowerCase() === "png"; //
      if (!isTypeFlag) {
        this.$message.error("上传文件只能是 jpg,png,jpeg格式!");
        return false;
      }
      return isTypeFlag && isLt500M;
    },
    handleUploadDocSuccess(res, file) {
      // console.log("res==", res);
      if (res.code == 0) {
        if (this.type == "add") {
          // this.uploadVisible = true;
        } else {
          this.softDocObj = res.data;
          this.fileArr.push(res.data);
        }
      }
    },
    handlePreview(file) {
      window.open(this.getUrl(file), "target");
      // window.location.href = url;
    },
    getUrl(file) {
      let url = process.env.VUE_APP_FILE_URL + file.response.data.url;
      if (process.env.NODE_ENV == "development") {
        if (window.location.host.includes("localhost:")) {
          url = process.env.VUE_APP_FILE_URL + file.response.data.url;
        }
      } else {
        url =
          window.location.protocol +
          "//" +
          window.location.host +
          file.response.data.url;
      }
      return url;
    },
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
    handleRemoveDocSuccess(removeFile, fileList) {
      this.fileList = fileList;
    },
    handleError(error, file, fileLis) {
      this.$message.error("文件上传失败，请您重新退出后再登录");
    },
    handleUpLimit(files, fileList) {
      this.$message({
        showClose: true,
        message: "上传文件已超出最大限制数量！",
        type: "warning",
      });
    },
    // handleGetGroupTree() {
    //   getPersonGroupTree().then((res) => {
    //     console.log("数===", res);
    //     if (res.data.code == 0) {
    //       this.deptArr = res.data.data;
    //     }
    //   });
    // },
  },
};
</script>
<style scoped lang="scss">
.group-container {
  padding-left: 32px;

  .group-title {
    color: #22242c;
    font-size: 18px;
    font-weight: 500;
  }

  .group-main {
    display: flex;
    margin-top: 20px;
    margin-right: 20px;

    .group-left {
      width: 200px;
      min-height: 493px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      .eltree {
        height: 400px;
        overflow: auto;
        width: 100%;
        padding: 10px;
        background-color: #ffffff;

        ::v-deep .el-tree-node {
          .el-tree-node__children {
            overflow: visible !important;
          }
        }
      }

      .search-box {
        padding: 12px;
      }

      .group-line {
        height: 1px;
        background: #ebeef5;
        width: 100%;
        overflow: auto;
      }

      .group-btn {
        width: 176px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        border-radius: 4px;
        opacity: 1;
        background: #3272ce;
        color: #fff;
        margin: 16px auto;
        cursor: pointer;
      }
    }

    .group-right {
      flex: 1;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      min-height: 493px;
      margin-left: 20px;

      .form-btns {
        position: absolute;
        top: 50px;
        right: 30px;
      }

      .one-group {
        position: relative;
        margin: 20px 10px;
        border-bottom: 1px solid #dcdfe6;

        .gp-title {
          margin: 0 0 20px 50px;
          font-size: 20px;
        }

        .input-sty {
          width: 300px;
        }
      }

      .form-line {
        width: 100%;
        height: 1px;
        border-bottom: 1px dotted #dcdfe6;
      }

      .material-list {
        //   height: 337px;
        // background-color: red;
        margin-bottom: 23px;
        //   border: 1px solid #dcdfe6;
        border-radius: 4px;

        .my-list {
          .list-topic {
            height: 40px;
            background-color: #f5f7fa;
            display: flex;
            line-height: 40px;

            .list-topic-item {
              font-size: 14px;
              color: #8e8ea1;
            }
          }

          .list-content {
            //   height: 240px;
            overflow: auto;

            // background-color: red;
            //   border-bottom: 1px solid #dcdfe6;
            .attachment {
              height: 50px;
              background-color: #fafafa;
            }

            .list-content-item {
              height: 72px;
              line-height: 72px;
              // border-bottom: 1px solid #dcdfe6;
              padding: 8px 0px;
              box-sizing: content-box;
              display: flex;

              .row {
                position: relative;

                .input {
                  width: 95%;
                }

                .option {
                  .expand {
                    position: absolute;
                    right: 0;
                  }

                  .delete {
                    color: #f56c6c;
                  }
                }
              }

              .row-sort {
                font-size: 14px;
                color: #8e8ea1;
              }
            }
          }

          .new-add {
            cursor: pointer;
            margin-top: 9px;
            margin-bottom: 8px;
            margin: 9px 11px 8px 11px;
            height: 40px;
            border: 1px dashed #3272ce;
            border-radius: 4px;
            text-align: center;
            transition: 0.5s;

            span {
              font-size: 14px;
              line-height: 40px;
              margin-right: 6px;
              color: #3272ce;
            }
          }

          .new-add:hover {
            background-color: #e1ebfa;
          }
        }
      }
    }
  }

  .width-one {
    text-align: center;
    width: 5%;
  }

  .width-two {
    width: 20%;
  }

  .width-three {
    width: 18%;
    margin-right: 10px;
  }

  .width-four {
    margin-right: 10px;
    width: 15%;
  }

  .caozuo-sty {
    display: flex;
    align-items: center;
  }

  .flex-content {
    display: flex;
  }

  .btn-sty {
    width: 60px;
    height: 40px;
  }
}

.show-upload {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}

.high-cust {
  ::v-deep .el-dialog__body {
    padding: 0;
    margin-bottom: 8px;
    height: 500px;
  }

  ::v-deep .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
