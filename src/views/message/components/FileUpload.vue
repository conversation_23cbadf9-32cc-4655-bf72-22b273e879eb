<template>
  <el-upload
    :show-file-list="true"
    :class="fileList.length > 0 ? 'show-upload' : ''"
    action="/portal-api/storage/upload"
    :on-success="handleUploadDocSuccess"
    :on-preview="handlePreview"
    :on-remove="handleRemoveDocSuccess"
    :on-error="handleError"
    :headers="headerObj"
    :on-change="handleFileChange"
    :file-list="fileList"
    :before-upload="beforeUploadFile"
    v-model="fileUrl"
    list-type="picture-card"
    ref="fileUpload"
  >
    <i class="el-icon-plus"></i>
    <div class="el-upload__tip" slot="tip" v-if="tipsFlag">
      <template> {{ tipsStr }} </template>
    </div>
  </el-upload>
</template>
<script>
export default {
  data() {
    return {
      fileList: [],
      fileUrl: "",
      headerObj: {},
    };
  },
  props: {
    value: String,
    fileUrls: String,
    tipsFlag: {
      type: <PERSON><PERSON>an,
      default: function () {
        return true;
      },
    },
    tipsStr: {
      type: String,
      default: function () {
        return "只能上传jpeg/jpg/png文件，且不超过5M";
      },
    },
    fileSize: {
      type: Number,
      default: function () {
        return 5;
      },
    },
    fileType: {
      type: Array,
      default: function () {
        return ["png", "jpg", "jpeg"];
      },
    },
  },
  watch: {
    // 检测查询变化
    value: {
      handler(val) {
        // console.log("检测到value的变化--", val);
        // this.fillValue()
      },
    },

    // 检测查询变化
    // fileUrl: {
    //   handler() {
    //     this.$emit('input', this.fileUrl)
    //   }
    // }
  },

  computed: {
    token() {
      return this.$store.getters.access_token;
    },
  },
  mounted() {
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
    if (this.fileUrls) {
      this.handleDoFile();
    }
  },
  methods: {
    //清空列表
    clearFiles() {
      this.fileList = [];
      this.$refs["fileUpload"].clearFiles();
    },
    handleDoFile() {
      let imageArr = this.fileUrls.split(",");
      // console.log("imageArr==", imageArr);
      if (imageArr && imageArr.length > 0) {
        this.fileList = [];
        imageArr.forEach((item) => {
          if (item) {
            let obj = Object.assign({}, item);
            // obj.name = item.attachmentName;
            let url = process.env.VUE_APP_FILE_URL + item;
            if (process.env.NODE_ENV == "development") {
              if (window.location.host.includes("localhost:")) {
                url = process.env.VUE_APP_FILE_URL + item;
              }
            } else {
              url =
                window.location.protocol + "//" + window.location.host + item;
            }
            obj.url = url;
            obj.response = {
              data: {
                url: item,
              },
            };
            this.fileList.push(obj);
          }
        });
      }
    },
    beforeUploadFile(file) {
      const isLt500M = file.size / 1024 / 1024 < this.fileSize;
      if (!isLt500M) {
        this.$message.error("上传文件大小不能超过 " + this.fileSize + "MB!");
        return false;
      }
      // let fileName = file.name;
      // let idx = fileName.lastIndexOf(".");
      // let suffix = fileName.substring(idx + 1);
      // const isTypeFlag =
      //   suffix.toLowerCase() === "jpg" ||
      //   suffix.toLowerCase() === "jpeg" ||
      //   suffix.toLowerCase() === "png"; //
      // if (!isTypeFlag) {
      //   this.$message.error("上传文件只能是 jpg/png格式!");
      // }
      console.log("this.fileType===", this.fileType);

      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          return !!(fileExtension && fileExtension.indexOf(type) > -1);
        });
        if (!isTypeOk) {
          this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      return true;
      // return isTypeFlag && isLt500M;
    },
    handlePreview(file) {
      // let url = process.env.VUE_APP_PIC_URL + file.response.data.url;
      let url = this.$getUrlByProcess(file.response.data.url);
      window.open(url, "target");
    },
    handleUploadDocSuccess(res, file) {
      console.log("res==", res);
      if (res.code == 200) {
        //  this.$emit('input', res.data.url)
        console.log("file--imagefileList--", this.fileList);
        let attachmentList = [];
        if (this.fileList && this.fileList.length > 0) {
          this.fileList.forEach((item) => {
            attachmentList.push(item.response.data.uri);
          });
          let image = attachmentList.join();

          console.log("file--image--", image);
          this.$emit("input", image);
        }
      }
    },
    // handleUploadImgSuccess(){
    // },
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
    handleRemoveDocSuccess(removeFile, fileList) {
      this.fileList = fileList;
      let attachmentList = [];
      if (this.fileList && this.fileList.length > 0) {
        this.fileList.forEach((item) => {
          attachmentList.push(item.response.data.uri);
        });
        let image = attachmentList.join();

        this.$emit("input", image);
      } else {
        this.$emit("input", "");
      }
    },
    handleError(error, file, fileLis) {
      this.$message.error("文件上传失败");
    },
  },
};
</script>
<style scoped lang="scss">
.show-upload {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
  ::v-deep .el-upload-list__item.is-success:focus:not(:hover) {
    display: none !important;
  }
}
::v-deep .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 80px;
}
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
}
</style>
