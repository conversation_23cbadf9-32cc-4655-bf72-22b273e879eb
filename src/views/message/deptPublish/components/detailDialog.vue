<!--
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2022-03-09 14:17:04
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-02-28 09:35:24
-->
<template>
  <cust-dialog
    :title="'临界预警'"
    :dialog-status.sync="dialogFlag"
    width="1100px"
    @closed="handleCloseDialog"
    class="mydialog"
  >
    <div class="notice-container">
      <h2 class="title-h2">{{ viewObj.templateName }}</h2>
      <div style="display: flex; justify-content: center">
        <h3 class="title-h4">{{ "接收时间：" + viewObj.sendTime }}</h3>
        <div style="width: 50px"></div>
        <h3 class="title-h4">{{ "来源：" + viewObj.clientName }}</h3>
      </div>
      <div class="notice-content" v-html="viewObj.content"></div>
    </div>
    <div
      class="link-block"
      v-for="(item, key) in viewObj.attachmentList"
      :key="key"
    >
      <a :href="spath + item.url" target="_blank" rel="noopenner noreferrer">{{
        item.fileName
      }}</a>
    </div>
    <span slot="bottom-footer" class="dialog-footer">
      <el-button type="primary" @click="handleCloseDialog">确定</el-button>
    </span>
  </cust-dialog>
</template>
<script>
export default {
  name: "DetailDialog",
  data() {
    return {
      dialogFlag: true,
      lineHight: 166,
      speedLoading: true,
    };
  },
  props: {
    // visibling: {
    //   type: Boolean,
    //   default: function () {
    //     return true;
    //   },
    // },
    pressData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    viewObj: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  computed: {
    spath() {
      return window.location.protocol + "//" + window.location.host;
    },
  },
  mounted() {},

  methods: {
    handleDealHight() {
      this.speedLoading = false;
      this.$nextTick(() => {
        setTimeout(() => {
          // console.log("this.$ref=1=", this.$refs);
          // console.log("this.$ref=2=", this.$refs.seeUlRef.scrollHeight);
          this.lineHight = this.$refs.seeUlRef.scrollHeight - 60;
          if (this.lineHight < 166) {
            this.lineHight = 166;
          }
        }, 100);
      });
    },
    handleCloseDialog() {
      this.dialogFlag = false;
      this.$emit("closeDialog");
    },
  },
};
</script>
<style scoped lang="scss">
.my-cust {
  // /deep/ .el-dialog__body {
  //   border-bottom: 1px solid #f0f0f0;
  //   padding: 0;
  //   margin-bottom: 2px;
  // }
  // /deep/ .el-dialog__header {
  //   padding-bottom: 0;
  //   min-height: 40px;
  // }
}
.notice-container {
  position: relative;
  border: 1px solid transparent;
  .title-h2 {
    text-align: center;
    font-size: 24px;
    margin-top: 24px;
    padding: 0 20px;
  }
  .title-h3 {
    text-align: center;
    font-size: 20px;
  }
  .notice-content {
    padding: 20px;
  }
}
.link-block {
  margin-left: 20px;
  a {
    // color: #111;
    color: #327fff;
    &:hover {
      text-decoration: underline;
    }
  }
}
.title-h4 {
  text-align: center;
  font-size: 14px;
  color: #999;
}
</style>
