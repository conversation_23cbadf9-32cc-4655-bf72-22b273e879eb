<template>
  <div class="banner">
    <el-card>
      <!-- <div>
        <el-button @click="handleAdd" icon="el-icon-plus" type="primary">新建</el-button>
      </div> -->
      <el-table v-loading="loading" class="m-t-10" :data="tableData" border>
        <el-table-column prop="pubDept" align="center" label="部门名称">
        </el-table-column>
        <el-table-column prop="articleType" align="center" label="内容类型">
          <template slot-scope="{ row }">
            <!-- <el-image style="width: 140px; height: 100px" :src="row.pic ? $getUrlByProcess(row.pic) : ''"
              :preview-src-list="[$getUrlByProcess(row.pic)]"></el-image> -->
            <!-- {{ row.articleType }} -->
            {{ getArticleTypeFn(row.articleType) }}
          </template>
        </el-table-column>
        <el-table-column prop="createBy" align="center" label="用户名">
        </el-table-column>
        <el-table-column prop="title" align="center" label="标题名称">
        </el-table-column>
        <el-table-column prop="pubStatus" align="center" label="发布状态">
          <template slot-scope="{ row }">
            <span v-if="row.pubStatus == 1">待审核</span>
            <span v-else-if="row.pubStatus == 2">已发布</span>
            <span v-else-if="row.pubStatus == -1">已驳回</span>
            <span v-else-if="row.pubStatus == -2">已下线</span>
            <span v-else-if="row.pubStatus == 0">待提交</span>
          </template>
        </el-table-column>
        <el-table-column prop="pubArticleNum" align="center" label="发布链接">
          <template slot-scope="{ row }">
            <!-- <a @click="handleClick2(row)">{{'/gateway/publicity/detail?id='+ row.id}}</a> -->
            <el-link type="primary" @click="handleClick2(row)">链接详情</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" align="center" label="创建时间">
        </el-table-column>

        <div slot="empty">
          <el-empty :image-size="200"></el-empty>
        </div>
      </el-table>
      <div class="avue-crud__pagination">
        <el-pagination class="is-background" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="filter.current" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="filter.size"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </el-card>

    <el-dialog :title="form.id ? '编辑' : '新增'" :visible.sync="dialogVisible" width="1000px" @close="handleClose"
      :append-to-body="true">
      <el-form :model="form" ref="form" label-width="100px" :rules="rules" class="demo-ruleForm">
        <el-form-item label="链接类型" prop="type">
          <el-select style="width: 100%" v-model="form.type" @change="handleChooseType" placeholder="请选择">
            <el-option v-for="item in urlOptionArr" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题内容">
            <template slot="append">
              <el-button @click="handleClick" v-if="isBlank === '0'">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="链接地址" prop="webUrl" v-show="isBlank === '1'">
          <el-input v-model="form.webUrl" placeholder="请输入链接地址"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :precision="0" :min="0">
          </el-input-number>
        </el-form-item>
        <el-form-item label="内容类型选择" prop="areaCode">
          <el-select v-model="form.areaCode" placeholder="请选择" :disabled="true">
            <el-option v-for="item in areaArr" :key="item.areaCode" :label="item.areaName" :value="item.areaCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="图片详情" prop="pic" :key="form.id">
          <file-upload ref="uploadPic" v-model="form.pic" :fileUrls="form.pic"></file-upload>
        </el-form-item>
        <el-form-item label="是否启用" prop="pubStatus">
          <el-switch v-model="form.pubStatus" active-value="1" inactive-value="0">
          </el-switch>
        </el-form-item>
        <el-form-item>
          <el-button v-loading="loading" type="primary" @click="submitForm('form')">提交</el-button>
          <el-button @click="handleClose">取消</el-button>
        </el-form-item>
      </el-form>

      <el-dialog width="1200px" title="新闻选择" height="600px" :visible.sync="innerVisible" append-to-body>
        <!-- <NewsInfo @handleChoose="handleChoose"></NewsInfo> -->
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
import {
  getBannerList,
  updateBanner,
  addBanner,
  deleteBanner,
  uploadOss,
  getNewsList,
  disableBanner,
  enableBanner,
} from "@/api/admin/banner";
import { remote } from "@/api/admin/sys/sys-dict";
import {
  getAllCitys, listDept, getDeptArticleDetails
} from "@/api/admin/sys/dept";

import FileUpload from "@/views/message/components/FileUpload.vue";
export default {
  name: "schoolList",
  components: {
    FileUpload,
  },
  data() {
    return {
      DeptArr: [],
      articleTypeArr: [],

      filter: {
        size: 10,
        current: 1,
      },
      areaArr: [],
      dateTime: "",
      isBlank: "", //是否为外链
      tableData: [
        {
          deptName: "123123",
          webUrl: "",
          pubStatus: "0",
          articleType: "22222",
          createTime: "",
          id: "",
          sort: "",
          type: "",
          pic: "",
          areaCode: "",
        }
      ],
      total: 0,
      form: {
        title: "",
        webUrl: "",
        pubStatus: "0",
      },
      dialogVisible: false,
      loading: false,
      innerVisible: false,
      pubOption: {
        0: "待发布",
        1: "已发布",
        2: "已下架",
      },
      urlOption: {
        0: "内链",
        1: "外链",
      },

      pubOptionArr: [
        {
          label: "隐藏",
          value: "0",
        },
        {
          label: "展示",
          value: "1",
        },
      ],
      urlOptionArr: [
        {
          label: "内链",
          value: "0",
        },
        {
          label: "外链",
          value: "1",
        },
      ],
      rules: {
        title: [
          {
            required: true,
            message: "请输入标题或选择新闻",
            trigger: "change",
          },
        ],
        pubStatus: [
          { required: true, message: "请选择是否启用", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择链接类型", trigger: "change" },
        ],
        pic: [{ required: true, message: "请选择图片", trigger: "change" }],
        sort: [{ required: true, message: "请填写排序", trigger: "blur" }],
      },
    };
  },
  filters: {},
  created() {
    console.log("banner---");
    this.handleGetSysData();

    console.log("this.$route.query.", this.$route.query)
    this.filter.deptId = this.$route.query.deptId
    this.filter.articleType = this.$route.query.articleType
    // this.areaArr = [{label:this.$store.getters.dept.areaName,value:this.$store.getters.dept.areaCode}];
    // this.getData();
    this.getlistDeptApi();
  },
  methods: {
    handleClick2(item) {
      if (item.type == 1) {
        // this.$router.push("/gateway/publicity/detail?id=" + item.id);
        window.open("/portal-web/#/gateway/publicity/detail?id=" + item.id);
      } else {
        // this.$router.push("/gateway/news/detail?id=" + item.id);
        window.open("/portal-web/#/gateway/news/detail?id=" + item.id);

      }
    },
    handleGetSysData() {
      remote("portal_article_type").then((res) => {
        console.log("获取的数据字典==", res);
        if (res.data.code == 200) {
          this.articleTypeArr = res.data.data;
        }
      });
    },
    getArticleTypeFn(type) {
      let value = "";
      if (type || type == 0) {
        let fObj = this.articleTypeArr.find((item) => {
          return item.dictValue == type;
        });
        if (fObj) {
          value = fObj.dictLabel;
        }
        return value;
      }
    },

    getlistDeptApi() {
      listDept().then((res) => {

        this.DeptArr = res.data.data;
      });
    },
    getAllCitysApi() {
      getAllCitys({ id: '410000000000' }).then((res) => {
        this.areaArr = [{ areaName: '河南省', areaCode: '410000000000' }, ...res.data.data || []];
      }).catch((err) => {

      })
    },
    //关闭  新增/编辑弹窗
    handleClose() {
      this.resetForm("form");
      this.dialogVisible = false;
      this.$refs["uploadPic"].clearFiles();
    },
    async handleSwitchChange(value, row) {
      try {
        if (value == 1) {
          await enableBanner({
            id: row.id,
          });
        } else {
          await disableBanner({
            id: row.id,
          });
        }
        this.getData();
      } catch (error) { }
      console.log(value, 25111);
    },
    handleChooseType(value) {
      this.$set(this.form, "webUrl", "");
      this.isBlank = value;
    },
    handleChoose(value) {
      this.$set(this.form, "title", value.title);
      this.form.articleId = value.id;
      this.innerVisible = false;
      console.log(value, 22555);
    },
    handleClick() {
      this.innerVisible = true;
    },
    resetData() {
      this.filter = {
        size: this.filter.size,
        current: this.filter.current,
      };
      this.dateTime = null;
      this.getData();
    },
    handleSizeChange(val) {
      this.filter.size = val;
      this.current = 1;
      this.getData();
    },
    handleCurrentChange(val) {
      this.filter.current = val;
      this.getData();
    },
    async getData() {


      this.loading = true;
      try {

        let {
          data: { data },
        } = await getDeptArticleDetails({ ...this.filter });
        console.log('getDeptArticleDetails', data);
        this.total = data.total;
        this.tableData = data.records;
      } catch (e) {
        console.log("getDeptArticleDetails catch==", e);
      }
      this.loading = false;
    },
    handleAdd() {
      this.dialogVisible = true;
      this.form = {
        type: "",
        title: "",
        webUrl: "",
        pubStatus: "0",
        areaCode: this.$store.getters.dept.areaCode,
      };
      this.isBlank = "";
    },
    async handleEdit(row) {
      try {
        this.dialogVisible = true;
        this.form = Object.assign({}, row);
        this.isBlank = row.type;
      } catch (error) { }
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.loading = true;
          try {
            if (this.form.id) {
              let res = await updateBanner(this.form);
              this.$message.success("修改成功");
            } else {
              await addBanner(this.form);
              this.$message.success("新建成功");
            }
            this.dialogVisible = false;
            this.form = {};
            this.getData();
          } catch (error) { }
        } else {
          return false;
        }
        this.loading = false;
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleDelete(row) {
      this.$confirm('是否确认删除标题为"' + row.title + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        try {
          await deleteBanner(row);
          this.$message.success("删除成功");
          this.getData();
        } catch (error) { }
      });
    },
  },
  async mounted() {
    // this.queryRouter = this.$route.query.name;

    this.getData();
    await this.getAllCitysApi();
    // this.filter.areaCode = '410000000000'


  },
};
</script>
<style lang="scss" scoped>
.banner {
  padding: 0px 20px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 128px;
  display: block;
}
</style>
