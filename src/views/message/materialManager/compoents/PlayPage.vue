<template>
  <cust-dialog
    :title="title"
    class="mydialog"
    @closed="handleCloseDialog"
    width="auto"
  >
    <div class="video" id="vedio"></div>
  </cust-dialog>
</template>
<script>
import Player from "xgplayer";
export default {
  name: "PlayPage",
  data() {
    return {
      url: "/hall-file/2022/05/25/4f1da5nsm1mcw1f5sqam/cs.mp4",
    };
  },
  mounted() {
    this.handleInitPlayer(this.url);
  },
  methods: {
    handleCloseDialog() {
      this.$emit("closeDialogFn");
    },
    handleInitPlayer(url) {
      setTimeout(() => {
        this.player = new Player({
          id: `vedio`,
          url: process.env.VUE_APP_PIC_URL + url,
          fitVideoSize: "fixHeight",
          height: 494,
          videoInit: true,
          //   lastPlayTime: this.handleSetPlayTime(), //视频起播时间（单位：秒）
          lastPlayTimeHideDelay: 5,
        });
      }, 200);
    },
  },
};
</script>
<style lang="scss" scoped>
.video {
//   width: 960px;
//   height: 640px;
//   flex: 3;
  background-color: black;
}
</style>