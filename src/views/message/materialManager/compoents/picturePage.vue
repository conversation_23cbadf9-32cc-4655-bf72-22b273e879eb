<template>
  <div>
    <div class="content">
      <div class="header">
        <el-button
          :style="{ 'margin-top': '3px' }"
          icon="el-icon-upload"
          type="primary"
          @click="handleAdd"
          >上传</el-button
        >
      </div>
      <div class="body">
        <template v-if="picList && picList.length > 0">
          <div v-for="item in picList" :key="item.sourceId" class="pic-item">
            <div class="item-pic">
              <img class="item-pic" :src="item.url" />
              <div class="item-opm">
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="查看"
                  placement="bottom-end"
                >
                  <div class="opm-view" @click="clickView(item.url)">
                    <i class="el-icon-view" :style="{ color: '#fff' }"></i>
                  </div>
                </el-tooltip>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="删除"
                  placement="bottom-end"
                >
                  <div class="opm-del" @click="handleDel(item)">
                    <i class="el-icon-delete" :style="{ color: '#fff' }"></i>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div class="item-text">
              <div class="text-top">
                <span>{{ item.sourceName }}</span>
              </div>
              <div class="text-down">
                <span>{{ item.createTime }}</span>
              </div>
            </div>
          </div>
        </template>
        <template v-if="noFlag">
          <div class="no-data">
            <div class="no-data-bg"></div>
            <div>暂无数据</div>
          </div>
        </template>
      </div>
      <div class="page">
        <el-pagination
          v-if="page.total > 0"
          background
          style="position: relative; top: 50%; transform: translateY(-50%)"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.pageCurrent"
          :page-sizes="[10]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        >
        </el-pagination>
      </div>
    </div>
    <cust-dialog
      title="文件上传"
      v-if="addFlag"
      :dialog-status.sync="addFlag"
      width="800px"
      class="mydialog"
      @closed="handleBack"
    >
      <div>
        <el-form :model="form" :rules="rules" v-loading="tableLoading">
          <el-form-item
            label="文件名称："
            label-width="100px"
            prop="sourceName"
          >
            <el-input
              maxlength="24"
              show-word-limit
              v-model="form.sourceName"
              placeholder="请输入文件名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="选择文件：" label-width="100px" prop="url">
            <el-upload
              list-type="picture-card"
              action="/portal-api/storage/upload"
              :on-success="handleUploadDocSuccess"
              :on-preview="handlePreview"
              :on-remove="handleRemoveDocSuccess"
              :on-error="handleError"
              :headers="headerObj"
              :on-change="handleFileChange"
              :file-list="fileList"
              :before-upload="beforeUploadFile"
              :limit="1"
              :class="fileList.length >= 1 ? 'no-show' : ''"
              accept="image/png, image/jpeg"
            >
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                最多上传1张以内的图片,支持格式：jpg，png，jpeg
              </div>
              <!-- <el-button size="small" type="primary">点击上传</el-button>
              -->
              <!-- <el-dialog :visible.sync="dialogVisible">
                      <img width="100%" :src="dialogImageUrl" alt="" />
                    </el-dialog> -->
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <span slot="bottom-footer" class="dialog-footer">
        <el-button @click="handleBack">返回</el-button>
        <el-button type="primary" @click="handleDebounceSubmit">提交</el-button>
      </span></cust-dialog
    >
  </div>
</template>
<script>
import { fetchList, delObj, addObj } from "@/api/message/material.js";
export default {
  name: "picturePage",
  data() {
    return {
      noFlag: false,
      ruleForm: {
        sourceName: "",
        url: "",
      },
      rules: {
        sourceName: [
          {
            required: true,
            message: "请输入文件名称",
            trigger: "change",
          },
        ],
        url: [
          {
            required: true,
            message: "请选择文件",
            trigger: "change",
          },
        ],
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条
      },
      form: {
        sourceName: "",
        url: "",
      },
      addFlag: false,
      tableLoading: false,
      fileList: [],
      tableData: [],
      picList: [],
      headerObj: {},
    };
  },
  computed: {
    token() {
      return this.$store.getters.access_token;
    },
  },
  mounted() {
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  created() {
    this.getList(this.page);
  },
  methods: {
    handleBack() {
      this.addFlag = false;
      this.form.sourceName = "";
      this.fileList = [];
      this.tableLoading = false;

      this.getList(this.page);
    },
    clickView(url) {
      console.log("url================", url);
      if (url) {
        window.open(url);
      }
    },
    handleAdd() {
      this.form.sourceName = "";
      this.fileList = [];
      this.addFlag = true;
    },
    handleDebounceSubmit() {
      let arr = [];
      let bizArr = [];
      this.fileList.forEach((item) => {
        arr.push(item.response.data.uri);
        bizArr.push(item.response.data.bizId);
      });
      let url = arr.join(",");
      let bizId = bizArr.join(",");
      console.log("llllllllllllllllllllll", this.fileList);
      let params = {
        sourceName: this.form.sourceName,
        sourceType: 1,
        url: url,
        bizId: bizId,
      };
      if (params.url.length > 0) {
        if (params.sourceName.length > 0) {
          addObj(params).then((res) => {
            console.log("res================", res);
            this.addFlag = false;
            this.getList(this.page);
          });
        } else {
          this.$message.error("请输入文件名称！");
        }
      } else {
        this.$message.error("请上传文件！");
      }

      console.log("params=============", params);
    },
    handleDel(item) {
      console.log("item==", item);
      this.$confirm("是否确认删除该素材" + " ?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(item.id);
        })
        .then((res) => {
          console.log("删除==", res);
          if (res.data.code == 200) {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.getList(this.page);
          }
        });
    },
    getList(page) {
      this.noFlag = false;
      let params = {
        current: page.currentPage,
        size: 10,
        sourceType: 1,
      };
      fetchList(params)
        .then((res) => {
          console.log("res==============", res);
          // res.data.data.records.forEach((item) => {
          //   item.url = process.env.VUE_APP_PIC_URL + item.url;
          // });
          if (res.data.code == 200) {
            if (
              res.data.data.records.length == 0 &&
              res.data.data.total > 0 &&
              this.page.currentPage > 1
            ) {
              this.page.currentPage--;
              this.getList(this.page);
            } else {
              this.tableData = res.data.data.records;
              this.page.total = res.data.data.total;
              this.picList = this.tableData || [];

              this.picList.forEach((item) => {
                if (
                  item.url.includes("http://") ||
                  item.url.includes("https://")
                ) {
                } else {
                  item.url = process.env.VUE_APP_BASE_FILE + item.url;
                }
              });
            }

            console.log("this.picList=22=", this.picList);
          } else {
            this.$message({
              type: "error",
              message: "查询失败!",
            });
            this.picList = true;
          }
        })
        .finally(() => {
          if (this.picList.length == 0) {
            this.noFlag = true;
          }
        });
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getList(this.page);
    },
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getList(this.page);
    },
    beforeUploadFile(file) {
      const size = this.fileList.length < 2;
      this.tableLoading = true;
      let fileName = file.name;
      let idx = fileName.lastIndexOf(".");
      let suffix = fileName.substring(idx + 1);
      const isTypeFlag =
        suffix.toLowerCase() === "jpg" ||
        suffix.toLowerCase() === "jpeg" ||
        suffix.toLowerCase() === "png"; //  ||suffix.toLowerCase()==='avi'
      if (!isTypeFlag) {
        this.$message.error("上传文件只能是 jpg,png,jpeg格式!");
        this.tableLoading = false;
      }
      if (!size) {
        this.$message.error("最多上传一个文件");
        this.tableLoading = false;
      }
      return size && isTypeFlag;
    },
    handleUploadDocSuccess(res, file) {
      console.log("成功==", res);
      if (res.code == 200) {
        this.softDocObj = res.data;
        this.tableLoading = false;
        // this.$set(this.record,"fileList",)
        // this.record.isOpenAttachment = true;
        console.log("filr==================", file);
        this.fileList.push({
          fileName: file.name,
          fileUrl: process.env.VUE_APP_BASE_FILE + file.response.data.uri,
        });
      }
    },
    handlePreview(file) {
      console.log("file==================", file);
      let url = this.getUrl(file);
      window.open(url, "target");
    },
    getUrl(file){
      return process.env.VUE_APP_BASE_FILE + file.response.data.uri
    },
    handleRemoveDocSuccess(removeFile, fileList) {
      this.fileList = fileList;
    },
    handleError(error, file, fileLis) {
      this.$message.error("文件上传失败，请您重新退出后再登录");
    },
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-upload-list__item .el-icon-close-tip {
  display: none !important;
}
::v-deep .el-upload-list__item.is-success .el-upload-list__item-status-label {
  display: none !important;
}
.no-show {
  ::v-deep .el-upload--picture-card {
    display: none !important;
  }
}
.content {
  height: auto;
  width: auto;
  .header {
    margin-left: 8px;
    margin-right: 15px;
    height: 55px;
    width: auto;
    border-bottom: 1px #ebeef5 solid;
  }
  .body {
    height: auto;
    width: 100%;
    display: flex;
    flex-flow: wrap;
    margin-left: 8px;
    .pic-item {
      margin-right: 18px;
      margin-top: 15px;
      width: 309px;
      height: 280px;
      border: 1px #ebeef5 solid;
      border-radius: 5px;
      .item-pic {
        width: 100%;
        height: 195px;
        background: #ebeef5;
        // position: absolute;
        position: relative;

        .item-opm {
          width: 70px;
          display: flex;
          height: 20px;
          margin-left: 230px;
          justify-content: space-between;
          position: absolute;
          bottom: 180px;
          .opm-view {
            margin-top: 12px;
            width: 32px;
            height: 20px;
            border-radius: 4px;
            background: #000000;
            opacity: 0.32;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
            &:hover,
            &.active {
              background: #000000;
              opacity: 0.5;
            }
          }
          .opm-del {
            margin-top: 12px;
            width: 32px;
            height: 20px;
            border-radius: 4px;
            background: #000000;
            opacity: 0.32;
            line-height: 20px;
            text-align: center;
            cursor: pointer;
            &:hover,
            &.active {
              background: #000000;
              opacity: 0.5;
            }
          }
        }
      }
      .item-text {
        height: 84px;
        .text-top {
          height: 42px;
          text-align: center;
          vertical-align: middle;
          font-size: 14px;
          font-weight: 500;
          color: #000000;
          line-height: 65px;
        }
        .text-down {
          text-align: center;
          height: 42px;
          font-size: 12px;
          font-weight: 500;
          color: #8e8ea1;
          line-height: 42px;
        }
      }
    }
  }
  .page {
    height: 80px;
    text-align: right;
  }
}
.no-data {
  position: relative;
  width: 100%;
  text-align: center;
  .no-data-bg {
    background: url("../../../../assets/image/img/no-data.png") no-repeat;
    width: 197px;
    height: 132px;
    background-size: 100%;
    margin: 20px auto;
  }
}
</style>
