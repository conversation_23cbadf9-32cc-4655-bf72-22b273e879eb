<template>
  <div>
    <div class="content">
      <div class="header">
        <el-button
          :style="{ 'margin-top': '3px' }"
          icon="el-icon-upload"
          type="primary"
          @click="handleAdd"
          >上传</el-button
        >
      </div>
      <div class="body">
        <template>
          <div v-for="item in picList" :key="item.sourceId" class="pic-item">
            <div
              class="item-pic"
              @mouseenter="item.playVisible = true"
              @mouseleave="item.playVisible = false"
            >
              <div
                class="play"
                v-show="item.playVisible"
                :style="getPlay()"
                @click="handlePlay(item.url)"
              ></div>
              <video
                id="video"
                :src="item.url"
                controls="controls"
                muted
                style="height: 100%; width: 100%"
              ></video>
              <div
                class="item-opm"
                @mouseenter="item.playVisible = false"
                @mouseleave="item.playVisible = true"
              >
                <!-- <el-tooltip
                class="item"
                effect="dark"
                content="查看"
                placement="bottom-end"
              >
                <div class="opm-view">
                  <i
                    class="cust-iconfont icon-yincang"
                    :style="{ color: '#fff' }"
                  ></i>
                </div>
              </el-tooltip> -->
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="删除"
                  placement="bottom-end"
                >
                  <div class="opm-del" @click="handleDel(item.id)">
                    <i class="el-icon-delete" :style="{ color: '#fff' }"></i>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div class="item-text">
              <div class="text-top">
                <span>{{ item.sourceName }}</span>
              </div>
              <div class="text-down">
                <span>{{ item.createTime }}</span>
              </div>
            </div>
          </div>
        </template>
        <template v-if="noFlag">
          <div class="no-data">
            <div class="no-data-bg"></div>
            <div>暂无数据</div>
          </div>
        </template>
      </div>
      <div class="page">
        <el-pagination
          v-if="page.total > 0"
          background
          style="position: relative; top: 50%; transform: translateY(-50%)"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.pageCurrent"
          :page-sizes="[10]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
        >
        </el-pagination>
      </div>
    </div>
    <cust-dialog
      title="文件上传"
      v-if="addFlag"
      :dialog-status.sync="addFlag"
      width="800px"
      class="mydialog"
      @closed="handleBack"
    >
      <div>
        <el-form :model="form" :rules="rules" v-loading="tableLoading">
          <el-form-item
            label="文件名称："
            label-width="100px"
            prop="sourceName"
          >
            <el-input
              maxlength="24"
              show-word-limit
              placeholder="请输入文件名称"
              v-model="form.sourceName"
            ></el-input>
          </el-form-item>
          <el-form-item label="选择文件：" label-width="100px" prop="url">
            <el-upload
              :show-file-list="false"
              action="/portal-api/storage/upload"
              :on-success="handleUploadDocSuccess"
              :on-preview="handlePreview"
              :on-remove="handleRemoveDocSuccess"
              :on-error="handleError"
              :headers="headerObj"
              :on-change="handleFileChange"
              :file-list="fileList"
              :before-upload="beforeUploadFile"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">
                最多上传1个以内的视频，支持格式：mp4
              </div>
              <!-- <el-dialog :visible.sync="dialogVisible">
                      <img width="100%" :src="dialogImageUrl" alt="" />
                    </el-dialog> -->
            </el-upload>
          </el-form-item>
          <upload :fileList="fileList" :uploadVisible="uploadVisible"></upload>
        </el-form>
      </div>
      <span slot="bottom-footer" class="dialog-footer">
        <el-button @click="handleBack">返回</el-button>
        <el-button type="primary" @click="handleDebounceSubmit">提交</el-button>
      </span></cust-dialog
    >
    <play-page
      :videoUrl="videoUrl"
      v-if="playFlag"
      @closeDialogFn="handleCloseDialogFn"
    ></play-page>
  </div>
</template>
<script>
import PlayPage from "./PlayPage.vue";
import upload from "./upload.vue";

import { fetchList, delObj, addObj } from "@/api/message/material.js";
export default {
  components: { PlayPage, upload },
  name: "picturePage",
  data() {
    return {
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      ruleForm: {
        sourceName: "",
        url: "",
      },
      form: {
        sourceName: "",
        url: "",
      },
      noFlag: false,
      videoUrl: "",
      uploadVisible: false,
      playFlag: false,
      playVisible: false,
      playImg: "", //require("@/assets/image/learn/play.png"),
      addFlag: false,
      tableLoading: false,
      fileList: [],
      picList: [],
      headerObj: {},
      rules: {
        sourceName: [
          {
            required: true,
            message: "请输入活动名称",
            trigger: "change",
          },
        ],
        url: [
          {
            required: true,
            message: "请输入活动名称",
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    token() {
      return this.$store.getters.access_token;
    },
  },
  mounted() {
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  created() {
    this.getList(this.page);
  },
  methods: {
    handleBack() {
      this.addFlag = false;
      this.form.sourceName = "";
      this.fileList = [];
      this.getList(this.page);
      this.tableLoading = false;
    },
    getList(page) {
      let params = {
        current: page.currentPage,
        size: 10,
        sourceType: 3,
      };
      fetchList(params)
        .then((res) => {
          console.log("res==============", res);
          // res.data.data.records.forEach((item) => {
          //   item.url = process.env.VUE_APP_PIC_URL + item.url;
          // });
          if (
            res.data.data.records.length == 0 &&
            res.data.data.total > 0 &&
            this.page.currentPage > 1
          ) {
            this.page.currentPage--;
            this.getList(this.page);
          } else {
            this.tableData = res.data.data.records;
            this.page.total = res.data.data.total;
            this.picList = this.tableData || [];
            this.picList.forEach((item) => {
              if (
                item.url.includes("http://") ||
                item.url.includes("https://")
              ) {
              } else {
                item.url = process.env.VUE_APP_BASE_FILE + item.url;
              }
            });
          }
        })
        .finally(() => {
          if (this.picList.length == 0) {
            this.noFlag = true;
          }
          console.log("this.noFlag", this.noFlag);
        });
    },
    handleDel(id) {
      this.$confirm("是否确认删除该素材" + " ?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(id);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.getList(this.page);
        });
    },
    handleDebounceSubmit() {
      let arr = [];
      let bizArr = [];
      this.fileList.forEach((item) => {
        arr.push(item.response.data.uri);
        bizArr.push(item.response.data.bizId);
      });
      let url = arr.join(",");
      let bizId = bizArr.join(",");
      console.log("llllllllllllllllllllll", this.fileList);
      let params = {
        sourceName: this.form.sourceName,
        sourceType: 3,
        url: url,
        bizId: bizId,
      };
      if (params.url.length > 0) {
        if (params.sourceName.length > 0) {
          addObj(params).then((res) => {
            console.log("res================", res);
            this.addFlag = false;
            this.getList(this.page);
          });
        } else {
          this.$message.error("请输入文件名称！");
        }
      } else {
        this.$message.error("请上传文件！");
      }
      console.log("params=============", params);
    },
    handlePlay(url) {
      this.playFlag = true;
      this.videoUrl = url;
    },
    handleAdd() {
      this.form.sourceName = "";
      this.fileList = [];
      this.addFlag = true;
    },
    handleCloseDialogFn() {
      this.playFlag = false;
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getList(this.page);
    },
    handleCurrentChange(val) {
      console.log("val=当前页=", val);
      this.page.currentPage = val;
      this.getList(this.page);
    },
    getPlay() {
      return `
        background: url(${this.playImg})
          no-repeat;
          background-size: 100% 100%;

      `;
    },
    beforeUploadFile(file) {
      const size = this.fileList.length < 2;
      this.tableLoading = true;
      let fileName = file.name;
      let idx = fileName.lastIndexOf(".");
      let suffix = fileName.substring(idx + 1);
      const isTypeFlag = suffix.toLowerCase() === "mp4"; //  ||suffix.toLowerCase()==='avi'
      if (!isTypeFlag) {
        this.tableLoading = false;
        this.$message.error("上传文件只能是mp4格式!");
      }
      if (!size) {
        this.$message.error("最多上传一个文件");
        this.tableLoading = false;
      }
      return size && isTypeFlag;
    },
    handleUploadDocSuccess(res, file) {
      console.log("res==", res);
      if (res.code == 200) {
        this.tableLoading = false;
        console.log("file=============", file);
        // this.softDocObj = res.data;
        // this.fileArr.push(res.data);
        // this.$set(this.record,"fileList",)
        // this.record.isOpenAttachment = true;
        this.uploadVisible = true;
        this.fileList.forEach((item) => {
          item.url = item.response.data.uri;
        });
      }
    },
    handlePreviewed(file) {
      let url = this.getUrl(file);
      window.open(url, "target");
    },
    getUrl(file){
      return process.env.VUE_APP_PIC_URL + file.response.data.url
    },
    handleRemoveDocSuccess(removeFile, fileList) {
      this.fileList = fileList;
    },
    handleError(error, file, fileLis) {
      this.$message.error("文件上传失败，请您重新退出后再登录");
    },
    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  height: auto;
  width: auto;
  .header {
    margin-left: 8px;
    margin-right: 15px;
    height: 55px;
    width: auto;
    border-bottom: 1px #ebeef5 solid;
  }
  .body {
    height: auto;
    width: 100%;
    display: flex;
    flex-flow: wrap;
    margin-left: 8px;
    .pic-item {
      margin-right: 18px;
      margin-top: 15px;
      width: 309px;
      height: 280px;
      border: 1px #ebeef5 solid;
      border-radius: 5px;

      .item-pic {
        width: 100%;
        height: 195px;
        background: #ebeef5;
        position: relative;
        .play {
          width: 46px;
          height: 46px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 20;
          cursor: pointer;
        }
        .item-opm {
          width: 70px;
          display: flex;
          height: 20px;
          margin-left: 230px;
          justify-content: space-between;
          position: absolute;
          bottom: 180px;
          z-index: 10;
          .opm-view {
            margin-top: 12px;
            width: 32px;
            height: 20px;
            border-radius: 4px;
            background: #000000;
            opacity: 0.32;
            text-align: center;
            line-height: 20px;
            cursor: pointer;
            &:hover,
            &.active {
              background: #000000;
              opacity: 0.5;
            }
          }
          .opm-del {
            margin-left: 25px;
            margin-top: 12px;
            width: 32px;
            height: 20px;
            border-radius: 4px;
            background: #000000;
            opacity: 0.32;
            line-height: 20px;
            text-align: center;
            cursor: pointer;
            &:hover,
            &.active {
              background: #000000;
              opacity: 0.5;
            }
          }
        }
      }
      .item-text {
        height: 84px;
        .text-top {
          height: 42px;
          text-align: center;
          vertical-align: middle;
          font-size: 14px;
          font-weight: 500;
          color: #000000;
          line-height: 65px;
        }
        .text-down {
          text-align: center;
          height: 42px;
          font-size: 12px;
          font-weight: 500;
          color: #8e8ea1;
          line-height: 42px;
        }
      }
    }
  }
  .page {
    height: 80px;
    text-align: right;
  }
}
.no-data {
  position: relative;
  width: 100%;
  text-align: center;
  .no-data-bg {
    background: url("../../../../assets/image/img/no-data.png") no-repeat;

    width: 197px;
    height: 132px;
    background-size: 100%;
    margin: 20px auto;
  }
}
</style>
