<template>
  <div class="main">
    <div class="console-tab">
      <ul>
        <li
          class="tab-item"
          :class="{ active: curIndex === index }"
          v-for="(item, index) in consoleTabData"
          :key="index"
          @click="handleClickTab(index)"
        >
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
        </li>
      </ul>
    </div>
    <basic-container class="container">
      <picture-page v-if="curIndex === 0" />
      <video-page v-if="curIndex === 1" />
      <!-- <agency-matter v-else-if="curIndex === 1" />
      <settled-matter v-else-if="curIndex === 2" />
      <back-matter v-else-if="curIndex === 3" />
      <time-out-matter v-else /> -->
    </basic-container>
  </div>
</template>
<script>
import picturePage from "./compoents/picturePage.vue";
import videoPage from "./compoents/videoPage.vue";
export default {
  name: "operateplatform",
  components: {
    picturePage,
    videoPage,
    // History,
    // Publish,
    // AgencyMatter,
    // SettledMatter,
    // BackMatter,
    // TimeOutMatter,
  },
  data() {
    return {
      curIndex: 0,
      consoleTabData: [
        {
          icon: "el-icon-picture-outline",
          name: "图片",
        },
        {
          icon: "el-icon-video-play",
          name: "视频",
        },
        // {
        //   icon: "cust-iconfont icon-banjieshixiang",
        //   name: "办结事项",
        // },
        // {
        //   icon: "cust-iconfont icon-huituishixiang",
        //   name: "回退事项",
        // },
        // {
        //   icon: "cust-iconfont icon-chaoshishixiang",
        //   name: "超时事项",
        // },
      ],
    };
  },
  methods: {
    handleClickTab(index) {
      this.curIndex = index;
    },
  },
};
</script>
<style lang="scss" scoped>
.console-tab {
  position: relative;
  z-index: 1;
  margin: 28px 18px 0px 18px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 50px;
  background-color: #dae3f0;
  ul {
    display: flex;
    list-style: none;
  }
  .tab-item {
    cursor: pointer;
    padding: 13px 15px 13px 15px;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    span {
      color: #3272ce;
    }
    i {
      color: #3272ce;
    }
  }
  .active {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background: linear-gradient(90deg, #1573e0, #009ee7);
    span {
      color: #ffffff;
    }
    i {
      color: #ffffff;
    }
  }
}
.container {
  position: relative;
  z-index: 999;
  margin-top: -10px;
  padding-top: 0px;
  ::v-deep .el-card {
    border-radius: 0 0 10px 10px;
  }
}
.upload {
  width: 50px;
  height: 50px;
  background-color: red;
  .input {
  }
}
</style>
