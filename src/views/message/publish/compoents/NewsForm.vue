<template>
  <div class="news-form">
    <el-form
      class="my-form lyfabu"
      :model="form"
      :rules="rules"
      ref="ruleForm"
      :disabled="addType == 'view'"
    >
      <el-row>
        <!-- <el-col :span="12">
            <el-form-item
              label="内容分类："
              label-width="100px"
              prop="contentType"
            >
              <el-input
                :disabled="true"
                v-model="form.contentType"
              ></el-input> </el-form-item
          ></el-col> -->
        <el-col :span="24">
          <el-form-item label="标题：" label-width="90px" prop="title">
            <el-input
              maxlength="100"
              show-word-limit
              v-model="form.title"
            ></el-input> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item label="新闻摘要" label-width="90px" prop="memo">
            <el-input
              maxlength="200"
              show-word-limit
              v-model="form.memo"
            ></el-input> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item label="地市选择" label-width="90px" prop="memo">
            <el-select
              v-model="form.areaCode"
              placeholder="请选择"
              :disabled="true"
            >
              <el-option
                v-for="item in areaArr"
                :key="item.areaCode"
                :label="item.areaName"
                :value="item.areaCode"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item label="发布部门" label-width="90px" prop="source">
            <el-input
              maxlength="100"
              show-word-limit
              :disabled="true"
              v-model="form.pubDept"
            ></el-input>
            <!-- <el-select v-model="form.pubDept" placeholder="请选择" :disabled="true">
              <el-option v-for="item in pubDeptArr" :key="item.id" :label="item.deptName" :value="item.id">
              </el-option>
            </el-select> -->
          </el-form-item></el-col
        >
        <!-- <el-col :span="12">
          <el-form-item label="发布站点" label-width="90px" prop="areaCode">
            <tree-select
              v-model="form.areaCode"
              :data="treeDeptTreeData"
              :props="defaultProps2"
              :loadNode="getChildTree2"
              placeholder=""
              @selfInput="selfInput"
              filterable
              slefFilter
              lazy
            ></tree-select>

          </el-form-item>
        </el-col> -->
        <!-- <el-input
                  maxlength="100"
                  show-word-limit
                  v-model="form.areaName"
                  disabled
              ></el-input> -->
        <el-col :span="12">
          <el-form-item
            label="内容类型"
            prop="articleType"
            :rules="[{ required: true, message: '内容类型不能为空' }]"
          >
            <el-select
              v-model="form.articleType"
              @change="changeTypeFn"
              placeholder="请选择"
            >
              <el-option
                v-for="item in articleTypeArr"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.dictValue"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="12">
          <el-form-item label="分组：" label-width="90px" prop="categoryName">
            <el-select v-model="form.categoryId" ref="selectTree" clearable>
              <el-option
                :value="form.categoryId"
                :label="form.categoryName"
                style="height: auto"
                hidden
              >
              </el-option>
              <el-tree
                ref="treeRef"
                :data="formTreeData"
                :props="defaultProps"
                :render-after-expand="true"
                :expand-on-click-node="false"
                node-key="id"
                default-expand-all
                @node-click="handleClickNode"
              />
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="12">
          <el-form-item
            label="是否显示发布人"
            label-width="120px"
            prop="showUser"
          >
            <el-radio-group v-model="form.showUser">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="正文类型" label-width="120px" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio label="0">富文本</el-radio>
              <el-radio label="1">纯文件（PDF）</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="封面图片" label-width="90px" prop="pic">
            <img
              style="width: 100px"
              v-if="addType == 'view'"
              :src="form.pic"
              alt=""
            />
            <file-upload v-model="form.pic" :fileUrls="form.pic"></file-upload>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item
            v-if="form.type == 0"
            label="正文："
            label-width="90px"
            prop="content"
          >
            <rich-text
              ref="skindeditor"
              :id="'s_kind_editor'"
              :content.sync="form.content"
              :readonlyFlag="addType == 'view' ? true : false"
              @input="setEidtSinfo"
            /> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item label="附件" label-width="90px" prop="">
            <file-upload-zujian
              ref="fjRef"
              :fileSize="200"
              v-model="fjList"
              :files="form.files"
              :addType="addType"
            ></file-upload-zujian> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item
            v-if="contentTypeDialog == '3'"
            label="上传封面："
            label-width="100px"
            prop="uu"
          >
            <div class="picBtn" @click="handleSelectPic">
              <div class="pic-up">
                <div class="pic-up-plus"></div>
                <!-- <i class="el-icon-plus pic-up-plus"></i> -->
              </div>
              <div class="pic-down">
                <span>选择图片</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="contentTypeDialog == '1'"
            label="上传视频："
            label-width="100px"
            prop="uum"
          >
            <el-button
              icon="el-icon-plus"
              type="primary"
              @click="handleSelectVideo"
              >选择视频</el-button
            >
          </el-form-item>
          <div :style="{ 'margin-left': '68px' }">
            <upload
              :fileList="fileList"
              :uploadVisible="uploadVisible"
            ></upload>
          </div>
        </el-col>
        <el-col :span="24">
          <div
            v-if="
              addType == 'view' &&
              form.auditRecordList &&
              form.auditRecordList.length > 0
            "
          >
            <div class="sh-jl r-title">审核记录</div>
            <!-- 操作类型 operateType ：0提交，1撤回，2审核通过，3驳回，4下线    -->
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in form.auditRecordList"
                :key="index"
                :timestamp="activity.updateTime"
                :color="activity.operateType == '3' ? '#F84F43' : '#0bbd87'"
                size="large"
                placement="top"
                :icon="
                  activity.operateType == '3'
                    ? 'el-icon-close'
                    : 'el-icon-check'
                "
              >
                <div class="line-block" v-if="index == 0">
                  <span class="sh-author">{{ activity.createBy }}</span>
                  <span>{{ activity.operateMemo }}</span>
                </div>
                <div class="line-block" v-else>
                  <span class="sh-author">{{ activity.updateBy }}</span>
                  <span class="sh-caozuo">{{ activity.operateMemo }}</span>
                  <span v-if="activity.reason">审核意见:</span>
                  <span> {{ activity.reason }}</span>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <div class="btn-content">
      <el-button @click="handleBack">取消</el-button>
      <el-button
        type="primary"
        v-if="addType != 'view'"
        @click="debounceHandleSubmit"
        >提交</el-button
      >
    </div>

    <pic-dialog
      :openType="openType"
      :fileList="fileArr"
      v-if="picFlag"
      @closePic="closePic"
      @handleData="picData"
    ></pic-dialog>
  </div>
</template>
<script>
import {
  addInfo,
  editObj,
  putObj,
  getArticleDetail,
  getPublishCategory,
} from "@/api/message/publish";
import RichText from "@/components/rich-text/index.vue";
import upload from "./upload.vue";
import picDialog from "./picDialog.vue";
import debounce from "@/util/debounce";
import FileUpload from "@/views/message/components/FileUpload.vue";

import FileUploadZujian from "@/components/upload/fileUpload.vue";
import { remote } from "@/api/admin/sys/sys-dict";
import TreeSelect from "@/components/TreeSelect";
import { fetchAllProvince, fetchChildren } from "@/api/admin/sys/area";
import { mapGetters } from "vuex";
import { getAllCitys } from "@/api/admin/sys/dept";
import { getDeptByAreaId } from "@/api/message/publish";
export default {
  name: "dialog",
  components: {
    RichText,
    upload,
    picDialog,
    FileUpload,
    FileUploadZujian,
    TreeSelect,
  },
  props: {
    contentTypeDialog: {
      type: String,
      default: function () {
        return "";
      },
    },
    // treeData: {
    //   type: Array,
    //   default: function () {
    //     return [];
    //   },
    // },
    formObj: {
      type: Object,
      default: function () {
        return {};
      },
    },
    // addType: {
    //   type: String,
    //   default: function () {
    //     return "";
    //   },
    // },
    editForm: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      treeData: [],
      title: "内容新增",
      file: [],
      nameList: [],
      fileList: [],
      headerObj: {},
      fileArr: [],
      openType: "",
      defaultProps: {
        /** 唯一标识 **/
        value: "id",
        /** 标签显示 **/
        label: "name",
        /** 子级 **/
        children: "children",
      },
      areaArr: [],
      pubDeptArr: [],
      form: {
        contentType: "",
        categoryName: "",
        articleTitle: "",
        content: "",
        categoryId: "",
        articleId: "",
        uu: "1",
        uum: "1",
        id: "",
        title: "",
        type: "0",
        showUser: "1",
        articleType: "",
        areaCode: "",
        areaName: "",
      },
      treeDeptTreeData: [],
      defaultProps2: {
        label: "areaName",
        value: "areaCode",
      },
      articleTypeArr: [],
      uploadVisible: false,
      picFlag: false,
      fjList: [],
      allTreeArr: [],
      addType: "",
      formTreeData: [],
      formId: "",
      rules: {
        categoryName: [
          { required: true, message: "请选择分组", trigger: "blur" },
        ],
        contentType: [
          { required: true, message: "请选择分类", trigger: "blur" },
        ],
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        uu: [{ required: true, message: "请上传图片", trigger: "blur" }],
        uum: [{ required: true, message: "请上传视频", trigger: "blur" }],
        areaCode: [
          { required: true, message: "请选择分类", trigger: "change" },
        ],
        // areaName: [{ required: true, message: "请补充发布站点", trigger: "blur" }],
      },
    };
  },
  computed: {
    token() {
      return this.$store.getters.access_token;
    },
    ...mapGetters(["userObj"]),
  },
  created() {
    this.handleGetSysData();
    this.handleGetTreeData();
    if (this.$route.query) {
      console.log("this.$route.query", this.$route.query);
      this.addType = this.$route.query.addType;
      this.formId = this.$route.query.id;
      if (this.$route.query.articleType) {
        // console.log("进来--1");
        this.form.articleType = this.$route.query.articleType;
      }
    }

    if (this.addType == "add") {
      this.form.categoryId = "";
      this.form.areaCode = this.$store.getters.dept.areaCode;
      this.form.pubDept = this.$store.getters.dept.deptName;
      this.form.pubDeptId = this.$store.getters.dept.id;
    }
    // console.log("formObj===", this.formObj);
  },
  mounted() {
    console.log("store", this.$store);

    // this.areaArr = [{ label: this.$store.getters.dept.areaName, value: this.$store.getters.dept.areaCode }];
    this.getAllCitysApi();
    // this.updateAreasOptions()
    // console.log("editForm=treeData=", this.treeData);
    // console.log("editForm==", this.userObj );

    // setTimeout(()=>{
    //     if(this.userObj && this.userObj.dept){
    //           this.form.areaName = this.userObj.dept.areaName;
    //           this.form.areaCode = this.userObj.dept.areaCode;
    //       }

    // },500)
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }

    this.handleData();
  },
  methods: {
    getAllCitysApi() {
      getAllCitys({ id: "410000000000" })
        .then((res) => {
          this.areaArr = [
            { areaName: "河南省", areaCode: "410000000000" },
            ...(res.data.data || []),
          ];
        })
        .catch((err) => {});
    },
    // async updateAreasOptions() {
    //   let res = await getDeptByAreaId({ areaId: this.$route.query.selectareaId })
    //   this.pubDeptArr = res.data.data;
    // },
    selfInput(val) {
      // this.form.parentId = val;
      // form.deptName
      // console.log("this.form.parentId==", this.form.parentId);
    },
    async getChildTree2(node, resolve) {
      // console.log("data--lxd-", node);
      if (node.level === 0) {
        this.rootNode = node;
        this.rootResolve = resolve;
        const root = await fetchAllProvince();
        if (root.data.code == 200) {
          this.treeDeptTreeData = root.data.data;
          // console.log("root==", root);
          // this.handleCheckChange(root.data.data[0]);
          return resolve(root.data.data);
        }
      } else if (node.level > 0) {
        const areaId = node.data.id;
        const child = await fetchChildren({ id: areaId });
        node.data.children = child.data.data;
        return resolve(child.data.data);
      }
    },
    handleGetTreeData() {
      let _this = this;
      getPublishCategory({
        areaCode:
          this.$store.getters.dept.areaCode ||
          window.localStorage.getItem("areaCodely"),
      }).then((res) => {
        console.log("treeDate==getPublishCategory200=2222", res);
        let data = res.data.data;
        _this.treeData = data;
        this.initForAllTree(_this.treeData);
        // console.log("_this.treeData=", _this.treeData);
        ////2024年4月29日 新增
        if (this.$route.query.articleType) {
          this.formTreeData = this.treeData.filter((item) => {
            return item.articleType == this.form.articleType;
          });
        } else {
          this.formTreeData = this.treeData;
        }

        ////2024年4月29日 新增

        setTimeout(() => {
          if (this.$route.query.categoryId) {
            this.form.categoryId = this.$route.query.categoryId;
            this.form.categoryName = this.$route.query.categoryName;

            console.log("this.form==lzs---", this.form);
          }
          if (this.form.categoryId) {
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(this.form.categoryId);
            });

            this.$forceUpdate();
          }
        }, 700);

        // _this.treeData.forEach((item) => {
        //   this.groupExpandedKeys.push(item.id);
        // });

        // groupExpandedKeys
      });
    },
    changeTypeFn() {
      // console.log("form.articleType==", this.form.articleType);
      // console.log("form.treeData==", this.treeData);
      this.form.categoryId = "";
      this.form.categoryName = "";
      // this.treeData
      this.formTreeData = this.treeData.filter((item) => {
        return item.articleType == this.form.articleType;
      });
    },
    handleGetSysData() {
      remote("portal_article_type").then((res) => {
        console.log("获取的数据字典==", res);
        if (res.data.code == 200) {
          this.articleTypeArr = res.data.data;
        }
      });
    },
    initForAllTree(arr) {
      if (arr && arr.length > 0) {
        arr.forEach((item) => {
          this.allTreeArr.push(item);
          if (item.children && item.children.length > 0) {
            this.initForAllTree(item.children);
          }
        });
      }
    },
    getArticleDetailFn() {
      getArticleDetail(this.formId).then((res) => {
        console.log("获取详情---", res);
        //
        if (res.data.code == 200) {
          let editForm = res.data.data;
          this.form = Object.assign({}, editForm);
          if (this.form.pic) {
            this.form.pic = "http://59.227.155.58:8068" + this.form.pic;
          }
          if (this.form.files) {
            this.$nextTick(() => {
              this.$refs.fjRef.initFilesList();
            });
          }
          console.log("更新后的pic", this.form.pic);

          this.formTreeData = this.treeData.filter((item) => {
            return item.articleType == this.form.articleType;
          });
          this.dealGetCategory();
        }
      });
    },
    dealGetCategory() {
      let fObj = this.allTreeArr.find((item) => {
        return item.id == this.form.categoryId;
      });
      console.log("this.form.allTreeArr==", this.allTreeArr);
      console.log("this.form.categoryId==", this.form.categoryId);
      console.log("fObj==", fObj);
      if (fObj) {
        this.form.categoryName = fObj.name;
      }
    },
    handleClickNode(data) {
      this.form.categoryId = data.id;
      this.form.categoryName = data.name;
      this.$refs.selectTree.blur();
      // 选择器执行完成后，使其失去焦点隐藏下拉框的效果
      // this.$refs.selectTree.blur();
    },
    setEidtSinfo(val) {
      this.form.content = val;
    },
    handleBack() {
      // this.$emit("closeDialog");
      this.$router.go(-1);
    },
    handleSelectPic() {
      this.fileArr = this.fileList;
      this.picFlag = true;
      this.openType = "pic";
    },
    handleSelectVideo() {
      this.fileArr = this.fileList;
      this.picFlag = true;
      this.openType = "video";
    },
    closePic() {
      this.picFlag = false;
    },
    picData(data) {
      this.fileList = data;
      this.uploadVisible = true;
    },
    handleData() {
      if (this.addType == "edit" || this.addType == "view") {
        // this.form = Object.assign({}, this.editForm);
        this.getArticleDetailFn();
        this.form.id = this.formId;
        this.title = "内容编辑";
        console.log("this.treeData==", this.treeData);
        setTimeout(() => {
          if (this.form.categoryId) {
            this.form.categoryName = this.$route.query.categoryName;
            this.$nextTick(() => {
              this.$refs.treeRef.setCurrentKey(this.form.categoryId);
            });

            this.$forceUpdate();
          }
        }, 500);
        // this.treeData.forEach((item) => {
        //   if (item.categoryId == this.editForm.categoryId) {
        //     this.form.categoryName = item.name;
        //   } else {
        //     if (item.child) {
        //       item.child.forEach((element) => {
        //         if (element.categoryId == this.editForm.categoryId) {
        //           this.form.categoryName = element.name;
        //         } else {
        //           if (element.child) {
        //             element.child.forEach((xqc) => {
        //               if (xqc.categoryId == this.editForm.categoryId) {
        //                 this.form.categoryName = xqc.name;
        //               }
        //             });
        //           }
        //         }
        //       });
        //     }
        //   }
        // });
        // this.form.categoryId = this.editForm.categoryId;
        // this.form.articleTitle = this.editForm.articleTitle;
        // this.form.content = this.editForm.content;
        // this.form.categoryName = this.editForm.categoryName;

        // this.$nextTick(() => {
        //   this.$refs.treeRef.setCurrentKey(this.form.categoryId);
        // });

        this.$forceUpdate();
      } else {
        setTimeout(() => {
          this.form.categoryId = this.formObj.id;
          this.form.categoryName = this.formObj.name;
          this.$nextTick(() => {
            this.$refs.treeRef.setCurrentKey(this.form.categoryId);
          });
          this.$forceUpdate();
        }, 500);
      }
    },
    debounceHandleSubmit: debounce(function () {
      this.handleSubmit();
    }, 500),
    handleSubmit() {
      console.log("fjList==222", this.fjList);
      //fileList
      if (this.fjList && this.fjList.length > 0) {
        let fileArr = [];
        this.fjList.forEach((item) => {
          let obj = {
            fileName: item.name,
            path: item.url,
            id: item.uid,
          };
          fileArr.push(obj);
        });
        this.form.fileList = fileArr;
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // this.form.contentType = "2";
          this.form.videoUrl = "";
          this.form.picUrl = "";
          if (this.addType == "edit") {
            this.form.id = this.formId;

            editObj(this.form).then((res) => {
              // console.log("res更新==", res);
              if (res.data.code == 200) {
                this.$message.success("更新成功");
                // this.$emit("closeDialog");
                this.$router.push("/message/publish/index");
              }
            });
          } else {
            addInfo(this.form).then((res) => {
              if (res.data.code == 200) {
                this.$message.success("提交成功");
                // this.$emit("closeDialog");
                this.$router.push("/message/publish/index");
              }
              // console.log("res");
            });
          }
          // if (this.form.type == 0 || this.form.type == 1) {
          // } else {
          //   let arr = [];
          //   if (this.fileList.length > 0) {
          //     this.fileList.forEach((item) => {
          //       if (item.url) {
          //         // item.url = item.url.substring(num1);
          //         arr.push(item.url);
          //       }
          //     });
          //     arr = arr.join(",");
          //     // if (
          //     //   this.form.contentType == "视频" ||
          //     //   this.form.contentType == "1"
          //     // ) {
          //     //   this.form.contentType = "1";
          //     //   this.form.videoUrl = arr;
          //     //   this.form.picUrl = "";
          //     // } else if (
          //     //   this.form.contentType == "图片" ||
          //     //   this.form.contentType == "3"
          //     // ) {
          //     //   this.form.contentType = "3";
          //     //   this.form.picUrl = arr;
          //     //   this.form.videoUrl = "";
          //     // } else if (this.form.contentType == "图文") {
          //     //   this.form.contentType = "2";
          //     //   this.form.videoUrl = "";
          //     //   this.form.picUrl = "";
          //     // }
          //     // if (this.addType == "edit") {
          //     //   this.form.articleId = this.editForm.articleId;
          //     //   // this.form.articleId = this.editForm.articleId;
          //     //   putObj(this.form).then((res) => {
          //     //     if (res.data.code == 200) {
          //     //       this.$message.success("更新成功");
          //     //       // this.$emit("closeDialog");
          //     //       this.$router.push("/publish/index");
          //     //     }
          //     //   });
          //     // } else {
          //     //   addInfo(this.form).then((res) => {
          //     //     if (res.data.code == 200) {
          //     //       this.$message.success("提交成功");
          //     //       // this.$emit("closeDialog");
          //     //       this.$router.push("/publish/index");
          //     //     }
          //     //   });
          //     // }
          //   } else {
          //     this.$message.error("请上传文件！");
          //   }
          // }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.news-form {
  // width: 1200px;
  // margin: 0 auto;
  width: calc(100% - 40px);
  background: #fff;
  border-radius: 8px;
  margin: 0 20px;
  border: 1px solid transparent;

  .my-form {
    margin-top: 20px;
    padding: 24px;
  }
}

::v-deep .el-upload-list__item .el-icon-close-tip {
  display: none !important;
}

::v-deep .el-upload-list__item.is-success .el-upload-list__item-status-label {
  display: none !important;
}

::v-deep .el-upload-dragger {
  width: 200px;
  height: 140px;
}

::v-deep.el-select {
  width: 240px;
}

.picBtn {
  width: 106px;
  height: 80px;
  border-radius: 4px;
  border: 1px dashed #3272ce;
  cursor: pointer;

  .pic-up {
    height: 40px;
    position: relative;

    .pic-up-plus {
      position: absolute;
      width: 30px;
      height: 30px;
      background: url("../../../../assets/img/picturePic.png") no-repeat;
      background-size: 100% 100%;
      left: 35px;
      top: 10px;
    }
  }

  .pic-down {
    text-align: center;
    height: 40px;
    font-size: 12px;
    font-weight: 500;
    color: #3272ce;
  }
}

.btn-content {
  text-align: right;
  margin-right: 30px;
}

.sh-jl {
  margin-top: 16px;
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}

.r-title {
  display: inline-block;
  width: 120px;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}
</style>
