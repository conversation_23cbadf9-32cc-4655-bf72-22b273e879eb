<template>
  <cust-dialog
    @closed="handlePicVisible"
    class="mydialog"
    :title="title"
    width="772px"
  >
    <div v-if="openType == 'pic'" class="main">
      <div class="item" v-for="(item, index) in picList" :key="index">
        <div class="item-pic">
          <img class="item-pic-pic" :src="item.url" />
          <div class="item-pic-check">
            <el-checkbox v-model="item.selectChecked"></el-checkbox>
          </div>
        </div>
        <div class="item-text">
          <span>{{ item.sourceName }}</span>
        </div>
      </div>
    </div>
    <div v-if="openType == 'video'" class="main">
      <div class="item" v-for="(item, index) in videoList" :key="index">
        <div class="item-pic">
          <video
            id="video"
            :src="item.url"
            controls="controls"
            muted
            style="height: 90%; width: 95%"
          ></video>
          <div class="item-pic-check">
            <el-checkbox v-model="item.selectChecked"></el-checkbox>
          </div>
        </div>
        <div class="item-text">
          <span>{{ item.sourceName }}</span>
        </div>
      </div>
    </div>
    <span slot="bottom-footer" class="dialog-footer">
      <el-button @click="handlePicVisible">取消</el-button>
      <el-button type="primary" @click="handleDebounceSubmit">确定</el-button>
    </span>
  </cust-dialog>
</template>
<script>
import { fetchList } from "@/api/message/material.js";
export default {
  name: "picDialog",
  props: {
    fileList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    openType: {
      type: String,
      default: function () {
        return "";
      },
    },
  },
  data() {
    return {
      title: "",
      picList: [],
      videoList: [],
      handleList: [],
    };
  },
  created() {
    this.handleData();
  },
  methods: {
    handlePicVisible() {
      this.$emit("closePic");
    },
    handleData() {
      if (this.openType == "pic") {
        this.title = "选择图片素材";
        let params = {
          current: 1,
          size: 100,
          sourceType: 1,
        };
        fetchList(params).then((res) => {
          this.handleList = res.data.data.records;
          console.log("handleList", this.handleList);
          console.log("fileList", this.fileList);
          this.fileList.forEach((item) => {
            this.handleList.forEach((element) => {
              if (item.url) {
                if (item.url == element.url) {
                  element.selectChecked = true;
                  this.$forceUpdate;
                }
              }
            });
          });
          this.picList = this.handleList;
        });
      } else if (this.openType == "video") {
        this.title = "选择视频素材";
        let params = {
          current: 1,
          size: 9,
          sourceType: 3,
        };
        fetchList(params).then((res) => {
          this.handleList = res.data.data.records;
          this.fileList.forEach((item) => {
            this.handleList.forEach((element) => {
              if (item.url) {
                if (item.url == element.url) {
                  element.selectChecked = true;
                  this.$forceUpdate;
                }
              }
            });
          });
          this.videoList = this.handleList;
        });
        this.$forceUpdate;
      }
    },
    handleDebounceSubmit() {
      let arr = [];
      if (this.openType == "pic") {
        this.picList.forEach((item) => {
          if (item.selectChecked == true) {
            arr.push({
              sourceId: item.sourceId,
              name: item.sourceName + ".jpg",
              url: item.url,
              selectChecked: item.selectChecked,
              createTime: item.createTime,
            });
          }
        });
      } else if (this.openType == "video") {
        this.videoList.forEach((item) => {
          if (item.selectChecked == true) {
            arr.push({
              sourceId: item.sourceId,
              name: item.sourceName + ".mp4",
              url: item.url,
              selectChecked: item.selectChecked,
              createTime: item.createTime,
            });
          }
        });
      }
      this.$emit("handleData", arr);
      this.$emit("closePic");
    },
  },
};
</script>
<style lang="scss" scoped>
.main {
  height: auto;
  width: 772px;
  margin-left: 10px;
  margin-right: 10px;
  display: flex;
  flex-flow: wrap;
  .item {
    margin-left: 14px;
    margin-right: 10px;
    width: 159px;
    height: 152px;
    .item-pic {
      height: 120px;
      position: relative;
      .item-pic-pic {
        width: 100%;
        height: 100%;
        background: #ebeef5;
      }
      .item-pic-check {
        position: absolute;
        left: 135px;
        bottom: 100px;
      }
    }
    .item-text {
      text-align: center;
      height: 32px;
      font-size: 12px;
      font-weight: 500;
      color: #22242c;
    }
  }
}
</style>
