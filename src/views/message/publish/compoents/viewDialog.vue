<template>
  <cust-dialog @closed="handleBack" class="mydialog" title="内容预览" width="721px">
    <div class="view">
      <div class="view-one">
        <div class="one-item">
          <div class="item-title">
            <span>分组</span>
          </div>
          <div class="item-content">
            <span :style="{ 'margin-left': '10px' }">{{
              viewObj.categoryName
            }}</span>
          </div>
        </div>
        <div class="one-item">
          <div class="item-title">
            <span>内容分类</span>
          </div>
          <div class="item-content">
            <span :style="{ 'margin-left': '10px' }">{{ form.type }}</span>
          </div>
        </div>
      </div>
      <div class="view-two">
        <div class="two-title">
          <span>标题</span>
        </div>
        <div class="two-content">
          <span :style="{ 'margin-left': '10px' }">{{ form.title }}</span>
        </div>
      </div>
      <div v-if="viewObj.contentType != '1'" class="view-three">
        <div class="three-title">
          <span>正文</span>
        </div>
        <div class="three-content">
          <div v-html="form.content" :style="{ 'margin-left': '10px', 'margin-top': '10px' }"></div>
        </div>
      </div>
      <div v-if="viewObj.contentType == '3'" class="view-three">
        <div class="three-title">
          <span>封面</span>
        </div>
        <div class="three-content">
          <upload :fileList="viewFileList" :openFlag="openType"></upload>
        </div>
      </div>
      <div v-if="viewObj.contentType == '1'" class="view-three">
        <div class="three-title">
          <span>视频</span>
        </div>
        <div class="three-content">
          <upload :fileList="viewFileList" :openFlag="openType"></upload>
        </div>
      </div>
    </div>
  </cust-dialog>
</template>
<script>
import upload from "./upload";
export default {
  name: "viewDialog",
  components: {
    upload,
  },
  props: {
    viewObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    treeData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },

  created() {
    this.handleData();
  },
  data() {
    return {
      size: "",
      form: { name: "", type: "", content: "", title: "", url: "" },
      viewFileList: [],
      openType: "view",
      categoryName: "",
    };
  },
  methods: {
    handleBack() {
      this.$emit("backDialog");
    },
    handleData() {
      console.log("this.viewObj==", this.viewObj);
      console.log("this.form==", this.form);
      console.log("this.treeData=w=", this.treeData);
      // this.treeData.forEach((item) => {
      //   if (item.child) {
      //     item.child.forEach((element) => {
      //       if (element.child) {
      //         element.child.forEach((xqc) => {
      //           if (xqc.id == this.viewObj.categoryId) {
      //             this.categoryName = xqc.name;
      //           }
      //         });
      //       }
      //     });
      //   }
      // });
      if (this.viewObj.contentType == 1) {
        this.form.type = "视频";
        this.form.url = this.viewObj.videoUrl;
      } else if (this.viewObj.contentType == 2) {
        this.form.type = "图文";
      } else if (this.viewObj.contentType == 3) {
        this.form.type = "图片";
        this.form.url = this.viewObj.picUrl;
      }
      this.form.name = this.viewObj.name;
      this.form.content = this.viewObj.content;
      this.form.title = this.viewObj.articleTitle;
      if (this.form.url.length > 0) {
        let arr = [];
        let arr1 = [];
        arr = this.form.url;
        arr.forEach((item) => {
          let index = item.lastIndexOf("/");
          let suffix = item.substr(index + 1);
          arr1.push({
            name: suffix,
            url: item,
          });
        });
        this.viewFileList = arr1;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.view {
  width: 657px;
  height: auto;
  border-left: 1px solid #dcdfe6;
  border-top: 1px solid #dcdfe6;
  margin-left: 20px;

  .view-one {
    height: 42px;
    display: flex;

    .one-item {
      width: 329px;
      display: flex;

      .item-title {
        width: 88px;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        line-height: 42px;
        text-align: center;
        background: #fafafa;
        border-right: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
      }

      .item-content {
        line-height: 42px;
        // text-align: center;
        // margin-left: 10px;
        width: 240px;
        font-size: 14px;
        font-weight: 500;
        color: #22242c;
        border-right: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
      }
    }
  }

  .view-two {
    height: 42px;
    display: flex;

    .two-title {
      width: 88px;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      text-align: center;
      line-height: 42px;
      background: #fafafa;
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;
    }

    .two-content {
      line-height: 42px;
      width: 569px;
      font-size: 14px;
      font-weight: 500;
      color: #22242c;
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;
    }
  }

  .view-three {
    min-height: 126px;
    display: flex;

    .three-title {
      width: 88px;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      text-align: center;
      line-height: 126px;
      background: #fafafa;
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;
    }

    .three-content {
      width: 569px;
      font-size: 14px;
      font-weight: 500;
      color: #22242c;
      border-right: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;
    }
  }
}
</style>
