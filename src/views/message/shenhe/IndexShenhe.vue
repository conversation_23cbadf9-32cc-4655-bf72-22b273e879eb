
<template>
  <div class="shen-container">
    <div class="new-content">
      <div class="nc-row">
        <span class="r-title">内容类型</span
        ><span class="r-value">
          {{ getArticleTypeFn(form.articleType) }}
        </span>
      </div>
      <div class="nc-row">
        <span class="r-title">栏目</span
        ><span class="r-value">
          {{ form.categoryNameList | nameArrToLevel }}
        </span>
      </div>
      <div class="nc-row">
        <span class="r-title">新闻摘要</span
        ><span class="r-value">
          {{ form.memo }}
        </span>
      </div>
      <div class="nc-row">
        <span class="r-title" style="align-self: flex-start;">封面图</span
        ><span class="r-value">
         <el-image
          v-if="form.pic"
          style="width: 78px; height: 78px"
          :src="form.pic ? $getUrlByProcess(form.pic) : ''"
          fit=""></el-image>
        </span>
      </div>
      <div class="nc-row">
        <span class="r-title">审核内容</span><span class="r-value"> </span>
      </div>
      <div class="nc-content">
        <div class="nt-title">{{ form.title }}</div>
        <div class="desc_info">
          <span>时间：{{ form.createTime }}</span>
          <span>发布部门：{{ form.source }}</span>
          <span v-if="form.showUser == 1">发布人：{{ form.createBy }}</span>

          <!-- <span>阅读：{{ form.viewNum }}</span> -->
        </div>
        <div
          class="detail"
          v-loading="loading"
          element-loading-text="正在加载中..."
        >
          <div v-if="!form.content">
            <pdf
              v-for="(item, index) in totalNumber"
              :key="index + 1"
              :page="index + 1"
              :src="pdfObj.path ? $getUrlByProcess(pdfObj.path) : ''"
            ></pdf>
          </div>
          <div v-else ref="content" class="content" v-html="form.content" />
        </div>
      </div>
      <div class="nc-row" v-if="type == 'shenhe'">
        <span class="r-title">审核意见</span>
        <div class="r-value">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入审核内容"
            v-model="shenheyijian"
            style="width: 100%"
            maxlength="100"
            show-word-limit
          >
          </el-input>
        </div>
      </div>
      <div v-if="type == 'shenhe'" class="tip-content">
        注意：驳回时请务必填写审核意见
      </div>
      <div v-if="type == 'view'">
        <div class="sh-jl r-title">审核记录</div>
        <el-timeline>
          <!-- 操作类型 operateType ：0提交，1撤回，2审核通过，3驳回，4下线    -->
          <el-timeline-item
            v-for="(activity, index) in form.auditRecordList"
            :key="index"
            :timestamp="activity.updateTime"
            :color="activity.operateType == '3' ? '#F84F43' : '#0bbd87'"
            size="large"
            placement="top"
            :icon="
              activity.operateType == '3' ? 'el-icon-close' : 'el-icon-check'
            "
          >
            <div class="line-block" v-if="index == 0">
              <span class="sh-author">{{ activity.createBy }}</span>
              <span>{{ activity.operateMemo }}</span>
            </div>
            <div class="line-block" v-else>
              <span class="sh-author">{{ activity.updateBy }}</span>
              <span class="sh-caozuo">{{ activity.operateMemo }}</span>
              <span v-if="activity.reason">审核意见:</span>
              <span> {{ activity.reason }}</span>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div class="bth-content">
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="handleAgree" v-if="type == 'shenhe'"
          >同意</el-button
        >
        <el-button type="danger" @click="handleBohui" v-if="type == 'shenhe'"
          >驳回</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import { getArticleDetail } from "@/api/message/publish";
import { remote } from "@/api/admin/sys/sys-dict";
import pdf from "vue-pdf";

import { shenhePass, shenheReject } from "@/api/message/shenhe";
export default {
  components: { pdf },
  data() {
    return {
      form: {},
      formId: "",
      articleTypeArr: [],
      shenheyijian: "",
      type: "",
      pdfObj: null,
      fileList:[],
      totalNumber:0,
      loading: false,
    };
  },
  created() {
    if (this.$route.query) {
      this.formId = this.$route.query.id;
      this.type = this.$route.query.type;
      this.getArticleDetailFn();
      this.handleGetSysData();
    }
  },
  methods: {
    handleGetSysData() {
      remote("portal_article_type").then((res) => {
        console.log("获取的数据字典==", res);
        if (res.data.code == 200) {
          this.articleTypeArr = res.data.data;
        }
      });
    },
    getArticleTypeFn(type) {
      let value = "";
      if (type || type == 0) {
        let fObj = this.articleTypeArr.find((item) => {
          return item.dictValue == type;
        });
        if (fObj) {
          value = fObj.dictLabel;
        }
        return value;
      }
    },
    getArticleDetailFn() {
      getArticleDetail(this.formId).then((res) => {
        console.log("获取详情---", res);
        //
        // this.loading = true;
        if (res.data.code == 200) {
          let editForm = res.data.data;
          this.form = Object.assign({}, editForm);
          this.shenheyijian = this.form.refuseReason;
          this.fileList = res.data.data.files
          ? JSON.parse(res.data.data.files)
          : [];
          for (let i = 0; i < this.fileList.length; i++) {
            let fileType = this.fileList[i].path
              .substring(this.fileList[i].path.lastIndexOf("."))
              .toLowerCase();
            console.log("fileType==", fileType);
            if (fileType == ".pdf") {
              this.pdfObj = this.fileList[i];
              break;
            }
          }
          if (!this.form.content) {
            this.getNumPages();
          } else {
            this.loading = false;
          }
          //   this.formTreeData = this.treeData.filter((item) => {
          //     return item.articleType == this.form.articleType;
          //   });
          //   this.dealGetCategory();
        }
      });
    },
    getNumPages() {
      if (this.pdfObj) {
        let url = this.$getUrlByProcess(this.pdfObj.path);
        // console.log("url==", url);
        let loadingTask = pdf.createLoadingTask(url);
        let that = this;
        loadingTask.promise
          .then((pdf) => {
            // console.log("pdf.numPages==", pdf.numPages);
            that.totalNumber = pdf.numPages;
            setTimeout(() => {
              this.loading = false;
            }, 800);
          })
          .catch((err) => {
            // console.error("pdf 加载失败", err);
          });
      } else {
        this.loading = false;
      }
    },
    goBack() {
      this.$router.replace({
        path:'/message/shenhe/index',
        query:{
          activeTab:'second'
        }
      })
    },
    handleAgree() {
      let params = {
        id: this.formId,
        msg: this.shenheyijian,
      };
      shenhePass(params).then((res) => {
        console.log("params==", res);
        if (res.data.code == 200) {
          this.$message.success("审核成功");

          this.$router.push("/message/shenhe/index");
        }
      });
    },
    handleBohui() {
      if (!this.shenheyijian) {
        this.$message({
          showClose: true,
          message: "请输入审核意见",
          type: "warning",
        });
        return;
      }
      let params = {
        id: this.formId,
        msg: this.shenheyijian,
      };
      shenheReject(params).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("驳回成功");

          this.$router.push("/message/shenhe/index");
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.shen-container {
  position: relative;
  background: #fff;
  width: calc(100% - 40px);
  margin: 20px auto;
  border-radius: 4px;
  padding: 20px;
  .new-content {
    position: relative;
    width: 1200px;
    margin: 0 auto;
    .nc-row {
      margin-top: 10px;
      display: flex;
      align-items: center;
      .r-title {
        display: inline-block;
        width: 120px;
        color: #333;
        font-size: 16px;
        font-weight: bold;
      }
      .r-value {
        width: calc(100% - 120px);
      }
    }
    .nc-content {
      width: 100%;
      height: 500px;
      overflow-y: scroll;
      border: 1px solid #c0c4cc;
      border-radius: 4px;
      padding: 20px;
      margin-top: 20px;
      .nt-title {
        color: #333;
        font-size: 22px;
        text-align: center;
        margin-top: 10px;
        font-weight: bold;
      }
      .desc_info {
        width: 100%;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin-bottom: 20px;

        & span:not(:first-child) {
          margin-left: 37px;
        }

        & span:last-child {
          span {
            cursor: pointer;
            margin: 0;

            &:not(:first-child) {
              margin-left: 10px;
            }
          }
        }
      }
    }
    .tip-content {
      margin-top: 10px;
      font-size: 12px;
      color: #888;
    }
    .bth-content {
      margin-top: 20px;
      text-align: center;
    }
  }
  .line-block {
    min-height: 50px;
    .sh-author {
      color: #333;
      font-size: 16px;
      margin-right: 16px;
    }
    .sh-caozuo {
      color: #3272ce;
      margin-right: 16px;
    }
  }
  .sh-jl {
    margin-top: 16px;
    margin-bottom: 16px;
    color: #333;
    font-size: 16px;
  }
}
</style>
