<template>
  <div class="execution">
    <basic-container>
      <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="待审核" name="first"></el-tab-pane>
          <el-tab-pane label="已审核" name="second"></el-tab-pane>
        </el-tabs>
      </div>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="rowDel"
      >
        <template slot-scope="scope" slot="menu">
          <el-button
            icon="el-icon-view"
            class="none-border"
            type="text"
            size="small"
            style="color: #3272ce"
            @click="handleOpenView(scope.row)"
            ><span class="word-style">查看</span></el-button
          >
          <el-button
            icon="el-icon-set-up"
            class="none-border"
            type="text"
            size="small"
            style="color: #3272ce"
            @click="handleOpenShenhe(scope.row)"
            v-if="activeName == 'first'"
            ><span class="word-style">审核</span></el-button
          >
          <!-- <el-button
            icon="el-icon-bottom-left"
            class="none-border"
            type="text"
            size="small"
            style="color: #3272ce"
            @click="handleCallback(scope.row)"
            v-if="activeName == 'first'"
            ><span class="word-style">撤回</span></el-button
          > -->
          <el-button
            icon="el-icon-bottom"
            class="none-border"
            type="text"
            size="small"
            style="color: #3272ce"
            @click="handleDownLineFn(scope.row)"
            v-if="activeName == 'second' && scope.row.pubStatus == '2'"
            ><span class="word-style">下线</span></el-button
          >
        </template>
        <template slot="categoryId" slot-scope="scope">
          <span v-if="scope.row.categoryNameList">{{
            scope.row.categoryNameList | nameArrToLevel
          }}</span>
        </template>
        <template slot="pubStatus" slot-scope="scope">
          <el-tag v-if="scope.row.pubStatus == '0'">待提交</el-tag>
          <el-tag v-else-if="scope.row.pubStatus == '1'" type="info"
            >待审核</el-tag
          >
          <el-tag v-else-if="scope.row.pubStatus == '2'" type="success"
            >已发布</el-tag
          >
          <el-tag v-else-if="scope.row.pubStatus == '-1'" type="danger"
            >已驳回</el-tag
          >
          <el-tag v-else-if="scope.row.pubStatus == '-2'" type="warning"
            >已下线</el-tag
          >
        </template>
        <template slot="updateBy" slot-scope="scope">
          <span v-if="scope.row.auditRecordList">
            {{ scope.row.auditRecordList.updateBy }}
          </span>
        </template>
        <template slot="updateTime" slot-scope="scope">
          <span v-if="scope.row.auditRecordList">
            {{ scope.row.auditRecordList.updateTime }}
          </span>
        </template>

        <template slot="pubStatusSearch" slot-scope="scope">
          <el-select
            v-model="searchSelfForm.pubStatus"
            placeholder="请选择审核状态"
            v-if="activeName == 'first'"
            clearable
          >
            <el-option label="待审核" :value="1"> </el-option>
          </el-select>
          <el-select
            v-model="searchSelfForm.pubStatus"
            v-else-if="activeName == 'second'"
            placeholder="请选择审核状态"
            clearable
          >
            <el-option label="已发布" :value="2"> </el-option>
            <el-option label="已驳回" :value="-1"> </el-option>
            <el-option label="已下线" :value="-2"> </el-option>
          </el-select>
        </template>
        <template slot="createTimeSearch" slot-scope="scope">
          <el-date-picker
            v-model="searchTimeArr"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
import {
  fetchList,
  putObj,
  delObj,
  shenheCallback,
  shenheDownline,
} from "@/api/message/shenhe";
import { tableOption } from "@/const/crud/message/shenhe";
import { mapGetters } from "vuex";

export default {
  name: "shenheIndex",
  data() {
    return {
      searchForm: {
        createTimeStart: "",
        createTimeEnd: "",
      },
      tableData: [],
      searchTimeArr: [],
      searchSelfForm: {
        pubStatus: "",
      },
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      activeName: "first",
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.news_shenhe_add, false),
        delBtn: this.vaildData(this.permissions.news_shenhe_del, false),
        editBtn: this.vaildData(this.permissions.news_shenhe_edit, false),
      };
    },
  },
  mounted() {
    if (this.$route.query.activeTab) {
      this.activeName = this.$route.query.activeTab;
      this.handleClick();
    }
  },
  methods: {
    handleOpenView(row) {
      // console.log("row==", row);
      this.$router.push({
        path: "/message/shenhe/IndexShenhe",
        query: {
          id: row.id,
          type: "view",
          activeTab: this.activeName,
        },
      });
    },
    handleOpenShenhe(row) {
      // console.log("row==", row);
      this.$router.push({
        path: "/message/shenhe/IndexShenhe",
        query: {
          id: row.id,
          type: "shenhe",
        },
      });
    },

    handleCallback(row) {
      console.log("row==", row);
      let params = {
        id: row.id,
      };
      this.$confirm("是否确认撤回标题为“" + row.title + "”的数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return shenheCallback(params);
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("撤回成功");
            this.getList(this.page);
          }
        });

      // shenheCallback(params).then((res) => {
      //   console.log("撤回==", res);
      //   if (res.data.code == 200) {
      //     this.$message.success("撤回成功");
      //     this.getList(this.page);
      //   }
      // });
    },
    //下线
    handleDownLineFn(row) {
      console.log("row==", row);
      let params = {
        id: row.id,
      };
      this.$confirm("是否确认下线标题为“" + row.title + "”的数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return shenheDownline(params);
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.$message.success("下线成功");
            this.getList(this.page);
          }
        });

      // shenheCallback(params).then((res) => {
      //   console.log("撤回==", res);
      //   if (res.data.code == 200) {
      //     this.$message.success("撤回成功");
      //     this.getList(this.page);
      //   }
      // });
    },
    handleClick(tab, event) {
      this.searchSelfForm.pubStatus = "";
      this.page.currentPage = 1;
      this.getList(this.page);
    },
    getList(page, params) {
      if (this.searchTimeArr && this.searchTimeArr.length > 0) {
        this.searchForm.createTimeStart = this.searchTimeArr[0];
        this.searchForm.createTimeEnd = this.searchTimeArr[1];
      }
      this.searchForm.pubStatus = this.searchSelfForm.pubStatus;

      this.tableLoading = true;
      let includeStatusArr = [];
      if (this.activeName == "first") {
        includeStatusArr = [1];
      } else if (this.activeName == "second") {
        includeStatusArr = [-2, -1, 2]; //0 待提交，去掉
      }

      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            includeStatus: includeStatusArr.toString(),
          },
          params,
          this.searchForm
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel: function (row, index) {
      this.$confirm("是否确认删除ID为" + row.id, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((data) => {
          this.$message.success("修改成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((data) => {
          this.$message.success("添加成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    searchChange(form, done) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
  },
};
</script>