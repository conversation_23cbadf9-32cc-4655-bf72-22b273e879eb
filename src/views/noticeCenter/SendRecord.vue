<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        :before-open="handleBeforeOpen"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="rowDel"
        v-model="form"
        :search.sync="searchForm"
      >
        <!-- <template slot="title" slot-scope="{ row }">

          <span class="title-content" @click="handleView(row)">{{
            row.title
          }}</span>

        </template> -->
        <template slot="sendTimeSearch" slot-scope="scope">
          <el-date-picker
            v-model="searchForm.searchTime"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="选择日期"
            :default-time="['00:00:00', '23:59:59']"
            @change="handleChangeDate(searchForm.searchTime)"
          >
          </el-date-picker>
        </template>
      </avue-crud>
    </basic-container>
    <!-- <notice-portal-view-dialog
      v-if="viewFlag"
      @closeDialog="closeViewDialog"
      :viewObj="viewObj"
    ></notice-portal-view-dialog> -->
  </div>
</template>

<script>
import { fetchList } from "@/api/noticeCenter/sendRecord";
import { tableOption } from "@/const/crud/noticeCenter/sendRecord";
import { mapGetters } from "vuex";
import RichText from "@/components/rich-text/index.vue";
import NoticePortalViewDialog from "@/views/noticePortal/compontents/NoticePortalViewDialog.vue";

export default {
  name: "noticePortal",
  components: {
    RichText,
    NoticePortalViewDialog,
  },
  data() {
    return {
      searchForm: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      form: {
        attachmentList: [],
      },
      headerObj: {},
      fileList: [],
      fileArr: [],
      fileObj: "",
      viewFlag: false,
      viewObj: {},
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.noticePortal_add, false),
        delBtn: this.vaildData(this.permissions.noticePortal_delete, false),
        editBtn: this.vaildData(this.permissions.noticePortale_edit, false),
      };
    },
    token() {
      return this.$store.getters.access_token;
    },
  },
  created() {
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  methods: {
    handleChangeDate(date) {
      console.log("date", date);
      if (date == null || date == []) {
        this.searchForm.startTime = "";
        this.searchForm.endTime = "";
      } else {
        this.searchForm.startTime = date[0] + " 00:00:00";
        this.searchForm.endTime = date[1] + " 23:59:59";
      }
      console.log("date", this.page);
    },
    handleEdit(row, index) {
      // this.$refs.crud.visible = true;
      this.$refs.crud.rowEdit(row, index);
    },
    getList(page, params) {
      this.tableLoading = true;
      if (params) {
        params["searchTime"] = null
      }
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            status: 1,
          },
          params,
          this.searchForm
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
          this.tableData.forEach((item) => {
            item.status = +item.status == 1;
          });
          //
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel: function (row, index) {
      this.$confirm("是否确认删除" + row.title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
    handleUpdate: function (row, index, done, loading) {
      row.status = +row.status;
      console.log("this.fileArr=handleUpdate=", this.fileArr);
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }
      putObj(row)
        .then((data) => {
          this.$message.success("修改成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleSave: function (row, done, loading) {
      row.status = +row.status;
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }

      addObj(row)
        .then((data) => {
          this.$message.success("添加成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    searchChange(form, done) {
      this.searchForm = form;
      if (form.createTime) {
        if (form.createTime[0]) {
          this.searchForm['startTime'] = form.createTime[0] + ' 00:00:00';
        }
        if (form.createTime[1]) {
          this.searchForm['endTime'] = form.createTime[1] + ' 23:59:59';
        }
      }
      this.searchForm["searchTime"] = null
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
    beforeAvatarUpload(file) {
      console.log("file==", file);
      let lastType = file.name.substr(file.name.lastIndexOf(".") + 1);
      const isJPG =
        file.type === "image/jpeg" ||
        file.type == "image/jpg" ||
        file.type == "image/png" ||
        file.type == "application/pdf" ||
        lastType == "doc" ||
        lastType == "docx" ||
        lastType == "ppt" ||
        lastType == "pptx" ||
        lastType == "xlsx" ||
        lastType == "xls";
      const isLt2M = file.size / 1024 / 1024 < 100;

      if (!isJPG) {
        this.$message.error("文件类型错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 100MB!");
      }
      return isJPG && isLt2M;
    },
    // 文件上传成功后
    handleUploadDocSuccess(res, file, fileList) {
      // console.log("file==", file);
      console.log("res==", res);
      // res.data.fileType
      if (res.code == 200) {
        this.fileObj = res.data;
        let obj = {
          name: file.name,
          url: window.location.protocol + "//" + window.location.host + res.data.url,
        };
        this.fileList.push(obj);

        // 保存到后端的数据不需要加domain
        let urlObj = {
          fileName: file.name,
          url: res.data.url,
          fileType: res.data.fileType,
        };
        this.fileArr.push(urlObj);
        console.log("this.fileArr==", this.fileArr);
      } else {
        console.log("fileList==", fileList);
        fileList.splice(fileList.length - 1, 1);
        this.$message({
          showClose: true,
          message: res.msg,
          type: "warning",
        });
      }
    },
    handleUploadDocError(res, file) {
      console.log("res==", res);
    },
    handlePreview(file) {
      console.log("file==", file);
      // let url = window.location.protocol + "//" + window.location.host + file.response.data.url;
      window.open(file.url, "target");
      // window.location.href = url;
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      let arr = [];
      this.fileArr.forEach((item) => {
        // 在文件列表中查找是否有被删除的文件，有的话则踢出，没有的话则重新放入新数组
        if (file.url.indexOf(item.url) === -1) {
          arr.push(item);
        }
      });
      this.fileArr = arr;
    },
    // hanleSwitchChange(row) {
    //   row.status = +row.status;
    //   let params = {
    //     status: row.status,
    //     id: row.id,
    //     title: row.title,
    //   };
    //   updateStatus(params).then((res) => {
    //     // console.log("res=修改状态=", res);
    //     if (res.data.code == 200) {
    //       this.getList(this.page);
    //     }
    //   });

    // },
    handleBeforeOpen(done) {
      this.fileList = [];
      this.fileArr = [];
      if (this.form.attachmentList && this.form.attachmentList.length > 0) {
        this.form.attachmentList.forEach((file) => {
          if (file.url) {
            let obj = {
              name: file.fileName,
              url: window.location.protocol + "//" + window.location.host + file.url,
            };
            this.fileList.push(obj);
            let urlObj = {
              fileName: file.fileName,
              url: file.url,
              fileType: file.fileType,
            };
            this.fileArr.push(urlObj);
          }
        });
      }

      done();
    },
    handleView(row) {
      console.log("--row=预览=", row);
      this.viewFlag = true;
      this.viewObj = row;
    },
    closeViewDialog() {
      this.viewFlag = false;
    },
    setSinfo(val) {
      // console.log("val", val);
      this.form.content = val;
      // console.log("form", this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
// /deep/ .el-table th.el-table__cell {
//   text-align: center;
// }
.title-content {
  color: #327fff;
  cursor: pointer;
}
</style>
