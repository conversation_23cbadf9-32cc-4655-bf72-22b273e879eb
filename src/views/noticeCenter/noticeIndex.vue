<template>
  <div>
    <!-- <page-notice-center></page-notice-center> -->
    <page-more-info></page-more-info>
  </div>
</template>
<script>
import pageNoticeCenter from "@/page/noticeCenter/index";
import pageMoreInfo from "@/page/noticeCenter/moreInfo";

export default {
  components: {
    pageNoticeCenter,
    pageMoreInfo,
  },
  mounted() {
    let dom = document.querySelector(".echart-container");
    // console.log("dom---", dom);
    if (dom) {
      dom.scrollTop = 0;
    }
  },
};
</script>