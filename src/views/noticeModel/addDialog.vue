<template>
  <cust-dialog :title="title" @closed="handleCloseDialog">
    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-position="left"
      :disabled="type == 'view'"
    >
      <el-form-item
        label="模板编码："
        v-if="type == 'view'"
        label-width="100px"
        prop="templateName"
      >
        <el-input
          v-model="form.templateCode"
          :style="{ width: '50%' }"
          placeholder="请输入模板名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="模板名称：" label-width="100px" prop="templateName">
        <el-input
          v-model="form.templateName"
          :style="{ width: '50%' }"
          placeholder="请输入模板名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="消息类型：" label-width="100px" prop="messageType">
        <el-select
          v-model="form.messageType"
          placeholder="请选择推送类型"
          style="width: 25%"
        >
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="通知渠道：" label-width="100px" prop="sendChannel">
        <el-radio-group
          @change="handleChangeSendChannel"
          v-model="form.sendChannel"
        >
          <el-radio :label="3">短信</el-radio>
          <el-radio :label="2">微信公众号</el-radio>
          <el-radio :label="1">站内消息</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="form.sendChannel == 3"
        label="模板code："
        label-width="100px"
      >
        <el-input v-model="form.messageCode1" placeholder="请输入"></el-input>
      </el-form-item>
      <div v-if="form.sendChannel == 2">
        <el-form-item label="模板code：" label-width="100px" prop="messageCode">
          <el-input
            v-model="form.messageCode"
            placeholder="请输入模板code"
          ></el-input>
        </el-form-item>
      </div>

      <div v-if="form.sendChannel == 1">
        <el-form-item
          label="模板标题："
          label-width="100px"
          prop="messageTitle"
        >
          <el-input
            v-model="form.messageTitle"
            placeholder="请输入模板标题"
          ></el-input>
        </el-form-item>
      </div>
      <div>
        <el-form-item
          label="模板内容："
          label-width="100px"
          prop="messageContent"
        >
          <el-input
            v-model="form.messageContent"
            type="textarea"
            placeholder="请输入模板内容（支持多行输入）"
            :rows="3"
            maxlength="250"
            show-word-limit
          ></el-input>
        </el-form-item>
      </div>

      <div v-if="form.sendChannel == 2">
        <div>
          <el-form-item
            label="模板跳转类型："
            label-width="120px"
            prop="messageJumpType"
          >
            <el-radio-group
              v-model="form.messageJumpType"
              @change="handleChangeMessageJumpType"
            >
              <el-radio v-model="form.messageJumpType" :label="0"
                >不跳转</el-radio
              >
              <el-radio v-model="form.messageJumpType" :label="1"
                >跳转至网页</el-radio
              >
              <el-radio v-model="form.messageJumpType" :label="2"
                >跳转至小程序</el-radio
              >
            </el-radio-group> </el-form-item
          ><el-form-item
            v-if="form.messageJumpType == '1'"
            label="模板跳转地址："
            label-width="120px"
            prop="messageJumpUrl"
          >
            <el-input
              v-model="form.messageJumpUrl"
              placeholder="请输入以http://或https://开头的跳转地址"
            ></el-input>
          </el-form-item>
          <div v-if="form.messageJumpType == '2'">
            <el-form-item prop="appId" label="APPID：" label-width="110px">
              <el-input
                v-model="form.appId"
                placeholder="请输入APPID"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="PAGEPATH："
              label-width="110px"
              prop="pagePath"
            >
              <el-input
                v-model="form.pagePath"
                placeholder="请输入PAGEPATH"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- <div class="info"></div> form.pagePath form.messageJumpAppUrl -->
      </div>
      <div v-if="form.sendChannel == '1'">
        <!-- <el-form-item
          label="模板内容："
          label-width="100px"
          prop="messageContent"
        >
          <el-input
            v-model="form.messageContent"
            type="textarea"
            placeholder="请输入模板内容（支持多行输入）"
          ></el-input>
        </el-form-item> -->
        <el-form-item
          label="跳转类型："
          label-width="100px"
          prop="messageJumpType"
        >
          <el-radio-group v-model="form.messageJumpType">
            <el-radio :label="0">不跳转</el-radio>
            <el-radio :label="1">跳转至网页</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="form.messageJumpType == '1'">
          <el-form-item
            label="模板跳转地址（pc）："
            label-width="180px"
            prop="messageJumpUrl"
          >
            <el-input
              v-model="form.messageJumpUrl"
              type="textarea"
              placeholder="请输入以http://或https://开头的跳转地址"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="模板跳转地址（移动端）："
            label-width="200px"
            prop="messageJumpAppUrl"
          >
            <el-input
              v-model="form.messageJumpAppUrl"
              type="textarea"
              placeholder="请输入以http://或https://开头的跳转地址"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注：" label-width="100px" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注（支持多行输入）"
          maxlength="250"
          :rows="3"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="tab-title" v-if="dataObjArr.length > 0">
      <div class="icon"></div>
      <div class="title">参数列表</div>
    </div>

    <div v-for="(item, i) in dataObjArr" :key="i">
      <div class="cs-content">
        <div class="cs-label">{{ item.key }}</div>
        <el-input
          type="text"
          v-model="item.value"
          :disabled="type == 'view'"
          placeholder="请输入参数说明"
        ></el-input>
      </div>
    </div>
    <div
      slot="bottom-footer"
      style="display: flex; flex-direction: row-reverse"
    >
      <el-button
        type="primary"
        v-if="type != 'view'"
        @click="submit('ruleForm')"
        >提交</el-button
      >
      <div style="width: 10px"></div>
      <el-button @click="handleCloseDialog">取消</el-button>
    </div>
  </cust-dialog>
</template>
<script>
import { addObj, putObj } from "@/api/noticePortal/noticeModel";
export default {
  props: {
    type: {
      type: String,
      default: function () {
        return "";
      },
    },
    row: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  watch: {
    "form.messageContent": {
      handler: function (newVal, oldVal) {
        console.log("11111111111111", newVal);
        // this.dataArr = this.getInfo(newVal, "{{", "}}");
        this.handleGetText(newVal);
      },
    },
  },

  data() {
    var validateUrl = (rule, value, callback) => {
      var reg = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
      console.log("8888-899--", value);
      // if(!reg.test(url)){
      // alert("这网址不是以http://https://开头，或者不是网址！");
      // }
      if (value == undefined || value == "" || (value && value.length == 0)) {
        callback(new Error("请填写模板跳转地址"));
      } else if (!reg.test(value)) {
        callback(new Error("请输入以http://或https://开头的完整网址"));
      } else {
        callback();
      }
    };
    var validateMessageCode = (rule, value, callback) => {
      console.log("校验-validateMessageCode-");
      if (value == undefined || value == "") {
        callback(new Error("请填写模板code"));
      } else {
        callback();
      }
    };

    return {
      title: "",
      form: {
        messageContent: "",
        templateName: "",
        messageCode: "",
        messageCode1: "",
        remark: "",
        messageJumpUrl: "",
        appId: "",
        pagePath: "",
        messageTitle: "",
        messageTitleNew: "",
        messageType: "",
        sendChannel: 3,
        messageJumpType: 0,
        messageJumpAppUrl: "",
      },
      dataArr: [],

      dataObj: {},
      dataObjArr: [],
      dataObjArrBak: [],
      typeList: [
        {
          label: "预警提醒",
          value: 1,
        },
        {
          label: "待办事项",
          value: 0,
        },
        {
          label: "其他消息",
          value: 2,
        },
      ],
      rules: {
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
        ],
        messageType: [
          { required: true, message: "请选择消息类型", trigger: "change" },
        ],
        sendChannel: [
          {
            required: true,
            message: "请选择通知渠道",
            trigger: "change",
          },
        ],
        messageCode: [
          {
            required: true,
            message: "请输入模板code",
            trigger: "blur",
          },
          { validator: validateMessageCode, trigger: "blur" },
        ],
        messageContent: [
          {
            required: true,
            message: "请输入模板内容",
            trigger: "blur",
          },
        ],
        remark: [
          { required: false, message: "请选择活动资源", trigger: "blur" },
        ],
        messageJumpType: [
          { required: true, message: "请填写模板跳转地址", trigger: "blur" },
        ],
        // messageJumpUrl: [
        //   { required: true, message: "请填写跳转地址（PC）", trigger: "blur" },
        // ],
        messageJumpUrl: [
          { required: true, message: "请填写模板跳转地址", trigger: "blur" },
          { validator: validateUrl, trigger: "blur" },
        ],
        appId: [{ required: true, message: "请填写appId", trigger: "blur" }],
        pagePath: [
          { required: true, message: "请填写PAGEPATH", trigger: "blur" },
        ],
        messageTitle: [
          { required: true, message: "请填写模板标题", trigger: "blur" },
        ],
        messageJumpAppUrl: [
          {
            required: true,
            message: "请填写跳转地址（移动端）",
            trigger: "blur",
          },
          { validator: validateUrl, trigger: "blur" },
        ],
      },
    };
  },
  created() {
    // let str =
    //   "	{{first.DATA}} 姓名：{{keyword1.DATA}} 上班时间：{{keyword2.DATA}} {{remark.DATA}}";
    // let result = this.getInfo(str, "{{", "}}");
    // console.log("result======", result);
  },
  mounted() {
    this.handleSetData();
    console.log("type==", this.type);
    console.log("row==", this.row);
    this.type == "edit"
      ? (this.title = "编辑消息模板")
      : this.type == "add"
      ? (this.title = "新增消息模板")
      : (this.title = "查看消息模板");

    if (
      (this.type == "edit" || this.type == "view") &&
      this.row.messageContent
    ) {
      this.handleGetText(this.row.messageContent);
    }

    setTimeout(() => {
      this.handleDealTextVal();
    }, 50);
  },
  methods: {
    //获取括号之间的内容 expr 可能是 @{obj.name}--@{obj.age}
    handleGetText(expr) {
      // this.dataArr = [];
      // this.dataObj = {};

      this.dataObjArr = [];
      // let dataObjArr = []

      if (expr.indexOf("${") !== -1) {
        //
        expr.replace(/\$\{(.+?)\}/g, (...args) => {
          this.dataArr.push(args[1]);
          this.dataObj[args[1]] = "";
          // this.dataObjVal[args[1]] = "dataObj." + args[1];
          let findex = this.dataObjArr.findIndex((item) => {
            return item.key == args[1];
          });
          if (findex == -1) {
            let obj = {
              key: args[1],
              value: "",
            };

            this.dataObjArr.push(obj);
          } else {
            // this.dataObjArr[findex] = this.dataObjArrBak[findex];
          }
        });
        // console.log("val=325=", this.dataArr);
      }
      // this.dataObjArrBak = JSON.stringify(this.dataObjArr);
      this.handleDealTextVal();
    },
    handleDealTextVal() {
      if (this.row.messageParams) {
        let obj = JSON.parse(this.row.messageParams);
        this.dataObjArr.forEach((item) => {
          item.value = obj[item.key];
        });
      }
      this.dataObjArrBak = JSON.stringify(this.dataObjArr);
    },
    // 获取值的方法
    getVal(expr) {
      return expr.split(".").reduce((data, currentVal) => {
        return data[currentVal];
      });
    },

    getInfo(string, sub1, sub2) {
      this.results = [];
      this.string = string;
      this.getAllResults(sub1, sub2);
      return this.results;
    },
    getAllResults(sub1, sub2) {
      // first check to see if we do have both substrings
      if (this.string.indexOf(sub1) < 0 || this.string.indexOf(sub2) < 0)
        return;

      // find one result
      var result = this.getFromBetween(sub1, sub2);
      // push it to the results array
      this.results.push(result);
      // remove the most recently found one from the string
      this.removeFromBetween(sub1, sub2);

      // if there's more substrings
      if (this.string.indexOf(sub1) > -1 && this.string.indexOf(sub2) > -1) {
        this.getAllResults(sub1, sub2);
      } else return;
    },
    removeFromBetween(sub1, sub2) {
      if (this.string.indexOf(sub1) < 0 || this.string.indexOf(sub2) < 0)
        return false;
      var removal = sub1 + this.getFromBetween(sub1, sub2) + sub2;
      this.string = this.string.replace(removal, "");
    },
    getFromBetween(sub1, sub2) {
      if (this.string.indexOf(sub1) < 0 || this.string.indexOf(sub2) < 0)
        return false;
      var SP = this.string.indexOf(sub1) + sub1.length;
      var string1 = this.string.substr(0, SP);
      var string2 = this.string.substr(SP);
      var TP = string1.length + string2.indexOf(sub2);
      return this.string.substring(SP, TP);
    },
    handleSetData() {
      if (this.type == "edit" || this.type == "view") {
        // this.form =  this.row;
        Object.assign(this.form, this.row);
        if (this.form.messageJumpType == 2) {
          this.form.pagePath = this.form.messageJumpAppUrl;
        }
        if (this.form.sendChannel == 3) {
          this.form.messageCode1 = this.form.messageCode;
        }
        // if (this.form.sendChannel == "站内消息") {
        //   this.form.sendChannel = "2";
        // } else if (this.form.sendChannel == "微信公众号") {
        //   this.form.sendChannel = "1";
        // } else if (this.form.sendChannel == "短信") {
        //   this.form.sendChannel = "0";
        // }
        // if (this.form.messageJumpType == "不跳转") {
        //   this.form.messageJumpType = "0";
        // } else if (this.form.messageJumpType == "跳转至网页") {
        //   this.form.messageJumpType = "1";
        // } else if (this.form.messageJumpType == "跳转至小程序") {
        //   this.form.messageJumpType = "2";
        // }
      }
    },
    handleChangeSendChannel(value) {
      // this.form = {
      //   templateName: this.form.templateName,
      //   messageCode: "",
      //   // messageContent: "",
      //   remark: "",
      //   messageJumpUrl: "",
      //   messageJumpAppUrl: "",
      //   appId: "",
      //   messageTitle: "",
      //   messageType: this.form.messageType,
      //   sendChannel: value,
      //   messageJumpType: "1",
      // };
      this.$forceUpdate();
    },
    handleChangeMessageJumpType() {
      this.form.messageJumpAppUrl = "";
      this.form.messageJumpUrl = "";
      this.form.appId = "";
      this.form.pagePath = "";
    },
    handleCloseDialog() {
      this.$emit("close");
    },
    submit(formName) {
      console.log("dataObj==", this.dataObjArr);
      if (this.dataObjArr && this.dataObjArr.length > 0) {
        let nparams = {};
        this.dataObjArr.forEach((item) => {
          nparams[item.key] = item.value;
        });
        console.log("parmas==", nparams);
        this.form.messageParams = JSON.stringify(nparams);
      }
      if (this.form.sendChannel == 3) {
        this.form.messageJumpType = "";
        this.form.appId = "";
        this.form.messageJumpAppUrl = "";
      }

      // this.$refs[formName].validateField("messageCode", (val) => {
      //   console.log("val--448===-", val);
      // });
      if (this.form.sendChannel == 3) {
        this.form.messageCode = this.form.messageCode1;
      }
      // if (this.form.sendChannel == 2 && !this.form.messageCode) {
      //   // this.$message.warning("请输入模板code");
      //   this.$message({
      //     showClose: true,
      //     message: "请输入模板code",
      //     type: "error",
      //   });
      //   return;
      // }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = this.form;
          if (this.form.messageType == "待办事项") {
            params.messageType = 0;
          } else if (this.form.messageType == "预警提醒") {
            params.messageType = 1;
          } else if (this.form.messageType == "其他消息") {
            params.messageType = 2;
          }
          console.log("params==", params);
          if (params.status) {
            params.status = 0;
          } else {
            params.status = 1;
          }
          // form.pagePath form.messageJumpAppUrl
          if (this.form.messageJumpType == 2) {
            this.form.messageJumpAppUrl = this.form.pagePath;
          }
          if (this.type == "add") {
            // params.status = 1;
            addObj(params).then((res) => {
              if (res.data.code == 200) {
                this.$emit("close");
              }
              2;
            });
          } else if (this.type == "edit") {
            putObj(params).then((res) => {
              if (res.data.code == 200) {
                this.$emit("close");
              }
            });
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.info {
  margin-left: 50px;
  width: 40%;
  background-color: #dae3f0;
  margin-bottom: 18px;
}
.tab-title {
  height: 44px;
  width: 100%;
  display: flex;
  .icon {
    height: 18px;
    width: 6px;
    background-color: #3272ce;
    display: inline-block;
    border-radius: 4px;
  }
  .title {
    margin-left: 15px;
    line-height: 18px;
    font-size: 14px;
    font-weight: 700;
    color: #22242c;
  }
}
.cs-content {
  display: flex;
  margin-top: 10px;
  .cs-label {
    width: 120px;
    font-size: 14px;
    color: #222;
    font-weight: bold;
  }
}
</style>
