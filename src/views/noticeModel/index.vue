<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        :before-open="handleBeforeOpen"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="rowDel"
        v-model="form"
      >
        <template slot-scope="scope" slot="menuLeft">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="small"
            @click="handleAdd"
            >新增</el-button
          >
        </template>
        <template slot="status" slot-scope="{ row }">
          <!-- {{ row.status }} -->
          <el-switch
            v-model="row.status"
            @change="hanleSwitchChange(row)"
            open-label="是"
            close-label="否"
          ></el-switch>
        </template>
        <template slot="menu" slot-scope="{ row, index }">
          <el-button
            type="text"
            icon="el-icon-document"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="text"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, index)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            size="small"
            @click="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </avue-crud>
    </basic-container>
    <!-- <notice-portal-view-dialog
      v-if="viewFlag"
      @closeDialog="closeViewDialog"
      :viewObj="viewObj"
    ></notice-portal-view-dialog> -->
    <addDialog
      :type="type"
      :row="row"
      v-if="addVisible"
      @close="handleClose"
    ></addDialog>
    <viewDialog v-if="viewVisible" :row="row" @close="handleClose"></viewDialog>
  </div>
</template>

<script>
import { fetchList, putObj, delObj } from "@/api/noticePortal/noticeModel";
import { tableOption } from "@/const/crud/noticePortal/noticeModel";
import { mapGetters } from "vuex";
import RichText from "@/components/rich-text/index.vue";
import addDialog from "./addDialog.vue";
import viewDialog from "./viewDialog.vue";
export default {
  name: "noticePortal",
  components: {
    RichText,
    viewDialog,
    addDialog,
  },
  data() {
    return {
      searchForm: {},
      addVisible: false,
      viewVisible: false,
      type: "",
      tableData: [
        {
          date1: 4396,
        },
      ],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      form: {
        attachmentList: [],
      },
      headerObj: {},
      fileList: [],
      fileArr: [],
      fileObj: "",
      viewFlag: false,
      viewObj: {},
      row: {},
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.admin_sysnotice_add, false),
        delBtn: this.vaildData(this.permissions.admin_sysnotice_del, false),
        editBtn: this.vaildData(this.permissions.admin_sysnotice_edit, false),
      };
    },
    token() {
      return this.$store.getters.access_token;
    },
  },
  created() {
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  methods: {
    handleAdd() {
      this.type = "add";
      this.addVisible = true;
    },
    handleView(row) {
      //查看
      this.row = row;
      // this.viewVisible = true;
      this.addVisible = true;
      this.type = "view";
      console.log("查看");
      // console.log("viewVisible=====", this.viewVisible);
    },
    handleClose() {
      this.addVisible = false;
      this.viewVisible = false;
      this.getList(this.page);
    },
    handleEdit(row, index) {
      this.type = "edit";
      this.addVisible = true;
      this.row = row;
      // this.$refs.crud.visible = true;
      // this.$refs.crud.rowEdit(row, index);
    },
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
          },
          params,
          this.searchForm
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
          this.tableData.forEach((item) => {
            item.status = +item.status == 0;
          });
          //
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel: function (row, index) {
      this.$confirm("是否确认删除" + row.title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
    handleUpdate: function (row, index, done, loading) {
      row.status = +row.status;
      console.log("this.fileArr=handleUpdate=", this.fileArr);
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }
      putObj(row)
        .then((data) => {
          this.$message.success("修改成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleSave: function (row, done, loading) {
      row.status = +row.status;
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }

      addObj(row)
        .then((data) => {
          this.$message.success("添加成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleDelete(row) {
      this.$confirm("是否确认删除" + " ?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          let params = { id: row.id };
          return delObj(params);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    searchChange(form, done) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
    beforeAvatarUpload(file) {
      console.log("file==", file);
      let lastType = file.name.substr(file.name.lastIndexOf(".") + 1);
      const isJPG =
        file.type === "image/jpeg" ||
        file.type == "image/jpg" ||
        file.type == "image/png" ||
        file.type == "application/pdf" ||
        lastType == "doc" ||
        lastType == "docx" ||
        lastType == "ppt" ||
        lastType == "pptx" ||
        lastType == "xlsx" ||
        lastType == "xls";
      const isLt2M = file.size / 1024 / 1024 < 100;

      if (!isJPG) {
        this.$message.error("文件类型错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 100MB!");
      }
      return isJPG && isLt2M;
    },
    // 文件上传成功后
    handleUploadDocSuccess(res, file, fileList) {
      // console.log("file==", file);
      console.log("res==", res);
      // res.data.fileType
      if (res.code == 200) {
        this.fileObj = res.data;
        let obj = {
          name: file.name,
          url: process.env.VUE_APP_PIC_URL + res.data.url,
        };
        this.fileList.push(obj);

        // 保存到后端的数据不需要加domain
        let urlObj = {
          fileName: file.name,
          url: res.data.url,
          fileType: res.data.fileType,
        };
        this.fileArr.push(urlObj);
        console.log("this.fileArr==", this.fileArr);
      } else {
        console.log("fileList==", fileList);
        fileList.splice(fileList.length - 1, 1);
        this.$message({
          showClose: true,
          message: res.msg,
          type: "warning",
        });
      }
    },
    handleUploadDocError(res, file) {
      console.log("res==", res);
    },
    handlePreview(file) {
      console.log("file==", file);
      // let url = process.env.VUE_APP_PIC_URL + file.response.data.url;
      window.open(file.url, "target");
      // window.location.href = url;
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      let arr = [];
      this.fileArr.forEach((item) => {
        // 在文件列表中查找是否有被删除的文件，有的话则踢出，没有的话则重新放入新数组
        if (file.url.indexOf(item.url) === -1) {
          arr.push(item);
        }
      });
      this.fileArr = arr;
    },
    hanleSwitchChange(row) {
      // console.log("row.status==", row.status);
      //根据后端要求，0是 1否 2023年5月9日
      // row.status = +row.status;

      let params = JSON.parse(JSON.stringify(row));
      params.status = +!row.status;
      putObj(params).then((res) => {
        // console.log("res=修改状态=", res);
        if (res.data.code == 200) {
          this.getList(this.page);
        }
      });

      // if (row.enableStatus) {
      //   this.$message.success("启用成功!");
      // } else {
      //   this.$message.success("禁用成功!");
      // }
    },
    handleBeforeOpen(done) {
      this.fileList = [];
      this.fileArr = [];
      if (this.form.attachmentList && this.form.attachmentList.length > 0) {
        this.form.attachmentList.forEach((file) => {
          if (file.url) {
            let obj = {
              name: file.fileName,
              url: process.env.VUE_APP_PIC_URL + file.url,
            };
            this.fileList.push(obj);
            let urlObj = {
              fileName: file.fileName,
              url: file.url,
              fileType: file.fileType,
            };
            this.fileArr.push(urlObj);
          }
        });
      }

      done();
    },

    closeViewDialog() {
      this.viewFlag = false;
    },
    setSinfo(val) {
      // console.log("val", val);
      this.form.content = val;
      // console.log("form", this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
// /deep/ .el-table th.el-table__cell {
//   text-align: center;
// }
</style>
