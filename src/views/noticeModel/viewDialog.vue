<template>
  <cust-dialog title="查看模板" @closed="handleCloseDialog">
    <el-form>
      <el-form-item label="模板ID： ">
        <el-input
          disabled
          v-model="form.templateCode"
          :style="{ width: '50%' }"
        ></el-input>
      </el-form-item>
      <el-form-item label="模板名称：">
        <el-input
          disabled
          v-model="form.templateName"
          :style="{ width: '50%' }"
        ></el-input>
      </el-form-item>
      <el-form-item label="消息类型：">
        <el-input
          disabled
          v-model="form.messageType"
          :style="{ width: '50%' }"
        ></el-input>
      </el-form-item>
      <el-form-item label="通知渠道：">
        <el-radio-group v-model="form.sendChannel" disabled>
          <el-radio v-model="form.sendChannel" label="0">短信</el-radio>
          <el-radio v-model="form.sendChannel" label="1"
            >微信公众号</el-radio
          >
          <el-radio v-model="form.sendChannel" label="2">站内消息</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="模板内容：" label-width="90px">
        <el-input
          disabled
          v-model="form.messageContent"
          type="textarea"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注：" label-width="90px">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注（支持多行输入）"
          disabled
        ></el-input>
      </el-form-item>
    </el-form>
  </cust-dialog>
</template>
<script>
export default {
  props: {
    row: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      form: {
        templateCode: "",
        templateName: "",
        messageType: "",
        sendChannel: "",
        messageContent: "",
        remark: "",
      },
    };
  },
  created() {
    this.handleSetData();
  },
  methods: {
    handleSetData() {
      if (this.row.sendChannel == "站内消息") {
        this.row.sendChannel = "2";
      } else if (this.row.sendChannel == "微信公众号") {
        this.row.sendChannel = "1";
      } else if (this.row.sendChannel == "短信") {
        this.row.sendChannel = "0";
      }
      this.form = this.row;
    },
    handleCloseDialog() {
      this.$emit("close");
    },
  },
};
</script>