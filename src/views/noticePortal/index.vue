<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        :before-open="handleBeforeOpen"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="rowDel"
        v-model="form"
      >
        <!-- <template slot="menuLeft">
          <el-button type="primary" @click="handlePublish" size="small"
            >发布公告</el-button
          >
        </template> -->
        <template slot="contentForm">
          <rich-text
            ref="skindeditor"
            :id="'s_kind_editor'"
            :content.sync="form.content"
            @input="setSinfo"
          />
        </template>
        <template slot="attachmentListForm">
          <el-upload
            class="upload-demo"
            action="/portal-api/storage/upload"
            :on-success="handleUploadDocSuccess"
            :on-error="handleUploadDocError"
            :on-preview="handlePreview"
            multiple
            :limit="10"
            :headers="headerObj"
            :file-list="fileList"
            :on-remove="handleRemove"
            :before-upload="beforeAvatarUpload"
            accept=".jpg,.jpeg,.png,.pdf,.PDF,.text,.doc,.docx,.xls,.ppt"
          >
            <!-- -->
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              文件格式支持jpg、png、jpeg、pdf、txt、doc、docx、xls、ppt格式，文件不超过100M。
            </div>
          </el-upload>
        </template>
        <template slot="visibleRangeForm" slot-scope="scope">
          <avue-radio
            v-model="form.visibleRange"
            :dic="radioDicArr"
          ></avue-radio>
          <!-- <avue-input-tree
            v-if="scope.value == 2"
            v-model="form.partDeptIdListStr"
            :node-click="loadNode"

          ></avue-input-tree> -->

          <tree-select
            v-if="scope.value == 1"
            v-model="form.partDeptIdListStr"
            :data="treeDeptDataArr"
            :showTitle="form.partDeptStrListStr"
            :props="deptProps"
            :loadNode="treeLoad"
            :selfSearchPage="selfSearchPage"
            placeholder="请选择，输入组织名称、编码或规范简称可搜索"
            @selfSearch="selfSearch"
            @selfInput="selfInput"
            @selfCurrentChange="selfCurrentChange"
            pageFlag
            filterable
            multiple
            slefFilter
            lazy
            ref="treeSelectRef"
          ></tree-select>
          <avue-input-tree
            v-else-if="scope.value == 2"
            v-model="form.partAppList"
            placeholder="请选择应用"
            :multiple="true"
            :dic="treeAppDataArr"
            :props="appProps"
          ></avue-input-tree>
        </template>

        <template slot="status" slot-scope="{ row }">
          <!-- {{ row.status }} -->
          <el-switch
            v-model="row.status"
            @change="hanleSwitchChange(row)"
            open-label="是"
            close-label="否"
          ></el-switch>
        </template>
        <template slot="content" slot-scope="{ row }">
          <div class="row-content">{{ row.content }}</div>
        </template>
        <template slot="menu" slot-scope="{ row, index }">
          <el-button
            type="text"
            icon="el-icon-document"
            @click="handleView(row)"
          >
            预览
          </el-button>
          <el-button
            type="text"
            v-if="!row.status && permissions.update_notice"
            icon="el-icon-edit-outline"
            @click="handleEdit(row, index)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            v-if="permissions.del_notice"
            icon="el-icon-delete"
            size="small"
            @click="$refs.crud.rowDel(row, index)"
            >删除</el-button
          >
        </template>
      </avue-crud>
    </basic-container>
    <notice-portal-view-dialog
      v-if="viewFlag"
      @closeDialog="closeViewDialog"
      :viewObj="viewObj"
    ></notice-portal-view-dialog>
  </div>
</template>

<script>
let _this = this;
import {
  fetchList,
  getObj,
  addObj,
  putObj,
  delObj,
  updateStatus,
} from "@/api/noticePortal/noticePortal";
import { tableOption } from "@/const/crud/noticePortal/noticePortal";
import { mapGetters } from "vuex";
import RichText from "@/components/rich-text/index.vue";
import NoticePortalViewDialog from "./compontents/NoticePortalViewDialog.vue";
import {
  canUseNetDeptTree,
  canUseDeptSonTree,
  searchDeptByName,
} from "@/api/admin/sys/dept";
import { getClientGroupTree } from "@/api/portal/portal.js";
import TreeSelect from "@/components/TreeSelect";
import debounce from "@/util/debounce";

export default {
  name: "noticePortal",
  components: {
    RichText,
    NoticePortalViewDialog,
    TreeSelect,
  },
  data() {
    return {
      radio: "0",
      searchForm: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      form: {
        attachmentList: [],
      },
      headerObj: {},
      fileList: [],
      fileArr: [],
      fileObj: "",
      viewFlag: false,
      viewObj: {},
      radioDicArr: [],
      deptProps: {
        label: "deptName",
        value: "id",
      },
      appProps: {
        label: "clientName",
        value: "clientId",
      },
      treeDeptData: [],
      treeDeptDataBase: [],
      treeDeptDataArr: [],
      treeAppDataArr: [],
      groupExpandedKeys: [],
      searchPage: {
        current: 1,
        size: 10,
        total: 0,
      },
      selfSearchPage: {
        current: 1,
        size: 10,
        total: 0,
      },
      searchFlag: false,
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.create_notice, false),
        delBtn: this.vaildData(this.permissions.del_notice, false),
        editBtn: this.vaildData(this.permissions.update_notice, false),
      };
    },
    token() {
      return this.$store.getters.access_token;
    },
  },
  watch: {
    deptSearch(val) {
      if (val && val.length > 0) {
        this.searchFlag = true;
      } else {
        this.searchFlag = false;
      }
      // this.$refs.deptTreeRef.filter(val);
      this.handleSearchDept(name, true);
    },
    "form.partDeptIdListStr": {
      handler(val, oldVal) {
        console.log("val==", val);
        console.log("val=treeDeptData=", this.treeDeptData);
        // this.form.deptId = val;
      },
    },
  },
  created() {
    if (this.token) {
      this.headerObj["Authorization"] = "Bearer " + this.token;
    }
  },
  methods: {
    debounceSearchDept: debounce(function (name) {
      if (name) {
        this.handleSearchDept(name);
      } else {
        this.treeDeptDataBase = JSON.parse(JSON.stringify(this.treeDeptData));
      }
    }, 500),
    handleSearchDept(name, searchFlag) {
      let params = {
        keyWord: name,
      };
      if (searchFlag) {
        params.current = this.searchPage.current;
        params.size = this.searchPage.size;
      }
      searchDeptByName(params).then((res) => {
        // console.log("res==搜索记过=", res);
        if (res.data.code == 200) {
          this.treeDeptDataBase = res.data.data.records;
          this.searchPage.total = res.data.data.total;
        }
      });
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchPage.size = val;
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchPage.current = val;
      this.debounceSearchDept(this.deptSearch);
    },
    selfCurrentChange(val, keyWord) {
      // console.log("val==", val);
      // console.log("keyWord==", keyWord);
      this.selfSearchPage.current = val;
      this.debounceSearchDept2(keyWord);
    },
    selfSearch(val) {
      // console.log("val-33--", val);
      this.debounceSearchDept2(val);
    },
    selfInput(val) {
      this.form.deptId = val;
      // console.log("this.form.deptId==", this.form.deptId);
    },
    debounceSearchDept2: debounce(function (name) {
      if (name) {
        this.handleSearchDept2(name);
      } else {
        this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
      }
    }, 500),
    handleSearchDept2(name) {
      let params = {
        keyWord: name,
      };
      params.current = this.selfSearchPage.current;
      params.size = this.selfSearchPage.size;
      searchDeptByName(params).then((res) => {
        // console.log("res==搜索记过=", res);
        if (res.data.code == 200) {
          this.treeDeptDataArr = res.data.data.records;
          this.selfSearchPage.tatal = res.data.data.total;
        }
      });
    },
    // treeLoad: async (node, resolve) => {
    //上面使用箭头函数，this不能使用 2024年5月8日
    async treeLoad(node, resolve) {
      // console.log("treeLoad=js---=", node);
      console.log("treeLoad=js--测试6-=", this);
      if (node.level === 0) {
        let params = { status: 1 };
        let res1 = await canUseNetDeptTree(params);

        setTimeout(() => {
          this.$refs.treeSelectRef.reloadVal();
        }, 500);

        return resolve(res1.data.data);
      } else if (node.level > 0) {
        // console.log("else---js-");
        const child = await canUseDeptSonTree({ deptId: node.data.id });
        // console.log("child==", child);
        node.data.children = child.data.data;

        setTimeout(() => {
          // console.log("rrr==测试5=");
          this.$refs.treeSelectRef.reloadVal();
        }, 500);
        return resolve(child.data.data);
      }
    },
    handleEdit(row, index) {
      // this.$refs.crud.visible = true;
      getObj(row.id).then((response) => {
        row = this.form = response.data.data;
        //按组织机构可见，拼接回显
        if (row.visibleRange == 1) {
          row.partDeptIdListStr =
            row.partDeptIdList && row.partDeptIdList.join(",");
          row.partDeptStrListStr =
            row.partDeptNameList && row.partDeptNameList.join(",");
        }
        this.$refs.crud.rowEdit(row, index);
      });
    },
    handlePublish(row) {
      this.$refs.crud.rowEdit(row);
    },
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
          },
          params,
          this.searchForm
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
          this.tableData.forEach((item) => {
            item.status = +item.status == 1;
          });
          //
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel: function (row, index) {
      this.$confirm("是否确认删除" + row.title, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
    handleUpdate: function (row, index, done, loading) {
      if (row.visibleRange == 1 && !row.partDeptIdListStr) {
        this.$message.error("请选择可见组织机构");
        loading();
        return;
      }
      if (
        row.visibleRange == 2 &&
        !(row.partAppList && row.partAppList.length)
      ) {
        this.$message.error("请选择可见应用");
        loading();
        return;
      }
      if (row.partDeptIdListStr) {
        row.partDeptIdList = row.partDeptIdListStr.split(",");
      }
      row.status = +row.status;
      // console.log("this.fileArr=handleUpdate=", this.fileArr);
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }
      putObj(row)
        .then((data) => {
          this.$message.success("修改成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleSave: function (row, done, loading) {
      if (row.visibleRange == 1 && !row.partDeptIdListStr) {
        this.$message.error("请选择可见组织机构");
        loading();
        return;
      }
      if (
        row.visibleRange == 2 &&
        !(row.partAppList && row.partAppList.length)
      ) {
        this.$message.error("请选择可见应用");
        loading();
        return;
      }
      if (row.partDeptIdListStr) {
        row.partDeptIdList = row.partDeptIdListStr.split(",");
      }
      row.status = +row.status;
      if (this.fileArr && this.fileArr.length > 0) {
        row.attachmentList = this.fileArr;
      }
      addObj(row)
        .then((data) => {
          this.$message.success("添加成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    searchChange(form, done) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
    beforeAvatarUpload(file) {
      console.log("file==", file);
      let lastType = file.name.substr(file.name.lastIndexOf(".") + 1);
      const isJPG =
        file.type === "image/jpeg" ||
        file.type == "image/jpg" ||
        file.type == "image/png" ||
        file.type == "application/pdf" ||
        lastType == "doc" ||
        lastType == "docx" ||
        lastType == "ppt" ||
        lastType == "pptx" ||
        lastType == "xlsx" ||
        lastType == "xls";
      const isLt2M = file.size / 1024 / 1024 < 100;

      if (!isJPG) {
        this.$message.error("文件类型错误!");
      }
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 100MB!");
      }
      return isJPG && isLt2M;
    },
    // 文件上传成功后
    handleUploadDocSuccess(res, file, fileList) {
      // console.log("file==", file);
      console.log("res==", res);
      // res.data.fileType
      if (res.code == 200) {
        this.fileObj = res.data;
        let obj = {
          name: file.name,
          url:
            window.location.protocol +
            "//" +
            window.location.host +
            res.data.uri,
        };
        this.fileList.push(obj);

        // 保存到后端的数据不需要加domain
        let urlObj = {
          fileName: file.name,
          url: res.data.uri,
          fileType: res.data.fileType,
        };
        this.fileArr.push(urlObj);
        console.log("this.fileArr==", this.fileArr);
      } else {
        console.log("fileList==", fileList);
        fileList.splice(fileList.length - 1, 1);
        this.$message({
          showClose: true,
          message: res.msg,
          type: "warning",
        });
      }
    },
    handleUploadDocError(res, file) {
      console.log("res==", res);
    },
    handlePreview(file) {
      console.log("file==", file);
      // let url = window.location.protocol + "//" + window.location.host + file.response.data.url;
      window.open(file.url, "target");
      // window.location.href = url;
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      let arr = [];
      this.fileArr.forEach((item) => {
        // 在文件列表中查找是否有被删除的文件，有的话则踢出，没有的话则重新放入新数组
        if (file.url.indexOf(item.url) === -1) {
          arr.push(item);
        }
      });
      this.fileArr = arr;
    },
    hanleSwitchChange(row) {
      row.status = +row.status;
      let params = {
        status: row.status,
        id: row.id,
        title: row.title,
      };
      updateStatus(params).then((res) => {
        // console.log("res=修改状态=", res);
        if (res.data.code == 200) {
          this.getList(this.page);
        }
      });

      // if (row.enableStatus) {
      //   this.$message.success("启用成功!");
      // } else {
      //   this.$message.success("禁用成功!");
      // }
    },
    getDialogData() {
      //有数据直接返回，避免重复请求
      if (this.treeDeptDataArr.length && this.treeAppDataArr.length) {
        return;
      }
      // 查询部门树
      let params = {
        status: 1,
      };
      this.radioDicArr = [
        {
          label: "全部",
          value: 0,
        },
      ];

      canUseNetDeptTree(params)
        .then((response) => {
          this.treeDeptData = response.data.data;
          this.treeDeptData.forEach((item, i) => {
            this.groupExpandedKeys.push(item.id);
          });
          if (this.treeDeptData && this.treeDeptData.length > 0) {
            for (let index = 0; index < this.treeDeptData.length; index++) {
              this.treeDeptData[index].level = "root";
            }
            this.radioDicArr.push({
              label: "按组织机构",
              value: 1,
            });
          }
          this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
          this.treeDeptDataBase = JSON.parse(JSON.stringify(this.treeDeptData));
          return getClientGroupTree();
        })
        .catch(() => {
          return getClientGroupTree();
        })
        .then((response) => {
          this.treeAppDataArr = response.data.data;
          if (this.treeAppDataArr && this.treeAppDataArr.length > 0) {
            for (let index = 0; index < this.treeAppDataArr.length; index++) {
              const obj = this.treeAppDataArr[index];
              obj.children = obj.clientResList;
              obj.clientName = obj.groupName;
            }
            this.radioDicArr.push({
              label: "按应用",
              value: 2,
            });
          }
        });
      if (this.form.attachmentList && this.form.attachmentList.length > 0) {
        this.form.attachmentList.forEach((file) => {
          if (file.url) {
            let obj = {
              name: file.fileName,
              url:
                window.location.protocol +
                "//" +
                window.location.host +
                file.url,
            };
            this.fileList.push(obj);
            let urlObj = {
              fileName: file.fileName,
              url: file.url,
              fileType: file.fileType,
            };
            this.fileArr.push(urlObj);
          }
        });
      }
    },
    handleBeforeOpen(done) {
      this.fileList = [];
      this.fileArr = [];
      this.getDialogData();
      done();
    },
    handleView(row) {
      // console.log("--row=预览=", row);
      this.viewFlag = true;
      this.viewObj = row;
    },
    closeViewDialog() {
      this.viewFlag = false;
    },
    setSinfo(val) {
      // console.log("val", val);
      this.form.content = val;
      // console.log("form", this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
// /deep/ .el-table th.el-table__cell {
//   text-align: center;
// }
.row-content {
  // width: 300px;
  height: 42px;
  line-height: 42px;
  white-space: nowrap; /*不允许换行*/
  text-overflow: ellipsis; /*省略号*/
  overflow: hidden; /*超出部分隐藏*/
}
</style>
