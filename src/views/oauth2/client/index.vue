<template>
  <basic-container>
    <div class="avue-crud">
      <!-- 搜索工作栏 -->
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="应用名" prop="clientName">
          <el-input
            v-model="queryParams.clientName"
            placeholder="请输入应用名"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="clientStatus">
          <el-select
            v-model="queryParams.clientStatus"
            placeholder="请选择状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in this.getDictDataByType(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-edit"
            class="filter-item"
            size="mini"
            @click="handleAdd"
            v-if="['system:oauth2-client:create']"
            >新增
          </el-button>
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <!-- 列表 -->
      <el-table v-loading="loading" :data="list">
        <el-table-column label="客户端编号" align="center" prop="clientId" />
        <el-table-column
          label="客户端密钥"
          align="center"
          prop="clientSecret"
        />
        <el-table-column label="应用名" align="center" prop="clientName" />
        <el-table-column label="应用图标" align="center" prop="clientLogo">
          <template v-slot="scope">
            <img
              v-if="scope.row.clientLogo"
              width="40px"
              height="40px"
              :src="
                scope.row.clientLogo.includes('http://') ||
                scope.row.clientLogo.includes('https://')
                  ? scope.row.clientLogo
                  : fileUrl + scope.row.clientLogo
              "
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="clientStatus">
          <template v-slot="scope">
            <dict-tag
              :type="DICT_TYPE.COMMON_STATUS"
              :value="scope.row.clientStatus"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="访问令牌的有效期"
          align="center"
          prop="accessTokenValiditySeconds"
        >
          <template v-slot="scope"
            >{{ scope.row.accessTokenValiditySeconds }} 秒</template
          >
        </el-table-column>
        <el-table-column
          label="刷新令牌的有效期"
          align="center"
          prop="refreshTokenValiditySeconds"
        >
          <template v-slot="scope"
            >{{ scope.row.refreshTokenValiditySeconds }} 秒</template
          >
        </el-table-column>
        <el-table-column
          label="授权类型"
          align="center"
          prop="authorizedGrantTypes"
        >
          <template v-slot="scope">
            <el-tag
              :disable-transitions="true"
              :key="index"
              v-for="(authorizedGrantType, index) in scope.row
                .authorizedGrantTypes"
              :index="index"
              style="margin: 4px 4px 0 0"
            >
              {{ getShouquanName(authorizedGrantType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="180"
        >
          <template v-slot="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template v-slot="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-if="permissions.open_oauth2_client_edit"
              >编辑
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-if="permissions.open_oauth2_client_del"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.current"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />

      <!-- 对话框(添加 / 修改) -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="1100px"
        append-to-body
        :close-on-click-modal="false"
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="160px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="客户端编号" prop="clientId">
                <el-input
                  v-model="form.clientId"
                  placeholder="请输入客户端编号"
                /> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="客户端密钥" prop="clientSecret">
                <el-input
                  v-model="form.clientSecret"
                  placeholder="请输入客户端密钥"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="应用名" prop="clientName">
            <el-input v-model="form.clientName" placeholder="请输入应用名" />
          </el-form-item>
          <el-form-item label="应用图标" prop="clientLogo">
            <imageUpload v-model="form.clientLogo" :limit="1" v-if="open" />
          </el-form-item>
          <el-form-item label="应用描述">
            <el-input
              type="textarea"
              v-model="form.clientDescription"
              placeholder="请输入应用描述"
            />
          </el-form-item>
          <el-form-item label="应用首页地址">
            <el-input
              v-model="form.clientIndexUrl"
              placeholder="请输入应用首页地址"
            />
          </el-form-item>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="状态" prop="clientStatus">
                <el-radio-group v-model="form.clientStatus">
                  <el-radio
                    v-for="dict in this.getDictDataByType(
                      DICT_TYPE.COMMON_STATUS
                    )"
                    :key="dict.value"
                    :label="parseInt(dict.value)"
                    >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否托管应用菜单" prop="syncMenu">
                <el-radio-group v-model="form.syncMenu">
                  <el-radio :label="0">否</el-radio>
                  <el-radio :label="1">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item
                label="访问令牌的有效期"
                prop="accessTokenValiditySeconds"
              >
                <el-input-number
                  v-model="form.accessTokenValiditySeconds"
                  placeholder="单位：秒"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="刷新令牌的有效期"
                prop="refreshTokenValiditySeconds"
              >
                <el-input-number
                  v-model="form.refreshTokenValiditySeconds"
                  placeholder="单位：秒"
                /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="授权类型" prop="authorizedGrantTypes">
                <el-select
                  v-model="form.authorizedGrantTypes"
                  multiple
                  filterable
                  placeholder="请输入授权类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in this.getDictDataByType(
                      DICT_TYPE.SYSTEM_OAUTH2_GRANT_TYPE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="授权范围" prop="scopes">
                <el-select
                  v-model="form.scopes"
                  multiple
                  filterable
                  allow-create
                  placeholder="请输入授权范围"
                  style="width: 100%"
                >
                  <el-option
                    v-for="scope in form.scopes"
                    :key="scope"
                    :label="scope"
                    :value="scope"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="自动授权范围" prop="autoApproveScopes">
            <el-select
              v-model="form.autoApproveScopes"
              multiple
              filterable
              placeholder="请输入授权范围"
              style="width: 100%"
            >
              <el-option
                v-for="scope in form.scopes"
                :key="scope"
                :label="scope"
                :value="scope"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="可重定向的 URI 地址" prop="redirectUris">
            <el-select
              v-model="form.redirectUris"
              multiple
              filterable
              allow-create
              placeholder="请输入可重定向的 URI 地址"
              style="width: 100%"
            >
              <el-option
                v-for="redirectUri in form.redirectUris"
                :key="redirectUri"
                :label="redirectUri"
                :value="redirectUri"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据接收地址">
            <el-input
              v-model="form.receiveDataUrl"
              placeholder="请输入数据接收地址"
            />
          </el-form-item>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="权限" prop="authorities">
                <el-select
                  v-model="form.authorities"
                  multiple
                  filterable
                  allow-create
                  placeholder="请输入权限"
                  style="width: 100%"
                >
                  <el-option
                    v-for="authority in form.authorities"
                    :key="authority"
                    :label="authority"
                    :value="authority"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="资源" prop="resourceIds">
                <el-select
                  v-model="form.resourceIds"
                  multiple
                  filterable
                  allow-create
                  placeholder="请输入资源"
                  style="width: 100%"
                >
                  <el-option
                    v-for="resourceId in form.resourceIds"
                    :key="resourceId"
                    :label="resourceId"
                    :value="resourceId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="厂家名称" prop="factoryName">
                <el-input
                  v-model="form.factoryName"
                  placeholder="请输入厂家名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属部门" prop="deptId">
                <!-- <el-input v-model="form.deptId" placeholder="请输入所属部门" />
                 -->
                <tree-select
                  v-model="form.deptId"
                  :data="treeDeptDataArr"
                  :props="defaultProps"
                  :loadNode="treeLoad"
                  :selfSearchPage="selfSearchPage"
                  placeholder="请选择，输入组织名称、编码或规范简称可搜索"
                  @selfSearch="selfSearch"
                  @selfInput="selfInput"
                  @selfCurrentChange="selfCurrentChange"
                  pageFlag
                  filterable
                  slefFilter
                  lazy
                ></tree-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="管理员姓名" prop="adminName">
                <el-input
                  v-model="form.adminName"
                  placeholder="请输入管理员姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="phoneNum">
                <el-input
                  v-model="form.phoneNum"
                  placeholder="请输入联系方式"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="附加信息" prop="additionalInformation">
            <el-input
              type="textarea"
              v-model="form.additionalInformation"
              placeholder="请输入附加信息，JSON 格式数据"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </basic-container>
</template>

<script>
import {
  addObj,
  putObj,
  delObj,
  getObj,
  fetchList,
} from "@/api/oauth2/oauth2client";
import ImageUpload from "@/components/upload/imgUpload";
import Editor from "@/components/editor";
import FileUpload from "@/components/upload/fileUpload";
import { mapGetters } from "vuex";
import { CommonStatusEnum } from "@/util/constants";
import TreeSelect from "@/components/TreeSelect";
import debounce from "@/util/debounce";
import {
  canUseNetDeptTree,
  canUseDeptSonTree,
  searchDeptByName,
} from "@/api/admin/sys/dept";
export default {
  name: "OAuth2Client",
  components: {
    FileUpload,
    ImageUpload,
    Editor,
    TreeSelect,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // OAuth2 客户端列表
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      treeDeptData: [],
      treeDeptDataArr: [],
      groupExpandedKeys: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        clientName: null,
        clientStatus: null,
      },
      defaultProps: {
        label: "deptName",
        value: "id",
      },
      // 表单参数
      form: {
        syncMenu: 0,
        receiveDataUrl: "",
        phoneNum: "",
        adminName: "",
        factoryName: "",
        deptId: "",
        deptName: "",
      },
      selfSearchPage: {
        current: 1,
        size: 20,
        total: 0,
      },
      treeLoad: async (node, resolve) => {
        // console.log("node=js---=", node);
        if (node.level === 0) {
          let params = { status: 1 };
          let res1 = await canUseNetDeptTree(params);
          // console.log("no--else----js---",res1);
          return resolve(res1.data.data);
        } else if (node.level > 0) {
          // console.log("else---js-");
          const child = await canUseDeptSonTree({ deptId: node.data.id });
          // console.log("child==", child);
          node.data.children = child.data.data;

          return resolve(child.data.data);
        }
      },
      // 表单校验
      rules: {
        phoneNum: [
          { required: true, message: "联系方式能为空", trigger: "blur" },
        ],
        adminName: [
          { required: true, message: "管理员不能为空", trigger: "blur" },
        ],
        factoryName: [
          { required: true, message: "厂家名称不能为空", trigger: "blur" },
        ],
        deptId: [
          { required: true, message: "所属部门不能为空", trigger: "change" },
        ],
        clientId: [
          { required: true, message: "客户端编号不能为空", trigger: "blur" },
        ],
        clientSecret: [
          { required: true, message: "客户端密钥不能为空", trigger: "blur" },
        ],
        clientName: [
          { required: true, message: "应用名不能为空", trigger: "blur" },
        ],
        // clientLogo: [{required: true, message: "应用图标不能为空", trigger: "blur"}],
        clientStatus: [
          { required: true, message: "状态不能为空", trigger: "blur" },
        ],
        syncMenu: [
          {
            required: true,
            message: "是否托管应用菜单不能为空",
            trigger: "blur",
          },
        ],
        accessTokenValiditySeconds: [
          {
            required: true,
            message: "访问令牌的有效期不能为空",
            trigger: "blur",
          },
        ],
        refreshTokenValiditySeconds: [
          {
            required: true,
            message: "刷新令牌的有效期不能为空",
            trigger: "blur",
          },
        ],
        redirectUris: [
          {
            required: true,
            message: "可重定向的 URI 地址不能为空",
            trigger: "blur",
          },
        ],
        authorizedGrantTypes: [
          { required: true, message: "授权类型不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();

    this.hangdleGetTreeDeptFn();
  },
  computed: {
    ...mapGetters(["permissions"]),
    fileUrl() {
      return window.location.protocol + "//" + window.location.host;
    },
    shouquanArr() {
      // SYSTEM_OAUTH2_GRANT_TYPE: 'system_oauth2_grant_type',
      return this.getDictDataByType("system_oauth2_grant_type");
    },
  },
  // filters: {
  //   shouquanTypeFilter(val) {
  //     console.log("val==", val);
  //     // console.log("val=shouquanArr=", this.shouquanArr);

  //     return "";
  //   },
  // },
  methods: {
    hangdleGetTreeDeptFn() {
      // 查询部门树
      let params = {
        status: 1,
      };
      canUseNetDeptTree(params).then((response) => {
        this.treeDeptData = response.data.data;
        this.treeDeptData.forEach((item, i) => {
          this.groupExpandedKeys.push(item.id);
        });
        if (this.treeDeptData && this.treeDeptData.length > 0) {
          for (let index = 0; index < this.treeDeptData.length; index++) {
            this.treeDeptData[index].level = "root";
          }
        }
        this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
        // this.treeDeptDataBase = JSON.parse(JSON.stringify(this.treeDeptData));
        //groupExpandedKeys
      });
    },
    selfCurrentChange(val, keyWord) {
      this.selfSearchPage.current = val;
      this.debounceSearchDept2(keyWord);
    },
    selfSearch(val) {
      // console.log("val-33--", val);
      this.debounceSearchDept2(val);
    },
    selfInput(val) {
      console.log("val---", val);
      this.form.deptId = val;
    },
    debounceSearchDept2: debounce(function (name) {
      // console.log("name==", name);
      if (name) {
        this.handleSearchDept2(name);
      } else {
        this.treeDeptDataArr = JSON.parse(JSON.stringify(this.treeDeptData));
      }
    }, 500),
    handleSearchDept2(name) {
      // console.log("搜索--", name);
      let params = {
        keyWord: name,
      };
      params.current = this.selfSearchPage.current;
      params.size = this.selfSearchPage.size;
      searchDeptByName(params).then((res) => {
        // console.log("res==搜索记过=", res);
        if (res.data.code == 200) {
          this.treeDeptDataArr = res.data.data.records;
          this.selfSearchPage.total = res.data.data.total;
        }
      });
    },
    getShouquanName(type) {
      let fObj = this.shouquanArr.find((item) => {
        return item.value == type;
      });

      if (fObj) {
        return fObj.label;
      } else {
        return "";
      }
    },
    /** 查询列表 */
    getList() {
      this.loading = true;
      // 执行查询
      fetchList(this.queryParams).then((response) => {
        this.list = response.data.data.records;
        this.total = response.data.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        clientId: undefined,
        clientSecret: undefined,
        clientName: undefined,
        clientLogo: undefined,
        clientDescription: undefined,
        clientStatus: CommonStatusEnum.ENABLE,
        accessTokenValiditySeconds: 30 * 60,
        refreshTokenValiditySeconds: 30 * 24 * 60,
        redirectUris: [],
        authorizedGrantTypes: [],
        scopes: [],
        autoApproveScopes: [],
        authorities: [],
        resourceIds: [],
        additionalInformation: undefined,
        syncMenu: 0,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // this.form.syncMenu = 0;
      this.open = true;
      this.title = "添加 OAuth2 客户端";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getObj(id).then((response) => {
        console.log(" this.form==", this.form);
        this.form = response.data.data;
        this.open = true;
        this.title = "修改 OAuth2 客户端";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return;
        }
        console.log("应用图标", this.form.clientLogo);
        if (this.form.clientLogo) {
          let filePath = window.location.protocol + "//" + window.location.host;
          this.form.clientLogo = this.form.clientLogo.replaceAll(filePath, "");
        }
        // 修改的提交
        if (this.form.id != null) {
          putObj(this.form).then((response) => {
            this.$message.success("修改成功");
            this.open = false;
            this.getList();
          });
          return;
        }
        // 添加的提交
        addObj(this.form).then((response) => {
          this.$message.success("新增成功");
          this.open = false;
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$confirm('是否确认删除客户端编号为"' + row.clientId + '"的数据项?')
        .then(function () {
          return delObj(id);
        })
        .then(() => {
          this.getList();
          this.$message.success("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
