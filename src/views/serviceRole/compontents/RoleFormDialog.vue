<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog
    :title="!form.id ? '新增' : '修改'"
    :visible.sync="visible"
    :before-close="handleCloseDialog"
  >
    <el-form ref="dataForm" :model="form" label-width="80px" :rules="rules">
      <!--  :disabled="editFlag" -->

      <div class="form-row">
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="form.roleName"
            placeholder="请输入角色名称"
          ></el-input>
        </el-form-item>
      </div>
      <el-form-item label="角色编码" prop="roleKey">
        <el-input
          v-model="form.roleKey"
          placeholder="请输入角色编码"
        ></el-input>
      </el-form-item>

      <el-form-item label="说明">
        <el-input
          type="textarea"
          v-model="form.roleDesc"
          placeholder="请输入说明"
          style="width: 60%"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="debounceDataFormSubmitFn"
        >确 定</el-button
      >
      <el-button @click="handleCloseDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import debounce from "@/util/debounce";

import { addObj, putObj } from "@/api/admin/sys/role";
export default {
  name: "roleForm",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项
      deptOptions: [],
      // 下拉用户列表
      users: [],
      // 是否显示弹出层
      visible: true,

      form: {
        roleName: "",
        roleKey: "",
        path: "",
        icon: "",
        sortOrder: "",
        roleDesc: "",
        // parentId: undefined,
      },
      poolareaNoOptions: [],
      deptArr: [],

      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        roleKey: [
          { required: true, message: "请输入角色编码", trigger: "blur" },
        ],
      },
    };
  },
  props: {
    sonSystemList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  created() {
    // this.getAllProviceFn();
    this.handleGetDaohangTreeFn();
  },
  mounted() {
    if (this.form.areaName) {
      this.$refs.poolareaNoRef.presentText = this.form.areaName;
    }
  },
  methods: {
    initEditForm(form) {
      this.form = Object.assign(this.form, form);
    },
    handleCloseDialog() {
      this.visible = false;
      this.$emit("handleCloseDiaolog");
    },
    getAllProviceFn() {
      getAllProvinces().then((res) => {
        // console.log("获取省份数据==", res);
        if (res.data.code == 200) {
          this.deptArr = res.data.data;
        }
      });
    },

    init(isEdit, id) {
      if (id !== null) {
        this.form.parentId = id;
      }
      this.form.remark = "";
      this.visible = true;
      // this.getTreeselect();
      this.handleGetDaohangTreeFn();
      // this.listAllUser();
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          // getObj(id).then((response) => {
          //   this.form = response.data.data;
          //   console.log("from==", this.form);
          //   if (this.form.areaName) {
          //     setTimeout(() => {
          //       this.$refs.poolareaNoRef.presentText = this.form.areaName;
          //     }, 400);
          //   }
          // });
        } else {
          this.form.id = undefined;
        }
      });
    },
    listAllUser() {
      listUser().then((response) => {
        this.users = response.data.data;
      });
    },
    debounceDataFormSubmitFn: debounce(function () {
      this.dataFormSubmit();
    }, 500),
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        // this.form.menuType = "C";
        if (valid) {
          if (this.form.id) {
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          } else {
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          }
        }
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getDaohangTree().then((response) => {
        this.deptOptions = [];
        const dept = { id: -1, name: "根", children: response.data.data };
        this.deptOptions.push(dept);
      });
    },
    handleGetDaohangTreeFn() {
      fetchMenuTree().then((res) => {
        // console.log("获取导航树", res);
        if (res.data.code == 200) {
          let treeDeptData = res.data.data;
          treeDeptData.forEach((item) => {
            item.show = false;
          });
          this.treeDeptData = treeDeptData;
          this.deptOptions = [];
          const dept = { id: -1, name: "根", children: res.data.data };
          this.deptOptions.push(dept);
          // this.$refs.deptTreeRef.setCurrentKey(this.currentDhId);

          //
        }
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
