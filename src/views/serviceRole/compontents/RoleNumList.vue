<template>
  <div class="table-container">
    <el-table
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <!-- <el-table-column label="日期" width="120">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column> -->
      <el-table-column prop="userName" label="系统用户名"> </el-table-column>
      <el-table-column prop="nickName" label="姓名"> </el-table-column>
      <el-table-column prop="status" label="账号状态">
        <template slot-scope="scope">
          <el-tag
            type="success"
            v-if="scope.row.status == CommonStatusEnum.ENABLE"
            >有效</el-tag
          >
          <el-tag
            type="info"
            v-else-if="scope.row.status == CommonStatusEnum.DISABLE"
            >停用</el-tag
          >
          <el-tag type="danger" v-else-if="scope.row.status == 9">锁定</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="systemName" label="说明"> </el-table-column> -->
    </el-table>
    <div class="page-content">
      <el-pagination
        v-if="pageObj.total > 0"
        background
        style="position: relative"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageObj.pageCurrent"
        :page-sizes="[10]"
        :page-size="pageObj.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageObj.total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { pageUserWithRole } from "@/api/admin/sys/role";
import { CommonStatusEnum } from "@/util/constants";
export default {
  data() {
    return {
      chooseSelection: [],
      tableData: [],
      pageObj: {
        current: 1,
        size: 10,
        total: 0,
      },
      roleId: "",
      chooseArrObj: {},
      CommonStatusEnum: CommonStatusEnum,
    };
  },
  methods: {
    handleGetRolesList(roleId) {
      // this.tableData = [];
      this.roleId = roleId;
      let params = {
        roleId: roleId,
        current: this.pageObj.current,
        size: this.pageObj.size,
      };
      // console.log("params==", params);
      pageUserWithRole(params)
        .then((res) => {
          // console.log("res=1122==", res);
          if (res.data.code == 200) {
            let chooseUser = "delRole" + this.pageObj.current;
            let chooseArr = this.chooseArrObj[chooseUser];
            // console.log("chooseArr===222222222233333=", chooseArr);

            this.tableData = res.data.data.records || [];
            this.pageObj.total = res.data.data.total;

            if (this.tableData && this.tableData.length == 0) {
              if (this.pageObj.current > 1) {
                this.pageObj.current = 1;
                this.handleGetRolesList(this.roleId);
              }
            }

            setTimeout(() => {
              if (chooseArr && chooseArr.length > 0) {
                chooseArr.forEach((item) => {
                  // this.toggleRowSelection(item);
                  let fIndex = this.tableData.findIndex((fItem) => {
                    return fItem.id == item.id;
                  });
                  // console.log("fIndex==", fIndex);
                  if (fIndex != -1) {
                    this.toggleRowSelection(this.tableData[fIndex]);
                  }
                });
              }
            }, 300);
          } else {
            this.tableData = [];
          }
        })
        .catch((err) => {
          this.tableData = [];
        });
    },
    toggleRowSelection(row) {
      //第一个参数为数据，第二个参数为是否勾选
      this.$refs.multipleTable.toggleRowSelection(row, true);
    },
    handleSelectionChange(val) {
      this.chooseSelection = val;
      // console.log("val==lzs===", val);
      //
      let chooseUser = "delRole" + this.pageObj.current;
      this.chooseArrObj[chooseUser] = val;
    },
    handleSizeChange(val) {
      // console.log("handleSizeChange==", val);
      this.pageObj.size = val;
      this.handleGetRolesList(this.roleId);
    },
    handleCurrentChange(val) {
      // console.log("handleCurrentChange==", val);
      this.pageObj.current = val;
      this.handleGetRolesList(this.roleId);
    },
  },
};
</script>
<style lang="scss" scoped>
.table-container {
  position: relative;
  .page-content {
    margin-top: 30px;
    text-align: right;
  }
}
</style>