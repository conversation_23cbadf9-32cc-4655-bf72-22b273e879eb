<template>
  <div class="dialog-main-tree">
    <div
      class="tree-container"
      v-loading="loading"
      element-loading-text="数据加载中"
      element-loading-spinner="el-icon-loading"
    >
      <div class="top-content">
        <div class="title-content">
          <span class="t-line"></span>
          <span class="t-word">选择资源授权</span>
        </div>

        <div class="btn-content">
          <div class="search-content">
            <el-input
              placeholder="输入菜单名称过滤"
              class="sc-input"
              v-model="searchMenuName"
            >
            </el-input>
          </div>
          <el-button type="" @click="updateResetTree">重置 </el-button>
          <el-button
            type="primary"
            @click="updatePermission"
            v-if="currentRow && currentRow.relationType != 1"
            >保存
          </el-button>
        </div>
      </div>

      <el-tree
        v-if="treeData && treeData.length > 0"
        ref="menuTree"
        :data="treeData"
        :default-checked-keys="checkedKeys"
        :check-strictly="false"
        :props="defaultProps"
        :filter-node-method="filterNode"
        class="filter-tree"
        node-key="id"
        highlight-current
        show-checkbox
      />
    </div>
  </div>
</template>
<script>
import { fetchRoleTree, addBindApp, permissionUpd } from "@/api/admin/sys/role";
import { fetchMenuTree } from "@/api/admin/sys/menu";
export default {
  data() {
    return {
      treeData: [],
      checkedKeys: [],
      oldCheckedKeys: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      appIdsArr: [],
      loading: false,
      searchMenuName: "",
      roleId: "",
      menuIds: "",
      currentRow: {},
    };
  },
  watch: {
    searchMenuName(val) {
      this.$refs.menuTree.filter(val);
    },
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    updateAppPermission(roleId) {
      this.roleId = roleId;
      //   console.log("roleId==", roleId);
      this.menuIds = "";
      // console.log("应用", this.$refs.appTreeData.getCheckedKeys());
      // if (this.$refs.appTreeData.getCheckedKeys().length !== 0) {
      //   this.appIdsArr = this.$refs.appTreeData.getCheckedKeys();
      // }
      this.appIdsArr = this.$refs.appMenuTree.getCheckedKeys();
      //   console.log("this.appIdsArr==", this.appIdsArr);
      let params = {
        roleId: roleId,
        appIds: this.appIdsArr,
        // authType: 0, //授权类型  0可用  1可分配
        // roleType: 1, // 0安全角色  1业务角色
      };
      addBindApp(params).then(() => {
        // this.dialogClientPermissionVisible = false;
        this.appPermissionVisible = false;
        this.$message.success("修改成功");
      });
    },
    handleInitData(row) {
      //   console.log("roww---", row);

      if (row && row.id) {
        this.roleId = row.id;
        this.handlePermission(row);
      }
    },
    updateResetTree() {
      this.checkedKeys = JSON.parse(JSON.stringify(this.oldCheckedKeys));
      // console.log("重置==了2=", this.checkedKeys);
      if (this.checkedKeys && this.checkedKeys.length > 0) {
        let checkArr = [];
        this.checkedKeys.forEach((narr) => {
          if (narr && narr.length > 0) {
            checkArr = [...checkArr, ...narr];
          }
        });
        // console.log("checkArr==", checkArr);
        this.$refs.menuTree.setCheckedKeys(checkArr);
      }
      // this.$refs.menuTree.setCheckedKeys([3]);
      // this.$refs.menuTree.setCheckedKeys(this.oldCheckedKeys);

      this.$forceUpdate();
    },
    updatePermission() {
      this.menuIds = "";
      this.menuIds = this.$refs.menuTree
        .getCheckedKeys()
        .join(",")
        .concat(",")
        .concat(this.$refs.menuTree.getHalfCheckedKeys().join(","));
      // console.log("this.menuIds=lzs=", this.menuIds);
      let params = {
        roleId: this.roleId,
        menuIds: this.menuIds,
        authType: 0, // 授权类型 0可用1可分配
        roleType: 1, //角色类型 0安全角色 1业务角色
      };
      permissionUpd(params).then(() => {
        // this.dialogPermissionVisible = false;
        this.$store.dispatch("GetMenu", { type: false });
        this.$message.success("修改成功");
        this.getNewChecksData(this.currentRow);
      });
    },
    handlePermission(row) {
      this.currentRow = row;
      this.loading = true;
      fetchRoleTree(row.id)
        .then((response) => {
          this.checkedKeys = response.data.data;
          // this.oldCheckedKeys = JSON.parse(JSON.stringify(this.checkedKeys));
          // console.log("数据-oldCheckedKeys--", this.oldCheckedKeys);
          return fetchMenuTree(null, null, true);
        })
        .then((response) => {
          this.loading = false;
          this.treeData = response.data.data;
          // console.log("数据---", this.treeData);
          // 解析出所有的太监节点
          this.checkedKeys = this.resolveAllEunuchNodeId(
            this.treeData,
            this.checkedKeys,
            []
          );
          this.oldCheckedKeys = JSON.parse(JSON.stringify(this.checkedKeys));
          //   this.dialogPermissionVisible = true;
          //   this.roleId = row.id;
          //   this.roleCode = row.roleCode;
        });
    },
    getNewChecksData(row) {
      fetchRoleTree(row.id).then((response) => {
        this.checkedKeys = response.data.data;
        this.checkedKeys = this.resolveAllEunuchNodeId(
          this.treeData,
          this.checkedKeys,
          []
        );
        this.oldCheckedKeys = JSON.parse(JSON.stringify(this.checkedKeys));
      });
    },
    /**
     * 解析出所有的太监节点id
     * @param json 待解析的json串
     * @param idArr 原始节点数组
     * @param temp 临时存放节点id的数组
     * @return 太监节点id数组
     */
    resolveAllEunuchNodeId(json, idArr, temp) {
      for (let i = 0; i < json.length; i++) {
        const item = json[i];
        // 存在子节点，递归遍历;不存在子节点，将json的id添加到临时数组中
        if (item.children && item.children.length !== 0) {
          this.resolveAllEunuchNodeId(item.children, idArr, temp);
        } else {
          temp.push(idArr.filter((id) => id === item.id));
        }
      }
      return temp;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./role.scss";
</style>
<style lang="scss" scoped>
.dialog-main-tree {
  min-height: 600px;
  //   height: 900px;

  position: relative;
  .tree-container {
    // width: 50%;
    position: relative;
    padding: 10px;
    // border: 1px solid #ebeef5;
  }
  .search-content {
    // width: 30%;
    width: 200px;
    margin-right: 12px;
  }
  .filter-tree {
    width: 100%;
    // height: 800px;
    // overflow-y: scroll;
    overflow-y: scroll;
    height: calc(100vh - 298px);
  }
  .btn-content {
    display: flex;
    // position: absolute;
    // left: 55%;
    // top: 2px;
  }
}
</style>