<template>
  <div class="service-container">
    <div class="service-content">
      <div
        class="table-container"
        v-loading="loading"
        element-loading-text="数据加载中"
        element-loading-spinner="el-icon-loading"
      >
        <div class="title-content">
          <span class="t-line"></span>
          <span class="t-word">业务角色列表</span>
        </div>
        <div class="btn-content">
          <img
            src="@/assets/image/appGroup/addAppGroup.png"
            style="width: 18px; width: 18px; cursor: pointer"
            alt=""
            @click="addRoleFn"
          />
          <span
            class="del-btn"
            @click="handleDelRow"
            v-if="
              currentRow &&
              currentRow.relationType != 1 &&
              tableData &&
              tableData.length > 0
            "
            ><i class="el-icon-delete"></i
          ></span>
        </div>
        <div class="search-content">
          <el-input
            placeholder="输入角色名称过滤"
            v-model="searchRoleName"
            clearable
          >
          </el-input>
        </div>
        <el-table
          class="table-content"
          ref="multipleTable"
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
          highlight-current-row
          @current-change="handleCurrentRowChange"
        >
          <!-- @selection-change="handleSelectionChange" -->
          <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
          <!-- <el-table-column label="日期" width="120">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column> -->
          <el-table-column label="选择" width="55">
            <template slot-scope="scope">
              <!-- <el-radio v-model="scope.row.id"></el-radio> -->
              <el-radio :label="scope.row.id" v-model="rowId"
                ><span></span
              ></el-radio>
            </template>
          </el-table-column>
          <el-table-column
            type="index"
            width="50"
            label="序号"
            :index="indexMethod"
          >
          </el-table-column>
          <el-table-column prop="roleKey" label="角色编码"> </el-table-column>
          <el-table-column prop="roleName" label="角色名称"> </el-table-column>
          <!-- <el-table-column
          prop="systemType"
          label="接入类型"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.systemType == 0"> 应用级 </span>
            <span v-else-if="scope.row.systemType == 1"> 菜单级 </span>
          </template>
        </el-table-column> -->
        </el-table>
        <div class="page-content">
          <el-pagination
            v-if="pageObj.total > 0"
            background
            style="position: relative"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageObj.pageCurrent"
            :page-sizes="[10]"
            :page-size="pageObj.pageSize"
            layout="total, prev, pager, next, jumper"
            :total="pageObj.total"
            :pager-count="5"
          >
          </el-pagination>
        </div>
      </div>
      <!-- <div class="right-tab">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="角色维护" name="first"></el-tab-pane>
          <el-tab-pane label="资源授权" name="second"></el-tab-pane>
          <el-tab-pane label="应用授权" name="third"></el-tab-pane>
        </el-tabs>
      </div> -->
      <div class="service-right">
        <el-tabs
          class="right-tab"
          v-model="activeName"
          @tab-click="handleClick"
        >
          <el-tab-pane label="角色维护" name="first"></el-tab-pane>
          <el-tab-pane label="资源授权" name="second"></el-tab-pane>
          <el-tab-pane label="应用授权" name="third"></el-tab-pane>
        </el-tabs>
        <template v-if="activeName == 'first'">
          <div class="dr-content" v-if="tableData && tableData.length > 0">
            <div class="ros-main">
              <div class="title-content">
                <span class="t-line"></span>
                <span class="t-word">角色维护</span>
              </div>
              <div
                class="btn-content"
                v-if="currentRow && currentRow.relationType != 1"
              >
                <el-button v-if="editFlag" @click="handleEdit">编辑</el-button>
                <el-button v-else @click="handleResetForm">重置</el-button>
                <el-button
                  v-if="editFlag && this.form.id"
                  @click="handleDelRow"
                  type="danger"
                  >删除</el-button
                >
                <el-button
                  v-else
                  type="primary"
                  @click="debounceDataFormSubmitFn"
                  >保存</el-button
                >
              </div>
            </div>

            <el-form
              ref="dataForm"
              :model="form"
              label-width="80px"
              :rules="rules"
              :disabled="editFlag"
            >
              <!--  :disabled="editFlag" -->

              <div class="form-row">
                <el-form-item
                  label="角色名称"
                  prop="roleName"
                  class="row-width"
                >
                  <el-input v-model="form.roleName"></el-input>
                </el-form-item>
                <el-form-item label="角色编码" prop="roleKey" class="row-width">
                  <el-input v-model="form.roleKey"></el-input>
                </el-form-item>
              </div>

              <el-form-item label="说明">
                <el-input type="textarea" v-model="form.roleDesc"></el-input>
              </el-form-item>
            </el-form>
          </div>

          <div class="dr-content" v-else>
            <div class="ros-main">
              <div class="title-content">
                <span class="t-line"></span>
                <span>角色维护</span>
              </div>
            </div>
            <div class="no-content">
              <div class="no-data"></div>
              暂无数据
            </div>
          </div>

          <div class="dr-system">
            <div class="sys-main">
              <div class="title-content">
                <span class="t-line"></span>
                <span class="t-word">角色账号列表</span>
              </div>
              <div class="ds-content">
                <el-button type="primary" @click="hangdleAddRoleUser"
                  >新增</el-button
                >
                <el-button type="danger" @click="handleDelRoleUser"
                  >删除</el-button
                >
              </div>
            </div>

            <role-num-list
              style="width: 100%"
              ref="roleNumRef"
              class="sys-content"
            ></role-num-list>
          </div>
        </template>
        <div v-else-if="activeName == 'second'">
          <role-set ref="roleSetRef"></role-set>
        </div>
        <div v-else-if="activeName == 'third'">
          <app-set ref="appSetRef"></app-set>
        </div>
      </div>
    </div>
    <role-form-dialog
      v-if="roleFormFlag"
      @refreshDataList="refreshDataList"
      @handleCloseDiaolog="closeRoleDialog"
      ref="roleFormRef"
    ></role-form-dialog>
    <add-role-user-dialog
      v-if="addRoleFlag"
      ref="addRoleRef"
      @refushList="refushList"
      @handleCloseDialog="handleCloseDialog"
    ></add-role-user-dialog>
  </div>
</template>
<script>
import { fetchList, delObj, unbindUser, putObj } from "@/api/admin/sys/role";
import RoleFormDialog from "./compontents/RoleFormDialog.vue";

import debounce from "@/util/debounce";
import RoleNumList from "./compontents/RoleNumList.vue";
import AddRoleUserDialog from "./compontents/AddRoleUserDialog.vue";

import RoleSet from "./compontents/RoleSet.vue";

import AppSet from "./compontents/AppSet.vue";
export default {
  components: {
    RoleFormDialog,
    RoleNumList,
    AddRoleUserDialog,
    RoleSet,
    AppSet,
  },
  data() {
    return {
      tableData: [],
      radio: "",
      rowId: "",
      pageObj: {
        current: 1,
        size: 10,
        total: 0,
      },
      currentRow: {},
      roleFormFlag: false,
      loading: false,
      activeName: "first",
      editFlag: true,
      addRoleFlag: false,
      searchRoleName: "",
      form: {
        roleName: "",
        roleKey: "",
        path: "",
        icon: "",
        sortOrder: "",
        roleDesc: "",
        // parentId: undefined,
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        roleKey: [
          { required: true, message: "请输入角色编码", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    searchRoleName(val) {
      this.loading = true;
      this.debounceGetRoleList();
    },
  },
  created() {
    this.getRolesList();
  },
  methods: {
    debounceGetRoleList: debounce(function () {
      this.getRolesList();
    }, 500),
    hangdleAddRoleUser() {
      // console.log("112233");
      if (this.rowId) {
        this.addRoleFlag = true;
        this.$nextTick(() => {
          this.$refs.addRoleRef.addBindUser(this.rowId);
        });
      } else {
        this.$message({
          showClose: true,
          message: "请先选择左侧角色",
          type: "warning",
        });
      }
    },
    handleDelRoleUser() {
      let that = this;
      // console.log("删除已选==", this.$refs.roleNumRef.chooseSelection);
      // console.log("删除已选=2=", this.$refs.roleNumRef.chooseArrObj);
      // let arr = [];
      // let chooseSelection = this.$refs.roleNumRef.chooseSelection;
      // if (chooseSelection && chooseSelection.length > 0) {
      //   chooseSelection.forEach((item) => {
      //     arr.push(item.id);
      //   });
      // }
      let userIds = [];
      Object.keys(this.$refs.roleNumRef.chooseArrObj).forEach((key) => {
        // console.log(key, this.chooseArrObj[key]);
        let arr = this.$refs.roleNumRef.chooseArrObj[key];
        if (arr && arr.length > 0) {
          arr.forEach((item) => {
            userIds.push(item.id);
          });
        }
      });
      if (userIds && userIds.length == 0) {
        this.$message({
          showClose: true,
          message: "请先选择要删除的用户",
          type: "warning",
        });
        return;
      }
      let params = { roleId: that.currentRow.id, userIdList: userIds };

      this.$confirm("是否确认删除该角色下的用户", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return unbindUser(params);
        })
        .then((res) => {
          // console.log("data=解绑用户下的角色---=", res);
          if (res.status && res.data.code == 200) {
            this.refushList();
            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    handleResetForm() {
      this.form.roleName = "";
      this.form.roleKey = "";
      this.form.roleDesc = "";
    },
    handleEdit() {
      // this.editFlag = false;
      this.roleFormFlag = true;
      this.$nextTick(() => {
        this.$refs.roleFormRef.initEditForm(this.form);
      });
    },
    handleClick() {
      this.editFlag = true;
      if (this.activeName == "first") {
        this.refushList();
      } else if (this.activeName == "second") {
        this.$nextTick(() => {
          // console.log("this.$refs.roleSetRef==", this.$refs.roleSetRef);

          this.$refs.roleSetRef.handleInitData(this.currentRow);
        });
        // setTimeout(() => {}, 50);
      } else if (this.activeName == "third") {
        this.$nextTick(() => {
          this.$refs.appSetRef.handleInitData(this.currentRow);
        });
        // setTimeout(() => {}, 50);
      }
    },
    refreshDataList() {
      this.getRolesList();
      this.closeRoleDialog();
    },
    closeRoleDialog() {
      this.roleFormFlag = false;
    },
    getRolesList() {
      this.loading = true;
      let params = {
        current: this.pageObj.current,
        size: this.pageObj.size,
        roleName: this.searchRoleName,
      };
      fetchList(params)
        .then((res) => {
          // console.log("res=lzs==", res);
          this.loading = false;
          if (res.data.code == 200) {
            this.tableData = res.data.data.records;
            this.pageObj.total = res.data.data.total;
            if (this.tableData && this.tableData.length > 0) {
              if (this.currentRow && this.currentRow.id) {
                let fObj = this.tableData.find((item) => {
                  return item.id == this.currentRow.id;
                });
                if (fObj) {
                  this.handleCurrentRowChange(fObj);
                }
              } else {
                this.handleCurrentRowChange(this.tableData[0]);
              }
            } else {
              if (this.pageObj.current > 1) {
                this.pageObj.current = 1;
                this.getRolesList();
              }
            }
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleCloseDialog() {
      this.addRoleFlag = false;
    },
    //角色新增用户关闭弹窗后，要刷新角色账号列表
    refushList() {
      this.$nextTick(() => {
        // this.$refs.roleNumRef.pageObj.current = 1;
        this.$refs.roleNumRef.handleGetRolesList(this.rowId);
      });
    },
    indexMethod(index) {
      return (this.pageObj.current - 1) * this.pageObj.size + index + 1;
    },
    handleCurrentRowChange(row) {
      this.editFlag = true;
      // console.log("选中单行==", row);
      this.rowId = row.id;
      this.currentRow = row;
      this.form = Object.assign(this.form, row);
      if (this.activeName == "first") {
        this.$nextTick(() => {
          this.$refs.roleNumRef.handleGetRolesList(this.rowId);
        });
      } else if (this.activeName == "second") {
        this.$nextTick(() => {
          this.$refs.roleSetRef.handleInitData(this.currentRow);
        });
      } else if (this.activeName == "third") {
        this.$nextTick(() => {
          this.$refs.appSetRef.handleInitData(this.currentRow);
        });
      }
    },
    handleSizeChange(val) {
      // console.log("handleSizeChange==", val);
      this.pageObj.size = val;
      this.getRolesList();
    },
    handleCurrentChange(val) {
      // console.log("handleCurrentChange==", val);
      this.pageObj.current = val;
      this.getRolesList();
    },
    addRoleFn() {
      // console.log("新增角色");
      this.roleFormFlag = true;
    },
    handleDelRow() {
      let that = this;
      this.$confirm("是否确认删除:" + this.currentRow.roleName, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(that.currentRow.id);
        })
        .then((res) => {
          // console.log("data==", res);
          if (res.status && res.data.code == 200) {
            this.$message.success("删除成功");
            // this.pageObj.current = 1;
            this.getRolesList();
            // this.form = {};
            that.currentRow = {};
            this.rowId = "";
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    debounceDataFormSubmitFn: debounce(function () {
      this.dataFormSubmit();
    }, 500),
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.visible = false;
              // this.$emit("refreshDataList");
              this.getRolesList();
            });
          } else {
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.visible = false;
              // this.$emit("refreshDataList");
            });
          }
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
@import "./compontents/role.scss";
.service-container {
  position: relative;
  padding: 20px;
  .service-content {
    overflow: auto;
    display: flex;

    .right-tab {
      // position: absolute;
      // top: 10px;
      // left: 30%;
    }

    .service-right {
      margin-left: 20px;
      flex: 1;
      background: #fff;
      padding: 10px 12px;
      // height: 938px;
      // height: calc(100vh - 188px);
      // border: 1px solid red;
      // overflow-y: scroll;
      position: relative;

      .dr-content {
        // border: 1px solid #dcdfe6;
        width: calc(100% - 10px);
        background: #fff;
        border-radius: 4px;
        padding: 10px;

        .ros-main {
          display: flex;
          margin: 0px 0 20px 0;
          align-items: center;
          justify-content: space-between;
          // padding: 0 24px;
        }
      }
      .dr-system {
        position: relative;
        margin-top: 36px;
        padding: 10px;
        width: calc(100% - 10px);
        background: #fff;
        .ds-content {
          text-align: right;
        }
        .sys-main {
          display: flex;
          justify-content: space-between;
        }
      }
      .title-content {
        display: flex;
        align-items: center;
        .t-line {
          height: 12px;
          width: 3px;
          // background: #002fa7;
          background: #327fff;
          display: inline-block;
          margin-right: 6px;
        }
      }
    }
  }
  .table-container {
    // width: 35%;
    width: 585px;
    min-width: 380px;
    min-height: 520px;
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    position: relative;
    .search-content {
      margin: 12px 0;
    }
    .table-content {
      margin-top: 16px;
    }
    .page-content {
      margin-top: 20px;
    }

    .btn-content {
      display: flex;
      align-items: center;
      position: absolute;
      top: 10px;
      right: 10px;
      .del-btn {
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }
  .no-content {
    text-align: center;
  }
  .no-data {
    background: url("../../assets/image/no-data.png") no-repeat;
    background-size: 100%;
    width: 197px;
    height: 132px;
    margin: 0 auto;
  }
  .form-row {
    display: flex;
    .row-width {
      width: 50%;
    }
  }
}
</style>