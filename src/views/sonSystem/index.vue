<template>
  <div>
    <basic-container>
      <!--   :permission="permissionList" -->
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-update="handleUpdate"
        @row-save="handleSave"
        @row-del="rowDel"
        :search.sync="searchForm"
        v-model="form"
      >
        <!-- <template slot="menuLeft">
          <el-tabs v-model="activeName" @tab-click="handleTabClick">
            <el-tab-pane label="全部" name="all"></el-tab-pane>
            <el-tab-pane label="已预约" name="first"></el-tab-pane>
            <el-tab-pane label="已履约" name="second"></el-tab-pane>
            <el-tab-pane label="已取消" name="third"></el-tab-pane>
            <el-tab-pane label="已爽约" name="fifth"></el-tab-pane>
          </el-tabs>
        </template> -->
        <template slot="remarkForm" slot-scope="scope">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入说明"
            v-model="form.remark"
            maxlength="50"
            show-word-limit
          >
          </el-input>
        </template>
        <!-- <template slot="dateTypeSearch" slot-scope="scope">
          <el-select
            v-model="search.dateType"
            placeholder="请选择"
            :clearable="true"
            @change="handleSelect(search.dateType)"
          >
            <el-option
              v-for="item in selectOrderType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template> -->
        <!-- <template slot="queryNameSearch" slot-scope="scope">
          <el-input
            placeholder="请输入姓名或手机号或身份证号"
            v-model="search.inputQuery"
            clearable
            @change="handleInput(search.inputQuery, scope.row)"
          >
          </el-input>
        </template> -->
        <!-- <template slot="datetimerangeSearch" slot-scope="scope">
          <el-date-picker
            :disabled="ableFlag"
            v-model="search.datetimerange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          >
          </el-date-picker>
        </template> -->
        <!-- <template slot="dateStartEndSearch" slot-scope="scope"> -->
        <!-- <el-time-select
            placeholder="起始时间"
            v-model="search.startingTime"
            @change="handleChangeStart(search.startingTime, scope.row)"
            :picker-options="{
              start: '09:00',
              step: '00:05',
              end: '17:00',
            }"
          >
          </el-time-select> -->
        <!-- <el-time-picker
            arrow-control
            v-model="search.startingTime"
            :picker-options="{
              selectableRange: '18:30 - 20:30',
            }"
            @change="handleChangeStart(search.startingTime, scope.row)"
            placeholder="任意时间点"
          >
          </el-time-picker>
          <el-time-select
            placeholder="结束时间"
            v-model="search.endingTime"
            @change="handleChangeEnd(search.endingTime, scope.row)"
            :picker-options="{
              start: '09:00',
              step: '00:05',
              end: '17:00',
              minTime: search.startingTime,
            }"
          >
          </el-time-select> -->
        <!-- <el-time-picker
            is-range
            v-model="search.dateStartEnd"
            range-separator="-"
            start-placeholder="预约时段起"
            end-placeholder="预约时段终"
            placeholder="选择时间范围"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 180px"
          >
          </el-time-picker>
        </template> -->
        <!-- <template slot="bookDateSearch">
          <el-date-picker
          v-if="searchSelect == 0"
            v-model="search.bookDate"
             type="date"
            value-format="yyyy-MM-dd hh:mm:ss"
          >
            >
          </el-date-picker>
        </template>
        <template slot="bookStartTimeSearch">
          <el-date-picker
          v-if="searchSelect == 1"
            v-model="search.bookStartTime"
             type="date"
            value-format="yyyy-MM-dd hh:mm:ss"
          >
            >
          </el-date-picker>
        </template>
        <template slot="bookEndTimeSearch">
          <el-date-picker
          v-if="searchSelect == 2"
            v-model="search.bookEndTime"
             type="date"
            value-format="yyyy-MM-dd hh:mm:ss"
          >
            >
          </el-date-picker>
        </template>
        <template slot="createTimeSearch">
          <el-date-picker
          v-if="searchSelect == 3"
            v-model="search.createTime"
             type="date"
            value-format="yyyy-MM-dd hh:mm:ss"
          >
            >
          </el-date-picker>
        </template>
        <template slot="changeTimeSearch">
          <el-date-picker
            v-if="searchSelect == 4"
            v-model="search.changeTime"
             type="date"
            value-format="yyyy-MM-dd hh:mm:ss"
          >
            >
          </el-date-picker>
        </template> -->
      </avue-crud>
    </basic-container>
  </div>
</template>
<script>
import { tableOption } from "@/const/crud/sonSystem/index";
import {
  fetchList,
  getObj,
  addObj,
  putObj,
  delObj,
} from "@/api/sonSystem/index";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      tableLoading: false,
      tableOption: tableOption,
      form: {
        remark: "",
      },
      searchForm: {},
      tableData: [],
      page: {
        total: 0,
        pageSize: 10,
        currentPage: 1,
      },
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.sonSystem_add, false),
        delBtn: this.vaildData(this.permissions.sonSystem_del, false),
        editBtn: this.vaildData(this.permissions.sonSystem_edit, false),
      };
    },
  },
  methods: {
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
        Object.assign(
          {
            current: page.currentPage,
            size: page.pageSize,
            // status: 1,
          },
          params,
          this.searchForm
        )
      )
        .then((response) => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
          this.tableData.forEach((item) => {
            item.status = +item.status == 1;
          });
          //
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    searchChange(form, done) {
      console.log("searchForm==", this.searchForm);
      //   this.hanldeSearchData();
      this.page.currentPage = 1;

      this.getList(this.page);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    handleSave: function (row, done, loading) {
      addObj(row)
        .then((data) => {
          this.$message.success("添加成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    handleUpdate: function (row, index, done, loading) {
      putObj(row)
        .then((data) => {
          this.$message.success("修改成功");
          done();
          this.getList(this.page);
        })
        .catch(() => {
          loading();
        });
    },
    rowDel: function (row, index) {
      // console.log("row--", row);
      this.$confirm("是否确认删除子系统名称为“" + row.sysName + "”", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .el-form-item__content {
  display: flex;
}
</style>