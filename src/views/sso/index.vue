<template>
  <div>
    <div class="login-weaper  animated bounceInDown">
      <div class="login-left animate__animated animate__fadeInLeft">
<!--        <img class="img" src="/img/logo.png" alt=""/>-->
        <p class="title">{{ website.title }}</p>
        <p>©{{ website.year }} {{ website.version }}</p>
      </div>
    </div>

  </div>
</template>

<script>
import {authorize, getAuthorize} from "@/api/sso";
import {mapGetters} from "vuex";

export default {
  name: "index",
  data() {
    return {
      loginForm: {
        scopes: [], // 已选中的 scope 数组
      },
      params: { // URL 上的 client_id、scope 等参数
        responseType: undefined,
        clientId: undefined,
        redirectUri: undefined,
        state: undefined,
        scopes: [], // 优先从 query 参数获取；如果未传递，从后端获取
      },
      client: { // 客户端信息
        name: '',
        logo: '',
      },
      loading: false
    };
  },
  computed: {
    ...mapGetters(["website"])
  },
  created() {
    // 解析参数
    // 例如说【自动授权不通过】：即scope无参数或者scope参数与后台对该客户端授权的不一致，例如：client_id=sso&response_type=code
    // 例如说【自动授权通过】：即scope参数与后台配置的一致，例如：：client_id=sso&response_type=code&scope=user.read
    this.params.responseType = this.$route.query.response_type
    this.params.clientId = this.$route.query.client_id
    this.params.redirectUri = this.$route.query.redirect_uri
    this.params.state = this.$route.query.state
    if (this.$route.query.scope) {
      this.params.scopes = this.$route.query.scope.split(' ')
    }
    console.log("路由参数：",this.params)

    // 如果有 scope 参数，先执行一次自动授权，看看是否之前都授权过了。
    this.doAuthorize(true, this.params.scopes, []).then(res => {
      const href = res.data.data
      if (!href) {
        console.log('自动授权未通过！')
        return;
      }
      location.href = href
    })
  },
  methods: {
    doAuthorize(autoApprove, checkedScopes, uncheckedScopes) {
      return authorize(this.params.responseType, this.params.clientId, this.params.redirectUri, this.params.state,
        autoApprove, checkedScopes, uncheckedScopes)
    },
  }
};
</script>
<style lang="scss" scoped>
.oauth-login {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.oauth-login-item {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.oauth-login-item img {
  height: 25px;
  width: 25px;
}

.oauth-login-item span:hover {
  text-decoration: underline red;
  color: red;
}
</style>
