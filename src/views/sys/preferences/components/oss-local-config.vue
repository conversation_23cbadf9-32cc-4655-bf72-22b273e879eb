<template>
  <div class="gray-box top-redius border-bottom-gray">
    <div class="title">{{dataForm.name}}</div>
    <el-form
      ref="dataForm"
      label-width="180px"
      size="mini"
      class="set-form"
      label-position="left"
      @keyup.enter.native="dataFormSubmit()"
      :model="dataForm"
    >
      <el-form-item label="url请求的路径:" style="width:640px" prop="address">
        <el-input
          v-model="dataForm.address"
          :placeholder="`url请求的路径`"
          controls-position="right"
        ></el-input>
      </el-form-item>
      <el-form-item label="图片存放的真实路径:" style="width:640px" prop="storagePath">
        <el-input
          v-model="dataForm.storagePath"
          :placeholder="`图片存放的真实路径`"
          controls-position="right"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否激活" style="width:640px" prop="enable">
        <el-switch v-model="dataForm.enable" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
      </el-form-item>
      <el-button @click="dataFormSubmit()">保存</el-button>
    </el-form>
    <span slot="footer" class="dialog-footer">
    </span>
  </div>
</template>

<script>
export default {
  props: {
    config: {
      type: Object,
    }
  },
  data() {
    return {
      dataForm: {
        name: 'local',
        address: '',
        storagePath: '',
        enable: false
      }
    }
  },
  mounted() {
    this.init()
  },
  watch: {
    config(val) {
      console.log("watch",val)
      this.dataForm = val
    }
  },
  methods: {
    init() {
      console.log("local init", this.config)
      this.dataForm = this.config
    },
    message(msg, type) {
      this.$message({
        message: msg,
        type: type,
        duration: 1500
      })
    },
    checkConfig() {
      if (this.dataForm.address == null || this.dataForm.accessKeyId === '') {
        this.message(`文件上传 url请求的路径 不能为空 `, 'error')
        return false
      }
      if (this.dataForm.storagePath == null || this.dataForm.accessKeySecret === '') {
        this.message(`文件上传 图片存放的真实路径 不能为空 `, 'error')
        return false
      }
      return true
    },
    // 表单提交
    dataFormSubmit() {
      if (this.dataForm.enable && !this.checkConfig()) {
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        console.log("local 提交",this.dataForm)
        this.$emit('child',this.dataForm)
      })
    }
  }
}
</script>
