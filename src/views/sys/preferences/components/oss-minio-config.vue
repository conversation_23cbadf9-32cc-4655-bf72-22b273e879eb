<template>
  <div class="gray-box top-redius border-bottom-gray">
    <div class="title">{{ dataForm.name }}</div>
    <el-form
      ref="dataForm"
      label-width="180px"
      size="mini"
      class="set-form"
      label-position="left"
      @keyup.enter.native="dataFormSubmit()"
      :model="dataForm"
    >
      <el-form-item
        label="oss—accessKey:"
        style="width: 640px"
        prop="accessKey"
      >
        <el-input
          v-model="dataForm.accessKey"
          :placeholder="`oss 文件上传 accessKeyId`"
          controls-position="right"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="oss—secretKey:"
        style="width: 640px"
        prop="secretKey"
      >
        <el-input
          v-model="dataForm.anquanKey"
          :placeholder="`oss 文件上传 accessKeySecret`"
          controls-position="right"
        ></el-input>
      </el-form-item>
      <el-form-item label="oss—endpoint:" style="width: 640px" prop="endpoint">
        <el-input
          v-model="dataForm.endpoint"
          :placeholder="`oss 文件上传 endpoint`"
          controls-position="right"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="oss—bucketName:"
        style="width: 640px"
        prop="bucketName"
      >
        <el-input
          v-model="dataForm.bucketName"
          :placeholder="`oss 文件上传 bucketName`"
          controls-position="right"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否激活" style="width: 640px" prop="enable">
        <el-switch
          v-model="dataForm.enable"
          active-color="#13ce66"
          inactive-color="#ff4949"
        ></el-switch>
      </el-form-item>
      <el-button @click="dataFormSubmit()">保存</el-button>
    </el-form>
    <span slot="footer" class="dialog-footer"> </span>
  </div>
</template>

<script>
export default {
  props: {
    config: {
      type: Object,
    },
  },
  data() {
    return {
      dataForm: {
        name: "minio",
        accessKey: "",
        anquanKey: "",
        endpoint: "",
        bucketName: "",
        enable: false,
      },
    };
  },
  mounted() {
    this.init();
  },
  watch: {
    config(val) {
      console.log("minio watch", val);
      this.dataForm = val;
    },
  },
  methods: {
    init() {
      console.log("minio init", this.config);
      this.dataForm = this.config;
    },
    message(msg, type) {
      this.$message({
        message: msg,
        type: type,
        duration: 1500,
      });
    },
    checkConfig() {
      if (this.dataForm.accessKey == null || this.dataForm.accessKey === "") {
        this.message(`oss 文件上传 accessKey 不能为空 `, "error");
        return false;
      }
      if (this.dataForm.anquanKey == null || this.dataForm.anquanKey === "") {
        this.message(`oss 文件上传 secretKey 不能为空 `, "error");
        return false;
      }
      if (this.dataForm.endpoint == null || this.dataForm.endpoint === "") {
        this.message(`oss 文件上传 endpoint 不能为空 `, "error");
        return false;
      }
      if (this.dataForm.bucketName == null || this.dataForm.bucketName === "") {
        this.message(`oss 文件上传 bucketName 不能为空 `, "error");
        return false;
      }
      return true;
    },
    // 表单提交
    dataFormSubmit() {
      this.dataForm.secretKey = this.dataForm.anquanKey;
      if (this.dataForm.enable && !this.checkConfig()) {
        return;
      }
      this.$refs["dataForm"].validate((valid) => {
        console.log("minio 提交", this.dataForm);
        this.$emit("child", this.dataForm);
      });
    },
  },
};
</script>
