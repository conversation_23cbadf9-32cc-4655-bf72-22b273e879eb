<template>
  <div class="mod-uploadfile">
    <el-alert
      title="配置多个按照配置从上往下读取，配置其中一个即可"
      type="warning"
      :closable="false"
      effect="dark"
    ></el-alert>
    <minio :config="storageConfig.minIo" @child="minioConfig" ref="minio" />
    <local :config="storageConfig.local" @child="localConfig" ref="local" />
    <aliyun :config="storageConfig.aliyun" @child="aliyunConfig" ref="aliyun" />
  </div>
</template>

<script>
import minio from "../components/oss-minio-config.vue";
import local from "../components/oss-local-config";
import aliyun from "../components/oss-aliyun-config";
import { saveStorageConfig, getStorageConfig } from "@/api/admin/sys/sysconfig";

export default {
  name: "oss",
  components: {
    minio,
    local,
    aliyun,
  },
  data() {
    return {
      storageConfig: {
        local: {
          name: "local", // 不可修改名称，需与后台枚举保持一致
          enable: true,
          address: "",
          storagePath: "",
        },
        minIo: {
          name: "minio", // 不可修改名称，需与后台枚举保持一致
          enable: false,
          accessKey: "",
          bucketName: "",
          endpoint: "",
          anquanKey: "",
        },
        aliyun: {
          name: "aliyun_oss", // 不可修改名称，需与后台枚举保持一致
          enable: false,
          accessKey: "",
          bucketName: "",
          endpoint: "",
          anquanKey: "",
          filePre: "",
        },
      },
    };
  },
  created() {
    this.initConfig();
  },
  methods: {
    initConfig() {
      getStorageConfig().then((res) => {
        let config = res.data.data;
        this.storageConfig = config;

        if (config.minIo.secretKey) {
          this.storageConfig.minIo.anquanKey = config.minIo.secretKey;
        }
        if (config.aliyun.secretKey) {
          this.storageConfig.aliyun.anquanKey = config.aliyun.secretKey;
        }

        console.log("加载服务端配置", config);
      });
    },
    localConfig(msg) {
      console.log("触发local", msg);

      this.storageConfig.local = msg;
      if (msg.enable) {
        this.storageConfig.minIo.enable = false;
        this.storageConfig.aliyun.enable = false;
      }
      this.save();
    },
    minioConfig(msg) {
      console.log("触发minio", msg);
      this.storageConfig.minIo = msg;
      this.storageConfig.minIo.anquanKey = msg.secretKey;
      if (msg.enable) {
        this.storageConfig.local.enable = false;
        this.storageConfig.aliyun.enable = false;
      }
      this.save();
    },
    aliyunConfig(msg) {
      console.log("触发aliyunOSS", msg);
      this.storageConfig.aliyun = msg;
      this.storageConfig.aliyun.anquanKey = msg.secretKey;
      if (msg.enable) {
        this.storageConfig.local.enable = false;
        this.storageConfig.minIo.enable = false;
      }
      this.save();
    },

    save() {
      saveStorageConfig(this.storageConfig).then((res) => {
        console.log("保存结果", res);
        if (res.data.code === 200) {
          this.$message.success("保存成功");
        } else {
          this.$message.error("保存成功");
        }
        this.initConfig();
      });
    },
  },
};
</script>

<style lang="scss">
/*
通用样式
*/
.mod-uploadfile {
  .sdp-top {
    .IsdistributionText {
      display: inline-block;
      padding-left: 10px;
    }
  }

  .footer-bar-pagination {
    width: calc(100% + 30px);
    background-color: white;
    bottom: 0px;
    position: fixed;
    height: 50px;
    text-align: center;
    z-index: 100;
    left: -30px;
    line-height: 50px;
  }

  #tabs {
    margin-top: 20px;
  }

  .tips {
    color: #9797a1;
    font-size: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: 700;
    display: inline-block;
    vertical-align: middle;
    padding-right: 20px;
  }

  .gray-box {
    background: #f2f2f6;
    padding: 20px 35px;
    height: 100%;
  }

  .border-bottom-gray {
    border-bottom: 1px solid #dddce2;
  }

  .bottom-redius {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .top-redius {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }

  .set-form {
    margin-left: 20%;
    margin: 20px;
  }

  .valid-input {
    margin-left: 20px;
    border-top-left-radius: 0px;
  }

  /**
    elenemt
    **/
  .el-checkbox__label,
  .el-radio__label {
    padding-left: 5px;
    font-size: 13px;
  }

  .el-checkbox__inner,
  .el-radio__inner {
    border: 2px solid #b2aebc;
  }

  .el-checkbox__inner::after {
    left: 3px;
    top: 0px;
  }

  .el-input__inner {
    border-radius: 0%;
  }
}
</style>
