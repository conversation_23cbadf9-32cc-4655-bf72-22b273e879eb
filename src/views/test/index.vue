<template>
  <div class="echart-container">
    <!-- <div class="echart-content">
      
    </div> -->
    <div class="avue-layout">
      钱多少不重要，重要的是身体健康11111
      <div class="main-left">
        <!-- 左侧导航栏 -->
        <!-- <sidebar /> -->
      </div>
      再回首1
      <div class="avue-main">钱多少不重要，重要的是身体健康</div>
      再回首2
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      topFlag: true,
    };
  },
  mounted() {
    console.log("mounted====");
  },
};
</script>
<style lang="scss" scoped>
.echart-container {
  width: 100%;
  height:100%;
  overflow-y: scroll;

  .echart-content {
    width: 100%;
    margin: 0 auto;
    // height: calc(100vh - 200px);
    height: 100%;
    // border: 1px solid red;
  }
  .main-left {
    width: 220px;
    position: fixed;
    height: 100%;
  }
}
</style>