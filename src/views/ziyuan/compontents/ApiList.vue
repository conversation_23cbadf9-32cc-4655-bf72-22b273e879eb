<template>
  <div class="table-container">
    <div class="title-content">
      <span class="t-line"></span>
      <span>API列表</span>
    </div>
    <div class="btn-content">
      <el-button type="primary" @click="addFunction">新增</el-button>
    </div>
    <el-table
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <!-- <el-table-column label="日期" width="120">
        <template slot-scope="scope">{{ scope.row.date }}</template>
      </el-table-column> -->
      <el-table-column type="index" label="序号" width="50"> </el-table-column>
      <el-table-column prop="name" label="API名称"> </el-table-column>
      <el-table-column prop="path" label="API路径" show-overflow-tooltip>
        <!-- <template slot-scope="scope">
          <span v-if="scope.row.systemType == 0"> 应用级 </span>
          <span v-else-if="scope.row.systemType == 1"> 菜单级 </span>
        </template> -->
      </el-table-column>
      <el-table-column prop="remark" label="描述"> </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            @click="handleClickEdit(scope.row)"
            type="text"
            size="small"
            >编辑</el-button
          >
          <el-button
            @click="handleClickDel(scope.row)"
            type="text"
            size="small"
            style="color: #f56c6c"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <api-add-dialog
      v-if="functionFlag"
      @closeDialog="closeFunctionDialog"
      :deptOptions="deptOptions"
      :chooseObj="chooseObj"
      :chooseRow="chooseRow"
      :editRow="editRow"
      :type="type"
    ></api-add-dialog>
  </div>
</template>
<script>
import {
  addObj,
  putObj,
  fetchMenuTree,
  getMenusInfoById,
  delObj,
} from "@/api/admin/sys/menu.js";

import ApiAddDialog from "./ApiAddDialog.vue";
export default {
  components: { ApiAddDialog },
  data() {
    return {
      chooseSelection: [],
      tableData: [],
      pageObj: {
        current: 1,
        size: 10,
        total: 0,
      },
      navigationId: "",
      functionFlag: false,
      deptOptions: [],
      treeDeptData: [],
      editRow: {},
      type: "",
      chooseRow: {},
    };
  },
  props: {
    chooseObj: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  created() {
    // this.getMenuInfoFn();
    this.handleGetDaohangTreeFn();
  },

  methods: {
    refshList(row) {
      this.tableData = [];
      this.chooseRow = row;
      this.getMenuInfoFn();
    },
    getMenuInfoFn() {
      if (!this.chooseRow.id) {
        return;
      }
      let params = {
        menuId: this.chooseRow.id,
      };
      getMenusInfoById(params).then((res) => {
        console.log("获取动作--lzs==", res);
        if (res.data.code == 200) {
          let menuObjArr = res.data.data;
          if (menuObjArr && menuObjArr.length > 0) {
            // this.tableData = menuObjArr[0].children || [];
            let tabArr = menuObjArr[0].children || [];
            this.tableData = tabArr.filter((item) => {
              return item.menuType == "A";
            });
          }
        }
      });
    },
    // handleGetDaohangList(navigationId) {
    //   this.navigationId = navigationId;
    //   let params = {
    //     navigationId: navigationId,
    //     current: this.pageObj.current,
    //     size: this.pageObj.size,
    //   };
    //   console.log("params==", params);
    //   getDaohangSystemList(params).then((res) => {
    //     console.log("res=1122==", res);
    //     if (res.data.code == 200) {
    //       this.tableData = res.data.data.records || [];
    //       this.pageObj.total = res.data.data.total;
    //     }
    //   });
    // },
    handleGetDaohangTreeFn() {
      fetchMenuTree().then((res) => {
        // console.log("获取导航树", res);
        if (res.data.code == 200) {
          let treeDeptData = res.data.data;
          treeDeptData.forEach((item) => {
            item.show = false;
          });
          this.treeDeptData = treeDeptData;
          this.deptOptions = [];
          const dept = { id: -1, name: "根", children: res.data.data };
          this.deptOptions.push(dept);
        }
      });
    },
    handleSelectionChange(val) {
      this.chooseSelection = val;
      console.log("val====", val);
    },
    handleSizeChange(val) {
      console.log("handleSizeChange==", val);
      this.pageObj.size = val;
      this.handleGetDaohangList(this.navigationId);
    },
    handleCurrentChange(val) {
      console.log("handleCurrentChange==", val);
      this.pageObj.current = val;
      this.handleGetDaohangList(this.navigationId);
    },
    handleClickDel(row) {
      console.log("row==", row);
      this.$confirm("是否确认删除功能名称为" + row.name, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(row.id);
        })
        .then((data) => {
          this.$message.success("删除成功");
          this.getMenuInfoFn();
        });
    },
    handleClickEdit(row) {
      console.log("row编辑==", row);
      this.functionFlag = true;
      this.editRow = row;
      this.type = "edit";
    },
    addFunction() {
      console.log("新增");
      this.type = "add";
      if (this.chooseRow && this.chooseRow.id) {
        this.functionFlag = true;
      } else {
        this.$message({
          showClose: true,
          message: "请先选择功能列表中的功能",
          type: "warning",
        });
      }
    },
    closeFunctionDialog() {
      this.functionFlag = false;

      if (this.chooseObj.id) {
        this.getMenuInfoFn();
      }
      // this.handleGetDaohangTreeFn();
    },
  },
};
</script>
<style lang="scss" scoped>
.table-container {
  position: relative;
  .page-content {
    margin-top: 30px;
    text-align: right;
  }
}
.title-content {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .t-line {
    height: 12px;
    width: 3px;
    // background: #002fa7;
    background: #327fff;
    display: inline-block;
    margin-right: 6px;
  }
}
.btn-content {
  position: absolute;
  right: 10px;
  top: 0px;
}
</style>