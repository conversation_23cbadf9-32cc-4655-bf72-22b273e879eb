<!--
 * @Description:
 * @Author: liuzhengshuai
 * @Date: 2022-03-09 14:17:04
 * @LastEditors: liuzhengshuai
 * @LastEditTime: 2022-10-12 16:02:11
-->
<template>
  <cust-dialog
    :dialog-status.sync="showFlag"
    width="1000px"
    title="新增功能"
    @closed="handleCloseDialog"
    class="my-cust"
  >
    <div>
      <el-form
        :label-position="labelPosition"
        label-width="80px"
        :rules="rules"
        ref="ruleForm"
        :model="form"
      >
        <!-- <el-row>
          <el-col :span="16">
            <el-form-item label="上级菜单" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="deptOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-form-item label="功能名称" prop="menuName">
          <el-input
            v-model="form.menuName"
            placeholder="请输入功能名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="权限标识" prop="perms">
          <el-input
            v-model="form.perms"
            placeholder="请输入权限标识"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="菜单名称">
          <el-input v-model="form.type"></el-input>
        </el-form-item> -->
        <el-row>
          <el-col :span="16">
            <el-form-item label="菜单名称" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="deptOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="请选择菜单名称"
                disabled
              />
              <!--  -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="remark">
          <el-input
            type="textarea"
            v-model="form.remark"
            placeholder="请输入描述"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="bottom-footer">
      <el-button @click="handleCloseDialog">关闭</el-button>
      <el-button type="primary" @click="handleAddMenu">确认</el-button>
    </div>
  </cust-dialog>
</template>
<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { addObj, putObj, fetchMenuTree } from "@/api/admin/sys/menu.js";
export default {
  name: "flowTipsDialog",
  components: { Treeselect },
  data() {
    return {
      showFlag: true,
      lineHight: 166,
      speedLoading: true,
      form: {
        parentId: "",
        menuType: "F",
        menuName: "",
        menuCode: "",
        perms: "",
        remark: "",
      },
      labelPosition: "right",
      // deptOptions: [],
      rules: {
        menuName: [
          { required: true, message: "请输入功能名称", trigger: "blur" },
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        perms: [{ required: true, message: "请输入权限标识", trigger: "blur" }],
      },
    };
  },
  props: {
    deptOptions: {
      type: Array,
      default: function () {
        return [];
      },
    },
    chooseObj: {
      type: Object,
      default: function () {
        return {};
      },
    },
    editRow: {
      type: Object,
      default: function () {
        return {};
      },
    },
    type: {
      type: String,
      default: function () {
        return "";
      },
    },
  },
  mounted() {
    // console.log("deptOptions==", this.deptOptions);

    // console.log("this.form==", this.form);
    if (this.type && this.type == "edit") {
      this.form = Object.assign(this.form, this.editRow);
      this.form.menuName = this.form.name;
      console.log("this.form==", this.form);
    } else {
      this.form.parentId = this.chooseObj.id;
      this.form.subSysId = this.chooseObj.subSysId;
    }
  },

  methods: {
    handleCloseDialog() {
      this.showFlag = false;
      this.$emit("closeDialog");
    },
    handleAddMenu() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.form.menuType = "F";
          if (this.type == "add") {
            addObj(this.form).then((res) => {
              // console.log("新增导航的数据==", res);
              if (res.data.code == 200) {
                this.$message({
                  showClose: true,
                  message: "新增成功",
                  type: "success",
                });
                // this.form = {};
                // this.handleGetDaohangTreeFn();
                this.$emit("closeDialog");
              }
            });
          } else {
            if (this.form.id) {
              putObj(this.form).then((res) => {
                // console.log("新增导航的数据==", res);
                if (res.data.code == 200) {
                  this.$message({
                    showClose: true,
                    message: "修改成功",
                    type: "success",
                  });
                  // this.form = {};
                  // this.handleGetDaohangTreeFn();
                  this.$emit("closeDialog");
                }
              });
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
<style scoped lang="scss">
.flow-dot-container {
  margin: 20px;
  p {
    margin-bottom: 12px;
  }
}
.my-cust {
  ::deep(.el-dialog__body) {
    padding: 0;
  }
  // /deep/ .el-dialog__header {
  //   padding-bottom: 0;
  // }
}
</style>
