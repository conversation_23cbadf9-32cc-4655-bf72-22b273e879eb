<template>
  <!-- 添加或修改菜单对话框 -->
  <el-dialog
    :title="!form.id ? '新增' : '修改'"
    :visible.sync="visible"
    :before-close="handleCloseDialog"
    :close-on-click-modal="false"
  >
    <el-form ref="dataForm" :model="form" label-width="80px" :rules="rules">
      <!--  :disabled="editFlag" -->
      <el-row>
        <el-col :span="16">
          <el-form-item label="上级菜单" prop="parentId">
            <treeselect
              v-model="form.parentId"
              :options="deptOptions"
              :normalizer="normalizer"
              :show-count="true"
              placeholder="选择上级菜单"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="form-row">
        <el-form-item label="菜单类型" prop="menuType">
          <el-radio-group v-model="form.menuType" size="small">
            <el-radio label="C">左菜单</el-radio>
            <el-radio label="T">顶菜单</el-radio>
            <el-radio label="M">目录</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="单页面"
          prop="routeType"
          v-if="form.menuType == 'T'"
        >
          <el-switch
            v-model="form.routeType"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>

        <el-form-item label="菜单名称" prop="menuName">
          <el-input
            v-model="form.menuName"
            placeholder="选输入菜单名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="菜单编号" prop="menuCode">
          <el-input
            v-model="form.menuCode"
            placeholder="选输入菜单编号"
          ></el-input>
          <!-- <el-input-number
                  v-model="form.sortOrder"
                  controls-position="right"
                  :min="0"
                ></el-input-number> -->
        </el-form-item>
        <el-form-item label="子系统" prop="subSysId">
          <el-select
            v-model="form.subSysId"
            filterable
            placeholder="请选择子系统"
          >
            <el-option
              v-for="item in sonSystemList"
              :key="item.id"
              :label="item.sysName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>

      <el-form-item label="菜单路径" prop="path">
        <el-input
          v-model="form.path"
          class="row-input"
          placeholder="选输入菜单路径"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="组件路径"
        prop="component"
        v-if="form.menuType != 'M'"
      >
        <el-input
          v-model="form.component"
          class="row-input"
          placeholder="请输入组件路径"
        ></el-input>
      </el-form-item>
      <el-form-item label="内嵌地址" prop="query">
        <el-input
          type="textarea"
          v-model="form.query"
          placeholder="请输入内嵌地址"
          style="width: 95%"
        />
        <!-- <el-tooltip
          class="item"
          effect="light"
          content="必须为json结构：{key:value}"
          placement="top"
        >
          <i style="padding-left: 5px" class="el-icon-question" />
        </el-tooltip> -->
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <avue-input-icon
          v-model="form.icon"
          :icon-list="iconList"
          class="row-input"
          placeholder="请选择图标"
        ></avue-input-icon>
      </el-form-item>
      <el-form-item label="排序">
        <el-input-number
          v-model="form.sortOrder"
          controls-position="right"
          :min="0"
          placeholder="请输入排序"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="说明">
        <el-input
          type="textarea"
          v-model="form.remark"
          style="width: 60%"
          placeholder="请输入说明"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="debounceDataFormSubmitFn"
        >确 定</el-button
      >
      <el-button @click="handleCloseDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import { listUser } from "@/api/admin/auth/user";
import iconList from "@/const/iconList";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import debounce from "@/util/debounce";
// import {
//   addDaohangData,
//   getDaohangTree,
//   updateDaohangData,
// } from "@/api/daohang/index";
import { addObj, putObj, fetchMenuTree } from "@/api/admin/sys/menu.js";
export default {
  name: "ziyuanForm",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单树选项
      deptOptions: [],
      // 下拉用户列表
      users: [],
      // 是否显示弹出层
      visible: true,
      iconList: iconList,
      form: {
        parentId: -1,
        subSysId: "",
        menuName: "",
        menuCode: "",
        path: "",
        icon: "",
        sortOrder: "",
        remark: "",
        routeType: "",
        query: "",
        // parentId: undefined,
      },
      poolareaNoOptions: [],
      deptArr: [],

      // 表单校验
      rules: {
        menuName: [
          { required: true, message: "请输入菜单名称", trigger: "blur" },
        ],
        menuCode: [
          { required: true, message: "请输入菜单编号", trigger: "blur" },
        ],
        subSysId: [
          { required: true, message: "请选择子系统", trigger: "change" },
        ],
        path: [{ required: true, message: "请输入菜单路径", trigger: "blur" }],
        parentId: [
          { required: false, message: "请选择上级导航", trigger: "change" },
        ],
        menuType: [
          { required: true, message: "请选择菜单类型", trigger: "change" },
        ],
      },
    };
  },
  props: {
    sonSystemList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  created() {
    // this.getAllProviceFn();
    this.handleGetDaohangTreeFn();
  },
  mounted() {
    if (this.form.areaName) {
      this.$refs.poolareaNoRef.presentText = this.form.areaName;
    }
  },
  methods: {
    handleCloseDialog() {
      this.visible = false;
      this.$emit("handleCloseDiaolog");
    },
    getAllProviceFn() {
      getAllProvinces().then((res) => {
        // console.log("获取省份数据==", res);
        if (res.data.code == 200) {
          this.deptArr = res.data.data;
        }
      });
    },
    setParentId(id) {
      // console.log("22-setParentId-", id);
      setTimeout(() => {
        this.form.parentId = id;
      }, 200);
    },
    init(isEdit, id) {
      if (id !== null) {
        this.form.parentId = id;
      }
      this.form.remark = "";
      this.visible = true;
      // this.getTreeselect();
      this.handleGetDaohangTreeFn();
      // this.listAllUser();
      this.$nextTick(() => {
        this.$refs["dataForm"].resetFields();
        if (isEdit) {
          // getObj(id).then((response) => {
          //   this.form = response.data.data;
          //   console.log("from==", this.form);
          //   if (this.form.areaName) {
          //     setTimeout(() => {
          //       this.$refs.poolareaNoRef.presentText = this.form.areaName;
          //     }, 400);
          //   }
          // });
        } else {
          this.form.id = undefined;
        }
      });
    },
    listAllUser() {
      listUser().then((response) => {
        this.users = response.data.data;
      });
    },
    debounceDataFormSubmitFn: debounce(function () {
      this.dataFormSubmit();
    }, 500),
    // 表单提交
    dataFormSubmit() {
      this.$refs["dataForm"].validate((valid) => {
        // this.form.menuType = "C";
        if (valid) {
          if (this.form.parentId === undefined) {
            this.form.parentId = -1;
          }

          if (this.form.id) {
            putObj(this.form).then((data) => {
              this.$message.success("修改成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          } else {
            addObj(this.form).then((data) => {
              this.$message.success("添加成功");
              this.visible = false;
              this.$emit("refreshDataList");
            });
          }
        }
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getDaohangTree().then((response) => {
        this.deptOptions = [];
        const dept = { id: -1, name: "根", children: response.data.data };
        this.deptOptions.push(dept);
      });
    },
    handleGetDaohangTreeFn() {
      fetchMenuTree().then((res) => {
        // console.log("获取导航树", res);
        if (res.data.code == 200) {
          let treeDeptData = res.data.data;
          treeDeptData.forEach((item) => {
            item.show = false;
          });
          this.treeDeptData = treeDeptData;
          this.deptOptions = [];
          const dept = { id: -1, name: "根", children: res.data.data };
          this.deptOptions.push(dept);
          // this.$refs.deptTreeRef.setCurrentKey(this.currentDhId);

          //
        }
      });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
