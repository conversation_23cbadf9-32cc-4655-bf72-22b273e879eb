<template>
  <div class="dh-container">
    <div class="dd-content">
      <div class="dept-left">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          "
        >
          <span style="font-size: 16px; font-weight: 500">资源列表</span>
          <img
            src="@/assets/image/appGroup/addAppGroup.png"
            style="width: 18px; width: 18px; cursor: pointer"
            alt=""
            @click="addOneLevelDaohang"
          />
        </div>

        <el-input class="ns-input" placeholder="搜索" v-model="nameSearch">
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <!-- treeDeptData   :default-expanded-keys="groupExpandedKeys"  :render-content="renderContent"  -->
        <el-tree
          class="filter-tree"
          :data="treeDeptData"
          :props="defaultProps"
          node-key="id"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultExpandedKeys"
          @node-click="handleCheckChange"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
          :filter-node-method="handleDeptfilterNode"
          ref="deptTreeRef"
        >
          <!-- <span
            class="custom-tree-node label-content"
            slot-scope="{ node, data }"
            style="width: 100%"
            @mouseenter="mouseenter(data)"
            @mouseleave="mouseleave(data)"
          >
            <span>{{ node.label }}</span>
            <span>
              <span
                v-show="data.show"
                style="margin-left: 5px"
                type="primary"
                class="el-icon-plus"
                @click.stop="handleMouseAdd(data)"
              ></span>

              <span
                v-show="data.show"
                size="mini"
                style="
                  margin-left: 5px;
                  display: inline-block;
                  width: 10px;
                  height: 10px;
                "
                type="primary"
                class="el-icon-delete"
              >
              </span>
            </span>
          </span> -->
        </el-tree>
      </div>
      <div class="de-right">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="菜单" name="first"></el-tab-pane>
          <el-tab-pane label="功能" name="second"></el-tab-pane>
        </el-tabs>
        <div class="dr-content" v-if="activeName == 'first'">
          <div class="ros-main">
            <div class="title-content">
              <span class="t-line"></span>
              <span>菜单维护</span>
            </div>
            <!-- <div class="btn-content">
              <el-button v-if="editFlag" @click="handleEdit">编辑</el-button>
              <el-button v-else @click="handleResetForm">重置</el-button>
              <el-button
                v-if="editFlag && this.form.id"
                @click="handleDel"
                type="danger"
                >删除</el-button
              >
              <el-button
                v-else
                type="primary"
                @click="debounceAddOrUpdateDaohangFn"
                >保存</el-button
              >
            </div> -->
          </div>

          <el-form
            ref="form"
            :model="form"
            label-width="80px"
            :rules="rules"
            :disabled="editFlag"
          >
            <!--  -->
            <el-row>
              <el-col :span="16">
                <el-form-item label="上级菜单" prop="parentId">
                  <treeselect
                    v-model="form.parentId"
                    :options="deptOptions"
                    :normalizer="normalizer"
                    :show-count="true"
                    placeholder="选择上级菜单"
                    :disabled="editFlag"
                  />
                  <!-- disabled -->
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="菜单类型" prop="menuType">
              <el-radio-group v-model="form.menuType" size="small">
                <el-radio label="C">左菜单</el-radio>
                <el-radio label="T">顶菜单</el-radio>
                <el-radio label="M">目录</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="单页面"
              prop="routeType"
              v-if="form.menuType == 'T'"
            >
              <el-switch
                v-model="form.routeType"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
            </el-form-item>
            <div class="form-row">
              <el-form-item label="菜单名称" prop="menuName">
                <el-input
                  v-model="form.menuName"
                  placeholder="请输入菜单名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="菜单编号" prop="menuCode">
                <el-input
                  v-model="form.menuCode"
                  placeholder="请输入菜单编号"
                ></el-input>
                <!-- <el-input-number
                  v-model="form.sortOrder"
                  controls-position="right"
                  :min="0"
                ></el-input-number> -->
              </el-form-item>
              <el-form-item label="子系统" prop="subSysId">
                <el-select v-model="form.subSysId" placeholder="请选择子系统">
                  <el-option
                    v-for="item in sonSystemList"
                    :key="item.id"
                    :label="item.sysName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="菜单路径" prop="path">
              <el-input
                v-model="form.path"
                class="row-input"
                placeholder="请输入菜单路径"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="组件路径"
              prop="component"
              v-if="form.menuType != 'M'"
            >
              <el-input
                v-model="form.component"
                class="row-input"
                placeholder="请输入组件路径"
              ></el-input>
            </el-form-item>
            <el-form-item label="内嵌地址" prop="query">
              <el-input
                type="textarea"
                v-model="form.query"
                placeholder="请输入内嵌地址"
                class="row-input"
              />
              <!-- <el-tooltip
                class="item"
                effect="light"
                content="必须为json结构：{key:value}"
                placement="top"
              >
                <i style="padding-left: 5px" class="el-icon-question" />
              </el-tooltip> -->
            </el-form-item>
            <el-form-item label="图标" prop="icon">
              <avue-input-icon
                v-model="form.icon"
                :icon-list="iconList"
                class="row-input"
                :disabled="editFlag"
                :close-on-click-modal="false"
                placeholder="请选择图标"
              ></avue-input-icon>
            </el-form-item>
            <el-form-item label="排序">
              <el-input-number
                v-model="form.sortOrder"
                controls-position="right"
                :min="0"
                placeholder="请输入排序"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="说明">
              <el-input
                type="textarea"
                v-model="form.remark"
                style="width: 60%"
                placeholder="请输入说明"
              ></el-input>
            </el-form-item>
          </el-form>
          <div class="btn-content">
            <el-button v-if="editFlag" @click="handleEdit">编辑</el-button>
            <el-button v-else @click="handleCancel">取消</el-button>
            <!-- <el-button v-else @click="handleResetForm">重置</el-button> -->
            <el-button
              v-if="editFlag && this.form.id"
              @click="handleDel"
              type="danger"
              >删除</el-button
            >
            <el-button
              v-else
              type="primary"
              @click="debounceAddOrUpdateDaohangFn"
              >保存</el-button
            >
          </div>
        </div>
        <div class="dr-content" v-if="activeName == 'second'">
          <function-list
            ref="functionRef"
            :chooseObj="chooseObj"
          ></function-list>
          <!-- <api-list
            class="api-content"
            ref="apiRef"
            :chooseObj="chooseObj"
          ></api-list> -->
        </div>
      </div>
    </div>
    <ziyuan-form
      v-if="ziyuanFlag"
      ref="daohangFormRef"
      @refreshDataList="hangdleGetTreeDeptFn"
      @handleCloseDiaolog="closeDaohangDialog"
      :sonSystemList="sonSystemList"
    ></ziyuan-form>
    <!-- <daohang-form
      v-if="dhFormVisible"
      ref="daohangFormRef"
      @refreshDataList="hangdleGetTreeDeptFn"
      @handleCloseDiaolog="closeDaohangDialog"
    >
    </daohang-form>
    <daohang-system
      v-if="dhSystemFlag"
      @handleCloseDialog="handleCloseSystem"
      ref="daohangSystemRef"
      :chooseObj="chooseObj"
    ></daohang-system> -->
  </div>
</template>
<script>
// import TreeNode from "./compontents/TreeNode.vue";

//
import debounce from "@/util/debounce";
import iconList from "@/const/iconList";
// import DaohangForm from "./compontents/DaohangForm.vue";
// import SystemList from "./compontents/SystemList.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { addObj, putObj, fetchMenuTree, delObj } from "@/api/admin/sys/menu.js";
// import DaohangSystem from "./compontents/DaohangSystem.vue";

import FunctionList from "./compontents/FunctionList.vue";
import ApiList from "./compontents/ApiList.vue";
import { getAllSonSystemList } from "@/api/sonSystem/index";

import ZiyuanForm from "./compontents/ZiyuanForm.vue";
import { findItem } from "@/util/util";
export default {
  components: {
    // DaohangForm,
    // SystemList,
    // DaohangSystem,
    Treeselect,
    FunctionList,
    ApiList,
    ZiyuanForm,
    // TreeNode,
  },
  data() {
    return {
      activeName: "first",
      ziyuanFlag: false,
      nameSearch: "",
      form: {
        parentId: -1,
        subSysId: "",
        menuName: "",
        menuCode: "",
        path: "",
        icon: "",
        sortOrder: "",
        remark: "",
        menuType: "",
        routeType: "",
        component: "",
        query: "",
        // parentId: undefined,
      },
      sonSysArr: [],
      iconList: iconList,
      chooseObj: {},
      treeDeptData: [],
      defaultProps: {
        label: "name",
        value: "id",
      },
      editFlag: true,
      dhSystemFlag: false,
      currentDhId: "",
      deptOptions: [],
      sonSystemList: [],
      defaultExpandedKeys: [],
      oldForm: {},
      rules: {
        menuName: [
          { required: true, message: "请输入菜单名称", trigger: "blur" },
        ],
        parentId: [
          { required: false, message: "请选择上级导航", trigger: "change" },
        ],
        menuCode: [
          { required: true, message: "请输入菜单编号", trigger: "blur" },
        ],
        subSysId: [
          { required: true, message: "请选择子系统", trigger: "change" },
        ],
        path: [{ required: true, message: "请输入菜单路径", trigger: "blur" }],
        menuType: [
          { required: true, message: "请选择菜单类型", trigger: "change" },
        ],
      },
      //   data: [
      //     {
      //       id: 0,
      //       name: "水果",
      //       show: false,
      //       children: [
      //         {
      //           id: 1,
      //           name: "苹果",
      //           show: false,
      //         },
      //         {
      //           id: 2,
      //           name: "芒果",
      //           show: false,
      //         },
      //       ],
      //     },
      //   ],
      //   nodeData: {
      //     name: "菜单1",
      //     id: "cd001",
      //     children: [
      //       {
      //         name: "菜单1-1",
      //         id: "cd00101",
      //         children: [
      //           {
      //             name: "菜单1-1-1",
      //             id: "cd0010101",
      //           },
      //         ],
      //       },
      //     ],
      //   },
    };
  },
  watch: {
    nameSearch(val) {
      this.$refs.deptTreeRef.filter(val);
    },
  },
  created() {
    this.handleGetDaohangTreeFn();
    this.getSonSystemListData();
  },
  methods: {
    getSonSystemListData() {
      getAllSonSystemList().then((res) => {
        // console.log("子系统==", res);
        if (res.data.code == 200) {
          this.sonSystemList = res.data.data;
        }
      });
    },
    handleClick() {
      if (this.activeName == "second") {
        this.$nextTick(() => {
          if (this.chooseObj.id) {
            this.$refs.functionRef.refshList(this.chooseObj);
            // this.$refs.apiRef.refshList(this.chooseObj);
          }
        });
      }
    },
    handleMouseAdd(data) {
      console.log("add===", data);
    },
    /** 查询菜单下拉树结构 */
    // getTreeselect() {
    //   getDaohangTree().then((response) => {
    //     this.deptOptions = [];
    //     const dept = { id: -1, name: "根", children: response.data.data };
    //     this.deptOptions.push(dept);
    //   });
    // },

    //chooseSelection
    handleDelSystem() {
      //sysListRef
      // console.log("this.9999---===", this.$refs.sysListRef);
      let chooseSelection = this.$refs.sysListRef.chooseSelection;
      if (chooseSelection && chooseSelection.length > 0) {
        let idList = [];
        chooseSelection.forEach((item) => {
          idList.push(item.id);
        });
        let params = {
          idList: idList,
        };

        let that = this;
        this.$confirm("是否确认批量删除?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return delBatchDaohangData(params);
          })
          .then((res) => {
            // console.log("data==", res);
            if (res.data.code == 200) {
              this.$message.success("删除成功");
              this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
            } else {
              this.$message.error("删除失败");
            }
          });

        // delBatchDaohangData(params).then((res) => {
        //   console.log("res----", res);
        //   if (res.data.code == 200) {
        //     this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
        //   }
        // });
      }
    },
    // renderContent(h, { node, data, store }) {
    //   if (data.level == "root") {
    //     return (
    //       <span>
    //         <el-tooltip
    //           class="item"
    //           effect="dark"
    //           content={node.label}
    //           placement="top"
    //         >
    //           <span>
    //             <i class="el-icon-s-home"></i> {node.label}
    //           </span>
    //         </el-tooltip>
    //       </span>
    //     );
    //   } else {
    //     return (
    //       <span>
    //         <el-tooltip
    //           class="item"
    //           effect="dark"
    //           content={node.label}
    //           placement="top"
    //         >
    //           <span>
    //             <i class="el-icon-document"></i> {node.label}
    //           </span>
    //         </el-tooltip>
    //       </span>
    //     );
    //   }
    // },
    renderContent(h, { node, data, store }) {
      // on-mouseover={() => this.clickMenu(data)}
      return (
        <span class="custom-tree-node">
          <span>{node.label}</span>
          <span>
            <el-button
              size="mini"
              type="text"
              on-click={() => this.clickMenu(data)}
            >
              Append
            </el-button>
            {/* <div>固定123456</div> */}
          </span>
        </span>
      );
    },
    mouseenter(data) {
      //   console.log(data);
      this.$set(data, "show", true);
      //   this.$set(data, "show", true);
      console.log("data.show==", data.show);
    },
    hangdleAddSystem() {
      // console.log("this.chooseObj==", this.chooseObj);
      if (!this.chooseObj.id) {
        this.$message({
          message: "请先选择左侧导航",
          type: "warning",
        });
        return;
      }

      this.dhSystemFlag = true;

      this.$nextTick(() => {
        this.$refs.daohangSystemRef.handleGetDaohangList();
      });
    },
    handleCloseSystem() {
      this.dhSystemFlag = false;
      this.$refs.sysListRef.handleGetDaohangList(this.chooseObj.id);
    },
    mouseleave(data) {
      //   console.log(data);

      this.$set(data, "show", false);
    },
    clickMenu(data) {
      console.log("menu--", data);
    },
    handleEdit() {
      if (!this.form.id) {
        this.$message({
          message: "请先选择左侧导航",
          type: "warning",
        });
        return;
      }
      this.editFlag = false;
    },
    handleResetForm() {
      // this.form.sortOrder = undefined;
      // this.form.name = "";
      // this.form.remark = "";
      this.form = JSON.parse(JSON.stringify(this.oldForm));
    },
    handleCancel() {
      this.editFlag = true;
    },
    handleDel() {
      if (!this.form.id) {
        return;
      }
      let that = this;
      this.$confirm("是否确认删除:" + this.form.name, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delObj(that.form.id);
        })
        .then((res) => {
          console.log("data==", res);
          if (res.status && res.data.code == 200) {
            this.$message.success("删除成功");
            this.currentDhId = "";
            this.chooseObj = {};
            this.handleGetDaohangTreeFn();
            // this.form = {};
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    addOneLevelDaohang() {
      // console.log("点击导航新增==");
      // this.form.parentId = -1;
      // this.form.name = "";
      // this.form.sortOrder = "";
      // this.form.remark = "";
      this.ziyuanFlag = true;
      // console.log(" this.form===", this.form);
      this.$nextTick(() => {
        this.$refs.daohangFormRef.setParentId(this.form.id);
      });
      //init
    },
    handleDeptfilterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    hangdleGetTreeDeptFn() {
      this.ziyuanFlag = false;
      this.handleGetDaohangTreeFn();
    },
    closeDaohangDialog() {
      this.ziyuanFlag = false;
    },
    handleGetDaohangTreeFn() {
      fetchMenuTree().then((res) => {
        // console.log("获取导航树", res);
        if (res.data.code == 200) {
          let treeDeptData = res.data.data;
          treeDeptData.forEach((item) => {
            item.show = false;
          });
          // this.treeDeptData = treeDeptData;
          this.treeDeptData = Object.assign(
            [],
            this.treeDeptData,
            treeDeptData
          );
          this.deptOptions = [];
          const dept = { id: -1, name: "根", children: res.data.data };
          this.deptOptions.push(dept);
          // this.$refs.deptTreeRef.setCurrentKey(this.currentDhId);
          setTimeout(() => {
            console.log("this.currentDhId==", this.currentDhId);
            if (this.currentDhId) {
              // console.log("设置。。。。", this.chooseObj);
              // this.$refs.deptTreeRef.setCheckedKeys([this.currentDhId]);

              //defaultExpandedKeys
              // let arr = findItem(this.treeDeptData, "id", this.currentDhId);
              // // console.log("arr===", arr);
              // this.defaultExpandedKeys = arr;
              this.$refs.deptTreeRef.setCurrentKey(this.currentDhId);
              // this.$refs.deptTreeRef.setCheckedNodes([this.chooseObj]);
              // this.$refs.deptTreeRef.setCurrentNode([this.chooseObj]);
            } else {
              if (this.treeDeptData && this.treeDeptData.length > 0) {
                this.$refs.deptTreeRef.setCurrentKey(this.treeDeptData[0].id); //默认选中第一条
                this.handleCheckChange(this.treeDeptData[0]);
              }
            }
          }, 500);
          //
        }
      });
    },
    debounceAddOrUpdateDaohangFn: debounce(function () {
      this.addOrUpdateDaohang();
    }, 500),
    addOrUpdateDaohang() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.parentId === undefined) {
            this.form.parentId = -1;
          }
          if (this.form.id) {
            putObj(this.form).then((res) => {
              // console.log("修改导航的数据==", res);
              if (res.data.code == 200) {
                this.editFlag = true;
                this.$message({
                  showClose: true,
                  message: "修改成功",
                  type: "success",
                });
                // this.form = {};
                this.handleGetDaohangTreeFn();
              }
            });
          } else {
            addObj(this.form).then((res) => {
              // console.log("新增导航的数据==", res);
              if (res.data.code == 200) {
                this.$message({
                  showClose: true,
                  message: "新增成功",
                  type: "success",
                });
                // this.form = {};
                this.handleGetDaohangTreeFn();
              }
            });
          }
        }
      });
    },
    // 树展开
    handleNodeExpand(data) {
      // console.log("data==", data);
      this.defaultExpandedKeys.push(data.id);
    },
    //树折叠时
    handleNodeCollapse(data) {
      let fIndex = this.defaultExpandedKeys.findIndex((id) => {
        return id == data.id;
      });

      if (fIndex != -1) {
        this.defaultExpandedKeys.splice(fIndex, 1);
      }
    },
    handleCheckChange(data) {
      // data.subSysId = "1749992083606016002";

      this.currentDhId = data.id;
      //   this.getList(this.page);
      this.editFlag = true;
      this.form = Object.assign(this.form, data);
      this.form.menuName = data.name;
      this.oldForm = JSON.parse(JSON.stringify(this.form));

      this.chooseObj = data;
      if (this.activeName == "second") {
        this.$nextTick(() => {
          this.$refs.functionRef.refshList(this.chooseObj);
          // this.$refs.apiRef.refshList(this.chooseObj);
        });
      }

      // this.$refs.sysListRef.handleGetDaohangList(data.id);
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }

      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.dh-container {
  //   background: #f0f2f5;
  position: relative;
  min-height: calc(100vh - 240px);
  .dd-content {
    margin-left: 20px;
    margin-top: 20px;
    display: flex;

    .dept-left {
      width: 320px;
      // background: #f0f2f5;
      // border: 1px solid #dcdfe6;
      padding: 4px;
      min-height: calc(100vh - 200px);
      background: #fff;
      border-radius: 4px;
      overflow-x: scroll;

      .ns-input {
        margin-bottom: 10px;
      }
    }
    .de-right {
      flex: 1;

      margin-left: 20px;
      background: #fff;
      padding: 20px;
      .dr-content {
        // border: 1px solid #dcdfe6;
        width: calc(100% - 10px);
        background: #fff;
        border-radius: 4px;
        padding: 10px;

        .ros-main {
          display: flex;
          margin: 20px 0;
          align-items: center;
          justify-content: space-between;
          // padding: 0 24px;
        }
        .api-content {
          margin-top: 20px;
        }
      }
      .dr-system {
        position: relative;
        margin-top: 36px;
        padding: 10px;
        width: calc(100% - 10px);
        background: #fff;
        .ds-content {
          text-align: right;
        }
        .sys-main {
          display: flex;
          justify-content: space-between;
        }
      }
      .title-content {
        display: flex;
        align-items: center;
        .t-line {
          height: 12px;
          width: 3px;
          // background: #002fa7;
          background: #327fff;
          display: inline-block;
          margin-right: 6px;
        }
      }

      .sys-content {
        // width: 60%;
      }
    }
  }
  .form-row {
    display: flex;
  }
  .row-input {
    width: 60%;
  }
  .label-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 0;
  }

  .no-content {
    text-align: center;
  }
  .no-data {
    background: url("../../assets/image/no-data.png") no-repeat;
    background-size: 100%;
    width: 197px;
    height: 132px;
    margin: 0 auto;
  }
  .btn-content {
    margin: 40px 0 40px 30%;
  }
}
::v-deep .vue-treeselect--disabled .vue-treeselect__control {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
::v-deep .vue-treeselect--disabled .vue-treeselect__single-value {
  color: #c0c4cc;
}
</style>