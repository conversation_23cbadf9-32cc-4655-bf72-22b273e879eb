/*
 * @Author: liuzhengshuai <EMAIL>
 * @Date: 2024-09-11 09:16:41
 * @LastEditors: liuzhengshuai <EMAIL>
 * @LastEditTime: 2024-11-08 15:55:01
 * @FilePath: \uc-portal\vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 配置该文件可以参考:
 * https://cli.vuejs.org/zh/config/#%E7%9B%AE%E6%A0%87%E6%B5%8F%E8%A7%88%E5%99%A8
 *
 */
const api = process.env.VUE_APP_BASE_API_PRE
const host = process.env.VUE_APP_BASE_HOST
const portalHost = process.env.VUE_MESSAGEAPP_BASE_HOST
const portal = process.env.VUE_APP_BASE_portal_PRE

const CompressionWebpackPlugin = require("compression-webpack-plugin");
const productionGzipExtensions = ["js", "css"];

// 判断是否是生产环境
const isProduction = process.env.ENV === "production";
const isTest = process.env.ENV === "test";

// 导入速度分析插件
const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");
const smp = new SpeedMeasurePlugin();

// 基础路径，发布前修改这里,当前配置打包出来的资源都是相对路径
let publicPath = process.env.VUE_APP_BASE_SERVER_PATH
module.exports = {
  publicPath: publicPath,
  outputDir: 'sjjnewsweb', // 修改输出目录名称为 sjjnewsweb
  lintOnSave: true,
  productionSourceMap: false,
  css: {
    // 忽略 CSS order 顺序警告
    extract: { ignoreOrder: true }
  },
  chainWebpack: config => {
    const entry = config.entry('app')
    entry
      .add('babel-polyfill')
      .end()
    entry
      .add('classlist-polyfill')
      .end()
  },
  configureWebpack: (config) => {
    if (isProduction || isTest) {
      // 仅在生产环境下启用该配置
      return smp.wrap({
        performance: {
          // 打包后最大文件大小限制
          maxAssetSize: 1024000,
        },
        plugins: [
          new CompressionWebpackPlugin({
            filename: "[path].gz[query]",
            algorithm: "gzip",
            test: new RegExp(
              "\\.(" + productionGzipExtensions.join("|") + ")$"
            ),
            threshold: 1024, // 只有大小大于该值的资源会被处理,当前配置为对于超过1k的数据进行处理，不足1k的可能会越压缩越大
            minRatio: 0.99, // 只有压缩率小于这个值的资源才会被处理
            deleteOriginalAssets: true, // 删除原文件
          }),
        ],
      });
    }
  },

  // 配置转发代理
  devServer: {
    port: 8080,
    proxy: {
      '/portal-api': {
        target: host,
        ws: true,
        pathRewrite: {
          '^/portal-api': api
        }
      },
      // '/portal': {
      //   target: portalHost,
      //   ws: true,
      //   changeOrigin: true,
      //   // pathRewrite: {
      //   //   '^/portal': portalHost
      //   // }
      // },
    }
  }
}